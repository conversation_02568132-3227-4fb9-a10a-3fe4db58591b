# Static Assets Directory

This directory contains all static assets for the Steelflow application. These files are served directly from the root URL path.

## Directory Structure

```
public/
├── images/                    # All static images that don't require optimization
│   ├── logos/                 # Company and partner logos
│   ├── icons/                 # Custom icon assets not provided by icon libraries
│   │   ├── apple-touch-icon.png  # Apple touch icon for iOS devices
│   │   ├── favicon-96x96.png     # 96x96 favicon for browsers
│   │   ├── favicon.svg           # Vector version of the favicon
│   │   ├── icon-192x192.png      # 192x192 icon for PWA
│   │   └── icon-512x512.png      # 512x512 icon for PWA
│   ├── backgrounds/           # Background images and patterns
│   └── placeholder.svg        # Placeholder image
├── fonts/                     # Custom font files (only if not using next/font)
├── documents/                 # PDFs, spreadsheets, and other downloadable files
├── favicon.ico                # Main favicon
└── site.webmanifest           # Web app manifest file for PWA support
```

## Usage Guidelines

### Referencing Assets

When referencing assets in your code, use root-relative paths:

```tsx
// Correct
<img src="/images/logos/partner.png" alt="Partner logo" />

// Incorrect - don't include 'public' in the path
<img src="/public/images/logos/partner.png" alt="Partner logo" />
```

### Image Optimization

For images that benefit from Next.js optimization (like responsive images or images that need to be resized), use the Next.js Image component instead:

```tsx
import Image from "next/image";

// For optimized images
<Image src="/images/logo.png" alt="Logo" width={100} height={100} />;
```

### When to Use Public Directory

Only use the public directory for:

- Assets that don't require processing
- Files that need a public URL
- Static files like robots.txt, favicon.ico, etc.

For dynamic or processed assets, consider using other approaches like:

- Importing images directly in components (for webpack/Next.js processing)
- Using the Next.js Image component for optimization
- Using a CDN for large media files

## File Types

### Images

- Use SVG for logos, icons, and simple graphics
- Use WebP for photos when possible (with fallbacks if needed)
- Use PNG for images requiring transparency
- Use JPEG for photographs without transparency

### Documents

- PDF files for downloadable documents
- Excel/CSV files for data exports
- Other document types as needed

## Maintenance

Keep this directory organized by:

- Removing unused files regularly
- Following the naming conventions
- Optimizing images before adding them
- Documenting any special assets in this README
