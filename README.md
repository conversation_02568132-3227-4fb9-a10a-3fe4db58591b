# Steelflow

A specialized Third-Party Logistics Provider (3PL) management platform designed specifically for the steel industry. Steelflow streamlines logistics operations, manages transportation providers, and optimizes the quote request process for steel product shipping.

## Overview

Steelflow is an internal platform that helps manage and optimize logistics operations for steel industry transportation. It provides comprehensive tools for managing transport providers, their service routes, and different types of steel cargo.

### Key Features

- **Providers Management**

  - Company profiles and details
  - Multiple contact management with primary contact designation
  - Provider capabilities and certifications

- **Routes Management**

  - Country pair service routes
  - Transport type support (coil line trailer, containers, etc.)
  - Route-specific requirements and restrictions

- **Cargo Types Catalog**

  - Steel product type management
  - Supported cargo types:
    - Coils
    - Slit Coils
    - Beams
    - Plates
    - Sheets
    - Pipes

- **RFQS**
  - A feature to create an RFQ, with source, destination cargo type, and equipment along with dimensions and weight.
  - Matched Providers
  - AI based RFQ-Send out with AI written emails
  - Once sent, AI captured bids capture.

## Technical Stack

### Frontend

- Next.js (App Router)
- TypeScript
- TailwindCSS
- Shadcn/ui

### Backend

- Supabase (Database & Authentication)
- Unkey (API Key Management & Authentication)

## API Authentication

All API routes in the `/api/v1/` path are protected with Unkey authentication. Clients must include a valid API key in the Authorization header to access these endpoints.

```javascript
// Example of making an authenticated API request
const response = await fetch("https://your-api.com/api/v1/resource", {
  method: "GET",
  headers: {
    Authorization: `Bearer ${apiKey}`,
  },
});
```

For more details on API protection, see [API Protection Guide](docs/API_PROTECTION_GUIDE.md).

## Getting Started

### Prerequisites

- Node.js 18.x or later
- pnpm (preferred package manager)
- Git
- Google Maps API key (for address autocomplete and validation)

### Environment Variables

The following environment variables are required:

- `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY`: Your Google Maps API key for address autocomplete and validation functionality. You can obtain one at [Google Cloud Console](https://console.cloud.google.com/). Make sure to enable the Places API, Geocoding API, and Maps JavaScript API.
- `UNKEY_API_ID`: Your Unkey API ID for API authentication. You can obtain one at [Unkey Dashboard](https://unkey.dev/).
- `UNKEY_ROOT_KEY`: Your Unkey root key for managing API keys. You can obtain one at [Unkey Dashboard](https://unkey.dev/).
- `SENDGRID_API_KEY`: Your SendGrid API key for sending emails. You can obtain one at [SendGrid Dashboard](https://app.sendgrid.com/).
- `SENDGRID_FROM_EMAIL`: The email address to use as the sender for all outgoing emails.
- `SENDGRID_FROM_NAME`: The name to use as the sender for all outgoing emails.
- `SENDGRID_SANDBOX_MODE`: Set to "true" to enable sandbox mode for testing (emails won't be sent to actual recipients).
- `SENDGRID_SANDBOX_TO_EMAIL`: The email address to use as the recipient for all emails when sandbox mode is enabled.