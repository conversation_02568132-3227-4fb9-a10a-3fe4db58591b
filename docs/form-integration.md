# React Hook Form and Shadcn/UI Integration Guide

This guide explains how to properly integrate React Hook Form with shadcn/ui form components and Next.js Server Actions.

## Table of Contents

1. [Overview](#overview)
2. [Common Issues](#common-issues)
3. [Correct Integration Pattern](#correct-integration-pattern)
4. [Working with Server Actions](#working-with-server-actions)
5. [Examples](#examples)
6. [Troubleshooting](#troubleshooting)

## Overview

The shadcn/ui form components are built on top of React Hook Form and require access to the form context to function properly. This guide explains how to correctly set up this integration to avoid common issues.

## Common Issues

### 1. Missing Form Context

The most common issue is the error:

```
Error: Cannot destructure property 'getFieldState' of '(0 , react_hook_form__WEBPACK_IMPORTED_MODULE_4__.useFormContext)(...)' as it is null.
```

This occurs when shadcn/ui form components try to access the form context, but it's not properly provided.

### 2. Mixing Form Libraries

Mixing Next.js Form component with shadcn/ui form components can cause context conflicts. The shadcn/ui components expect to be used within a React Hook Form context.

### 3. Incorrect Form Submission

Not properly connecting form submission with React Hook Form's handleSubmit can lead to validation issues or form data not being processed correctly.

## Correct Integration Pattern

### Step 1: Import Required Components

```typescript
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
```

### Step 2: Set Up Form with React Hook Form

```typescript
const form = useForm<YourFormValues>({
  resolver: zodResolver(YourFormSchema),
  defaultValues: {
    field1: "",
    field2: "",
    // other fields
  },
  mode: "onChange",
});

// Get form methods
const { watch, setValue } = form;

// Watch form values for reactive updates
const formValues = watch();
```

### Step 3: Wrap Form Elements with FormProvider

```tsx
return (
  <FormProvider {...form}>
    <div className="space-y-6">
      <form onSubmit={form.handleSubmit(handleFormSubmit)}>
        {/* Form fields go here */}
      </form>
    </div>
  </FormProvider>
);
```

### Step 4: Use FormField Components Correctly

```tsx
<FormField
  control={form.control}
  name="fieldName"
  render={() => (
    <FormItem>
      <FormLabel>Field Label</FormLabel>
      <FormControl>
        <Input
          name="fieldName"
          value={formValues.fieldName}
          onChange={(e) => setValue("fieldName", e.target.value)}
        />
      </FormControl>
      <FormMessage />
    </FormItem>
  )}
/>
```

## Working with Server Actions

When using Next.js Server Actions with React Hook Form:

### Step 1: Create a Form Submission Handler

```typescript
const handleFormSubmit = (values: YourFormValues) => {
  // Create a FormData object
  const formData = new FormData();

  // Add form values to FormData
  Object.entries(values).forEach(([key, value]) => {
    formData.append(key, value?.toString() || "");
  });

  // Submit the form using the server action
  yourServerAction(formData);
};
```

### Step 2: Set Up Action State

```typescript
const [actionState, action, isPending] = useActionState<
  YourActionState,
  FormData
>(yourServerAction, {
  success: false,
  values: {
    // default values
  },
});
```

### Step 3: Handle Action Results

```typescript
useEffect(() => {
  if (actionState.success) {
    toast.success("Operation successful");
    // Additional success handling
  } else if (actionState.error) {
    toast.error(actionState.error);

    // Handle field validation errors
    if (actionState.fieldErrors) {
      Object.entries(actionState.fieldErrors).forEach(([field, errors]) => {
        if (errors && errors.length > 0) {
          form.setError(field as any, {
            type: "server",
            message: errors[0],
          });
        }
      });
    }
  }
}, [actionState, form]);
```

## Examples

### Basic Form Example

```tsx
"use client";

import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { toast } from "@/components/ui/sonner";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { z } from "zod";

// Define form schema
const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
});

type FormValues = z.infer<typeof formSchema>;

export function ExampleForm() {
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
    },
    mode: "onChange",
  });

  const { watch, setValue } = form;
  const formValues = watch();

  const handleFormSubmit = (values: FormValues) => {
    // Process form submission
    console.log(values);
    toast.success("Form submitted successfully");
  };

  return (
    <FormProvider {...form}>
      <div className="space-y-6">
        <form onSubmit={form.handleSubmit(handleFormSubmit)}>
          <FormField
            control={form.control}
            name="name"
            render={() => (
              <FormItem>
                <FormLabel required>Name</FormLabel>
                <FormControl>
                  <Input
                    name="name"
                    value={formValues.name}
                    onChange={(e) => setValue("name", e.target.value)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={() => (
              <FormItem>
                <FormLabel required>Email</FormLabel>
                <FormControl>
                  <Input
                    name="email"
                    value={formValues.email}
                    onChange={(e) => setValue("email", e.target.value)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="mt-4 flex justify-end">
            <Button type="submit">Submit</Button>
          </div>
        </form>
      </div>
    </FormProvider>
  );
}
```

## Troubleshooting

### Form Context Error

If you see the error about `getFieldState` being null:

1. Check that you've wrapped your form with `<FormProvider {...form}>`
2. Ensure you're not mixing Next.js Form component with shadcn/ui components
3. Verify that all form components are within the FormProvider

### Form Values Not Updating

If form values aren't updating:

1. Make sure you're using `watch()` to get reactive form values
2. Check that you're using `setValue` correctly to update form values
3. Verify that the form field names match your schema

### Form Submission Not Working

If form submission isn't working:

1. Ensure you're using `form.handleSubmit` correctly
2. Check that your form values are being properly passed to the submission handler
3. Verify that your server action is receiving the correct data
