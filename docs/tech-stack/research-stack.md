# Research Stack

This document contains detailed information about the technologies used in this project, including the latest versions, features, installation instructions, best practices, and integration patterns. All information is based on the latest documentation available through Context7.

## Table of Contents

1. [Next.js 15](#nextjs-15)
2. [React 19](#react-19)
3. [State Management](#state-management)
   - [Zustand](#zustand)
   - [TanStack Query](#tanstack-query)
4. [Google Gemini AI](#google-gemini-ai)
5. [UI Libraries](#ui-libraries)
   - [Shadcn UI](#shadcn-ui)
   - [Radix UI](#radix-ui)
   - [Tailwind CSS](#tailwind-css)
6. [Supabase](#supabase)

## Next.js 15

### Version Information

Next.js 15 is the latest version of the React framework developed by Vercel. It builds on the App Router architecture introduced in Next.js 13 and includes significant improvements to performance, developer experience, and features.

### Installation and Setup

To create a new Next.js 15 project:

```bash
npx create-next-app@latest my-app
```

During setup, you'll be prompted to configure your project:

```
Would you like to use TypeScript? Yes
Would you like to use ESLint? Yes
Would you like to use Tailwind CSS? Yes
Would you like to use `src/` directory? Yes
Would you like to use App Router? (recommended) Yes
Would you like to customize the default import alias (@/*)? Yes
```

### App Router Features

Next.js 15 uses the App Router, which is built on React Server Components and provides a new way to organize your application:

- **Layouts**: Shared UI between routes that preserves state and doesn't re-render
- **Server Components**: Default component type that renders on the server
- **Client Components**: Components that render on the client with the `"use client"` directive
- **Streaming**: Progressively render UI from the server
- **Suspense**: Defer rendering parts of your application until some condition is met

### Server Components vs Client Components

#### Server Components

Server Components are the default in Next.js 15. They:

- Render on the server and send HTML to the client
- Can fetch data directly
- Can access backend resources directly
- Cannot use hooks or browser APIs
- Cannot handle client-side interactivity

#### Client Components

Client Components are opt-in with the `"use client"` directive. They:

- Render on the client
- Can use hooks and browser APIs
- Can handle client-side interactivity
- Cannot access backend resources directly
- Should be used sparingly for interactive parts of the UI

### Data Fetching

Next.js 15 provides several ways to fetch data:

- **Server Components**: Fetch data directly in the component
- **Route Handlers**: Create API endpoints with the `route.ts` file
- **Server Actions**: Perform mutations with form actions
- **Client-Side Fetching**: Use SWR or React Query for client-side data fetching

### Best Practices

1. **Use Server Components by default** - Only use Client Components when necessary for interactivity or browser APIs
2. **Colocate components** - Keep components close to where they're used
3. **Use React Server Actions for forms** - Simplifies form handling and validation
4. **Implement proper error boundaries** - Use error.tsx files to handle errors gracefully
5. **Use Suspense for loading states** - Provides a better user experience during data loading

## React 19

### Version Information

React 19 is the latest version of the React library, with significant improvements to server components, concurrent rendering, and new hooks.

### Installation

```bash
pnpm add react@latest react-dom@latest
```

### New Features and Hooks

- **Enhanced Server Components**: Improved performance and capabilities
- **Concurrent Rendering**: Better user experience with non-blocking rendering
- **useActionState**: Replaces useFormState with improved functionality
- **useFormStatus**: Enhanced with new properties (data, method, action)
- **useOptimistic**: Create optimistic UI updates for better UX

### Integration with Next.js

React 19 is designed to work seamlessly with Next.js 15, particularly with:

1. **Server Components**: The default component type in Next.js App Router
2. **Suspense**: Used for loading states in Next.js
3. **Server Actions**: Used for form handling and data mutations

### Best Practices

1. **Prefer function components over class components**
2. **Use hooks for state and side effects**
3. **Implement proper error boundaries**
4. **Use React.memo for performance optimization when necessary**
5. **Keep components small and focused on a single responsibility**

## State Management

Our application uses a combination of state management solutions for different purposes:

1. **UI State**: Zustand for lightweight, flexible client-side state
2. **Server State**: TanStack Query for data fetching, caching, and synchronization
3. **Form State**: React's built-in useActionState with server actions

### Zustand

Zustand is a small, fast, and scalable state management solution. It uses a simple API based on hooks and is perfect for managing UI state.

#### Installation

```bash
pnpm add zustand
```

#### Key Features

- **Simple API**: Create stores with a single hook
- **Minimal Boilerplate**: No providers, reducers, or complex setup
- **TypeScript Support**: Full type safety with TypeScript
- **Middleware Support**: Includes middleware for persistence, devtools, etc.
- **Selective Updates**: Components only re-render when their subscribed state changes

#### Basic Usage

```typescript
// src/lib/store/counter.ts
import { create } from 'zustand';

interface CounterState {
  count: number;
  increment: () => void;
  decrement: () => void;
}

export const useCounterStore = create<CounterState>((set) => ({
  count: 0,
  increment: () => set((state) => ({ count: state.count + 1 })),
  decrement: () => set((state) => ({ count: state.count - 1 })),
}));

// In a component
import { useCounterStore } from '@/lib/store/counter';

function Counter() {
  const { count, increment, decrement } = useCounterStore();

  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={increment}>Increment</button>
      <button onClick={decrement}>Decrement</button>
    </div>
  );
}
```

#### Best Practices

1. **Create separate stores for different domains**: Keep stores focused on specific features
2. **Use selectors for performance**: Only subscribe to the state you need
3. **Combine with React.memo**: For optimal performance in large applications
4. **Use TypeScript**: For type safety and better developer experience
5. **Use middleware when needed**: For persistence, logging, etc.

### TanStack Query

TanStack Query (formerly React Query) is a data fetching and caching library that makes working with server state easy and efficient.

#### Installation

```bash
pnpm add @tanstack/react-query
```

#### Key Features

- **Automatic Caching**: Caches query results and provides them instantly
- **Background Fetching**: Automatically refetches data in the background
- **Stale-While-Revalidate**: Shows stale data while fetching fresh data
- **Pagination and Infinite Queries**: Built-in support for pagination
- **Mutations**: Easily update server state
- **Prefetching**: Prefetch data before it's needed
- **Devtools**: Powerful devtools for debugging

#### Setup

```typescript
// src/lib/providers/query-provider.tsx
'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useState, ReactNode } from 'react';

export function QueryProvider({ children }: { children: ReactNode }) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 60 * 1000, // 1 minute
        gcTime: 5 * 60 * 1000, // 5 minutes
        retry: 1,
      },
    },
  }));

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}
```

#### Basic Usage

```typescript
// Custom hook for fetching data
import { useQuery } from '@tanstack/react-query';
import { fetchProviders } from '@/lib/services/provider.service';

export function useProviders(params: GetProvidersParams) {
  return useQuery({
    queryKey: ['providers', params],
    queryFn: () => fetchProviders(params),
  });
}

// In a component
import { useProviders } from '@/lib/hooks/use-providers';

function ProvidersList() {
  const { data, isLoading, error } = useProviders({ page: 1, pageSize: 10 });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <ul>
      {data?.map(provider => (
        <li key={provider.id}>{provider.name}</li>
      ))}
    </ul>
  );
}
```

#### Best Practices

1. **Use query keys consistently**: Structure query keys to represent the data being fetched
2. **Set appropriate stale times**: Adjust based on how frequently data changes
3. **Use mutations for updates**: Use mutations to update server state
4. **Prefetch data when possible**: Improve user experience by prefetching data
5. **Use suspense mode when appropriate**: For a more declarative data fetching approach
6. **Implement error boundaries**: Handle errors gracefully

## Google Gemini AI

### Version Information

Google Gemini AI is Google's multimodal AI model that can understand and generate text, images, and other content. In this project, we use the Google GenAI SDK (version 0.12.0) to integrate with Gemini for text enhancement capabilities.

### Installation and Setup

```bash
pnpm add @google/genai
```

### Key Features and Components

The Gemini AI integration in this project provides several key features:

1. **Text Enhancement**: Improve email content with various enhancement options
2. **React Components**: Ready-to-use UI components for AI text enhancement
3. **Context Provider**: Simplified state management for AI interactions
4. **Error Handling**: Robust error handling for API issues

#### Core Components

- **GeminiProvider**: Context provider for Gemini AI integration
- **TextEnhancer**: Button with dropdown menu for text enhancement options
- **EnhancedTextarea**: Textarea with built-in AI enhancement capabilities
- **FloatingActionButton**: Floating button that can enhance text in any textarea

### Enhancement Types

The Gemini integration supports various text enhancement options:

```tsx
export enum EnhancementType {
  SHORTEN = "shorten",
  SPELLING_GRAMMAR = "spelling_grammar",
  SIMPLIFY = "simplify",
  IMPROVE_FLOW = "improve_flow",
  REWRITE = "rewrite",
  PROFESSIONAL = "professional",
  GENERIC = "generic",
}
```

### Best Practices

1. **Use server-side API calls** - Keep API keys secure by making Gemini API calls on the server
2. **Implement proper error handling** - Handle API errors gracefully
3. **Provide loading states** - Show loading indicators during API calls
4. **Use streaming for long responses** - Implement streaming for better UX with long responses
5. **Implement rate limiting** - Protect your API from abuse

## UI Libraries

### Shadcn UI

Shadcn UI is a collection of reusable components built with Radix UI and Tailwind CSS. It's not a component library but a collection of components that you can copy and paste into your project.

#### Installation and Setup

```bash
npx shadcn-ui@latest init
```

During setup, you'll be prompted to configure your project:

```
Which style would you like to use? Default
Which color would you like to use as base color? Slate
Where is your global CSS file? app/globals.css
Do you want to use CSS variables for colors? Yes
Where is your tailwind.config.js located? tailwind.config.js
Configure the import alias for components? Yes
Configure the import alias for utils? Yes
Are you using React Server Components? Yes
```

This will set up the necessary configuration files and create a components directory with the base components.

#### Component Usage and Customization

To add a component to your project:

```bash
npx shadcn-ui@latest add button
```

This will add the Button component to your project, which you can then import and use:

```tsx
import { Button } from "@/components/ui/button";

export default function Home() {
  return <Button variant="outline">Click me</Button>;
}
```

#### Best Practices

1. **Customize components to match your design** - Shadcn UI components are meant to be customized
2. **Use the CLI to add components** - This ensures proper setup and configuration
3. **Import only what you need** - Import components individually to reduce bundle size
4. **Extend components when needed** - Create your own variants and styles
5. **Use the form components with React Hook Form** - They're designed to work together

### Radix UI

Radix UI is a low-level UI component library focused on accessibility, customization, and developer experience. It provides the foundation for Shadcn UI components.

#### Installation

```bash
pnpm add @radix-ui/react-dialog @radix-ui/react-dropdown-menu
# Install other primitives as needed
```

#### Key Primitives

- **Dialog**: Modal dialogs
- **DropdownMenu**: Dropdown menus
- **Tabs**: Tabbed interfaces
- **Popover**: Popovers and tooltips
- **Select**: Select menus
- **Checkbox**: Checkboxes and radio buttons

#### Best Practices

1. **Use with Tailwind CSS** - Radix UI works well with Tailwind for styling
2. **Leverage accessibility features** - Radix UI components are accessible by default
3. **Compose primitives** - Combine primitives to create complex components
4. **Use with Shadcn UI** - Shadcn UI provides styled versions of Radix primitives
5. **Implement proper keyboard navigation** - Test keyboard navigation for all components

### Tailwind CSS

Tailwind CSS is a utility-first CSS framework that allows you to build custom designs without leaving your HTML.

#### Installation and Setup

To install Tailwind CSS in a Next.js project:

```bash
pnpm add -D tailwindcss postcss autoprefixer
pnpm exec tailwindcss init -p
```

Configure your `tailwind.config.js`:

```js
// tailwind.config.js
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        // Your custom colors
      },
    },
  },
  plugins: [],
};
```

#### Best Practices

1. **Use utility classes** - Embrace the utility-first approach
2. **Extract components** - Use @apply for repeated patterns
3. **Use responsive prefixes** - Design for mobile first, then add responsive variants
4. **Customize the theme** - Extend the theme to match your design system
5. **Use plugins** - Add plugins for additional functionality

## Supabase

### Version Information

Supabase is an open-source Firebase alternative that provides a PostgreSQL database, authentication, storage, and more.

### Installation and Setup

```bash
pnpm add @supabase/supabase-js
```

Create a Supabase client:

```typescript
// src/lib/supabase/client.ts
import { createClient } from "@supabase/supabase-js";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);
```

### Key Features

1. **PostgreSQL Database**: Full-featured PostgreSQL database
2. **Authentication**: User authentication with various providers
3. **Storage**: File storage with access control
4. **Realtime**: Real-time subscriptions for live updates
5. **Edge Functions**: Serverless functions for custom logic

### Integration with Next.js

Supabase provides official helpers for Next.js:

```bash
npm install @supabase/auth-helpers-nextjs
```

#### Server-Side Usage

```typescript
// src/app/page.tsx
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export default async function Page() {
  const supabase = createServerComponentClient({ cookies });
  const { data } = await supabase.from('posts').select();

  return (
    <ul>
      {data?.map((post) => (
        <li key={post.id}>{post.title}</li>
      ))}
    </ul>
  );
}
```

#### Client-Side Usage

```typescript
// src/app/client-component.tsx
'use client';

import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useEffect, useState } from 'react';

export default function ClientComponent() {
  const [posts, setPosts] = useState([]);
  const supabase = createClientComponentClient();

  useEffect(() => {
    const fetchPosts = async () => {
      const { data } = await supabase.from('posts').select();
      setPosts(data || []);
    };

    fetchPosts();
  }, [supabase]);

  return (
    <ul>
      {posts.map((post) => (
        <li key={post.id}>{post.title}</li>
      ))}
    </ul>
  );
}
```

### Best Practices

1. **Use environment variables for credentials** - Never hardcode your Supabase URL or API keys
2. **Implement Row Level Security** - Secure your data with RLS policies
3. **Handle errors properly** - Always check for errors in Supabase responses
4. **Use TypeScript for better type safety** - Define types for your database tables
5. **Use the auth helpers for Next.js** - They handle many common authentication patterns
6. **Keep sensitive operations server-side** - Use Server Components or API routes for sensitive operations
7. **Set up proper indexes** - Optimize your database queries with appropriate indexes
8. **Use transactions for related operations** - Ensure data consistency with transactions
