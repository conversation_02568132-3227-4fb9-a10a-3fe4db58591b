# Form Handling with Next.js 15 Server Actions

This document outlines the recommended approach for implementing forms in our application using Next.js 15 Server Actions. All new forms should follow these patterns, and existing forms should be gradually migrated to this approach.

## Table of Contents

- [Introduction to Server Actions](#introduction-to-server-actions)
- [Benefits Over Current Implementation](#benefits-over-current-implementation)
- [Implementation Guide](#implementation-guide)
  - [Defining Server Actions](#defining-server-actions)
  - [Form Components](#form-components)
  - [Validation](#validation)
  - [Error Handling](#error-handling)
  - [Loading States](#loading-states)
  - [Optimistic Updates](#optimistic-updates)
- [Migration Guide](#migration-guide)
- [Examples](#examples)

## Introduction to Server Actions

Server Actions in Next.js 15 are asynchronous functions that execute on the server. They provide a direct way to handle form submissions and data mutations from both Server and Client Components without creating separate API routes.

Key features:

- Marked with the `'use server'` directive
- Can be defined at function level or module level
- Integrated with Next.js caching and revalidation
- Support progressive enhancement (forms work without JavaScript)
- Automatically receive form data when used with the `action` prop

## Benefits Over Current Implementation

Adopting Server Actions for form handling offers several advantages:

1. **Simplified Form Submission**: Server Actions replace the current pattern of form submission via custom handlers, reducing the amount of code needed.

2. **Reduced Boilerplate**: Less code needed for form submission and validation, as Server Actions handle the form data extraction and submission process.

3. **Progressive Enhancement**: Forms work even without JavaScript, improving reliability and accessibility.

4. **Improved Error Handling**: Better integration with React's error boundaries and the new `useActionState` hook for managing form state.

5. **Optimistic Updates**: Easier implementation of optimistic UI updates with the `useOptimistic` hook.

## Implementation Guide

### Defining Server Actions

Server Actions should be defined in dedicated files under `src/lib/actions/` to maintain our service-action layer architecture.

```typescript
// src/lib/actions/item.actions.ts
"use server";

import { revalidatePath } from "next/cache";
import { createItem, validateItem } from "@/lib/services/item.service";
import { getCurrentUser } from "@/lib/services/auth.service";

export async function createItemAction(prevState: any, formData: FormData) {
  // Get current user for authorization
  const user = await getCurrentUser();
  if (!user) {
    return {
      success: false,
      message: "You must be signed in to perform this action",
    };
  }

  try {
    // Validate form data using our existing validation utilities
    const validationResult = await validateItem(formData);
    if (!validationResult.success) {
      return {
        success: false,
        message: validationResult.error?.message || "Validation failed",
        fieldErrors: validationResult.error?.fieldErrors,
      };
    }

    // Process validated data using our service layer
    const item = await createItem({
      ...validationResult.data,
      created_by: user.id,
    });

    // Revalidate related paths to update the UI
    revalidatePath("/dashboard/items");

    return {
      success: true,
      data: item,
      message: "Item created successfully",
    };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to create item",
    };
  }
}
```

### Form Components

Client components should use the `useActionState` hook to manage form state and the `action` prop to invoke Server Actions:

```tsx
// src/app/dashboard/items/_components/item-form.tsx
"use client";

import { useActionState } from "react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { toast } from "@/components/ui/sonner";
import { createItemAction } from "@/lib/actions/item.actions";
import { SubmitButton } from "./submit-button";

export function ItemForm() {
  const router = useRouter();
  const [state, formAction, pending] = useActionState(createItemAction, {});

  // Handle form submission results
  useEffect(() => {
    if (state?.success) {
      toast.success(state.message);
      router.push(`/dashboard/items/${state.data.id}`);
    } else if (state?.message) {
      toast.error(state.message);
    }
  }, [state, router]);

  return (
    <form action={formAction} className="space-y-6">
      <div className="space-y-2">
        <label htmlFor="name" className="text-sm font-medium">
          Name <span className="text-red-500">*</span>
        </label>
        <input
          id="name"
          name="name"
          type="text"
          required
          className="w-full rounded-md border p-2"
        />
        {state?.fieldErrors?.name && (
          <p className="text-sm text-red-500">{state.fieldErrors.name[0]}</p>
        )}
      </div>

      {/* Additional form fields */}

      <SubmitButton>Create Item</SubmitButton>
    </form>
  );
}
```

### Validation

Continue using Zod schemas for validation, but adapt them to work with Server Actions:

```typescript
// src/lib/services/item.service.ts
import { z } from "zod";
import { validateWithSchema } from "@/lib/utils/validation";
import { ItemSchema } from "@/lib/schemas/item.schema";

export async function validateItem(formData: FormData) {
  // Convert FormData to object
  const data = Object.fromEntries(formData.entries());

  // Validate using our existing schema
  return validateWithSchema(data, ItemSchema);
}
```

### Error Handling

Server Actions should return structured error responses that can be handled by the client:

```typescript
// Standard error response format
type ActionResponse<T> =
  | { success: true; data: T; message: string }
  | {
      success: false;
      message: string;
      fieldErrors?: Record<string, string[]>;
    };
```

### Loading States

Use the `pending` state from `useActionState` or create a separate submit button component with `useFormStatus`:

```tsx
// src/components/ui/submit-button.tsx
"use client";

import { useFormStatus } from "react-dom";
import { Button } from "@/components/ui/button";

export function SubmitButton({ children }: { children: React.ReactNode }) {
  const { pending } = useFormStatus();

  return (
    <Button type="submit" disabled={pending}>
      {pending ? "Processing..." : children}
    </Button>
  );
}
```

### Optimistic Updates

For operations where immediate feedback is important, use the `useOptimistic` hook:

```tsx
"use client";

import { useOptimistic } from "react";
import { addComment } from "@/lib/actions/comment.actions";

export function CommentForm({ comments, postId }) {
  const [optimisticComments, addOptimisticComment] = useOptimistic(
    comments,
    (state, newComment) => [...state, { text: newComment, pending: true }],
  );

  const formAction = async (formData) => {
    const comment = formData.get("comment");
    addOptimisticComment(comment);
    await addComment(postId, comment);
  };

  return (
    <>
      <div className="space-y-4">
        {optimisticComments.map((comment, i) => (
          <div key={i} className={comment.pending ? "opacity-50" : ""}>
            {comment.text}
          </div>
        ))}
      </div>
      <form action={formAction}>
        <textarea name="comment" className="w-full p-2 border rounded" />
        <button type="submit">Add Comment</button>
      </form>
    </>
  );
}
```

## Migration Guide

To migrate existing forms to use Server Actions:

1. Create a new Server Action in the appropriate actions file if one does not exist.

- If it exists, adapt it without breaking code. Ensure everything that depends on it is also fixed.

2. Convert the form component to use `useActionState` and the `action` prop
3. Update the validation logic to work with FormData

## Examples

### Basic Form Example

```tsx
// src/lib/actions/provider.actions.ts
"use server";

import { revalidatePath } from "next/cache";
import { createProvider } from "@/lib/services/provider.service";
import { validateWithSchema } from "@/lib/utils/validation";
import { CreateProviderSchema } from "@/lib/schemas/provider.schema";

export async function createProviderAction(prevState: any, formData: FormData) {
  try {
    // Validate form data
    const data = Object.fromEntries(formData.entries());
    const validationResult = validateWithSchema(data, CreateProviderSchema);

    if (!validationResult.success) {
      return {
        success: false,
        message: "Validation failed",
        fieldErrors: validationResult.error?.fieldErrors,
      };
    }

    // Create provider using service
    const provider = await createProvider(validationResult.data);

    // Revalidate path
    revalidatePath("/dashboard/providers");

    return {
      success: true,
      data: provider,
      message: "Provider created successfully",
    };
  } catch (error) {
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to create provider",
    };
  }
}
```

```tsx
// src/app/dashboard/providers/_components/provider-form.tsx
"use client";

import { useActionState } from "react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { toast } from "@/components/ui/sonner";
import { createProviderAction } from "@/lib/actions/provider.actions";

export function ProviderForm() {
  const router = useRouter();
  const [state, formAction, pending] = useActionState(createProviderAction, {});

  useEffect(() => {
    if (state?.success) {
      toast.success(state.message);
      router.push("/dashboard/providers");
    } else if (state?.message) {
      toast.error(state.message);
    }
  }, [state, router]);

  return (
    <form action={formAction} className="space-y-6">
      {/* Form fields */}
      <button
        type="submit"
        disabled={pending}
        className="px-4 py-2 bg-blue-500 text-white rounded"
      >
        {pending ? "Creating..." : "Create Provider"}
      </button>
    </form>
  );
}
```

---

By following these patterns, we can achieve more consistent, maintainable, and user-friendly forms throughout our application.
