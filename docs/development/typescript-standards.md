# TypeScript Standards and Best Practices

This document outlines the TypeScript standards and best practices for the Steelflow application. Following these guidelines ensures consistent, maintainable, and type-safe code across the codebase.

## Table of Contents

1. [Type Imports](#type-imports)
2. [Type Definitions](#type-definitions)
3. [Zod Schema Usage](#zod-schema-usage)
4. [Type Hierarchy](#type-hierarchy)
5. [Code Review Guidelines](#code-review-guidelines)

## Type Imports

### Central Schema Imports

**ALWAYS import types from the central schema index file (`@/lib/schemas`)** rather than from individual schema files or directly from Supabase types.

```typescript
// ✅ CORRECT: Import from central schema index
import { type Provider, type Country } from "@/lib/schemas";

// ❌ INCORRECT: Import from individual schema files
import { type Provider } from "@/lib/schemas/provider.schema";
import { type Country } from "@/lib/schemas/country.schema";

// ❌ INCORRECT: Import directly from Supabase types
import { Tables } from "@/lib/types/supabase";
type Provider = Tables<"providers">;
```

### Benefits of Central Schema Imports

1. **Consistency**: Ensures all components use the same type definitions
2. **Maintainability**: Makes refactoring easier as import paths won't change
3. **Decoupling**: Reduces direct dependency on database schema
4. **Documentation**: Centralized types often include JSDoc comments
5. **Validation**: Schema-derived types ensure data validation

## Type Definitions

### Type Naming Conventions

- Use PascalCase for type names
- Use descriptive names that indicate the entity and purpose
- Add suffixes for special types:
  - `Input` for creation/update payloads (e.g., `CreateProviderInput`)
  - `Params` for query parameters (e.g., `GetProvidersParams`)
  - `Response` for API responses (e.g., `ProviderResponse`)

### Type Export Patterns

```typescript
// Export types derived from Zod schemas
export type Provider = z.infer<typeof ProviderSchema>;
export type CreateProviderInput = z.infer<typeof CreateProviderSchema>;
export type UpdateProviderInput = z.infer<typeof UpdateProviderSchema>;
export type GetProvidersParams = z.infer<typeof GetProvidersParamsSchema>;
```

## Zod Schema Usage

### Validation with Zod

Always validate external data using Zod schemas:

```typescript
// ✅ CORRECT: Validate with schema
import { CreateProviderSchema, type Provider } from "@/lib/schemas";

const validatedData = CreateProviderSchema.parse(formData);
const newProvider = await createProvider(validatedData);

// ❌ INCORRECT: Type assertion without validation
const newProvider = await createProvider(formData as Provider);
```

## Type Hierarchy

Types should flow through the architecture in this order:

1. **Database Types**: Generated by Supabase CLI in `src/lib/supabase/types.ts`
2. **Service Layer Types**: Import and use database types, export service-specific types
3. **Action Layer Types**: Import and use service types, export action-specific types
4. **Hook Layer Types**: Import and use action types, export hook-specific types
5. **Component Layer Types**: Import and use hook types

This hierarchy ensures type consistency and prevents circular dependencies.

## Code Review Guidelines

The following guidelines should be enforced during code reviews to maintain type safety and architectural boundaries:

### Type Safety Guidelines

- **No Direct Supabase Types**: Don't import directly from `@/types/supabase`
- **No Duplicate Types in Hooks**: Don't define types in hooks that already exist elsewhere

When reviewing code, look for:

```typescript
// ❌ AVOID: Direct Supabase type imports
import { Tables } from "@/types/supabase";

// ❌ AVOID: Duplicate type definitions in hooks
export type Provider = { /* ... */ };
```

Instead, encourage:

```typescript
// ✅ BETTER: Import from central schema
import { type Provider } from "@/lib/schemas";
```

### Code Review Checklist

When reviewing TypeScript code, check for:

- ✓ Types are imported from central schema index
- ✓ External data is validated with Zod schemas
- ✓ Type assertions are avoided in favor of validation
- ✓ Type definitions follow naming conventions
- ✓ The type hierarchy is respected (see [Type Hierarchy](#type-hierarchy))
