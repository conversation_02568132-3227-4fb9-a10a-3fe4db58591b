# Pagination Implementation Guide

This guide outlines the standardized approach for implementing pagination in the Steelflow application. It covers the complete implementation from the page level down to the data fetching layer.

## Table of Contents

- [Overview](#overview)
- [Implementation Steps](#implementation-steps)
- [Component Structure](#component-structure)
- [Example Implementation](#example-implementation)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

## Overview

Our pagination implementation follows a URL-based approach where the current page and page size are stored in the URL parameters. This enables:

- Bookmarkable pages
- Shareable URLs with specific page states
- Browser history navigation
- SEO-friendly URLs

## Implementation Steps

### 1. Page Component (`page.tsx`)

The page component handles URL parameters and passes them to child components:

```typescript
export default async function ExamplePage({
  searchParams,
}: {
  searchParams: { page?: string; pageSize?: string };
}) {
  const page = searchParams.page ? parseInt(searchParams.page) : 1;
  const pageSize = searchParams.pageSize ? parseInt(searchParams.pageSize) : 10;

  // Validate page number
  if (page < 1) {
    const params = new URLSearchParams();
    params.set("page", "1");
    params.set("pageSize", pageSize.toString());
    return redirect(`/dashboard/example?${params.toString()}`);
  }

  return (
    <Suspense fallback={<ExampleSkeleton />}>
      <ExampleContent page={page} pageSize={pageSize} />
    </Suspense>
  );
}
```

### 2. Content Component (`_components/example-content.tsx`)

The content component fetches data and passes pagination info to the data table:

```typescript
interface ExampleContentProps {
  page: number;
  pageSize: number;
}

export async function ExampleContent({ page, pageSize }: ExampleContentProps) {
  const { data, error, totalCount } = await getExampleData({ page, pageSize });

  if (error) {
    return <ErrorAlert error={error} />;
  }

  return (
    <ExampleDataTable
      data={data}
      totalCount={totalCount}
      currentPage={page}
      pageSize={pageSize}
    />
  );
}
```

### 3. Data Table Component (`_components/example-data-table.tsx`)

The data table component handles pagination state and URL updates:

```typescript
interface ExampleDataTableProps {
  data: ExampleType[];
  totalCount?: number;
  currentPage?: number;
  pageSize?: number;
}

export function ExampleDataTable({
  data,
  totalCount,
  currentPage = 1,
  pageSize = 10,
}: ExampleDataTableProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const handlePaginationChange = useCallback((page: number, newPageSize: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("page", page.toString());
    params.set("pageSize", newPageSize.toString());
    router.push(`${pathname}?${params.toString()}`);
  }, [pathname, router, searchParams]);

  return (
    <DataTable
      columns={columns}
      data={data}
      paginationConfig={{
        totalItems: totalCount,
        manualPagination: true,
        initialPage: currentPage - 1, // Convert to 0-based index
        initialPageSize: pageSize,
        onPaginationChange: handlePaginationChange,
      }}
    />
  );
}
```

### 4. Data Fetching Action (`lib/actions/example.ts`)

The action handles pagination parameters and returns total count:

```typescript
export async function getExampleData(params?: {
  page?: number;
  pageSize?: number;
}): Promise<{
  data: ExampleType[];
  error: string | null;
  totalCount?: number;
}> {
  try {
    const result = await fetchExampleData({
      page: params?.page || 1,
      pageSize: params?.pageSize || 10,
    });
    return {
      data: result.data,
      error: null,
      totalCount: result.count,
    };
  } catch (error) {
    return {
      data: [],
      error: error instanceof Error ? error.message : "Failed to fetch data.",
    };
  }
}
```

## Component Structure

```
src/app/dashboard/example/
├── page.tsx                    # Handles URL parameters
├── _components/
│   ├── example-content.tsx     # Fetches data and manages state
│   ├── example-data-table.tsx  # Renders table with pagination
│   └── example-skeleton.tsx    # Loading state
└── lib/
    └── actions/
        └── example.ts          # Data fetching with pagination
```

## Best Practices

1. **URL Parameters**

   - Always use URL parameters for pagination state
   - Validate and normalize page numbers
   - Provide default values for page and pageSize

2. **Error Handling**

   - Handle invalid page numbers gracefully
   - Provide meaningful error messages
   - Log errors for debugging

3. **Performance**

   - Use server-side pagination
   - Implement proper loading states
   - Cache results when appropriate

4. **User Experience**
   - Show loading states during page transitions
   - Maintain scroll position when possible
   - Provide clear feedback for errors

## Troubleshooting

### Common Issues

1. **Page Number Mismatch**

   - Ensure page numbers are 1-based in URLs
   - Convert to 0-based for DataTable component
   - Check for proper type conversion

2. **URL Not Updating**

   - Verify router.push is being called
   - Check searchParams handling
   - Ensure proper event handling

3. **Data Not Refreshing**
   - Verify revalidation is implemented
   - Check cache settings
   - Ensure proper error handling

### Debugging Tips

1. Add logging at key points:

```typescript
logger.info(`Page parameters: page=${page}, pageSize=${pageSize}`);
logger.info(`Fetched ${data.length} items, total: ${totalCount}`);
```

2. Use browser dev tools to:

   - Monitor network requests
   - Check URL changes
   - Verify state updates

3. Test edge cases:
   - First/last page
   - Invalid page numbers
   - Empty results
   - Error states

## Example Implementation

See the cargo and providers pages for complete working examples of this pagination implementation.
