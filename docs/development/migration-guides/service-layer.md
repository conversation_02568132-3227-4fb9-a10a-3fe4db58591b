# Service Layer Migration Guide

This guide provides step-by-step instructions for migrating existing code to follow the service layer architecture in the Steelflow application.

## Table of Contents

1. [Migration Overview](#migration-overview)
2. [Step 1: Identify Code to Refactor](#step-1-identify-code-to-refactor)
3. [Step 2: Create Schemas](#step-2-create-schemas)
4. [Step 3: Implement Service Functions](#step-3-implement-service-functions)
5. [Step 4: Update API Routes](#step-4-update-api-routes)
6. [Step 5: Update Server Actions](#step-5-update-server-actions)
7. [Step 6: Test the Migration](#step-6-test-the-migration)
8. [Migration Checklist](#migration-checklist)
9. [Example Migration](#example-migration)

## Migration Overview

The service layer architecture separates business logic from API routes and server actions, ensuring a single source of truth for database operations and business rules. This migration guide will help you refactor existing code to follow this architecture.

### Benefits of Migration

- **Eliminates duplication** of business logic between API routes and server actions
- **Improves maintainability** by centralizing database operations
- **Enhances testability** by isolating business logic
- **Ensures consistency** in error handling and validation
- **Simplifies future changes** by providing a single place to update business logic

## Step 1: Identify Code to Refactor

Look for the following patterns in your codebase:

- API routes that directly access the database
- Server actions that directly access the database
- Duplicated business logic across different files
- Inconsistent error handling

### Example of Code to Refactor

```typescript
// API route with direct database access
// src/app/api/providers/route.ts
export async function GET(request: NextRequest) {
  const supabase = createAdminClient();
  const { data, error } = await supabase.from("providers").select("*");

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  return NextResponse.json({ data });
}

// Server action with direct database access
// src/lib/actions/provider.ts
("use server");

export async function getProviders() {
  const supabase = createAdminClient();
  const { data, error } = await supabase.from("providers").select("*");

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true, data };
}
```

## Step 2: Create Schemas

Create a schema file in `src/lib/schemas/` if it doesn't exist. Define Zod schemas for all data structures and export TypeScript types derived from schemas.

```typescript
// src/lib/schemas/provider.ts
import { z } from "zod";

// For fetching providers with pagination or by ID
export const GetProvidersParamsSchema = z.object({
  id: z.string().uuid("Invalid UUID format").optional(),
  page: z.coerce.number().int().min(1).default(1),
  pageSize: z.coerce.number().int().min(1).max(100).default(10),
});

// For creating a new provider
export const CreateProviderSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email format").optional(),
  phone: z.string().optional(),
});

// For updating an existing provider
export const UpdateProviderSchema = CreateProviderSchema.extend({
  id: z.string().uuid("Invalid UUID format"),
});

// For deleting a provider
export const DeleteProviderParamsSchema = z.object({
  id: z.string().uuid("Invalid UUID format"),
});

// Export TypeScript types
export type GetProvidersParams = z.infer<typeof GetProvidersParamsSchema>;
export type CreateProviderParams = z.infer<typeof CreateProviderSchema>;
export type UpdateProviderParams = z.infer<typeof UpdateProviderSchema>;
export type DeleteProviderParams = z.infer<typeof DeleteProviderParamsSchema>;
```

## Step 3: Implement Service Functions

Create a service file in `src/lib/services/` if it doesn't exist. Implement service functions for all database operations.

```typescript
// src/lib/services/provider.service.ts
import { createAdminClient } from "@/lib/supabase/admin";
import {
  GetProvidersParams,
  CreateProviderParams,
  UpdateProviderParams,
  DeleteProviderParams,
} from "@/lib/schemas/provider";
import { Provider } from "@/types/supabase";
import { NotFoundError, DatabaseError } from "@/lib/utils/errors";

// Fetch providers with optional filtering and pagination
export async function fetchProviders(
  params: GetProvidersParams,
): Promise<Provider[]> {
  const supabase = createAdminClient();
  let query = supabase.from("providers").select("*");

  if (params.id) {
    query = query.eq("id", params.id);
  }

  // Add pagination
  const from = (params.page - 1) * params.pageSize;
  const to = from + params.pageSize - 1;
  query = query.range(from, to);

  const { data, error } = await query;

  if (error) {
    throw new DatabaseError(
      `Failed to fetch providers: ${error.message}`,
      error,
    );
  }

  return data;
}

// Fetch a single provider by ID
export async function fetchProvider(id: string): Promise<Provider> {
  const supabase = createAdminClient();
  const { data, error } = await supabase
    .from("providers")
    .select("*")
    .eq("id", id)
    .single();

  if (error) {
    throw new DatabaseError(
      `Failed to fetch provider: ${error.message}`,
      error,
    );
  }

  if (!data) {
    throw new NotFoundError("Provider", id);
  }

  return data;
}

// Create a new provider
export async function addProvider(
  params: CreateProviderParams,
): Promise<Provider> {
  const supabase = createAdminClient();
  const { data, error } = await supabase
    .from("providers")
    .insert([params])
    .select()
    .single();

  if (error) {
    throw new DatabaseError(
      `Failed to create provider: ${error.message}`,
      error,
    );
  }

  return data;
}

// Update an existing provider
export async function modifyProvider(
  params: UpdateProviderParams,
): Promise<Provider> {
  const { id, ...updateData } = params;

  const supabase = createAdminClient();
  const { data, error } = await supabase
    .from("providers")
    .update(updateData)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    throw new DatabaseError(
      `Failed to update provider: ${error.message}`,
      error,
    );
  }

  if (!data) {
    throw new NotFoundError("Provider", id);
  }

  return data;
}

// Delete a provider
export async function removeProvider(
  params: DeleteProviderParams,
): Promise<void> {
  const supabase = createAdminClient();
  const { error } = await supabase
    .from("providers")
    .delete()
    .eq("id", params.id);

  if (error) {
    throw new DatabaseError(
      `Failed to delete provider: ${error.message}`,
      error,
    );
  }
}
```

## Step 4: Update API Routes

Refactor API routes to use the service layer instead of directly accessing the database.

```typescript
// src/app/api/providers/route.ts
import { NextRequest } from "next/server";
import { fetchProviders, addProvider } from "@/lib/services/provider.service";
import {
  GetProvidersParamsSchema,
  CreateProviderSchema,
} from "@/lib/schemas/provider";
import {
  validateWithSchema,
  handleApiError,
  formatApiResponse,
} from "@/lib/utils/api";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const params = {
      id: searchParams.get("id") ?? undefined,
      page: searchParams.get("page") ?? undefined,
      pageSize: searchParams.get("pageSize") ?? undefined,
    };

    // Validate params
    const [isValid, validatedData, errorResponse] = validateWithSchema(
      GetProvidersParamsSchema,
      params,
    );

    if (!isValid) {
      return errorResponse;
    }

    // Use service to fetch data
    const result = await fetchProviders(validatedData!);

    return formatApiResponse(result);
  } catch (error) {
    return handleApiError(error, "Failed to fetch providers");
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate body
    const [isValid, validatedData, errorResponse] = validateWithSchema(
      CreateProviderSchema,
      body,
    );

    if (!isValid) {
      return errorResponse;
    }

    // Use service to create provider
    const result = await addProvider(validatedData!);

    return formatApiResponse(result, 201);
  } catch (error) {
    return handleApiError(error, "Failed to create provider");
  }
}
```

## Step 5: Update Server Actions

Refactor server actions to use the service layer instead of directly accessing the database.

```typescript
// src/lib/actions/provider.ts
"use server";

import { revalidatePath } from "next/cache";
import {
  fetchProviders,
  fetchProvider,
  addProvider,
  modifyProvider,
  removeProvider,
} from "@/lib/services/provider.service";
import {
  GetProvidersParamsSchema,
  CreateProviderSchema,
  UpdateProviderSchema,
  DeleteProviderParamsSchema,
} from "@/lib/schemas/provider";

// Get providers (for compatibility with existing code)
export async function getProviders(formData?: FormData) {
  const params = formData
    ? {
        id: formData.get("id")?.toString() ?? undefined,
        page: formData.get("page")?.toString() ?? undefined,
        pageSize: formData.get("pageSize")?.toString() ?? undefined,
      }
    : {};

  // Validate params
  const validationResult = GetProvidersParamsSchema.safeParse(params);

  if (!validationResult.success) {
    return {
      success: false,
      errors: validationResult.error.format(),
    };
  }

  try {
    // Use service to fetch data
    const providers = await fetchProviders(validationResult.data);

    return {
      success: true,
      data: providers,
    };
  } catch (error) {
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "An unknown error occurred",
    };
  }
}

// Create provider
export async function createProvider(prevState: any, formData: FormData) {
  const data = Object.fromEntries(formData.entries());

  // Validate data
  const validationResult = CreateProviderSchema.safeParse(data);

  if (!validationResult.success) {
    return {
      success: false,
      errors: validationResult.error.format(),
    };
  }

  try {
    // Use service to create provider
    await addProvider(validationResult.data);

    // Revalidate providers list page
    revalidatePath("/dashboard/providers");

    return {
      success: true,
    };
  } catch (error) {
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "An unknown error occurred",
    };
  }
}

// Additional actions (update, delete)
```

## Step 6: Test the Migration

Test the refactored code to ensure it works as expected:

1. Test API routes:

   ```bash
   # Get all providers
   curl http://localhost:3000/api/providers

   # Get a specific provider
   curl http://localhost:3000/api/providers?id=YOUR_ID

   # Create a provider
   curl -X POST http://localhost:3000/api/providers -H "Content-Type: application/json" -d '{"name":"Test Provider"}'
   ```

2. Test UI components that use server actions:
   - Navigate to the providers list page
   - Try creating, editing, and deleting providers

## Migration Checklist

Use this checklist to ensure your refactoring is complete:

- [ ] Created schemas for all data structures
- [ ] Implemented service functions for all database operations
- [ ] Refactored API routes to use service functions
- [ ] Refactored server actions to use service functions
- [ ] Used utility functions for validation, error handling, and response formatting
- [ ] Tested all refactored code
- [ ] Updated documentation

## Example Migration

For a complete example of migrating a feature to the service layer architecture, see the [Cargo Types Migration Example](./examples/cargo-types.md).

## Related Documentation

- [Service Layer Architecture](../../architecture/service-layer.md)
- [API Protection](../../architecture/api-protection.md)
- [Error Handling](../../architecture/error-handling.md)
