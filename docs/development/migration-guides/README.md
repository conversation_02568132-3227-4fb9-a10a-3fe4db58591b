# Migration Guides

This directory contains guides for migrating code to follow our architectural guidelines and best practices.

## Available Guides

### Supabase Types Refactoring

- [Supabase Types Refactoring Guide](./supabase-types-refactoring-guide.md) - Comprehensive guide for refactoring direct imports from "@/types/supabase" to entity-specific schema imports

## Tools

- [Audit Schema Imports Script](../../scripts/audit-schema-imports.ts) - Script to find direct imports from "@/types/supabase"

## Getting Started

To start the refactoring process:

```bash
# Run the audit script to identify files that need to be refactored
npx ts-node scripts/audit-schema-imports.ts
```

Follow the instructions in the specific migration guide for detailed steps.
