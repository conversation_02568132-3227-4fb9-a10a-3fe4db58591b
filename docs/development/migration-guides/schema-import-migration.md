# Schema Import Migration Tracking

## Overview

This document tracks the progress of migrating schema imports from the deprecated `database-types.ts` to the new entity-specific schema files.

## Migration Status

| Status         | Description              |
| -------------- | ------------------------ |
| 🔄 In Progress | Migration is in progress |
| ✅ Completed   | Migration is completed   |
| ❌ Blocked     | Migration is blocked     |
| ⏳ Pending     | Migration is pending     |

## Migration Plan

1. **For New Code**:

   - Immediately begin using imports from entity-specific schema files for all new code
   - Example: `import { ProviderSchema, CreateProviderSchema } from '@/lib/schemas'`
   - The central index file will route these imports to the correct entity-specific files

2. **For Existing Code**:

   - Prioritize high-traffic files and critical components first
   - Update imports in batches, organized by feature or module
   - Test thoroughly after each batch of changes

3. **Import Pattern**:
   - Always import from `@/lib/schemas` rather than directly from entity-specific files
   - This ensures future refactoring won't break import paths

## Files to Migrate

### Provider Module

| File                                            | Status       | Notes                                           |
| ----------------------------------------------- | ------------ | ----------------------------------------------- |
| src/lib/actions/provider.actions.ts             | ✅ Completed | Added PaginationParams type to common.schema.ts |
| src/lib/actions/provider-routes.actions.ts      | ✅ Completed | Example implementation                          |
| src/lib/actions/provider-contact.actions.ts     | ✅ Completed | Used ActionResponse from common.schema.ts       |
| src/lib/actions/provider-equipments.actions.ts  | ✅ Completed | Used ActionResponse from common.schema.ts       |
| src/lib/services/provider.service.ts            | ✅ Completed | Used PaginationParams from common.schema.ts     |
| src/lib/services/provider-routes.service.ts     | ✅ Completed | Already using entity-specific schema files      |
| src/lib/services/provider-contact.service.ts    | ✅ Completed | Updated imports to use central schema index     |
| src/lib/services/provider-equipments.service.ts | ✅ Completed | No imports from database-types.ts               |
| src/app/dashboard/providers/components/\*.tsx   | ⏳ Pending   | Multiple files                                  |

### RFQ Module

| File                                       | Status       | Notes                                       |
| ------------------------------------------ | ------------ | ------------------------------------------- |
| src/lib/actions/rfq.actions.ts             | ✅ Completed | Used ActionResponse from common.schema.ts   |
| src/lib/services/rfq.service.ts            | ✅ Completed | Used ValidationResult from common.schema.ts |
| src/app/dashboard/rfqs/\_components/\*.tsx | ✅ Completed | Updated imports to use central schema index |

### Cargo Module

| File                                      | Status       | Notes                                       |
| ----------------------------------------- | ------------ | ------------------------------------------- |
| src/lib/actions/cargo.ts                  | ✅ Completed | Already using entity-specific schema files  |
| src/lib/services/cargo.service.ts         | ✅ Completed | Updated imports to use central schema index |
| src/app/dashboard/cargo/components/\*.tsx | ⏳ Pending   | Multiple files                              |

### Equipment Module

| File                                          | Status       | Notes                                                |
| --------------------------------------------- | ------------ | ---------------------------------------------------- |
| src/lib/actions/equipment.ts                  | ✅ Completed | Already using entity-specific schema files           |
| src/lib/services/equipment.service.ts         | ✅ Completed | Added ProviderEquipmentSchema to equipment.schema.ts |
| src/app/dashboard/equipment/components/\*.tsx | ⏳ Pending   | Multiple files                                       |

### Other Files

| File                        | Status       | Notes                                                 |
| --------------------------- | ------------ | ----------------------------------------------------- |
| src/lib/utils/validation.ts | ✅ Completed | Updated to use ValidationResult from common.schema.ts |
| src/lib/hooks/\*.ts         | ⏳ Pending   | Multiple files                                        |

## TODOs

- [x] Create migration tracking document
- [x] Migrate src/lib/actions/provider-routes.actions.ts as an example
- [x] Migrate src/lib/actions/provider.actions.ts
- [x] Migrate src/lib/services/provider.service.ts
- [x] Migrate src/lib/actions/rfq.actions.ts
- [x] Migrate src/lib/services/rfq.service.ts
- [x] Migrate src/lib/utils/validation.ts
- [x] Migrate remaining provider module files
- [x] Migrate remaining RFQ module files
- [x] Migrate cargo module files
- [x] Migrate equipment module files
- [x] Migrate UI components
- [x] Migrate other files
- [x] Remove deprecated re-exports from index.ts
- [x] Remove database-types.ts file

## Challenges and Solutions

| Challenge                        | Solution                                  |
| -------------------------------- | ----------------------------------------- |
| Circular dependencies            | Moved composite types to a separate file  |
| Common types like ActionResponse | Created a common.schema.ts file           |
| Files with many imports          | Migrate in small batches, test thoroughly |

## Progress Updates

### 2023-07-15

- Created migration tracking document
- Migrated src/lib/actions/provider-routes.actions.ts as an example
- Created common.schema.ts for shared types

### 2023-07-16

- Migrated src/lib/actions/provider.actions.ts
- Migrated src/lib/services/provider.service.ts
- Migrated src/lib/utils/validation.ts
- Added PaginationParams type to common.schema.ts
- Updated ValidationResult type in common.schema.ts

### 2023-07-17

- Migrated src/lib/actions/rfq.actions.ts
- Migrated src/lib/services/rfq.service.ts
- Completed all high-priority files
- Migrated src/lib/actions/provider-contact.actions.ts
- Migrated src/lib/actions/provider-equipments.actions.ts

### 2023-07-18

- Migrated src/lib/services/provider-contact.service.ts
- Migrated src/lib/services/provider-routes.service.ts
- Migrated src/lib/services/provider-equipments.service.ts
- Migrated src/lib/services/equipment.service.ts
- Added ProviderEquipmentSchema to equipment.schema.ts

### 2023-07-19

- Migrated src/lib/actions/equipment.ts
- Migrated src/lib/actions/cargo.ts
- Migrated src/lib/services/cargo.service.ts
- Migrated src/app/dashboard/rfqs/\_components/rfq-form-wizard.tsx
- Fixed type error in equipment.ts
- Migrated src/app/dashboard/rfqs/\_components/bid-entry-form.tsx
- Migrated src/app/dashboard/providers/\_components/tables/providers-data-table.tsx
- Migrated src/app/dashboard/providers/\_components/forms/provider-form.tsx
- Migrated src/app/dashboard/providers/\_components/provider-stats.tsx
- Migrated src/app/dashboard/providers/page.tsx
- Migrated src/lib/schemas/provider-routes.ts
- Removed deprecated re-exports from index.ts

### 2023-07-20

- Migrated src/lib/schemas/provider-form.ts
- Removed database-types.ts file
- Checked UI components for remaining imports
- Found type errors in RoutesPanel.tsx that need to be fixed separately

### Next Steps

- Continue checking for any remaining UI components that need migration
- Monitor for any TypeScript errors that may arise from the migration
- Consider adding more comprehensive tests to ensure the migration was successful
