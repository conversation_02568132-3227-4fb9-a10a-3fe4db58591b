# Service Layer Migration Example: Cargo Types

This guide provides a practical example of migrating the Cargo Types feature to use the service layer architecture. Use this as a reference for migrating other features.

## Table of Contents

1. [Current Structure](#current-structure)
2. [Step 1: Create Schemas](#step-1-create-schemas)
3. [Step 2: Implement Service Functions](#step-2-implement-service-functions)
4. [Step 3: Update API Routes](#step-3-update-api-routes)
5. [Step 4: Update Server Actions](#step-4-update-server-actions)
6. [Step 5: Update UI Components](#step-5-update-ui-components)
7. [Testing the Migration](#testing-the-migration)
8. [Verification Checklist](#verification-checklist)

## Current Structure

Currently, cargo-types has:

- An API route at `src/app/api/cargo-types/route.ts` that handles HTTP requests directly with Supabase
- Actions at `src/lib/actions/cargo-type.ts` that also interact with Supabase directly
- Possible duplication of business logic between these two files

### Current API Route

```typescript
// src/app/api/cargo-types/route.ts
import { NextRequest, NextResponse } from "next/server";
import { createAdminClient } from "@/lib/supabase/admin";

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const id = searchParams.get("id");

  const supabase = createAdminClient();
  let query = supabase.from("cargo_types").select("*");

  if (id) {
    query = query.eq("id", id);
  }

  const { data, error } = await query;

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  return NextResponse.json({ data });
}

export async function POST(request: NextRequest) {
  const body = await request.json();

  if (!body.name) {
    return NextResponse.json({ error: "Name is required" }, { status: 400 });
  }

  const supabase = createAdminClient();
  const { data, error } = await supabase
    .from("cargo_types")
    .insert([body])
    .select()
    .single();

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  return NextResponse.json({ data }, { status: 201 });
}
```

### Current Server Action

```typescript
// src/lib/actions/cargo-type.ts
"use server";

import { revalidatePath } from "next/cache";
import { createAdminClient } from "@/lib/supabase/admin";

export async function getCargoTypes() {
  const supabase = createAdminClient();
  const { data, error } = await supabase.from("cargo_types").select("*");

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true, data };
}

export async function createCargoType(prevState: any, formData: FormData) {
  const name = formData.get("name")?.toString();
  const description = formData.get("description")?.toString();
  const image_url = formData.get("image_url")?.toString();

  if (!name) {
    return {
      success: false,
      errors: { name: ["Name is required"] },
    };
  }

  const supabase = createAdminClient();
  const { error } = await supabase
    .from("cargo_types")
    .insert([{ name, description, image_url }]);

  if (error) {
    return { success: false, error: error.message };
  }

  revalidatePath("/dashboard/cargo-types");
  return { success: true };
}
```

## Step 1: Create Schemas

Create a new file `src/lib/schemas/cargo-type.ts` with validation schemas:

```typescript
// src/lib/schemas/cargo-type.ts
import { z } from "zod";

// For fetching cargo types with pagination or by ID
export const GetCargoTypesParamsSchema = z.object({
  id: z.string().uuid("Invalid UUID format").optional(),
  page: z.coerce.number().int().min(1).default(1),
  pageSize: z.coerce.number().int().min(1).max(100).default(10),
});

// For creating a new cargo type
export const CreateCargoTypeSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  image_url: z.string().url("Invalid URL format").optional().or(z.literal("")),
});

// For updating an existing cargo type
export const UpdateCargoTypeSchema = CreateCargoTypeSchema.extend({
  id: z.string().uuid("Invalid UUID format"),
});

// For deleting a cargo type
export const DeleteCargoTypeParamsSchema = z.object({
  id: z.string().uuid("Invalid UUID format"),
});

// Export TypeScript types
export type GetCargoTypesParams = z.infer<typeof GetCargoTypesParamsSchema>;
export type CreateCargoTypeParams = z.infer<typeof CreateCargoTypeSchema>;
export type UpdateCargoTypeParams = z.infer<typeof UpdateCargoTypeSchema>;
export type DeleteCargoTypeParams = z.infer<typeof DeleteCargoTypeParamsSchema>;
```

## Step 2: Implement Service Functions

Create a new file `src/lib/services/cargo-type.service.ts` with service functions:

```typescript
// src/lib/services/cargo-type.service.ts
import { createAdminClient } from "@/lib/supabase/admin";
import {
  GetCargoTypesParams,
  CreateCargoTypeParams,
  UpdateCargoTypeParams,
  DeleteCargoTypeParams,
} from "@/lib/schemas/cargo-type";
import { CargoType } from "@/types/supabase";
import { NotFoundError, DatabaseError } from "@/lib/utils/errors";

// Fetch cargo types with optional filtering and pagination
export async function fetchCargoTypes(
  params: GetCargoTypesParams,
): Promise<CargoType[]> {
  const supabase = createAdminClient();
  let query = supabase.from("cargo_types").select("*");

  if (params.id) {
    query = query.eq("id", params.id);
  }

  // Add pagination
  const from = (params.page - 1) * params.pageSize;
  const to = from + params.pageSize - 1;
  query = query.range(from, to);

  const { data, error } = await query;

  if (error) {
    throw new DatabaseError(
      `Failed to fetch cargo types: ${error.message}`,
      error,
    );
  }

  return data;
}

// Fetch a single cargo type by ID
export async function fetchCargoType(id: string): Promise<CargoType> {
  const supabase = createAdminClient();
  const { data, error } = await supabase
    .from("cargo_types")
    .select("*")
    .eq("id", id)
    .single();

  if (error) {
    throw new DatabaseError(
      `Failed to fetch cargo type: ${error.message}`,
      error,
    );
  }

  if (!data) {
    throw new NotFoundError("Cargo Type", id);
  }

  return data;
}

// Create a new cargo type
export async function addCargoType(
  params: CreateCargoTypeParams,
): Promise<CargoType> {
  const supabase = createAdminClient();
  const { data, error } = await supabase
    .from("cargo_types")
    .insert([params])
    .select()
    .single();

  if (error) {
    throw new DatabaseError(
      `Failed to create cargo type: ${error.message}`,
      error,
    );
  }

  return data;
}

// Update an existing cargo type
export async function modifyCargoType(
  params: UpdateCargoTypeParams,
): Promise<CargoType> {
  const { id, ...updateData } = params;

  const supabase = createAdminClient();
  const { data, error } = await supabase
    .from("cargo_types")
    .update(updateData)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    throw new DatabaseError(
      `Failed to update cargo type: ${error.message}`,
      error,
    );
  }

  if (!data) {
    throw new NotFoundError("Cargo Type", id);
  }

  return data;
}

// Delete a cargo type
export async function removeCargoType(
  params: DeleteCargoTypeParams,
): Promise<void> {
  const supabase = createAdminClient();
  const { error } = await supabase
    .from("cargo_types")
    .delete()
    .eq("id", params.id);

  if (error) {
    throw new DatabaseError(
      `Failed to delete cargo type: ${error.message}`,
      error,
    );
  }
}
```

## Step 3: Update API Routes

Refactor `src/app/api/cargo-types/route.ts` to use the service layer:

```typescript
// src/app/api/cargo-types/route.ts
import { NextRequest } from "next/server";
import {
  fetchCargoTypes,
  addCargoType,
  modifyCargoType,
  removeCargoType,
} from "@/lib/services/cargo-type.service";
import {
  GetCargoTypesParamsSchema,
  CreateCargoTypeSchema,
  UpdateCargoTypeSchema,
  DeleteCargoTypeParamsSchema,
} from "@/lib/schemas/cargo-type";
import {
  validateWithSchema,
  handleApiError,
  formatApiResponse,
} from "@/lib/utils/api";
import { createUnkeyHandler } from "@/lib/utils/unkey";

export const GET = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const searchParams = request.nextUrl.searchParams;
      const params = {
        id: searchParams.get("id") ?? undefined,
        page: searchParams.get("page") ?? undefined,
        pageSize: searchParams.get("pageSize") ?? undefined,
      };

      // Validate params
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        GetCargoTypesParamsSchema,
        params,
      );

      if (!isValid) {
        return errorResponse;
      }

      // Use service to fetch data
      const result = await fetchCargoTypes(validatedData!);

      return formatApiResponse(result);
    } catch (error) {
      return handleApiError(error, "Failed to fetch cargo types");
    }
  },
  { requiredPermissions: ["read:cargo"] },
);

export const POST = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate body
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        CreateCargoTypeSchema,
        body,
      );

      if (!isValid) {
        return errorResponse;
      }

      // Use service to create cargo type
      const result = await addCargoType(validatedData!);

      return formatApiResponse(result, 201);
    } catch (error) {
      return handleApiError(error, "Failed to create cargo type");
    }
  },
  { requiredPermissions: ["write:cargo"] },
);

// Add PUT and DELETE handlers similarly
```

## Step 4: Update Server Actions

Refactor `src/lib/actions/cargo-type.ts` to use the service layer:

```typescript
// src/lib/actions/cargo-type.ts
"use server";

import { revalidatePath } from "next/cache";
import {
  fetchCargoTypes as fetchCargoTypesService,
  addCargoType as addCargoTypeService,
  modifyCargoType as modifyCargoTypeService,
  removeCargoType as removeCargoTypeService,
} from "@/lib/services/cargo-type.service";
import {
  GetCargoTypesParamsSchema,
  CreateCargoTypeSchema,
  UpdateCargoTypeSchema,
  DeleteCargoTypeParamsSchema,
} from "@/lib/schemas/cargo-type";

// Get cargo types (for compatibility with existing code)
export async function getCargoTypes(formData?: FormData) {
  const params = formData
    ? {
        id: formData.get("id")?.toString() ?? undefined,
        page: formData.get("page")?.toString() ?? undefined,
        pageSize: formData.get("pageSize")?.toString() ?? undefined,
      }
    : {};

  // Validate params
  const validationResult = GetCargoTypesParamsSchema.safeParse(params);

  if (!validationResult.success) {
    return {
      success: false,
      errors: validationResult.error.format(),
    };
  }

  try {
    // Use service to fetch data
    const cargoTypes = await fetchCargoTypesService(validationResult.data);

    return {
      success: true,
      data: cargoTypes,
    };
  } catch (error) {
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "An unknown error occurred",
    };
  }
}

// Create cargo type
export async function createCargoType(prevState: any, formData: FormData) {
  const data = {
    name: formData.get("name")?.toString() ?? "",
    description: formData.get("description")?.toString() ?? "",
    image_url: formData.get("image_url")?.toString() ?? "",
  };

  // Validate data
  const validationResult = CreateCargoTypeSchema.safeParse(data);

  if (!validationResult.success) {
    return {
      success: false,
      errors: validationResult.error.format(),
    };
  }

  try {
    // Use service to create cargo type
    await addCargoTypeService(validationResult.data);

    // Revalidate cargo types list page
    revalidatePath("/dashboard/cargo-types");

    return {
      success: true,
    };
  } catch (error) {
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "An unknown error occurred",
    };
  }
}

// Add update and delete actions similarly
```

## Step 5: Update UI Components

If necessary, update the UI components to use the new action signatures. The current page component may use the older getCargoTypes function, which we've maintained for compatibility.

## Testing the Migration

1. Test the API routes:

   ```bash
   # Get all cargo types
   curl http://localhost:3000/api/cargo-types

   # Get a specific cargo type
   curl http://localhost:3000/api/cargo-types?id=YOUR_ID

   # Create a cargo type
   curl -X POST http://localhost:3000/api/cargo-types -H "Content-Type: application/json" -d '{"name":"Test Cargo"}'
   ```

2. Test the UI components that use server actions:
   - Navigate to `/dashboard/cargo-types`
   - Try creating, editing, and deleting cargo types

## Verification Checklist

- [ ] All core database logic is in the service layer
- [ ] API routes use the service layer
- [ ] Server actions use the service layer
- [ ] API endpoints return appropriate status codes and data
- [ ] Server actions revalidate paths as needed
- [ ] UI components work correctly with the new implementation

## Next Steps

Apply the same pattern to other entities in the application, such as equipment types and providers. The service layer architecture provides a consistent, maintainable, and testable approach to handling business logic and database operations.
