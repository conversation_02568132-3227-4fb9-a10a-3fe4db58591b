# API Route Migration Guide: Adding Unkey Protection

This guide provides step-by-step instructions for migrating existing API routes to use Unkey authentication in the Steelflow application.

## Table of Contents

1. [Migration Overview](#migration-overview)
2. [Prerequisites](#prerequisites)
3. [Step 1: Update Imports](#step-1-update-imports)
4. [Step 2: Convert Function Declarations](#step-2-convert-function-declarations)
5. [Step 3: Add Required Permissions](#step-3-add-required-permissions)
6. [Step 4: Test the Protected API Route](#step-4-test-the-protected-api-route)
7. [Example: Before and After](#example-before-and-after)
8. [Common Permission Patterns](#common-permission-patterns)
9. [Special Cases](#special-cases)
10. [Troubleshooting](#troubleshooting)

## Migration Overview

All API routes in the `/api/v1/` path should be protected with Unkey authentication to ensure that only authorized clients can access them. This migration guide will help you update existing API routes to use the `createUnkeyHandler` utility function.

### Benefits of Migration

- **Enhanced security**: Only authenticated clients can access API routes
- **Fine-grained access control**: Permissions control what actions clients can perform
- **Rate limiting**: Prevent abuse of API endpoints
- **Analytics**: Track API usage and performance

## Prerequisites

Before starting the migration, ensure you have:

1. Set up Unkey in your project (see [API Protection Guide](../../architecture/api-protection.md))
2. Added the required environment variables:
   ```
   UNKEY_API_ID=your_unkey_api_id
   UNKEY_ROOT_KEY=your_unkey_root_key
   ```
3. Installed the Unkey Next.js SDK:
   ```bash
   npm install @unkey/nextjs
   ```

## Step 1: Update Imports

Add the import for `createUnkeyHandler`:

```typescript
// Add this import
import { createUnkeyHandler } from "@/lib/utils/unkey";
```

## Step 2: Convert Function Declarations

Replace function declarations with const exports wrapped in `createUnkeyHandler`:

Before:

```typescript
export async function GET(request: NextRequest) {
  // ...
}
```

After:

```typescript
export const GET = createUnkeyHandler(
  async (request: NextRequest) => {
    // ...
  },
  { requiredPermissions: ["read:resource"] },
);
```

## Step 3: Add Required Permissions

Determine the appropriate permissions for each HTTP method:

- `GET` methods typically need `read:resource` permission
- `POST` methods typically need `write:resource` permission
- `PUT` methods typically need `write:resource` permission
- `DELETE` methods typically need `delete:resource` permission

Replace `resource` with the specific resource name (e.g., `providers`, `cargo`, `rfqs`).

## Step 4: Test the Protected API Route

Test the protected API route using a valid API key:

```bash
curl -X GET "http://localhost:3000/api/v1/resource" \
  -H "Authorization: Bearer your_api_key"
```

For local development, you can create a test API key using the `/api/v1/keys` endpoint:

```bash
curl -X POST "http://localhost:3000/api/v1/keys" \
  -H "Content-Type: application/json" \
  -d '{"name":"Test Key","permissions":["read:resource","write:resource"]}'
```

## Example: Before and After

### Before

```typescript
import { NextRequest } from "next/server";
import { fetchResource } from "@/lib/services/resource.service";
import { GetResourceParamsSchema } from "@/lib/schemas/resource";
import {
  validateWithSchema,
  handleApiError,
  formatApiResponse,
} from "@/lib/utils/api";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const params = {
      id: searchParams.get("id") ?? undefined,
    };

    // Validate params
    const [isValid, validatedData, errorResponse] = validateWithSchema(
      GetResourceParamsSchema,
      params,
    );

    if (!isValid) {
      return errorResponse;
    }

    // Use service to fetch data
    const result = await fetchResource(validatedData!);

    return formatApiResponse(result);
  } catch (error) {
    return handleApiError(error, "Failed to fetch resource");
  }
}
```

### After

```typescript
import { NextRequest } from "next/server";
import { fetchResource } from "@/lib/services/resource.service";
import { GetResourceParamsSchema } from "@/lib/schemas/resource";
import {
  validateWithSchema,
  handleApiError,
  formatApiResponse,
} from "@/lib/utils/api";
import { createUnkeyHandler } from "@/lib/utils/unkey";

export const GET = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const searchParams = request.nextUrl.searchParams;
      const params = {
        id: searchParams.get("id") ?? undefined,
      };

      // Validate params
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        GetResourceParamsSchema,
        params,
      );

      if (!isValid) {
        return errorResponse;
      }

      // Use service to fetch data
      const result = await fetchResource(validatedData!);

      return formatApiResponse(result);
    } catch (error) {
      return handleApiError(error, "Failed to fetch resource");
    }
  },
  { requiredPermissions: ["read:resource"] },
);
```

## Common Permission Patterns

Here are some common permission patterns to use for different resources:

### Providers

- `read:providers`: Read provider information
- `write:providers`: Create or update providers
- `delete:providers`: Delete providers

### Cargo

- `read:cargo`: Read cargo type information
- `write:cargo`: Create or update cargo types
- `delete:cargo`: Delete cargo types

### RFQs

- `read:rfqs`: Read RFQ information
- `write:rfqs`: Create or update RFQs
- `delete:rfqs`: Delete RFQs
- `manage:rfqs`: Manage RFQ providers and bids

### Equipment

- `read:equipment`: Read equipment information
- `write:equipment`: Create or update equipment
- `delete:equipment`: Delete equipment

## Special Cases

### Public API Routes

Some API routes should remain public (not protected by Unkey). These include:

- `/api/v1/keys`: For creating API keys
- `/api/auth/*`: Authentication-related endpoints

For these routes, do not apply the Unkey protection.

### Routes with Multiple Permissions

For routes that require multiple permissions, you can specify them in the `requiredPermissions` array:

```typescript
export const POST = createUnkeyHandler(
  async (request: NextRequest) => {
    // ...
  },
  { requiredPermissions: ["write:resource", "admin:resource"] },
);
```

This requires the API key to have both permissions.

### Routes with Different Permissions Based on Request

For routes that require different permissions based on the request, you can use the `withUnkey` function directly:

```typescript
import { NextResponse } from "next/server";
import { NextRequestWithUnkeyContext, withUnkey } from "@unkey/nextjs";

export const POST = withUnkey(
  async (req: NextRequestWithUnkeyContext) => {
    try {
      const body = await req.json();

      // Check if this is an admin operation
      const isAdminOperation = body.adminOperation === true;

      // Check permissions based on the operation
      if (
        isAdminOperation &&
        !req.unkey.permissions?.includes("admin:resource")
      ) {
        return NextResponse.json(
          { error: "Insufficient permissions" },
          { status: 403 },
        );
      }

      if (!req.unkey.permissions?.includes("write:resource")) {
        return NextResponse.json(
          { error: "Insufficient permissions" },
          { status: 403 },
        );
      }

      // Continue with the API logic
    } catch (error) {
      // Error handling
    }
  },
  {
    apiId: process.env.UNKEY_API_ID,
  },
);
```

## Troubleshooting

### Common Issues

1. **401 Unauthorized**: The API key is missing or invalid

   - Check that the API key is included in the Authorization header
   - Verify that the API key exists in Unkey

2. **403 Forbidden**: The API key doesn't have the required permissions

   - Check the permissions assigned to the API key
   - Verify that the required permissions are correctly specified in the API route

3. **500 Internal Server Error**: Error verifying the API key
   - Check that the UNKEY_API_ID environment variable is set correctly
   - Verify that the Unkey service is available

### Debugging

To debug Unkey authentication issues, you can add logging to the `onError` callback:

```typescript
export const GET = withUnkey(
  async (req: NextRequestWithUnkeyContext) => {
    // Handler logic
  },
  {
    apiId: process.env.UNKEY_API_ID,
    onError: async (req, res) => {
      console.error(`Unkey error: ${res.code}: ${res.message}`);
      return NextResponse.json(
        { error: "Error verifying API key" },
        { status: 500 },
      );
    },
  },
);
```

## Related Documentation

- [API Protection Guide](../../architecture/api-protection.md)
- [Unkey Documentation](https://unkey.dev/docs)
