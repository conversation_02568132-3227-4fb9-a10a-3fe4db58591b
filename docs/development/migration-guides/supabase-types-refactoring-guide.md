# Supabase Types Refactoring Guide

## Table of Contents

1. [Introduction](#introduction)
2. [Background](#background)
3. [Correct Pattern](#correct-pattern)
4. [Refactoring Checklist](#refactoring-checklist)
   - [Service Layer](#service-layer)
   - [Action Layer](#action-layer)
   - [Component Layer](#component-layer)
   - [Hook Layer](#hook-layer)
5. [Refactoring Examples](#refactoring-examples)
   - [Service Layer Examples](#service-layer-examples)
   - [Component Layer Examples](#component-layer-examples)
   - [Hook Layer Examples](#hook-layer-examples)
   - [Action Layer Examples](#action-layer-examples)
6. [Complex Cases](#complex-cases)
   - [Multiple Entity Types in a Single File](#multiple-entity-types-in-a-single-file)
   - [Composite Types](#composite-types)
7. [Maintenance Guidelines](#maintenance-guidelines)
   - [Best Practices](#best-practices)
   - [Tools and Automation](#tools-and-automation)
   - [Process Guidelines](#process-guidelines)
   - [Handling Schema Updates](#handling-schema-updates)
8. [Tools](#tools)
9. [Progress Tracking](#progress-tracking)

## Introduction

This document provides a comprehensive guide for refactoring all instances where code directly imports Supabase database types using `import type { Database } from "@/types/supabase"` or `import { Tables } from "@/types/supabase"`. This pattern violates our architectural guidelines, which require using entity-specific schema files instead.

For a complete reference on TypeScript standards and best practices, including type imports, see the [TypeScript Standards](../typescript-standards.md) document.

## Background

Our codebase currently has many instances where components, services, and actions directly import types from the Supabase-generated types file. This creates several issues:

1. **Tight coupling** to the database schema
2. **Lack of validation** that schema files provide
3. **Inconsistent type usage** across the application
4. **Difficulty maintaining** when database schema changes

By using entity-specific schema files, we get:

1. **Validation** through Zod schemas
2. **Consistent type usage** across the application
3. **Decoupling** from the database schema
4. **Easier maintenance** when the database schema changes
5. **Better documentation** through JSDoc comments

## Correct Pattern

Instead of:

```typescript
import type { Database } from "@/types/supabase";
type Provider = Database["public"]["Tables"]["providers"]["Row"];
```

Or:

```typescript
import { Tables } from "@/types/supabase";
type Provider = Tables<"providers">;
```

We should use:

```typescript
import { ProviderSchema, type Provider } from "@/lib/schemas";
```

## Refactoring Checklist

### Service Layer

#### Cargo Service

- [ ] **File**: `src/lib/services/cargo.service.ts`
  - **Replace**:
    ```typescript
    import { Database } from "@/types/supabase";
    type Cargo = Database["public"]["Tables"]["cargo_types"]["Row"];
    ```
  - **With**:
    ```typescript
    import { type CargoType } from "@/lib/schemas";
    ```
  - **Update**: Function parameters and return types to use `CargoType` instead of `Cargo`

#### Country Service

- [ ] **File**: `src/lib/services/country.service.ts`
  - **Replace**:
    ```typescript
    import { Database } from "@/types/supabase";
    type Country = Database["public"]["Tables"]["countries"]["Row"];
    ```
  - **With**:
    ```typescript
    import { type Country } from "@/lib/schemas";
    ```
  - **Update**: Function parameters and return types to use imported `Country` type

#### Equipment Service

- [ ] **File**: `src/lib/services/equipment.service.ts`
  - **Replace**:
    ```typescript
    import { Database } from "@/types/supabase";
    type Equipment = Database["public"]["Tables"]["equipment_types"]["Row"];
    ```
  - **With**:
    ```typescript
    import { type EquipmentType } from "@/lib/schemas";
    ```
  - **Update**: Function parameters and return types to use `EquipmentType` instead of `Equipment`

#### Provider Capabilities Service

- [ ] **File**: `src/lib/services/provider-capabilities.service.ts`
  - **Replace**:
    ```typescript
    import { Database } from "@/types/supabase";
    type ProviderCapability =
      Database["public"]["Tables"]["provider_capabilities"]["Row"];
    type EquipmentType = Database["public"]["Tables"]["equipment_types"]["Row"];
    ```
  - **With**:
    ```typescript
    import { type ProviderCapability, type EquipmentType } from "@/lib/schemas";
    ```
  - **Update**: Function parameters and return types to use imported types

#### Provider Contact Service

- [ ] **File**: `src/lib/services/provider-contact.service.ts`
  - **Replace**:
    ```typescript
    import { Database } from "@/types/supabase";
    ```
  - **With**:
    ```typescript
    import { type ProviderContact } from "@/lib/schemas";
    ```
  - **Update**: Function parameters and return types to use `ProviderContact` type

#### User Service

- [ ] **File**: `src/lib/services/user.service.ts`
  - **Replace**:
    ```typescript
    import { Database } from "@/types/supabase";
    type User = Database["public"]["Tables"]["users"]["Row"];
    ```
  - **With**:
    ```typescript
    import { type User } from "@/lib/schemas";
    ```
  - **Update**: Function parameters and return types to use imported `User` type

### Action Layer

#### Equipment Actions

- [ ] **File**: `src/lib/actions/equipment.ts`
  - **Replace**:
    ```typescript
    import type { Tables } from "@/types/supabase";
    ```
  - **With**:
    ```typescript
    import { type EquipmentType } from "@/lib/schemas";
    ```
  - **Update**: Any usage of `Tables<"equipment_types">` to use `EquipmentType`

### Component Layer

#### Provider Contacts Panel

- [ ] **File**: `src/app/dashboard/providers/[id]/ContactsPanel.tsx`
  - **Replace**:
    ```typescript
    import type { Database } from "@/types/supabase";
    ```
  - **With**:
    ```typescript
    import { type ProviderContact } from "@/lib/schemas";
    ```
  - **Update**: Any usage of `Database` types to use schema types

#### RFQ Shipping Details Form

- [ ] **File**: `src/app/dashboard/rfqs/_components/form-steps/shipping-details-form.tsx`
  - **Replace**:
    ```typescript
    import { Tables } from "@/types/supabase";
    type Country = Tables<"countries">;
    ```
  - **With**:
    ```typescript
    import { type Country } from "@/lib/schemas";
    ```

#### RFQ Cargo Specifications Form

- [ ] **File**: `src/app/dashboard/rfqs/_components/form-steps/cargo-specifications-form.tsx`
  - **Replace**:
    ```typescript
    import { Tables } from "@/types/supabase";
    type CargoType = Tables<"cargo_types">;
    ```
  - **With**:
    ```typescript
    import { type CargoType } from "@/lib/schemas";
    ```

#### RFQ Review Form

- [ ] **File**: `src/app/dashboard/rfqs/_components/form-steps/review-form.tsx`
  - **Replace**:
    ```typescript
    import { Tables } from "@/types/supabase";
    type Country = Tables<"countries">;
    type CargoType = Tables<"cargo_types">;
    type EquipmentType = Tables<"equipment_types">;
    ```
  - **With**:
    ```typescript
    import {
      type Country,
      type CargoType,
      type EquipmentType,
    } from "@/lib/schemas";
    ```

#### Equipment Form

- [ ] **File**: `src/app/dashboard/equipment/_components/equipment-form.tsx`
  - **Replace**:
    ```typescript
    import { Tables } from "@/types/supabase";
    ```
  - **With**:
    ```typescript
    import { type EquipmentType } from "@/lib/schemas";
    ```
  - **Update**: Any usage of `Tables<"equipment_types">` to use `EquipmentType`

### Hook Layer

#### Cargo Types Hook

- [ ] **File**: `src/hooks/use-cargo-types.ts`
  - **Replace**:
    ```typescript
    import { Tables } from "@/types/supabase";
    type CargoType = Tables<"cargo_types">;
    ```
  - **With**:
    ```typescript
    import { type CargoType } from "@/lib/schemas";
    ```

#### Equipment Types Hook

- [ ] **File**: `src/hooks/use-equipment-types.ts`
  - **Replace**:
    ```typescript
    import { Tables } from "@/types/supabase";
    type EquipmentType = Tables<"equipment_types">;
    ```
  - **With**:
    ```typescript
    import { type EquipmentType } from "@/lib/schemas";
    ```

## Refactoring Examples

### Service Layer Examples

#### Basic Service File

**Before:**

```typescript
import { createClient } from "@/lib/supabase/server/server";
import { Database } from "@/types/supabase";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("CargoService");

// Define the Cargo type using the Supabase generated types
type Cargo = Database["public"]["Tables"]["cargo_types"]["Row"];

export async function getCargoTypes(): Promise<Cargo[]> {
  const supabase = createClient();
  const { data, error } = await supabase.from("cargo_types").select("*");

  if (error) {
    logger.error("Error fetching cargo types:", error);
    throw new Error(`Failed to fetch cargo types: ${error.message}`);
  }

  return data;
}
```

**After:**

```typescript
import { createClient } from "@/lib/supabase/server/server";
import { type CargoType } from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("CargoService");

export async function getCargoTypes(): Promise<CargoType[]> {
  const supabase = createClient();
  const { data, error } = await supabase.from("cargo_types").select("*");

  if (error) {
    logger.error("Error fetching cargo types:", error);
    throw new Error(`Failed to fetch cargo types: ${error.message}`);
  }

  return data;
}
```

#### Service with Multiple Entity Types

**Before:**

```typescript
import { createClient } from "@/lib/supabase/server/server";
import { Database } from "@/types/supabase";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("ProviderCapabilitiesService");

// Define provider capability type using Supabase generated types
type ProviderCapability =
  Database["public"]["Tables"]["provider_capabilities"]["Row"];
type EquipmentType = Database["public"]["Tables"]["equipment_types"]["Row"];

export async function getProviderCapabilities(
  providerId: string,
): Promise<(ProviderCapability & { equipment?: EquipmentType })[]> {
  const supabase = createClient();
  const { data, error } = await supabase
    .from("provider_capabilities")
    .select("*, equipment:equipment_type_id(id, name, description)")
    .eq("provider_id", providerId);

  if (error) {
    logger.error("Error fetching provider capabilities:", error);
    throw new Error(`Failed to fetch provider capabilities: ${error.message}`);
  }

  return data;
}
```

**After:**

```typescript
import { createClient } from "@/lib/supabase/server/server";
import {
  type ProviderCapability,
  type EquipmentType,
  type ProviderCapabilityWithEquipment,
} from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("ProviderCapabilitiesService");

export async function getProviderCapabilities(
  providerId: string,
): Promise<ProviderCapabilityWithEquipment[]> {
  const supabase = createClient();
  const { data, error } = await supabase
    .from("provider_capabilities")
    .select("*, equipment:equipment_type_id(id, name, description)")
    .eq("provider_id", providerId);

  if (error) {
    logger.error("Error fetching provider capabilities:", error);
    throw new Error(`Failed to fetch provider capabilities: ${error.message}`);
  }

  return data;
}
```

### Component Layer Examples

#### Form Component with Multiple Entity Types

**Before:**

```typescript
"use client";

import * as React from "react";
import { UseFormReturn } from "react-hook-form";
import { z } from "zod";
import { CreateRFQSchema } from "@/lib/schemas";
import { Button } from "@/components/ui/button";
import { Tables } from "@/types/supabase";

type FormValues = z.infer<typeof CreateRFQSchema>;
type Country = Tables<"countries">;
type CargoType = Tables<"cargo_types">;
type EquipmentType = Tables<"equipment_types">;

interface ReviewFormProps {
  form: UseFormReturn<FormValues>;
  onSubmit: (data: FormValues) => void;
  isSubmitting: boolean;
  countries: Country[];
  cargoTypes: CargoType[];
  equipmentTypes: EquipmentType[];
}

export function ReviewForm({
  form,
  onSubmit,
  isSubmitting,
  countries,
  cargoTypes,
  equipmentTypes,
}: ReviewFormProps) {
  // Component implementation...
}
```

**After:**

```typescript
"use client";

import * as React from "react";
import { UseFormReturn } from "react-hook-form";
import { z } from "zod";
import {
  CreateRFQSchema,
  type Country,
  type CargoType,
  type EquipmentType,
} from "@/lib/schemas";
import { Button } from "@/components/ui/button";

type FormValues = z.infer<typeof CreateRFQSchema>;

interface ReviewFormProps {
  form: UseFormReturn<FormValues>;
  onSubmit: (data: FormValues) => void;
  isSubmitting: boolean;
  countries: Country[];
  cargoTypes: CargoType[];
  equipmentTypes: EquipmentType[];
}

export function ReviewForm({
  form,
  onSubmit,
  isSubmitting,
  countries,
  cargoTypes,
  equipmentTypes,
}: ReviewFormProps) {
  // Component implementation...
}
```

### Hook Layer Examples

#### Data Fetching Hook

**Before:**

```typescript
"use client";

import { useState, useEffect } from "react";
import { createClient } from "@/lib/supabase/client";
import { Tables } from "@/types/supabase";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("CargoTypesHook");

type CargoType = Tables<"cargo_types">;

export function useCargoTypes() {
  const [cargoTypes, setCargoTypes] = useState<CargoType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    async function fetchCargoTypes() {
      try {
        setIsLoading(true);
        const supabase = createClient();
        const { data, error } = await supabase.from("cargo_types").select("*");

        if (error) {
          throw new Error(`Failed to fetch cargo types: ${error.message}`);
        }

        setCargoTypes(data);
      } catch (err) {
        logger.error("Error in useCargoTypes:", err);
        setError(err instanceof Error ? err : new Error(String(err)));
      } finally {
        setIsLoading(false);
      }
    }

    fetchCargoTypes();
  }, []);

  return { cargoTypes, isLoading, error };
}
```

**After:**

```typescript
"use client";

import { useState, useEffect } from "react";
import { createClient } from "@/lib/supabase/client";
import { type CargoType } from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("CargoTypesHook");

export function useCargoTypes() {
  const [cargoTypes, setCargoTypes] = useState<CargoType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    async function fetchCargoTypes() {
      try {
        setIsLoading(true);
        const supabase = createClient();
        const { data, error } = await supabase.from("cargo_types").select("*");

        if (error) {
          throw new Error(`Failed to fetch cargo types: ${error.message}`);
        }

        setCargoTypes(data);
      } catch (err) {
        logger.error("Error in useCargoTypes:", err);
        setError(err instanceof Error ? err : new Error(String(err)));
      } finally {
        setIsLoading(false);
      }
    }

    fetchCargoTypes();
  }, []);

  return { cargoTypes, isLoading, error };
}
```

### Action Layer Examples

#### Server Action with Database Types

**Before:**

```typescript
"use server";

import { revalidatePath } from "next/cache";
import { fetchEquipment, addEquipment } from "@/lib/services/equipment.service";
import { CreateEquipmentTypeSchema } from "@/lib/schemas";
import type { Tables } from "@/types/supabase";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("EquipmentActions");

export async function getEquipmentTypes() {
  try {
    const equipment = await fetchEquipment({});
    return {
      success: true,
      data: equipment,
    };
  } catch (error) {
    logger.error("Error fetching equipment types:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch equipment types",
    };
  }
}

export async function createEquipment(formData: FormData) {
  try {
    const rawData = Object.fromEntries(formData.entries());
    const validatedData = CreateEquipmentTypeSchema.parse(rawData);

    const newEquipment = await addEquipment(validatedData);
    revalidatePath("/dashboard/equipment");

    return {
      success: true,
      data: newEquipment,
    };
  } catch (error) {
    logger.error("Error creating equipment:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to create equipment",
    };
  }
}
```

**After:**

```typescript
"use server";

import { revalidatePath } from "next/cache";
import { fetchEquipment, addEquipment } from "@/lib/services/equipment.service";
import { CreateEquipmentTypeSchema, type EquipmentType } from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("EquipmentActions");

export async function getEquipmentTypes() {
  try {
    const equipment = await fetchEquipment({});
    return {
      success: true,
      data: equipment,
    };
  } catch (error) {
    logger.error("Error fetching equipment types:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch equipment types",
    };
  }
}

export async function createEquipment(formData: FormData) {
  try {
    const rawData = Object.fromEntries(formData.entries());
    const validatedData = CreateEquipmentTypeSchema.parse(rawData);

    const newEquipment = await addEquipment(validatedData);
    revalidatePath("/dashboard/equipment");

    return {
      success: true,
      data: newEquipment,
    };
  } catch (error) {
    logger.error("Error creating equipment:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to create equipment",
    };
  }
}
```

## Complex Cases

### Multiple Entity Types in a Single File

When a file uses multiple entity types, import all needed types from the schema files:

**Before:**

```typescript
import { Tables } from "@/types/supabase";

type Provider = Tables<"providers">;
type ProviderContact = Tables<"provider_contacts">;
type ProviderRoute = Tables<"provider_routes">;
```

**After:**

```typescript
import {
  type Provider,
  type ProviderContact,
  type ProviderRoute,
} from "@/lib/schemas";
```

### Composite Types

For composite types that combine multiple entity types, use the composite types from the schema files:

**Before:**

```typescript
import { Tables } from "@/types/supabase";

type Provider = Tables<"providers">;
type ProviderWithContacts = Provider & {
  contacts: Tables<"provider_contacts">[];
};
```

**After:**

```typescript
import {
  type Provider,
  type ProviderContact,
  type ProviderWithContacts,
} from "@/lib/schemas";
```

## Maintenance Guidelines

### Best Practices

#### 1. Always Import Types from Schema Files

When you need to use a database entity type in your code, always import it from the appropriate schema file:

```typescript
// ✅ Correct
import { type Provider } from "@/lib/schemas";

// ❌ Incorrect
import { Tables } from "@/types/supabase";
type Provider = Tables<"providers">;
```

#### 2. Use Central Schema Imports

Always import from the central schema index file (`@/lib/schemas`) rather than directly from entity-specific files:

```typescript
// ✅ Correct
import { type Provider, type ProviderContact } from "@/lib/schemas";

// ❌ Incorrect
import { type Provider } from "@/lib/schemas/provider.schema";
import { type ProviderContact } from "@/lib/schemas/provider-contact.schema";
```

This ensures that future refactoring won't break import paths.

#### 3. Use Schema Validation

When accepting user input or external data, always validate it using the appropriate schema:

```typescript
// ✅ Correct
import { CreateProviderSchema, type Provider } from "@/lib/schemas";

const validatedData = CreateProviderSchema.parse(formData);
const newProvider = await createProvider(validatedData);

// ❌ Incorrect
const newProvider = await createProvider(formData as Provider);
```

#### 4. Extend Schemas When Needed

If you need a specialized version of a schema, extend the existing schema rather than creating a new one from scratch:

```typescript
// ✅ Correct
import { ProviderSchema } from "@/lib/schemas";
import { z } from "zod";

const ProviderWithStatusSchema = ProviderSchema.extend({
  status: z.enum(["active", "inactive"]).default("active"),
});

// ❌ Incorrect
import { z } from "zod";

const ProviderWithStatusSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  // Duplicating all provider fields...
  status: z.enum(["active", "inactive"]).default("active"),
});
```

### Tools and Automation

#### Audit Script

We've created a script to find all instances of direct imports from "@/types/supabase":

```bash
# Run the audit script
npx ts-node scripts/audit-schema-imports.ts
```

This script will:

- Find all files with direct imports from "@/types/supabase"
- Group matches by directory to identify hotspots
- Group matches by import type to identify patterns
- Generate a report with suggested next steps

### Process Guidelines

#### 1. Code Review Checklist

Add the following items to your code review checklist:

- [ ] No direct imports from "@/types/supabase"
- [ ] Types are imported from "@/lib/schemas"
- [ ] Schema validation is used for user input and external data
- [ ] No duplicate type definitions that already exist in schema files

#### 2. Responsibility Assignment

- **Architecture Team**: Responsible for maintaining schema files and ensuring they are up-to-date with the database schema
- **Tech Leads**: Responsible for enforcing proper schema usage during code reviews
- **All Developers**: Responsible for following the guidelines in their code

#### 3. Periodic Auditing

Schedule a quarterly audit of the codebase to ensure compliance:

1. Run the automated audit script
2. Review any exceptions and determine if they are justified
3. Update the guidelines if necessary
4. Document any exceptions and the reasons for them

### Handling Schema Updates

When the database schema changes, follow these steps:

1. Update the Supabase types:

   ```bash
   npx supabase gen types typescript --project-id mshnooylwlnxdeinjlfw --schema public > src/types/supabase.ts
   ```

2. Regenerate the schema files:

   ```bash
   pnpm supazod -i src/types/supabase.ts -o src/lib/schemas/schemas.ts -t src/lib/schemas/schemas.d.ts -s public --verbose
   ```

3. Update any entity-specific schema files that need to be modified based on the schema changes

4. Run the automated audit script to ensure no new direct imports have been introduced

## Tools

### Audit Schema Imports Script

We've created a script to find all instances of direct imports from "@/types/supabase":

```typescript
// scripts/audit-schema-imports.ts
import { execSync } from "child_process";
import * as fs from "fs";
import * as path from "path";

// Find all files with direct imports from @/types/supabase
function findDirectImports(): FileMatch[] {
  try {
    // Use grep to find all imports from @/types/supabase
    const result = execSync(
      'grep -n -r --include="*.ts" --include="*.tsx" "from [\'\\"]@/types/supabase[\'\\"]" src/',
    ).toString();

    // Parse the results
    // ...
  } catch (error) {
    // grep returns non-zero exit code if no matches found
    return [];
  }
}

// Generate a report of the findings
function generateReport(matches: FileMatch[]): void {
  // ...
}

// Main function
function main(): void {
  console.log(
    "Scanning codebase for direct imports from @/types/supabase...\n",
  );

  const matches = findDirectImports();
  generateReport(matches);

  // Exit with error code if matches found (useful for CI/CD)
  process.exit(matches.length > 0 ? 1 : 0);
}

// Run the script
main();
```

## Progress Tracking

- [ ] **Service Layer**: 0/6 files completed
- [ ] **Action Layer**: 0/1 files completed
- [ ] **Component Layer**: 0/4 files completed
- [ ] **Hook Layer**: 0/2 files completed
- [ ] **Maintenance**: 0/9 tasks completed

**Total Progress**: 0/22 tasks completed (0%)
