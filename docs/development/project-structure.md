# Project Structure & Component Organization

This guide outlines the recommended project structure and component organization for the Steelflow application. Following these guidelines ensures a consistent, maintainable, and efficient codebase.

## Table of Contents

1. [CRUD-Focused Next.js Folder Structure](#crud-focused-nextjs-folder-structure)
2. [Component Organization](#component-organization)
3. [Implementation Strategy](#implementation-strategy)
4. [Best Practices](#best-practices)
5. [Summary Table](#summary-table)

## CRUD-Focused Next.js Folder Structure

### Core Principles

- **Organize by feature/view, not by UI type.**
- **Colocate modal and form components with their parent page** if they are only used there.
- **Flatten the structure**: avoid unnecessary folders and deep nesting.
- **Remove empty or unused folders**.
- **Shared logic** (schemas, actions) should remain in a dedicated `_lib` folder.

### Example Structure

```
providers/
  list/
    page.tsx
    // Inline ProviderForm, ProviderDialog, ContactFormModal, etc. here if only used once
    // Or extract to sibling files if they grow too large
  [id]/
    page.tsx
  _lib/
    schemas.ts
    actions.ts
  README.md
```

### Overall Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── api/                # API routes
│   │   └── v1/             # API version 1
│   │       ├── providers/  # Provider API routes
│   │       └── rfqs/       # RFQ API routes
│   ├── (auth)/             # Auth-related routes
│   │   ├── login/          # Login page
│   │   └── register/       # Register page
│   ├── dashboard/          # Dashboard routes
│   │   ├── providers/      # Provider management
│   │   ├── rfqs/           # RFQ management
│   │   └── settings/       # User settings
│   └── page.tsx            # Landing page
├── components/             # React components
│   ├── ui/                 # UI components (from shadcn/ui)
│   ├── features/           # Feature-specific components
│   └── shared/             # Shared components
├── lib/                    # Shared utilities
│   ├── actions/            # Server actions
│   ├── schemas/            # Zod schemas
│   ├── services/           # Business logic services
│   ├── supabase/           # Supabase client and types
│   └── utils/              # Utility functions
└── styles/                 # Global styles
```

## Component Organization

### Component Placement Strategy

1. **Inline components** in the page file unless they are large or reused.
2. **Extract to sibling files** if components grow too large or complex.
3. **Move to shared folders** only if components are used across multiple features.

### Decision Tree for Component Placement

```
Is the component used in multiple features?
├── Yes → Place in shared component folder
└── No → Is the component complex (>100 lines)?
    ├── Yes → Extract to a sibling file
    └── No → Inline in the page file
```

### Example: Provider Management

#### Inline Approach (Preferred for Simple Components)

```tsx
// src/app/dashboard/providers/list/page.tsx
export default function ProvidersListPage() {
  // Page implementation

  // Inline ProviderForm component
  function ProviderForm({ provider, onSubmit }) {
    // Form implementation
    return <form>{/* Form fields */}</form>;
  }

  return (
    <div>
      <h1>Providers</h1>
      <ProviderForm />
    </div>
  );
}
```

#### Extracted Approach (For Complex Components)

```tsx
// src/app/dashboard/providers/list/page.tsx
import { ProviderForm } from "./provider-form";

export default function ProvidersListPage() {
  // Page implementation
  return (
    <div>
      <h1>Providers</h1>
      <ProviderForm />
    </div>
  );
}

// src/app/dashboard/providers/list/provider-form.tsx
export function ProviderForm({ provider, onSubmit }) {
  // Form implementation
  return <form>{/* Form fields */}</form>;
}
```

#### Shared Approach (For Reused Components)

```tsx
// src/components/features/providers/provider-form.tsx
export function ProviderForm({ provider, onSubmit }) {
  // Form implementation
  return <form>{/* Form fields */}</form>;
}

// Used in multiple places:
// src/app/dashboard/providers/list/page.tsx
// src/app/dashboard/providers/[id]/page.tsx
```

## Implementation Strategy

To implement this structure in the Steelflow project:

1. **Move all modal and form components** into their parent page folder if only used there.
2. **Inline components** in the page file unless they are large or reused.
3. **Remove empty or unnecessary folders**.
4. **Update all imports and references** after moving/inlining.
5. **Add documentation** (like this file) to explain the structure and rationale.

## Best Practices

- **Start simple:** Inline everything in the page file.
- **Refactor only when needed:** Extract components if the file grows too large or you need reuse.
- **Document your reasoning:** Add a comment if you extract, explaining why.
- **Keep shared logic in \_lib:** Schemas, actions, and other shared logic should be in a dedicated folder.
- **Use relative imports:** Import from sibling files using relative paths.
- **Use absolute imports:** Import from shared folders using absolute paths.
- **Avoid circular dependencies:** Be careful when importing between components.

## Summary Table

| Component Type        | Placement           | Example                                                                         |
| --------------------- | ------------------- | ------------------------------------------------------------------------------- |
| Simple UI components  | Inline in page file | `function ProviderForm() {...}`                                                 |
| Complex UI components | Sibling file        | `import { ProviderForm } from "./provider-form";`                               |
| Reused UI components  | Shared folder       | `import { ProviderForm } from "@/components/features/providers/provider-form";` |
| UI primitives         | UI folder           | `import { Button } from "@/components/ui/button";`                              |
| Business logic        | Service layer       | `import { fetchProviders } from "@/lib/services/provider.service";`             |
| Form handling         | Server actions      | `import { createProvider } from "@/lib/actions/provider";`                      |
| Data validation       | Schemas             | `import { ProviderSchema } from "@/lib/schemas/provider";`                      |

## Examples

### Example 1: Simple Provider List

```tsx
// src/app/dashboard/providers/list/page.tsx
import { getProviders } from "@/lib/actions/provider";

export default async function ProvidersListPage() {
  const { success, data, error } = await getProviders();

  if (!success) {
    return <div>Error: {error}</div>;
  }

  return (
    <div>
      <h1>Providers</h1>
      <table>
        <thead>
          <tr>
            <th>Name</th>
            <th>Email</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {data.map((provider) => (
            <tr key={provider.id}>
              <td>{provider.name}</td>
              <td>{provider.email}</td>
              <td>
                <button>Edit</button>
                <button>Delete</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
```

### Example 2: Complex Provider Form

```tsx
// src/app/dashboard/providers/list/page.tsx
import { ProviderForm } from "./provider-form";

export default function ProvidersListPage() {
  return (
    <div>
      <h1>Providers</h1>
      <ProviderForm />
    </div>
  );
}

// src/app/dashboard/providers/list/provider-form.tsx
("use client");

import { useFormState } from "react-dom";
import { createProvider } from "@/lib/actions/provider";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

const initialState = {
  success: false,
  error: null,
  errors: {},
};

export function ProviderForm() {
  const [state, formAction] = useFormState(createProvider, initialState);

  return (
    <form action={formAction}>
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="name">
            Name <span className="text-destructive">*</span>
          </Label>
          <Input id="name" name="name" required />
          {state.errors?.name && (
            <p className="text-sm text-destructive">{state.errors.name}</p>
          )}
        </div>

        {/* More form fields */}

        {state.error && (
          <div className="bg-destructive/10 p-3 rounded-md text-destructive text-sm">
            {state.error}
          </div>
        )}

        <Button type="submit">Create Provider</Button>
      </div>
    </form>
  );
}
```

## Related Documentation

- [Architecture Overview](../architecture/overview.md)
- [Service Layer Architecture](../architecture/service-layer.md)
- [Contributing Guidelines](./contributing.md)

# Update Schemas

- npx supabase gen types typescript --project-id mshnooylwlnxdeinjlfw --schema public > src/lib/types/supabase.ts
- pnpm supazod -i src/lib/types/supabase.ts -o src/lib/schemas/schemas.ts -t src/lib/schemas/schemas.d.ts -s public --verbose
