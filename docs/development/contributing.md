# Contributing to Steelflow

This guide outlines the process and standards for contributing to the Steelflow project. Following these guidelines ensures consistent, maintainable, and high-quality code across the codebase.

## Table of Contents

1. [Development Workflow](#development-workflow)
2. [Code Organization](#code-organization)
3. [Coding Standards](#coding-standards)
4. [Pull Request Process](#pull-request-process)
5. [Testing Guidelines](#testing-guidelines)
6. [Documentation](#documentation)

## Development Workflow

### Getting Started

1. Clone the repository:

   ```bash
   git clone https://github.com/your-organization/steelflow.git
   cd steelflow
   ```

2. Install dependencies:

   ```bash
   pnpm install
   ```

3. Set up environment variables:

   ```bash
   cp .env.example .env.local
   ```

   Edit `.env.local` with your Supabase credentials and other required variables.

4. Run the development server:
   ```bash
   pnpm dev
   ```

### Branch Strategy

- `main`: Production-ready code
- `develop`: Integration branch for feature development
- `feature/feature-name`: Feature branches for new features
- `fix/issue-name`: Fix branches for bug fixes

### Development Process

1. Create a new branch from `develop` for your feature or fix
2. Implement your changes following the architecture and coding standards
3. Write tests for your changes
4. Update documentation as needed
5. Submit a pull request to the `develop` branch

## Code Organization

Steelflow uses a layered architecture pattern that separates concerns between UI components, server actions, API routes, and core business logic.

### Layer Structure

| Layer          | Location                        | Purpose                                 |
| -------------- | ------------------------------- | --------------------------------------- |
| Schemas        | `src/lib/schemas/*.ts`          | Zod schemas for validation              |
| Services       | `src/lib/services/*.service.ts` | Core business logic and database access |
| API Routes     | `src/app/api/*/route.ts`        | External API endpoints                  |
| Server Actions | `src/lib/actions/*.ts`          | Server functions for UI interaction     |
| UI Components  | `src/app/**/*.tsx`              | Next.js pages and components            |

### Development Workflow

When implementing a new feature or modifying an existing one, follow this workflow:

1. Start by defining or updating **schemas** for your entity
2. Implement core logic in the **service layer**
3. Create/update **API routes** that use the service
4. Create/update **server actions** that use the service
5. Build UI components that use the server actions

## Coding Standards

### TypeScript

- Use TypeScript for all code
- Define interfaces and types for all data structures
- Use Zod schemas for validation
- Avoid using `any` type
- Use proper error handling with typed errors

### React and Next.js

- Use React Server Components by default
- Only use Client Components when necessary for interactivity
- Follow the Next.js App Router patterns
- Use Server Actions for form handling
- Implement proper error boundaries
- Use Suspense for loading states

### Naming Conventions

- **Files**: Use kebab-case for file names (e.g., `provider-list.tsx`)
- **Components**: Use PascalCase for component names (e.g., `ProviderList`)
- **Functions**: Use camelCase for function names (e.g., `fetchProviders`)
- **Variables**: Use camelCase for variable names (e.g., `providerList`)
- **Constants**: Use UPPER_SNAKE_CASE for constants (e.g., `MAX_PROVIDERS`)
- **Types/Interfaces**: Use PascalCase for types and interfaces (e.g., `Provider`)
- **Schemas**: Use PascalCase with Schema suffix (e.g., `ProviderSchema`)

### Code Style

- Use 2 spaces for indentation
- Use semicolons at the end of statements
- Use single quotes for strings
- Use trailing commas in multi-line objects and arrays
- Keep lines under 100 characters
- Use meaningful variable and function names
- Add comments for complex logic
- Use JSDoc comments for public APIs

## Pull Request Process

### Before Submitting a PR

1. Ensure your code follows the architecture and coding standards
2. Run tests to ensure they pass
3. Update documentation as needed
4. Rebase your branch on the latest `develop` branch
5. Resolve any merge conflicts

### PR Checklist

Before submitting a PR, please ensure:

- [ ] **NO duplication of business logic** between API routes and server actions
- [ ] All database operations go through the service layer
- [ ] Input validation uses appropriate Zod schemas
- [ ] API routes follow REST conventions and provide useful error messages
- [ ] Server Actions include proper state feedback for UI components
- [ ] Cache invalidation (`revalidatePath`) happens in actions when appropriate
- [ ] Types are properly defined and used consistently
- [ ] Tests are written for new functionality
- [ ] Documentation is updated

### PR Review Process

1. At least one approval is required from a code owner
2. All comments must be resolved before merging
3. CI checks must pass
4. PR should be squashed and merged to keep a clean history

## Testing Guidelines

### Types of Tests

- **Unit Tests**: Test individual functions and components
- **Integration Tests**: Test interactions between components
- **End-to-End Tests**: Test complete user flows using Playwright

### Testing Standards

- Write tests for all new functionality
- Maintain test coverage above 80%
- Use meaningful test descriptions
- Follow the Arrange-Act-Assert pattern
- Mock external dependencies
- Use test fixtures for consistent test data

For detailed testing guidelines, see the [Testing Guidelines](../testing/guidelines.md) document.

## Documentation

### Code Documentation

- Add JSDoc comments to public APIs
- Document complex logic with inline comments
- Keep comments up-to-date with code changes

### Project Documentation

- Update README.md with new features and changes
- Update architecture documentation when making significant changes
- Create or update user guides for new features
- Document API endpoints with examples

### Example: Adding a New Feature

When adding a new feature (e.g., "shipping routes"):

1. **Define schemas**:

   ```typescript
   // src/lib/schemas/shipping-route.ts
   import { z } from "zod";

   export const ShippingRouteSchema = z.object({
     origin: z.string().min(1, "Origin is required"),
     destination: z.string().min(1, "Destination is required"),
     distance: z.number().positive("Distance must be positive"),
   });
   ```

2. **Implement service functions**:

   ```typescript
   // src/lib/services/shipping-route.service.ts
   import { createAdminClient } from "@/lib/supabase/admin";
   import { ShippingRoute } from "@/types/supabase";

   export async function fetchShippingRoutes(): Promise<ShippingRoute[]> {
     const supabase = createAdminClient();
     const { data, error } = await supabase.from("shipping_routes").select("*");

     if (error) {
       throw new Error(`Failed to fetch shipping routes: ${error.message}`);
     }

     return data;
   }
   ```

3. **Create API routes**:

   ```typescript
   // src/app/api/shipping-routes/route.ts
   import { NextRequest } from "next/server";
   import { fetchShippingRoutes } from "@/lib/services/shipping-route.service";
   import { formatApiResponse, handleApiError } from "@/lib/utils/api";

   export async function GET(request: NextRequest) {
     try {
       const routes = await fetchShippingRoutes();
       return formatApiResponse(routes);
     } catch (error) {
       return handleApiError(error, "Failed to fetch shipping routes");
     }
   }
   ```

4. **Implement server actions**:

   ```typescript
   // src/lib/actions/shipping-route.ts
   "use server";

   import { revalidatePath } from "next/cache";
   import { fetchShippingRoutes } from "@/lib/services/shipping-route.service";

   export async function getShippingRoutes() {
     try {
       const routes = await fetchShippingRoutes();
       return { success: true, data: routes };
     } catch (error) {
       return {
         success: false,
         error:
           error instanceof Error ? error.message : "An unknown error occurred",
       };
     }
   }
   ```

5. **Build UI components**:

   ```tsx
   // src/app/dashboard/shipping-routes/page.tsx
   import { getShippingRoutes } from "@/lib/actions/shipping-route";
   import { ShippingRouteList } from "./shipping-route-list";

   export default async function ShippingRoutesPage() {
     const { success, data, error } = await getShippingRoutes();

     if (!success) {
       return <div>Error: {error}</div>;
     }

     return (
       <div>
         <h1>Shipping Routes</h1>
         <ShippingRouteList routes={data} />
       </div>
     );
   }
   ```

## Related Documentation

- [Architecture Overview](../architecture/overview.md)
- [Service Layer Architecture](../architecture/service-layer.md)
- [Project Structure](./project-structure.md)
- [Testing Guidelines](../testing/guidelines.md)
