# Steelflow Documentation

Welcome to the Steelflow documentation. This repository contains comprehensive documentation for the Steelflow application, including architecture, development guidelines, testing procedures, and feature-specific documentation.

## Documentation Structure

```
docs/
├── architecture/                # Architecture and design patterns
│   ├── overview.md              # High-level architecture overview
│   ├── service-layer.md         # Service layer architecture details
│   ├── api-protection.md        # API protection with Unkey
│   └── error-handling.md        # Error handling patterns
│
├── development/                 # Development guidelines
│   ├── contributing.md          # Contributing guidelines
│   ├── project-structure.md     # Project structure guidelines
│   ├── typescript-standards.md  # TypeScript standards and best practices
│   ├── migration-guides/        # Migration guides
│   │   ├── service-layer.md     # Service layer migration guide
│   │   ├── unkey-protection.md  # API route to Unkey migration
│   │   └── examples/            # Example migrations
│   │       └── cargo-types.md   # Cargo types migration example
│
├── testing/                     # Testing documentation
│   ├── guidelines.md            # Testing guidelines
│   └── mocking-auth.md          # Mocking Google Auth in tests
│
├── features/                    # Feature-specific documentation
│   ├── email-management.md      # Email management system
│   └── api-keys.md              # API key management
│
└── tech-stack/                  # Technology documentation
    └── research-stack.md        # Research on tech stack components
```

## Getting Started

If you're new to the project, we recommend starting with the following documents:

1. [Architecture Overview](./architecture/overview.md) - Understand the high-level architecture
2. [Contributing Guidelines](./development/contributing.md) - Learn how to contribute to the project
3. [Project Structure](./development/project-structure.md) - Understand the project structure
4. [Research Stack](./tech-stack/research-stack.md) - Learn about the technologies used

## Architecture

The architecture documentation provides a comprehensive overview of the Steelflow application architecture:

- [Architecture Overview](./architecture/overview.md) - High-level architecture overview
- [Service Layer Architecture](./architecture/service-layer.md) - Service layer architecture details
- [API Protection](./architecture/api-protection.md) - API protection with Unkey
- [Error Handling](./architecture/error-handling.md) - Error handling patterns

## Development Guidelines

The development documentation provides guidelines for contributing to the project:

- [Contributing Guidelines](./development/contributing.md) - Guidelines for contributing to the project
- [Project Structure](./development/project-structure.md) - Project structure guidelines
- [TypeScript Standards](./development/typescript-standards.md) - TypeScript standards and best practices

### Migration Guides

The migration guides provide step-by-step instructions for migrating existing code to follow the architecture patterns:

- [Service Layer Migration Guide](./development/migration-guides/service-layer.md) - Migrating to the service layer architecture
- [Unkey Protection Migration Guide](./development/migration-guides/unkey-protection.md) - Adding Unkey protection to API routes
- [Cargo Types Migration Example](./development/migration-guides/examples/cargo-types.md) - Example of migrating the Cargo Types feature

## Testing

The testing documentation provides guidelines for writing and running tests:

- [Testing Guidelines](./testing/guidelines.md) - Guidelines for writing Playwright tests
- [Mocking Google Auth](./testing/mocking-auth.md) - Mocking Google Authentication in tests

## Features

The feature documentation provides detailed information about specific features:

- [Email Management](./features/email-management.md) - Email management system
- [API Keys](./features/api-keys.md) - API key management

## Technology Stack

The technology documentation provides information about the technologies used in the project:

- [Research Stack](./tech-stack/research-stack.md) - Research on tech stack components

## Contributing to Documentation

We welcome contributions to the documentation. If you find any issues or have suggestions for improvement, please submit a pull request or open an issue.

### Documentation Guidelines

1. **Use Markdown**: All documentation should be written in Markdown format
2. **Follow the Structure**: Place new documentation in the appropriate folder
3. **Link Related Documents**: Add links to related documents
4. **Keep It Updated**: Update documentation when the code changes
5. **Use Code Examples**: Include code examples where appropriate
6. **Be Concise**: Keep documentation concise and to the point
7. **Use Headings**: Use headings to organize content
8. **Add Table of Contents**: Add a table of contents for longer documents

## License

This documentation is licensed under the same license as the Steelflow project.
