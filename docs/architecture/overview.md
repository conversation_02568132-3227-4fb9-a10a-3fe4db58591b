# Architecture Overview

This document provides a high-level overview of the Steelflow application architecture, explaining the key components, their relationships, and the design principles that guide the system.

## Table of Contents

1. [Core Architecture](#core-architecture)
2. [Key Components](#key-components)
3. [Design Principles](#design-principles)
4. [Data Flow](#data-flow)
5. [Deployment Architecture](#deployment-architecture)

## Core Architecture

Steelflow follows a layered architecture with clear separation of concerns:

```
UI Layer → Server Actions → Service Layer → Database
           API Routes   ↗
```

This architecture ensures:

- Separation of concerns
- Single source of truth for business logic
- Consistent error handling
- Type safety throughout the application
- Elimination of code duplication

## Key Components

### 1. UI Layer

The UI layer consists of React components built with Next.js 15 App Router:

- **Server Components**: Default component type that renders on the server
- **Client Components**: Components that render on the client with the `"use client"` directive
- **Layouts**: Shared UI between routes that preserves state
- **Error Boundaries**: Components that catch errors in their child component tree

### 2. Server Actions

Server actions are server-side functions that can be called from the client:

- Handle form submissions and user interactions
- Call service functions to execute business logic
- Revalidate cache when data changes
- Return data and status to the UI

### 3. API Routes

API routes handle HTTP requests from external clients:

- Validate input data using schemas
- Call service functions to execute business logic
- Format responses consistently
- Handle errors gracefully
- Implement authentication and authorization

### 4. Service Layer

The service layer contains all business logic and database access:

- Implement CRUD operations for all entities
- Enforce business rules and validation
- Handle complex operations that span multiple entities
- Throw meaningful errors with descriptive messages

### 5. Database

Steelflow uses Supabase as its database and authentication provider:

- PostgreSQL database with Row Level Security
- Supabase Auth for authentication
- Supabase Storage for file storage
- Real-time subscriptions for live updates

## Design Principles

### 1. Separation of Concerns

Each layer has a specific responsibility and should not be concerned with the implementation details of other layers.

### 2. Single Source of Truth

Business logic should only exist in the service layer to avoid duplication and inconsistencies.

### 3. Type Safety

Use TypeScript types and Zod schemas for validation throughout the application.

### 4. DRY (Don't Repeat Yourself)

Avoid duplicating code across different layers or components.

### 5. Progressive Enhancement

Build applications that work without JavaScript and enhance them with client-side features.

## Data Flow

### Request Flow

1. User interacts with the UI (clicks a button, submits a form)
2. UI calls a server action or makes an API request
3. Server action or API route validates the input
4. Server action or API route calls a service function
5. Service function executes business logic and accesses the database
6. Service function returns data or throws an error
7. Server action or API route handles the response or error
8. UI updates based on the response

### Authentication Flow

1. User logs in with Google OAuth
2. Supabase Auth handles the authentication
3. User session is stored in cookies
4. Server components can access the user session
5. Row Level Security enforces data access rules

## Deployment Architecture

Steelflow is deployed on Google Cloud Run:

- Dockerized Next.js application
- Google Cloud Run for serverless hosting
- Google Cloud Artifact Registry for Docker images
- Google Cloud Scheduler for scheduled tasks
- Google Cloud SQL for PostgreSQL database (via Supabase)

## Related Documentation

- [Service Layer Architecture](./service-layer.md)
- [API Protection](./api-protection.md)
- [Error Handling](./error-handling.md)
- [Project Structure](../development/project-structure.md)
