# Service Layer Architecture

This document outlines the Service Layer Architecture implemented in the Steelflow project. This architecture ensures separation of concerns, eliminates duplication of business logic, and creates a maintainable codebase that supports both internal UI operations and external API consumption.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Core Principles](#core-principles)
3. [Layer Responsibilities](#layer-responsibilities)
4. [Implementation Guidelines](#implementation-guidelines)
5. [Best Practices](#best-practices)
6. [Anti-Patterns to Avoid](#anti-patterns-to-avoid)

## Architecture Overview

The Service Layer Architecture consists of the following layers:

```
UI Components → Server Actions → Service Layer → Database
                API Routes   ↗
```

Each layer has a specific responsibility and communicates only with adjacent layers:

```
+---------------------+       +--------------------------+        +-------------------------------+
| UI Layer            |       | Action Layer             |        | Service Layer                 |
| (React/Next.js)     | ----> | (Server Actions/API)     | -----> | (Business Logic/Data Access)  |
+---------------------+       +--------------------------+        +-------------------------------+
```

## Core Principles

1. **Separation of Concerns**: Each layer has a specific responsibility.
2. **Single Source of Truth**: Business logic should only exist in the service layer.
3. **Consistent Error Handling**: Errors should be handled consistently across the application.
4. **Type Safety**: Use TypeScript types and Zod schemas for validation.
5. **DRY (Don't Repeat Yourself)**: Avoid duplicating code across different layers.

## Layer Responsibilities

### 1. Service Layer (`src/lib/services/*.service.ts`)

The service layer is the heart of the application, containing all business logic and database access.

**Responsibilities**:

- Implement all database access
- Enforce business rules and validation
- Handle complex operations that span multiple entities
- Throw meaningful errors with descriptive messages

**Example**:

```typescript
// src/lib/services/provider.service.ts
export async function fetchProviders(
  params: GetProvidersParams,
): Promise<Provider[]> {
  const supabase = createAdminClient();
  const { data, error } = await supabase.from("providers").select("*");

  if (error) {
    throw new Error(`Failed to fetch providers: ${error.message}`);
  }

  return data;
}
```

### 2. API Routes (`src/app/api/*/route.ts`)

API routes handle HTTP requests and responses, but delegate business logic to the service layer.

**Responsibilities**:

- Validate input data using schemas
- Call service functions to execute business logic
- Format responses consistently
- Handle errors gracefully
- Implement authentication and authorization

**Example**:

```typescript
// src/app/api/providers/route.ts
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const params = {
      id: searchParams.get("id") ?? undefined,
    };

    // Validate params
    const [isValid, validatedData, errorResponse] = validateWithSchema(
      GetProvidersParamsSchema,
      params,
    );

    if (!isValid) {
      return errorResponse;
    }

    // Use service to fetch data
    const result = await fetchProviders(validatedData!);

    return formatApiResponse(result);
  } catch (error) {
    return handleApiError(error, "Failed to fetch providers");
  }
}
```

### 3. Server Actions (`src/lib/actions/*.ts`)

Server actions are server-side functions that can be called from the client.

**Responsibilities**:

- Handle form submissions and user interactions
- Call service functions to execute business logic
- Revalidate cache when data changes
- Return data and status to the UI

**Example**:

```typescript
// src/lib/actions/provider.ts
"use server";

import { revalidatePath } from "next/cache";
import { fetchProviders } from "@/lib/services/provider.service";
import { GetProvidersParamsSchema } from "@/lib/schemas/provider";

export async function getProviders(formData: FormData) {
  const params = {
    id: formData.get("id")?.toString() ?? undefined,
  };

  // Validate params
  const validationResult = GetProvidersParamsSchema.safeParse(params);

  if (!validationResult.success) {
    return {
      success: false,
      errors: validationResult.error.format(),
    };
  }

  try {
    // Use service to fetch data
    const providers = await fetchProviders(validationResult.data);

    return {
      success: true,
      data: providers,
    };
  } catch (error) {
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "An unknown error occurred",
    };
  }
}
```

### 4. UI Components (`src/app/**/*.tsx`)

UI components are responsible for rendering the user interface and handling user interactions.

**Responsibilities**:

- Render the user interface
- Handle user interactions
- Call server actions
- Display data and errors

## Implementation Guidelines

### 1. Schema Definition

Create a schema file for each entity:

```typescript
// src/lib/schemas/provider.ts
import { z } from "zod";

export const GetProvidersParamsSchema = z.object({
  id: z.string().uuid("Invalid UUID format").optional(),
  page: z.coerce.number().int().min(1).default(1),
  pageSize: z.coerce.number().int().min(1).max(100).default(10),
});

export type GetProvidersParams = z.infer<typeof GetProvidersParamsSchema>;
```

### 2. Service Implementation

Implement service functions for all database operations:

```typescript
// src/lib/services/provider.service.ts
import { createAdminClient } from "@/lib/supabase/admin";
import { GetProvidersParams } from "@/lib/schemas/provider";
import { Provider } from "@/types/supabase";

export async function fetchProviders(
  params: GetProvidersParams,
): Promise<Provider[]> {
  const supabase = createAdminClient();
  let query = supabase.from("providers").select("*");

  if (params.id) {
    query = query.eq("id", params.id);
  }

  const { data, error } = await query;

  if (error) {
    throw new Error(`Failed to fetch providers: ${error.message}`);
  }

  return data;
}
```

## Best Practices

### DO:

✅ **Always use the service layer for database access** - Never access Supabase directly from API routes or server actions  
✅ **Keep service functions focused and atomic** - Each function should do one thing well  
✅ **Use proper error handling** - Services should throw meaningful errors  
✅ **Always validate inputs** - Use Zod schemas for all external inputs  
✅ **Use TypeScript types** - Leverage Supabase-generated types  
✅ **Document complex functions** - Add JSDoc comments to explain complex logic  
✅ **Write tests for service functions** - Ensure business logic works as expected

### DON'T:

❌ **Don't duplicate business logic** - Keep it in the service layer only  
❌ **Don't access the database directly from API routes or server actions** - Always go through the service layer  
❌ **Don't mix concerns** - Keep each layer focused on its responsibility  
❌ **Don't return Supabase errors directly** - Transform them into meaningful application errors  
❌ **Don't bypass validation** - Always validate inputs before processing

## Anti-Patterns to Avoid

### 1. Direct Database Access

❌ **ANTI-PATTERN**: Accessing the database directly from API routes or server actions.

```typescript
// ❌ INCORRECT: Direct database access in API route
export async function GET() {
  const supabase = createAdminClient();
  const { data, error } = await supabase.from("providers").select("*");
  // ...
}
```

### 2. Duplicated Business Logic

❌ **ANTI-PATTERN**: Implementing the same business logic in multiple places.

```typescript
// ❌ INCORRECT: Duplicating logic in API route and server action
// API route
export async function GET() {
  const supabase = createAdminClient();
  const { data, error } = await supabase.from("providers").select("*");
  // Business logic here...
}

// Server action
export async function getProviders() {
  const supabase = createAdminClient();
  const { data, error } = await supabase.from("providers").select("*");
  // Same business logic duplicated here...
}
```

### 3. Inconsistent Error Handling

❌ **ANTI-PATTERN**: Handling errors differently across the application.

### 4. Bypassing Validation

❌ **ANTI-PATTERN**: Not validating input data before processing.

## Related Documentation

- [Service Layer Migration Guide](../development/migration-guides/service-layer.md)
- [Cargo Types Migration Example](../development/migration-guides/examples/cargo-types.md)
