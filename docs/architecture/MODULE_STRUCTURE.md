# Dashboard Module Structure

This document outlines the standardized structure for dashboard modules in the Steelflow application. Following these guidelines ensures consistency across the codebase, making it easier to maintain and extend.

## Table of Contents

- [Architecture Overview](#architecture-overview)
- [Directory Structure](#directory-structure)
- [Component Naming Conventions](#component-naming-conventions)
- [Server Component Patterns](#server-component-patterns)
- [Client Component Patterns](#client-component-patterns)
- [Data Flow Patterns](#data-flow-patterns)
- [Common Anti-Patterns to Avoid](#common-anti-patterns-to-avoid)
- [Implementation Examples](#implementation-examples)
- [Refactoring Plan](#refactoring-plan)

## Architecture Overview

The Steelflow application follows a service-action layer architecture:

```
UI Components → Server Actions → Service Layer → Database
           API Routes   ↗
```

Each layer has specific responsibilities:

1. **UI Components**: Present data and handle user interactions
2. **Server Actions**: Process form submissions and data mutations
3. **API Routes**: Handle external HTTP requests
4. **Service Layer**: Encapsulate business logic and database access

## Directory Structure

Each dashboard module should follow this standardized directory structure:

```
src/app/dashboard/module-name/
├── _components/                  # Module-specific components
│   ├── module-data-table.tsx     # Data table component
│   ├── module-form.tsx           # Form component
│   ├── dialogs/                  # Dialog components
│   │   ├── add-module-dialog.tsx
│   │   └── edit-module-dialog.tsx
│   └── module-content.tsx        # Server component for data fetching
├── [id]/                         # Detail page (if applicable)
│   ├── _components/              # Detail page components
│   │   └── module-detail-skeleton.tsx
│   └── page.tsx                  # Detail page component
├── layout.tsx                    # Module-specific layout
├── page.tsx                      # Main listing page
└── module_summary.md             # Module documentation
```

## Component Naming Conventions

### Server Components

- **Page Components**: `ModulePage` (e.g., `CargoPage`)
- **Content Components**: `ModuleContent` (e.g., `CargoContent`)
- **Layout Components**: `ModuleLayout` (e.g., `CargoLayout`)

### Client Components

- **Data Tables**: `ModuleDataTable` (e.g., `CargoDataTable`)
- **Forms**: `ModuleForm` (e.g., `CargoForm`)
- **Dialogs**: `AddModuleDialog`, `EditModuleDialog` (e.g., `AddCargoDialog`)
- **Skeletons**: `ModuleSkeleton`, `ModuleDetailSkeleton` (e.g., `CargoSkeleton`)

## Server Component Patterns

### Page Component Pattern

```typescript
// src/app/dashboard/module-name/page.tsx
import { Suspense } from "react";
import { Metadata } from "next";
import { ModuleContent } from "./_components/module-content";
import { ModuleSkeleton } from "./_components/module-skeleton";
import { Button } from "@/components/ui/button";
import { PlusIcon } from "lucide-react";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Module Title | Steelflow",
  description: "Module description",
};

export default function ModulePage() {
  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Module Title</h1>
          <p className="text-muted-foreground">Module description</p>
        </div>
        <Button asChild>
          <Link href="/dashboard/module-name/new">
            <PlusIcon className="mr-2 h-4 w-4" />
            Create Module
          </Link>
        </Button>
      </div>

      <Suspense fallback={<ModuleSkeleton />}>
        <ModuleContent />
      </Suspense>
    </div>
  );
}
```

### Content Component Pattern

```typescript
// src/app/dashboard/module-name/_components/module-content.tsx
import { getModuleDataAction } from "@/lib/actions/module.actions";
import { ModuleDataTable } from "./module-data-table";

export async function ModuleContent() {
  const result = await getModuleDataAction();

  if (!result.success) {
    return (
      <div className="bg-destructive/15 p-4 rounded-md text-destructive">
        {result.error}
      </div>
    );
  }

  return <ModuleDataTable data={result.data.data} pagination={result.data.pagination} />;
}
```

### Detail Page Pattern

```typescript
// src/app/dashboard/module-name/[id]/page.tsx
import { Suspense } from "react";
import { notFound } from "next/navigation";
import { getModuleItemAction } from "@/lib/actions/module.actions";
import { ModuleDetailSkeleton } from "./_components/module-detail-skeleton";
import { ModuleForm } from "../_components/module-form";

export default async function ModuleDetailPage({
  params,
}: {
  params: Promise<{ id: string }> | { id: string };
}) {
  const { id } = await params;
  const result = await getModuleItemAction(id);

  if (!result.success || !result.data) {
    notFound();
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold tracking-tight mb-6">Edit Module</h1>
      <ModuleForm initialData={result.data} />
    </div>
  );
}
```

## Client Component Patterns

### Data Table Pattern

```typescript
// src/app/dashboard/module-name/_components/module-data-table.tsx
"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { DataTable } from "@/components/ui/tables";
import { Button } from "@/components/ui/button";
import { PencilIcon, TrashIcon, MoreHorizontal } from "lucide-react";
import { toast } from "@/components/ui/sonner";
import { deleteModuleAction } from "@/lib/actions/module.actions";
import type { Module, PaginationData } from "@/lib/schemas";

interface ModuleDataTableProps {
  data: Module[];
  pagination?: PaginationData;
}

export function ModuleDataTable({ data, pagination }: ModuleDataTableProps) {
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);

  // Table columns and actions
  // ...

  return (
    <DataTable
      columns={columns}
      data={data}
      pagination={pagination}
      filterConfig={{
        search: {
          placeholder: "Filter by name...",
          columnId: "name",
        },
      }}
    />
  );
}
```

### Form Pattern

```typescript
// src/app/dashboard/module-name/_components/module-form.tsx
"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { toast } from "@/components/ui/sonner";
import { createModuleAction, updateModuleAction } from "@/lib/actions/module.actions";
import { ModuleSchema, type Module } from "@/lib/schemas";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

interface ModuleFormProps {
  initialData?: Module;
}

export function ModuleForm({ initialData }: ModuleFormProps) {
  const router = useRouter();
  const form = useForm({
    resolver: zodResolver(ModuleSchema),
    defaultValues: initialData || {
      name: "",
      description: "",
    },
  });

  // Form submission handler
  // ...

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Form fields */}
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? "Saving..." : initialData ? "Update" : "Create"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
```

## Data Flow Patterns

### Server to Client Data Flow

```
Server Component (page.tsx)
  ↓
Async Data Fetching (module-content.tsx)
  ↓
Pass Data to Client Component (module-data-table.tsx)
  ↓
Client-side Interactivity
```

### Form Submission Flow

```
Client Form Component (module-form.tsx)
  ↓
Server Action (module.actions.ts)
  ↓
Service Layer (module.service.ts)
  ↓
Database (Supabase)
  ↓
Response back to Client
```

## Common Anti-Patterns to Avoid

### 1. Direct Database Access in UI Components

❌ **Bad Practice**:

```typescript
// Directly accessing database in a component
export default async function BadComponent() {
  const supabase = await createClient();
  const { data, error } = await supabase.from("table").select("*");
  return <div>{data.map(item => <div key={item.id}>{item.name}</div>)}</div>;
}
```

✅ **Good Practice**:

```typescript
// Using service layer through actions
export default async function GoodComponent() {
  const result = await getDataAction();
  return <DataDisplay data={result.data} />;
}
```

### 2. Mixing Server and Client Logic

❌ **Bad Practice**:

```typescript
// Mixing server and client code
"use client";
export default async function MixedComponent() { // Error: async functions can't be used in client components
  const data = await fetchData();
  return <div onClick={() => handleClick()}>{data}</div>;
}
```

✅ **Good Practice**:

```typescript
// Server component for data fetching
export default async function ParentComponent() {
  const data = await fetchData();
  return <ClientComponent initialData={data} />;
}

// Client component for interactivity
"use client";
export function ClientComponent({ initialData }) {
  return <div onClick={() => handleClick()}>{initialData}</div>;
}
```

### 3. Inconsistent Component Structure

❌ **Bad Practice**:

```typescript
// Inconsistent structure across modules
// Module A: page.tsx directly fetches data
// Module B: uses a separate content component
// Module C: uses a client wrapper component
```

✅ **Good Practice**:

```typescript
// Consistent structure across all modules
// All modules use a content component for data fetching
// All modules wrap content in Suspense with appropriate skeleton
```

### 4. Prop Drilling Through Many Layers

❌ **Bad Practice**:

```typescript
// Passing props through multiple component layers
<ParentComponent data={data} onUpdate={handleUpdate} />
  // which passes to
  <ChildComponent data={data} onUpdate={onUpdate} />
    // which passes to
    <GrandchildComponent data={data} onUpdate={onUpdate} />
```

✅ **Good Practice**:

```typescript
// Using context or state management for deeply shared state
// Or restructuring components to minimize prop passing
```

### 5. Inconsistent Error Handling

❌ **Bad Practice**:

```typescript
// Different error handling approaches in different modules
// Module A: Uses try/catch and returns custom error component
// Module B: Lets errors bubble up
// Module C: Shows toast notifications for errors
```

✅ **Good Practice**:

```typescript
// Consistent error handling across all modules
// All server components handle errors and return appropriate UI
// All client components use consistent error handling patterns
```

## Implementation Examples

### Example: Standard Page Component

```typescript
// src/app/dashboard/module-name/page.tsx
import { Suspense } from "react";
import { Metadata } from "next";
import { ModuleContent } from "./_components/module-content";
import { ModuleSkeleton } from "./_components/module-skeleton";
import { Button } from "@/components/ui/button";
import { PlusIcon } from "lucide-react";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Module Title | Steelflow",
  description: "Module description",
};

export default function ModulePage() {
  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Module Title</h1>
          <p className="text-muted-foreground">Module description</p>
        </div>
        <Button asChild>
          <Link href="/dashboard/module-name/new">
            <PlusIcon className="mr-2 h-4 w-4" />
            Create Module
          </Link>
        </Button>
      </div>

      <Suspense fallback={<ModuleSkeleton />}>
        <ModuleContent />
      </Suspense>
    </div>
  );
}
```

### Example: Content Component with Data Fetching

```typescript
// src/app/dashboard/module-name/_components/module-content.tsx
import { getModuleDataAction } from "@/lib/actions/module.actions";
import { ModuleDataTable } from "./module-data-table";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("ModuleContent");

export async function ModuleContent() {
  logger.info("Fetching module data");
  const result = await getModuleDataAction();

  if (!result.success) {
    logger.error("Error fetching module data:", result.error);
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{result.error}</AlertDescription>
      </Alert>
    );
  }

  logger.info(`Successfully fetched ${result.data?.data?.length || 0} items`);

  return <ModuleDataTable data={result.data.data} pagination={result.data.pagination} />;
}
```

### Example: Dialog Component Structure

```typescript
// src/app/dashboard/module-name/_components/dialogs/add-module-dialog.tsx
"use client";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ModuleForm } from "../forms/module-form";

interface AddModuleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function AddModuleDialog({ open, onOpenChange, onSuccess }: AddModuleDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New Module</DialogTitle>
        </DialogHeader>
        <ModuleForm
          onSuccess={() => {
            onSuccess();
          }}
        />
      </DialogContent>
    </Dialog>
  );
}
```

# Refactoring Plan

The following plan outlines the steps to standardize all dashboard modules according to the structure defined above. Each task has a checkbox to track progress.

## Phase 1: Preparation and Documentation

### Documentation Tasks

- [ ] Create module documentation template with required sections
  - Module purpose
  - Component structure
  - Data flow diagram
  - API endpoints
  - Server actions
- [ ] Update main README.md with link to MODULE_STRUCTURE.md
- [ ] Create visual diagrams of component relationships
- [ ] Document common patterns and anti-patterns

### Setup Tasks

- [ ] Add ESLint rules to enforce naming conventions
  - Component naming rules
  - File naming rules
  - Directory structure rules
- [ ] Create shared utility functions for common patterns
  - Error handling
  - Loading states
  - Pagination

## Phase 2: Cargo Module Refactoring ✅

The Cargo module is the simplest and will serve as a reference implementation.

### Directory Structure

- [x] Create `_components` directory if not exists
- [x] Create `dialogs` subdirectory in `_components`

### Component Refactoring

- [x] Extract `CargoContent` from `page.tsx` to `_components/cargo-content.tsx`
- [x] Rename `cargo-table.tsx` to `cargo-data-table.tsx`
- [x] Move dialog logic from `cargo-table.tsx` to `dialogs/add-cargo-dialog.tsx` and `dialogs/edit-cargo-dialog.tsx`
- [x] Create `cargo-skeleton.tsx` for loading state

### File Updates

- [x] Update imports in all affected files
- [x] Update `page.tsx` to use new component structure
- [x] Create `cargo_summary.md` with module documentation

## Phase 3: Equipment Module Refactoring ✅

### Directory Structure

- [x] Create `dialogs` subdirectory in `_components`

### Component Refactoring

- [x] Create `equipment-content.tsx` for data fetching
- [x] Rename `equipment-table.tsx` to `equipment-data-table.tsx`
- [x] Move `equipment-dialog.tsx` to `dialogs/equipment-dialog.tsx`
- [x] Refactor `equipment-client.tsx` to follow standard pattern
- [x] Create `equipment-skeleton.tsx` for loading state

### File Updates

- [x] Update imports in all affected files
- [x] Update `page.tsx` to use new component structure
- [x] Create `equipment_summary.md` with module documentation

## Phase 4: Providers Module Refactoring ✅

### Directory Structure

- [x] Organize `_components` directory
  - [x] Create `dialogs` subdirectory
  - [x] Rename `tables` directory to match standard (if needed)
  - [x] Ensure `forms` directory follows standard

### Component Refactoring

- [x] Extract `ProvidersContent` from `page.tsx` to `_components/providers-content.tsx`
- [x] Rename `providers-data-table.tsx` to follow convention (if needed)
- [x] Move `add-provider-modal.tsx` to `dialogs/add-provider-dialog.tsx`
- [x] Standardize detail page components in `[id]/_components`
- [x] Create proper skeleton components

### Detail Page Refactoring

- [x] Extract content component from detail page
- [x] Standardize tabs implementation
- [x] Ensure proper loading states with Suspense

### File Updates

- [x] Update imports in all affected files
- [x] Update `page.tsx` and `[id]/page.tsx` to use new component structure
- [x] Create `providers_summary.md` with module documentation

## Phase 5: RFQs Module Refactoring ✅

### Directory Structure

- [x] Organize `_components` directory
  - [x] Create `dialogs` subdirectory
  - [x] Create `forms` subdirectory
  - [x] ~~Move `form-steps` to `forms/steps`~~ (Deprecated: Multi-step form replaced with consolidated form)

### Component Refactoring

- [x] Extract `RFQsContent` from `page.tsx` to `_components/rfqs-content.tsx`
- [x] Rename `rfqs-data-table.tsx` to follow convention (if needed)
- [x] Move dialog components to `dialogs/` subdirectory:
  - [x] `bid-entry-dialog.tsx` → `dialogs/bid-entry-dialog.tsx`
  - [x] `bid-update-dialog.tsx` → `dialogs/bid-update-dialog.tsx`
  - [x] `email-composition-dialog.tsx` → `dialogs/email-composition-dialog.tsx`
  - [x] `manual-provider-dialog.tsx` → `dialogs/manual-provider-dialog.tsx`
- [x] Move form components to `forms/` subdirectory:
  - [x] `bid-entry-form.tsx` → `forms/bid-entry-form.tsx`
  - [x] `bid-update-form.tsx` → `forms/bid-update-form.tsx`
  - [x] ~~`rfq-form-wizard.tsx` → `forms/rfq-form-wizard.tsx`~~ (Deprecated: Replaced with `forms/rfq-form.tsx`)
- [x] Create proper skeleton components

### Detail Page Refactoring

- [x] Extract content component from detail page
- [x] Standardize component organization
- [x] Ensure proper loading states with Suspense

### File Updates

- [x] Update imports in all affected files
- [x] Update `page.tsx` and `[id]/page.tsx` to use new component structure
- [x] Create `rfqs_summary.md` with module documentation

## Phase 6: Testing and Validation

### Unit Testing

- [ ] Create tests for standardized components
  - [ ] Test server components
  - [ ] Test client components
  - [ ] Test data flow

### Integration Testing

- [ ] Verify all modules follow the standard structure
- [ ] Test navigation between pages
- [ ] Test form submissions
- [ ] Test error states

### Performance Testing

- [ ] Measure and compare loading times before and after refactoring
- [ ] Verify Suspense boundaries are correctly implemented
- [ ] Test with slow network conditions

## Phase 7: Final Review and Documentation

### Code Review

- [ ] Conduct comprehensive code review of all refactored modules
- [ ] Verify naming conventions are followed
- [ ] Ensure directory structure is consistent

### Documentation Finalization

- [ ] Update all module documentation with final structure
- [ ] Create migration guide for future module development
- [ ] Document lessons learned and best practices

### Knowledge Sharing

- [ ] Conduct team walkthrough of the new structure
- [ ] Create onboarding documentation for new developers
- [ ] Document common patterns and anti-patterns
