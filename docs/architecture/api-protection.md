# API Protection with Unkey

This document explains how to protect API routes with Unkey authentication in the Steelflow application.

## Table of Contents

1. [Overview](#overview)
2. [Setup and Configuration](#setup-and-configuration)
3. [Protecting API Routes](#protecting-api-routes)
4. [Permission System](#permission-system)
5. [Public API Routes](#public-api-routes)
6. [Testing Protected Routes](#testing-protected-routes)
7. [Troubleshooting](#troubleshooting)

## Overview

All API routes in the `/api/v1/` path should be protected with Unkey authentication to ensure that only authorized clients can access them. This is implemented using the Unkey Next.js SDK and our custom utility functions.

Unkey is an open-source API management platform that provides:

- API key authentication
- Permission-based access control
- Rate limiting
- Analytics

## Setup and Configuration

### Required Environment Variables

Add the following environment variables to your `.env.local` file:

```
# Unkey Configuration
UNKEY_API_ID=your_unkey_api_id
UNKEY_ROOT_KEY=your_unkey_root_key
```

### Installation

Install the Unkey Next.js SDK:

```bash
npm install @unkey/nextjs
```

## Protecting API Routes

There are two ways to protect API routes with Unkey:

### 1. Using the `createUnkeyHandler` Utility

The `createUnkeyHandler` utility function is a higher-order function that wraps your API route handlers with Unkey authentication. This is the recommended approach for most API routes.

```typescript
import { NextRequest } from "next/server";
import { createUnkeyHandler } from "@/lib/utils/unkey";
import { formatApiResponse, handleApiError } from "@/lib/utils/api";

// Define the GET handler with Unkey protection
export const GET = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      // Your API logic here
      const data = { message: "This is a protected API route" };
      return formatApiResponse(data);
    } catch (error) {
      return handleApiError(error, "Failed to process request");
    }
  },
  {
    // Optional: Specify required permissions
    requiredPermissions: ["read:resource"],
  },
);
```

### 2. Using the `withUnkey` Function Directly

For more complex scenarios, you can use the `withUnkey` function from the Unkey Next.js SDK directly:

```typescript
import { NextResponse } from "next/server";
import { NextRequestWithUnkeyContext, withUnkey } from "@unkey/nextjs";
import { formatApiResponse, handleApiError } from "@/lib/utils/api";

export const GET = withUnkey(
  async (req: NextRequestWithUnkeyContext) => {
    try {
      // Check if the API key is valid
      if (!req.unkey.valid) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      // Check permissions if needed
      if (!req.unkey.permissions?.includes("read:resource")) {
        return NextResponse.json(
          { error: "Insufficient permissions" },
          { status: 403 },
        );
      }

      // Your API logic here
      const data = { message: "This is a protected API route" };
      return formatApiResponse(data);
    } catch (error) {
      return handleApiError(error, "Failed to process request");
    }
  },
  {
    apiId: process.env.UNKEY_API_ID,
  },
);
```

## Permission System

API keys can be assigned permissions when they are created. These permissions control what actions the API key can perform. The `createUnkeyHandler` utility can check if an API key has the required permissions for a specific API route.

### Common Permission Patterns

- `read:resource`: Read-only access to a resource
- `write:resource`: Create or update a resource
- `delete:resource`: Delete a resource
- `admin:resource`: Full access to a resource

Replace `resource` with the specific resource name (e.g., `providers`, `cargo`, `rfqs`).

### Example: Protecting a Resource with Different Permissions

```typescript
// GET - Read access
export const GET = createUnkeyHandler(
  async (request: NextRequest) => {
    // Handler logic
  },
  { requiredPermissions: ["read:resource"] },
);

// POST - Write access
export const POST = createUnkeyHandler(
  async (request: NextRequest) => {
    // Handler logic
  },
  { requiredPermissions: ["write:resource"] },
);

// PUT - Write access
export const PUT = createUnkeyHandler(
  async (request: NextRequest) => {
    // Handler logic
  },
  { requiredPermissions: ["write:resource"] },
);

// DELETE - Delete access
export const DELETE = createUnkeyHandler(
  async (request: NextRequest) => {
    // Handler logic
  },
  { requiredPermissions: ["delete:resource"] },
);
```

### Resource-Specific Permissions

Here are some common permission patterns to use for different resources:

#### Providers

- `read:providers`: Read provider information
- `write:providers`: Create or update providers
- `delete:providers`: Delete providers

#### Cargo

- `read:cargo`: Read cargo type information
- `write:cargo`: Create or update cargo types
- `delete:cargo`: Delete cargo types

#### RFQs

- `read:rfqs`: Read RFQ information
- `write:rfqs`: Create or update RFQs
- `delete:rfqs`: Delete RFQs
- `manage:rfqs`: Manage RFQ providers and bids

#### Equipment

- `read:equipment`: Read equipment information
- `write:equipment`: Create or update equipment
- `delete:equipment`: Delete equipment

## Public API Routes

Some API routes should remain public (not protected by Unkey). These include:

- `/api/v1/keys`: For creating API keys
- `/api/auth/*`: Authentication-related endpoints

For these routes, do not apply the Unkey protection.

## Testing Protected Routes

To test protected API routes, you need to include an API key in the Authorization header:

```bash
curl -X GET "https://your-api.com/api/v1/resource" \
  -H "Authorization: Bearer your_api_key"
```

For local development, you can create a test API key using the `/api/v1/keys` endpoint:

```bash
curl -X POST "http://localhost:3000/api/v1/keys" \
  -H "Content-Type: application/json" \
  -d '{"name":"Test Key","permissions":["read:resource","write:resource"]}'
```

## Troubleshooting

### Common Issues

1. **401 Unauthorized**: The API key is missing or invalid

   - Check that the API key is included in the Authorization header
   - Verify that the API key exists in Unkey

2. **403 Forbidden**: The API key doesn't have the required permissions

   - Check the permissions assigned to the API key
   - Verify that the required permissions are correctly specified in the API route

3. **500 Internal Server Error**: Error verifying the API key
   - Check that the UNKEY_API_ID environment variable is set correctly
   - Verify that the Unkey service is available

### Debugging

To debug Unkey authentication issues, you can add logging to the `onError` callback:

```typescript
export const GET = withUnkey(
  async (req: NextRequestWithUnkeyContext) => {
    // Handler logic
  },
  {
    apiId: process.env.UNKEY_API_ID,
    onError: async (req, res) => {
      console.error(`Unkey error: ${res.code}: ${res.message}`);
      return NextResponse.json(
        { error: "Error verifying API key" },
        { status: 500 },
      );
    },
  },
);
```

## Related Documentation

- [Unkey Migration Guide](../development/migration-guides/unkey-protection.md)
- [API Routes in Next.js](https://nextjs.org/docs/app/building-your-application/routing/route-handlers)
