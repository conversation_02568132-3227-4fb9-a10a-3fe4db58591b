# Code Review Guidelines for Architectural Boundaries

This document provides a comprehensive guide for enforcing architectural boundaries during code reviews, particularly focusing on the patterns that were previously enforced by ESLint rules.

## Table of Contents

1. [Introduction](#introduction)
2. [Architectural Principles](#architectural-principles)
3. [Code Review Checklist](#code-review-checklist)
4. [Common Anti-Patterns](#common-anti-patterns)
5. [Review Process](#review-process)
6. [Tools and Resources](#tools-and-resources)

## Introduction

Code reviews are a critical part of maintaining architectural integrity in our codebase. With the removal of the `eslint-plugin-architecture` plugin, we now rely on manual code reviews to enforce these boundaries. This guide provides a structured approach to reviewing code for architectural compliance.

## Architectural Principles

Our application follows a clear layered architecture:

1. **Services Layer** (`src/lib/services/*.service.ts`)
   - Encapsulates business logic and data access
   - Pure functions independent of UI or routing
   - Should not import from UI or React components

2. **Actions Layer** (`src/lib/actions/*.ts`)
   - Handles server-side operations
   - Calls into services for business logic
   - Handles server-side validation

3. **Hooks Layer** (`src/hooks/*.ts` or `src/lib/hooks/*.ts`)
   - Encapsulates client-side state and data fetching
   - Calls actions, not services directly
   - Uses TanStack Query for data fetching

4. **Components Layer** (`src/components/*` and `src/app/*`)
   - Renders UI
   - Uses hooks for data and state management
   - Should not call services directly

## Code Review Checklist

### 1. Hooks Architecture

When reviewing hooks, check for the following:

- [ ] **Hooks call actions, not services or Supabase directly**
  - No imports from `@/lib/supabase/client` in hook files
  - No imports from `@/lib/services/*` in hook files
  - Actions are properly used as the data source

- [ ] **TanStack Query is used for data fetching**
  - No `useEffect` + `useState` combinations for data fetching
  - `useQuery`, `useMutation`, etc. are used appropriately
  - Query keys are properly structured

- [ ] **Types are imported, not redefined**
  - No duplicate type definitions that already exist in schemas
  - Types are imported from `@/lib/schemas` or from actions
  - No direct imports from `@/types/supabase`

### 2. Service Layer Boundaries

- [ ] **Services don't import from React or UI layers**
  - No imports from `react` or any UI components
  - No imports from `next/navigation` or other routing libraries

- [ ] **Services properly handle database operations**
  - Database logic is encapsulated within services
  - Types are properly defined and exported

### 3. Action Layer Boundaries

- [ ] **Actions call services, not database directly**
  - No direct Supabase client usage in actions
  - Services are called for business logic

- [ ] **Actions validate input data**
  - Zod schemas are used for validation
  - Errors are properly handled and returned

### 4. Component Layer Boundaries

- [ ] **Components use hooks for data fetching**
  - No direct action calls in effect hooks
  - Custom hooks are used to encapsulate data fetching logic

- [ ] **Components don't bypass the hooks layer**
  - No direct imports from services
  - No direct imports from Supabase client

## Common Anti-Patterns

### 1. Direct Database Access in Hooks

❌ **Anti-pattern:**
```typescript
// Hook directly accesses Supabase
import { createClient } from "@/lib/supabase/client";

export function useProviders() {
  const [providers, setProviders] = useState([]);
  
  useEffect(() => {
    const supabase = createClient();
    supabase.from("providers").select("*").then(({ data }) => {
      setProviders(data);
    });
  }, []);
  
  return { providers };
}
```

✅ **Preferred approach:**
```typescript
// Hook uses action via TanStack Query
import { useQuery } from "@tanstack/react-query";
import { getProvidersAction } from "@/lib/actions/provider.actions";

export function useProviders() {
  const { data: providers = [] } = useQuery({
    queryKey: ["providers"],
    queryFn: () => getProvidersAction(),
  });
  
  return { providers };
}
```

### 2. Data Fetching with useEffect

❌ **Anti-pattern:**
```typescript
// Using useEffect for data fetching
export function useCargoTypes() {
  const [cargoTypes, setCargoTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      const result = await getCargoTypesAction();
      setCargoTypes(result.data);
      setLoading(false);
    }
    fetchData();
  }, []);
  
  return { cargoTypes, loading };
}
```

✅ **Preferred approach:**
```typescript
// Using TanStack Query
export function useCargoTypes() {
  const { data: cargoTypes = [], isLoading } = useQuery({
    queryKey: ["cargo-types"],
    queryFn: () => getCargoTypesAction(),
  });
  
  return { cargoTypes, loading: isLoading };
}
```

### 3. Type Duplication

❌ **Anti-pattern:**
```typescript
// Duplicating types in hook files
export type Provider = {
  id: string;
  name: string;
  status: string;
  verified: boolean;
};

export function useProviders() {
  // ...
}
```

✅ **Preferred approach:**
```typescript
// Importing types from actions or schemas
import { type Provider } from "@/lib/schemas";

export function useProviders() {
  // ...
}
```

## Review Process

1. **First Pass: Architecture Boundaries**
   - Focus only on architectural boundaries and layer violations
   - Flag any cross-layer imports that violate the architecture

2. **Second Pass: Implementation Details**
   - Review the actual implementation
   - Check for proper error handling, validation, etc.

3. **Follow-up: Documentation**
   - Ensure any new patterns are documented
   - Update guidelines if necessary

## Tools and Resources

### 1. Manual Verification Commands

Use these commands to help verify architectural boundaries:

```bash
# Find hooks that import Supabase client
grep -r --include="*.ts" "from ['\"]@/lib/supabase/client['\"]" src/hooks/

# Find hooks that import service files
grep -r --include="*.ts" "from ['\"]@/lib/services/" src/hooks/

# Find hooks with useEffect for data fetching
grep -r --include="*.ts" -A 5 "useEffect" src/hooks/ | grep -i "fetch\|load\|get"
```

### 2. Documentation References

- [Architectural Best Practices](./eslint-rules.md)
- [Hooks Integration Guide](./hooks-integration.md)
- [Service Layer Architecture](./service-layer.md)

### 3. Example PRs

Reference these exemplary PRs that follow our architectural boundaries:

- [#123: Refactor cargo hook to use TanStack Query](https://github.com/example/repo/pull/123)
- [#456: Implement new provider actions and hooks](https://github.com/example/repo/pull/456)

## Conclusion

By following these guidelines during code reviews, we can maintain the architectural integrity of our codebase without relying on automated tools. This manual process also encourages deeper understanding of our architecture and better collaboration among team members. 