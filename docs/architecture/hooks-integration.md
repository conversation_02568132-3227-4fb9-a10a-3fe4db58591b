# Hooks Integration with Service Layer Architecture

This document provides comprehensive guidelines for integrating React hooks with our service layer architecture, addressing common issues like architectural violations and type duplication.

## Table of Contents

1. [Architecture Review](#architecture-review)
2. [How Hooks Fit Into This Architecture](#how-hooks-fit-into-this-architecture)
3. [Benefits of Using Hooks](#benefits-of-using-hooks)
4. [Common Issues to Address](#common-issues-to-address)
5. [Implementation Guidelines](#implementation-guidelines)
6. [Type Management](#type-management)
7. [Migration Guide](#migration-guide)
8. [Examples](#examples)

## Architecture Review

Our application follows a service-layer architecture with:

1. **Services** (`src/lib/services/*.service.ts`):

   - Encapsulate business logic and data access
   - Must not know about HTTP, route parameters, or React
   - Define and export database-related types

2. **Actions** (`src/lib/actions/*.ts`):

   - Call into your service layer for business logic
   - Must not call API routes directly
   - Re-export types from services or define action-specific types

3. **API Routes** (`src/app/api/**/route.ts`):

   - Server-side only
   - Import and call services to handle requests
   - Must not import or call client-side actions

4. **UI Components** (`src/app/**` & `src/components/ui/**`):
   - Invoke actions (via form submissions, button handlers, etc.)
   - Must not call services or API routes directly

## How Hooks Fit Into This Architecture

Custom hooks in React are primarily a client-side pattern for encapsulating and reusing stateful logic. They serve as an additional layer between UI components and actions:

```
+---------------------+       +---------------------+       +--------------------------+        +-------------------------------+
| UI Layer            |       | Hooks Layer         |       | Action Layer             |        | Service Layer                 |
| (React/Next.js)     | ----> | (Custom Hooks)      | ----> | (Server Actions/API)     | -----> | (Business Logic/Data Access)  |
+---------------------+       +---------------------+       +--------------------------+        +-------------------------------+
```

## Benefits of Using Hooks

1. **Separation of Concerns**: Hooks can maintain the separation between UI components and business logic by:

   - Encapsulating data fetching logic that calls actions (not services directly)
   - Managing UI-specific state (like form state, loading states)
   - Handling client-side effects related to data fetching

2. **Maintaining Architectural Boundaries**: Well-designed hooks can reinforce your architecture by:

   - Ensuring components only interact with actions, not services
   - Providing a consistent interface for components to access data
   - Centralizing error handling and loading states

3. **Reducing Boilerplate**: Hooks can reduce repetitive code in components by:

   - Abstracting common patterns for calling actions
   - Managing loading/error states consistently
   - Handling data transformation and caching

4. **Improved Testability**: Hooks can make components more testable by:
   - Isolating side effects
   - Making dependencies explicit
   - Allowing for easier mocking in tests

## Common Issues to Address

### 1. Architectural Violations

Many hooks in our codebase currently violate architectural boundaries by:

- Directly using Supabase client instead of calling actions
- Bypassing the action layer entirely
- Implementing business logic that belongs in services

For example:

```typescript
// ANTI-PATTERN: Hook directly uses Supabase client
export function useCargoTypes() {
  useEffect(() => {
    async function fetchCargoTypes() {
      const supabase = createClient();
      const { data } = await supabase.from("cargo_types").select("*");
      // ...
    }
    fetchCargoTypes();
  }, []);
}
```

### 2. Type Duplication

Many hooks define their own types that duplicate definitions from:

- Supabase generated types (`src/lib/supabase/types.ts`)
- Zod schemas (`src/lib/schemas/*.ts`)
- Service layer types

For example:

```typescript
// ANTI-PATTERN: Duplicating types in hooks
export type Provider = {
  id: string;
  name: string;
  status: string;
  verified: boolean;
  // ...
};
```

### 3. Inconsistent Data Fetching Patterns

Our codebase uses a mix of:

- Direct Supabase queries in hooks
- TanStack Query with actions
- useState + useEffect for data fetching

This inconsistency makes the codebase harder to maintain and understand.

## Implementation Guidelines

### DO:

✅ **Create hooks that use actions, not services or Supabase directly**

```typescript
// Good: Hook uses actions via TanStack Query
function useProviders(params) {
  return useQuery({
    queryKey: ["providers", params],
    queryFn: () => getProvidersAction(params),
  });
}
```

✅ **Keep hooks focused on UI concerns**

- Data fetching and caching (via TanStack Query)
- UI state management (via Zustand)
- Form handling
- Component lifecycle

✅ **Maintain clear boundaries**

- Hooks should not contain business logic
- Business logic belongs in services
- Actions should be thin wrappers around services

✅ **Use TanStack Query for all data fetching**

- Consistent pattern for data fetching
- Built-in caching, loading states, and error handling
- Easier to maintain and understand

✅ **Import types from services or schemas**

- Avoid duplicating type definitions
- Use types from Supabase generated types or schemas
- Re-export types from actions if needed

### DON'T:

❌ **Don't bypass the action layer**

```typescript
// Bad: Hook bypasses actions and calls services directly
function useProviders() {
  return useQuery({
    queryKey: ["providers"],
    queryFn: () => providerService.fetchProviders(),
  });
}
```

❌ **Don't use Supabase client directly in hooks**

```typescript
// Bad: Hook uses Supabase client directly
function useProviders() {
  const [providers, setProviders] = useState([]);
  useEffect(() => {
    const supabase = createClient();
    supabase
      .from("providers")
      .select("*")
      .then(({ data }) => {
        setProviders(data);
      });
  }, []);
}
```

❌ **Don't implement business logic in hooks**

- Business logic should remain in the service layer
- Hooks should focus on UI concerns and data fetching

❌ **Don't define duplicate types in hooks**

- Import types from services, actions, or schemas
- Use Supabase generated types for database entities

## Type Management

### Type Flow in the Architecture

Types should flow through the architecture as follows:

1. **Database Types**: Generated by Supabase CLI in `src/lib/supabase/types.ts`
2. **Service Layer Types**: Import and use database types, export service-specific types
3. **Action Layer Types**: Import and use service types, export action-specific types
4. **Hook Layer Types**: Import and use action types, export hook-specific types
5. **Component Layer Types**: Import and use hook types

### Best Practices for Type Management

1. **Use Supabase Generated Types for Database Entities**

```typescript
// In services
import { Tables } from "@/types/supabase";
type Provider = Tables<"providers">;
```

2. **Use Zod Schemas for Validation and Type Inference**

```typescript
// In schemas
export const CreateProviderSchema = z.object({...});
export type CreateProviderInput = z.infer<typeof CreateProviderSchema>;
```

3. **Re-export Types Through Layers**

```typescript
// In actions
import { Provider } from "@/lib/services/provider.service";
export type { Provider };
```

4. **Define Composite Types in a Central Location**

```typescript
// In src/lib/types/rfq.ts
import { Tables } from "@/types/supabase";
export type RFQWithDetails = Tables<"rfqs"> & {
  provider?: Tables<"providers">;
  // ...
};
```

## Migration Guide

To migrate existing hooks to follow these guidelines:

1. **Identify Hooks That Violate Architectural Boundaries**

   - Hooks that use Supabase client directly
   - Hooks that implement business logic

2. **Create or Update Actions**

   - Create actions for data fetching if they don't exist
   - Ensure actions call services for business logic

3. **Refactor Hooks to Use Actions**

   - Replace direct Supabase calls with action calls
   - Use TanStack Query for data fetching

4. **Eliminate Type Duplication**

   - Remove duplicate type definitions from hooks
   - Import types from services, actions, or schemas

5. **Update Components to Use Refactored Hooks**
   - Ensure components use the new hook interfaces
   - Update any type references

## Examples

### Good Example: Data Fetching Hook with TanStack Query

```typescript
// src/lib/hooks/use-providers.ts
import { useQuery } from "@tanstack/react-query";
import { getProvidersAction } from "@/lib/actions/provider.actions";
import type {
  GetProvidersParams,
  Provider,
} from "@/lib/actions/provider.actions";

export function useProviders(params: GetProvidersParams) {
  return useQuery<Provider[]>({
    queryKey: ["providers", params],
    queryFn: () => getProvidersAction(params),
  });
}
```

### Good Example: UI State Hook with Zustand

```typescript
// src/lib/hooks/use-filter-state.ts
import { create } from "zustand";

interface FilterState {
  filters: Record<string, any>;
  setFilter: (key: string, value: any) => void;
  resetFilters: () => void;
}

export const useFilterStore = create<FilterState>((set) => ({
  filters: {},
  setFilter: (key, value) =>
    set((state) => ({
      filters: { ...state.filters, [key]: value },
    })),
  resetFilters: () => set({ filters: {} }),
}));
```

### Good Example: Form Submission Hook

```typescript
// src/lib/hooks/use-provider-form.ts
import { useState } from "react";
import { createProviderAction } from "@/lib/actions/provider.actions";
import type {
  CreateProviderInput,
  Provider,
} from "@/lib/actions/provider.actions";

export function useProviderForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const submitProvider = async (
    data: CreateProviderInput,
  ): Promise<Provider | null> => {
    setIsSubmitting(true);
    setError(null);

    try {
      const result = await createProviderAction(data);

      if (!result.success) {
        setError(result.error || "Failed to create provider");
        return null;
      }

      return result.data;
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "An unknown error occurred",
      );
      return null;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    submitProvider,
    isSubmitting,
    error,
  };
}
```

### Example of Refactoring a Non-Compliant Hook

Before:

```typescript
// src/hooks/use-cargo-types.ts
"use client";

import { useState, useEffect } from "react";
import { createClient } from "@/lib/supabase/client";
import { Tables } from "@/types/supabase";

type CargoType = Tables<"cargo_types">;

export function useCargoTypes() {
  const [cargoTypes, setCargoTypes] = useState<CargoType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    async function fetchCargoTypes() {
      try {
        setLoading(true);
        const supabase = createClient();

        const { data, error } = await supabase
          .from("cargo_types")
          .select("*")
          .order("name");

        if (error) {
          throw error;
        }

        setCargoTypes(data || []);
      } catch (err) {
        console.error("Error fetching cargo types:", err);
        setError(
          err instanceof Error ? err : new Error("Failed to fetch cargo types"),
        );
      } finally {
        setLoading(false);
      }
    }

    fetchCargoTypes();
  }, []);

  return { cargoTypes, loading, error };
}
```

After:

```typescript
// src/lib/actions/cargo.actions.ts
"use server";

import { getCargoTypes } from "@/lib/services/cargo.service";
import { Tables } from "@/types/supabase";

// Re-export the type
export type CargoType = Tables<"cargo_types">;

export async function getCargoTypesAction() {
  try {
    const cargoTypes = await getCargoTypes();
    return {
      success: true,
      data: cargoTypes,
    };
  } catch (error) {
    console.error("Error fetching cargo types:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to fetch cargo types",
    };
  }
}

// src/hooks/use-cargo-types.ts
("use client");

import { useQuery } from "@tanstack/react-query";
import { getCargoTypesAction, CargoType } from "@/lib/actions/cargo.actions";

export function useCargoTypes() {
  const {
    data: cargoTypes = [],
    isLoading: loading,
    error,
  } = useQuery<CargoType[]>({
    queryKey: ["cargo-types"],
    queryFn: async () => {
      const result = await getCargoTypesAction();
      if (!result.success) {
        throw new Error(result.error || "Failed to fetch cargo types");
      }
      return result.data;
    },
  });

  return {
    cargoTypes,
    loading,
    error:
      error instanceof Error
        ? error
        : error
          ? new Error("Unknown error")
          : null,
  };
}
```

## Enforcing Architectural Boundaries

To help enforce the architectural boundaries described in this document, we rely on code reviews and best practices. These guidelines ensure our code maintains the proper separation of concerns and follows our architectural patterns.

### Architectural Guidelines

Instead of automated ESLint rules, we now enforce these principles through code reviews:

1. **Hooks Should Call Actions**: Hooks should not directly import services or Supabase client
2. **Use TanStack Query for Data Fetching**: Prefer TanStack Query over useEffect for data fetching operations
3. **Avoid Type Duplication**: Import types from schemas, actions, or Supabase types rather than redefining them

For more details on these guidelines, see [eslint-rules.md](./eslint-rules.md), which has been updated to serve as a best practices guide.

### Code Review Checklist

When reviewing hook-related code, check for:

- ✓ No direct imports of Supabase client in hook files
- ✓ No direct imports of service files in hook files
- ✓ TanStack Query is used for data fetching rather than useEffect
- ✓ Types are imported from appropriate sources rather than duplicated

### Documentation and Training

To help developers understand and follow these patterns:

1. Read the architectural best practices documentation
2. Review the examples provided in this document
3. When in doubt, consult with more experienced team members

## Conclusion

Custom hooks are a good fit for our service layer architecture as long as they respect the boundaries between layers. They provide a clean interface for components to interact with the action layer while managing UI-specific concerns like loading states, caching, and local state.

When implemented correctly, hooks can enhance our architecture by:

- Reducing boilerplate in components
- Ensuring consistent patterns for data fetching and state management
- Maintaining the separation of concerns
- Improving code reusability and testability

By addressing the common issues of architectural violations and type duplication through code reviews and documentation, we can create a more maintainable and consistent codebase.
