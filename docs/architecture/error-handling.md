# Error Handling in Steelflow

This document outlines the standard error handling practices for the Steelflow application using Next.js 15 and the App Router.

## Table of Contents

1. [Error Handling Architecture](#error-handling-architecture)
2. [Error Types](#error-types)
3. [Server-Side Error Handling](#server-side-error-handling)
4. [Client-Side Error Handling](#client-side-error-handling)
5. [Error Boundaries](#error-boundaries)
6. [Not Found Pages](#not-found-pages)
7. [API Error Handling](#api-error-handling)
8. [Form Validation Errors](#form-validation-errors)
9. [Toast Notifications](#toast-notifications)
10. [Logging and Monitoring](#logging-and-monitoring)
11. [Best Practices](#best-practices)

## Error Handling Architecture

Steelflow implements a comprehensive error handling strategy across all layers of the application:

```
UI Layer → Server Actions → Service Layer → Database
           API Routes   ↗
```

Each layer has specific error handling responsibilities:

1. **Service Layer**: Throws typed errors with descriptive messages
2. **API Routes**: Catches errors and returns appropriate HTTP responses
3. **Server Actions**: Catches errors and returns structured error objects
4. **UI Layer**: Displays errors to users and provides recovery options

## Error Types

Steelflow uses a set of custom error types to represent different error scenarios:

```typescript
// src/lib/utils/errors.ts
export class AppError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "AppError";
  }
}

export class ValidationError extends AppError {
  constructor(
    message: string,
    public errors?: Record<string, string[]>,
  ) {
    super(message);
    this.name = "ValidationError";
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string, id?: string) {
    super(`${resource}${id ? ` with ID ${id}` : ""} not found`);
    this.name = "NotFoundError";
  }
}

export class AuthorizationError extends AppError {
  constructor(
    message: string = "You are not authorized to perform this action",
  ) {
    super(message);
    this.name = "AuthorizationError";
  }
}

export class DatabaseError extends AppError {
  constructor(
    message: string,
    public originalError?: Error,
  ) {
    super(message);
    this.name = "DatabaseError";
  }
}
```

## Server-Side Error Handling

### Service Layer

The service layer should throw appropriate errors when operations fail:

```typescript
// src/lib/services/provider.service.ts
import { NotFoundError, DatabaseError } from "@/lib/utils/errors";

export async function fetchProvider(id: string) {
  const supabase = createAdminClient();
  const { data, error } = await supabase
    .from("providers")
    .select("*")
    .eq("id", id)
    .single();

  if (error) {
    throw new DatabaseError(
      `Failed to fetch provider: ${error.message}`,
      error,
    );
  }

  if (!data) {
    throw new NotFoundError("Provider", id);
  }

  return data;
}
```

### API Routes

API routes should catch errors and return appropriate HTTP responses:

```typescript
// src/app/api/providers/[id]/route.ts
import { NextRequest, NextResponse } from "next/server";
import { fetchProvider } from "@/lib/services/provider.service";
import { handleApiError } from "@/lib/utils/api";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const provider = await fetchProvider(params.id);
    return NextResponse.json({ data: provider });
  } catch (error) {
    return handleApiError(error, "Failed to fetch provider");
  }
}
```

The `handleApiError` utility function maps error types to appropriate HTTP status codes:

```typescript
// src/lib/utils/api.ts
import { NextResponse } from "next/server";
import {
  AppError,
  ValidationError,
  NotFoundError,
  AuthorizationError,
  DatabaseError,
} from "@/lib/utils/errors";

export function handleApiError(
  error: unknown,
  defaultMessage: string = "An error occurred",
) {
  console.error(error);

  if (error instanceof ValidationError) {
    return NextResponse.json(
      { error: error.message, errors: error.errors },
      { status: 400 },
    );
  }

  if (error instanceof NotFoundError) {
    return NextResponse.json({ error: error.message }, { status: 404 });
  }

  if (error instanceof AuthorizationError) {
    return NextResponse.json({ error: error.message }, { status: 403 });
  }

  if (error instanceof DatabaseError) {
    return NextResponse.json(
      { error: "Database operation failed" },
      { status: 500 },
    );
  }

  if (error instanceof AppError) {
    return NextResponse.json({ error: error.message }, { status: 400 });
  }

  return NextResponse.json({ error: defaultMessage }, { status: 500 });
}
```

### Server Actions

Server actions should catch errors and return structured error objects:

```typescript
// src/lib/actions/provider.ts
"use server";

import { revalidatePath } from "next/cache";
import { fetchProvider } from "@/lib/services/provider.service";
import {
  AppError,
  ValidationError,
  NotFoundError,
  AuthorizationError,
  DatabaseError,
} from "@/lib/utils/errors";

export async function getProvider(id: string) {
  try {
    const provider = await fetchProvider(id);
    return { success: true, data: provider };
  } catch (error) {
    if (error instanceof NotFoundError) {
      return { success: false, error: error.message, code: "NOT_FOUND" };
    }

    if (error instanceof ValidationError) {
      return {
        success: false,
        error: error.message,
        code: "VALIDATION_ERROR",
        errors: error.errors,
      };
    }

    if (error instanceof AuthorizationError) {
      return { success: false, error: error.message, code: "UNAUTHORIZED" };
    }

    console.error("Error in getProvider action:", error);
    return {
      success: false,
      error: "Failed to fetch provider",
      code: "UNKNOWN_ERROR",
    };
  }
}
```

## Client-Side Error Handling

### Error Boundaries

Use React Error Boundary components to catch and display errors in the UI:

```tsx
// src/app/dashboard/providers/[id]/error.tsx
"use client";

import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { AlertCircle } from "lucide-react";

export default function ProviderError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error);
  }, [error]);

  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] p-6">
      <div className="flex items-center gap-2 text-destructive mb-4">
        <AlertCircle className="h-6 w-6" />
        <h2 className="text-xl font-semibold">Something went wrong!</h2>
      </div>
      <p className="text-muted-foreground mb-6 text-center max-w-md">
        {error.message ||
          "An error occurred while loading the provider details."}
      </p>
      <Button onClick={reset}>Try again</Button>
    </div>
  );
}
```

### Form Validation Errors

Display form validation errors using the `useFormState` hook:

```tsx
"use client";

import { useFormState } from "react-dom";
import { createProvider } from "@/lib/actions/provider";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";

const initialState = {
  success: false,
  error: null,
  errors: {},
};

export function ProviderForm() {
  const [state, formAction] = useFormState(createProvider, initialState);

  return (
    <form action={formAction}>
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="name">
            Name <span className="text-destructive">*</span>
          </Label>
          <Input id="name" name="name" required />
          {state.errors?.name && (
            <p className="text-sm text-destructive">{state.errors.name}</p>
          )}
        </div>

        {/* More form fields */}

        {state.error && (
          <div className="bg-destructive/10 p-3 rounded-md text-destructive text-sm">
            {state.error}
          </div>
        )}

        <Button type="submit">Create Provider</Button>
      </div>
    </form>
  );
}
```

## Best Practices

1. **Use typed errors**: Create custom error classes for different error scenarios
2. **Centralize error handling**: Use utility functions for consistent error handling
3. **Provide helpful error messages**: Error messages should be clear and actionable
4. **Log errors**: Log errors for debugging and monitoring
5. **Handle errors at the appropriate level**: Catch errors at the level where they can be handled best
6. **Provide recovery options**: Allow users to retry operations or navigate away from error states
7. **Use toast notifications**: Display transient errors using toast notifications
8. **Implement error boundaries**: Catch and display UI errors using error boundary components
9. **Validate input data**: Prevent errors by validating input data before processing
10. **Handle network errors**: Account for network failures in client-side code

## Related Documentation

- [Service Layer Architecture](./service-layer.md)
- [API Protection](./api-protection.md)
- [Form Validation](../development/form-validation.md)
