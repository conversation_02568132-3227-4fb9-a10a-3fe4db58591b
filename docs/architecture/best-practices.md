# Architectural Best Practices

This document provides comprehensive guidelines for architectural patterns and best practices in our codebase. It serves as the primary reference for maintaining clean architectural boundaries in the absence of automated enforcement via ESLint.

## Table of Contents

1. [Introduction](#introduction)
2. [Architectural Overview](#architectural-overview)
3. [Layer Interaction Guidelines](#layer-interaction-guidelines)
   - [Service Layer](#service-layer)
   - [Action Layer](#action-layer)
   - [Hooks Layer](#hooks-layer)
   - [Component Layer](#component-layer)
4. [Naming Conventions](#naming-conventions)
5. [Type Management](#type-management)
6. [Common Anti-Patterns](#common-anti-patterns)
7. [Enforcement Mechanisms](#enforcement-mechanisms)
8. [Migration Guides](#migration-guides)

## Introduction

This guide has been developed to ensure architectural consistency across our codebase. Previously, some of these patterns were enforced via the ESLint plugin `eslint-plugin-architecture`, but we've now migrated to a documentation and code review-based approach that provides greater flexibility while still maintaining architectural integrity.

## Architectural Overview

Our application follows a layered architecture pattern with clear boundaries between each layer:

### 1. Service Layer (`src/lib/services/*.service.ts`)

Services encapsulate business logic and data access. They:
- Are pure functions without UI or routing dependencies
- Handle direct database operations
- Export database-related types
- Contain core business logic

### 2. Action Layer (`src/lib/actions/*.ts`)

Actions bridge the server and client divide. They:
- Call into services for business logic
- Handle server-side validation
- Re-export types from services or define action-specific types
- Are callable from client components via Server Actions

### 3. Hooks Layer (`src/hooks/*.ts` or `src/lib/hooks/*.ts`)

Hooks provide UI-specific state management and data access. They:
- Call actions (not services or database directly)
- Encapsulate data fetching using TanStack Query
- Manage UI-specific state and side effects
- Import and use types from actions or schemas

### 4. Component Layer (`src/components/*` and `src/app/*`)

Components focus on UI rendering and user interaction. They:
- Use hooks for data and state management
- Handle user inputs and events
- Focus on presentation concerns
- Should never access services or database directly

## Layer Interaction Guidelines

### Service Layer

**DO:**
- ✅ Keep services focused on business logic
- ✅ Use dependency injection for external dependencies
- ✅ Return data rather than UI components
- ✅ Properly handle errors and edge cases
- ✅ Define and export database-related types

**DON'T:**
- ❌ Import UI components or React
- ❌ Import routing libraries like Next.js Router
- ❌ Reference UI-specific concerns like screen size
- ❌ Handle HTTP request/response directly

### Action Layer

**DO:**
- ✅ Call services for business logic
- ✅ Validate input with Zod schemas
- ✅ Handle errors and provide meaningful responses
- ✅ Re-export types from services
- ✅ Use the "use server" directive for server actions

**DON'T:**
- ❌ Import Supabase client directly (use services)
- ❌ Implement complex business logic (belongs in services)
- ❌ Return UI components
- ❌ Directly access the database (use services)

### Hooks Layer

**DO:**
- ✅ Call actions, not services directly
- ✅ Use TanStack Query for data fetching
- ✅ Import types from schemas or actions
- ✅ Keep hooks focused on UI-specific concerns
- ✅ Properly handle loading and error states

**DON'T:**
- ❌ Import Supabase client directly
- ❌ Call service methods directly
- ❌ Implement business logic that belongs in services
- ❌ Define types that already exist in schema or action layers
- ❌ Use useState + useEffect for data fetching (use TanStack Query)

### Component Layer

**DO:**
- ✅ Use hooks for data fetching and state
- ✅ Focus on UI rendering and user interaction
- ✅ Pass props to child components
- ✅ Handle UI-specific events

**DON'T:**
- ❌ Call services directly
- ❌ Import Supabase client
- ❌ Implement complex business logic
- ❌ Fetch data directly (use hooks)

## Naming Conventions

Consistent naming helps reinforce architectural boundaries:

1. **Services**
   - Files: `[name].service.ts`
   - Functions: `getUsers`, `createUser`, etc.
   - Types: `User`, `UserWithProfile`, etc.

2. **Actions**
   - Files: `[name].actions.ts` or `[name].ts` in `/actions` directory
   - Functions: `getUsersAction`, `createUserAction`, etc.
   - Types: Re-export from services or `ActionResult<T>`

3. **Hooks**
   - Files: `use-[name].ts`
   - Functions: `useUsers`, `useUserForm`, etc.
   - Always start with "use" prefix

4. **Components**
   - Files: PascalCase.tsx
   - Component names: PascalCase
   - Props interfaces: `ComponentNameProps`

## Type Management

### Type Flow in the Architecture

Types should flow through the architecture in this order:

1. **Database Types**: Generated by Supabase CLI in `src/lib/supabase/types.ts`
2. **Service Layer Types**: Import and use database types, export service-specific types
3. **Action Layer Types**: Import and use service types, export action-specific types
4. **Hook Layer Types**: Import and use action types, export hook-specific types
5. **Component Layer Types**: Import and use hook types

### Best Practices for Type Management

1. **Use Central Schema Imports**

```typescript
// ✅ CORRECT: Import from central schema index
import { type Provider, type Country } from "@/lib/schemas";

// ❌ INCORRECT: Import from individual schema files
import { type Provider } from "@/lib/schemas/provider.schema";
```

2. **Use Zod Schemas for Validation**

```typescript
// ✅ CORRECT: Validate with schema
import { CreateProviderSchema, type Provider } from "@/lib/schemas";

const validatedData = CreateProviderSchema.parse(formData);
const newProvider = await createProvider(validatedData);

// ❌ INCORRECT: Type assertion without validation
const newProvider = await createProvider(formData as Provider);
```

3. **Avoid Duplicate Types**

```typescript
// ❌ INCORRECT: Duplicating types in hooks
export type Provider = {
  id: string;
  name: string;
  status: string;
  verified: boolean;
};

// ✅ CORRECT: Import from schema or actions
import { type Provider } from "@/lib/schemas";
```

## Common Anti-Patterns

### 1. Direct Database Access in Hooks

❌ **Anti-pattern:**
```typescript
// Hook directly accesses Supabase
import { createClient } from "@/lib/supabase/client";

export function useProviders() {
  const [providers, setProviders] = useState([]);
  
  useEffect(() => {
    const supabase = createClient();
    supabase.from("providers").select("*").then(({ data }) => {
      setProviders(data);
    });
  }, []);
  
  return { providers };
}
```

✅ **Preferred approach:**
```typescript
// Hook uses action via TanStack Query
import { useQuery } from "@tanstack/react-query";
import { getProvidersAction } from "@/lib/actions/provider.actions";

export function useProviders() {
  const { data: providers = [] } = useQuery({
    queryKey: ["providers"],
    queryFn: () => getProvidersAction(),
  });
  
  return { providers };
}
```

### 2. Data Fetching with useEffect

❌ **Anti-pattern:**
```typescript
// Using useEffect for data fetching
export function useCargoTypes() {
  const [cargoTypes, setCargoTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      const result = await getCargoTypesAction();
      setCargoTypes(result.data);
      setLoading(false);
    }
    fetchData();
  }, []);
  
  return { cargoTypes, loading };
}
```

✅ **Preferred approach:**
```typescript
// Using TanStack Query
export function useCargoTypes() {
  const { data: cargoTypes = [], isLoading } = useQuery({
    queryKey: ["cargo-types"],
    queryFn: () => getCargoTypesAction(),
  });
  
  return { cargoTypes, loading: isLoading };
}
```

### 3. Type Duplication

❌ **Anti-pattern:**
```typescript
// Duplicating types in hook files
export type Provider = {
  id: string;
  name: string;
  status: string;
  verified: boolean;
};

export function useProviders() {
  // ...
}
```

✅ **Preferred approach:**
```typescript
// Importing types from actions or schemas
import { type Provider } from "@/lib/schemas";

export function useProviders() {
  // ...
}
```

### 4. Component-to-Service Direct Calls

❌ **Anti-pattern:**
```typescript
// Component directly calls service
import { fetchUsers } from "@/lib/services/user.service";

export function UserList() {
  const [users, setUsers] = useState([]);
  
  useEffect(() => {
    async function load() {
      const data = await fetchUsers();
      setUsers(data);
    }
    load();
  }, []);
  
  return (/* ... */);
}
```

✅ **Preferred approach:**
```typescript
// Component uses hook, which calls action
import { useUsers } from "@/hooks/use-users";

export function UserList() {
  const { users, isLoading } = useUsers();
  
  return (/* ... */);
}
```

## Enforcement Mechanisms

### 1. Code Reviews

The primary method of enforcing architectural boundaries is through code reviews. Please refer to [Code Review Guidelines](./code-review-guidelines.md) for details.

#### Key Review Checklist Items:

- ✓ Hooks only call actions, not services or Supabase directly
- ✓ Data fetching uses TanStack Query instead of useEffect
- ✓ Types are imported from appropriate sources, not duplicated
- ✓ Services don't import UI components or routing libraries
- ✓ Components use hooks for data access

### 2. Documentation and Education

Maintaining architectural boundaries requires continuous education:

- Reference this document in PR templates
- Include architecture discussions in team meetings
- Provide examples of correct implementations
- Document reasons behind architectural decisions

### 3. Optional Runtime Checks

For critical architectural boundaries, we can implement runtime checks in development mode. These checks can:

- Verify correct layer access
- Detect unauthorized database calls
- Warn about potential performance issues
- See [Runtime Checks](./runtime-checks.md) for implementation details

## Migration Guides

When migrating code to follow our architectural guidelines, refer to the following resources:

- [Hooks Integration Guide](./hooks-integration.md) - For refactoring hooks to follow architectural patterns
- [Supabase Types Refactoring Guide](../development/migration-guides/supabase-types-refactoring-guide.md) - For using proper types instead of direct Supabase imports

## Conclusion

By following these architectural guidelines, we can maintain a clean, maintainable codebase with clear separation of concerns. While we no longer use automated enforcement via ESLint, the combination of clear documentation, code reviews, and developer education can achieve the same goals while providing more flexibility for edge cases. 