# Architectural Best Practices (Formerly ESLint Rules)

This document describes the architectural boundaries in our codebase, particularly for hooks integration with our service layer architecture. These were previously enforced with ESLint rules but are now maintained through code reviews and best practices.

## Table of Contents

1. [Overview](#overview)
2. [Architectural Guidelines](#architectural-guidelines)
3. [Code Review Checklist](#code-review-checklist)
4. [Troubleshooting](#troubleshooting)

## Overview

Our architectural boundaries are described in [hooks-integration.md](./hooks-integration.md). These guidelines help ensure that:

1. Hooks only call actions, not services or Supabase directly
2. Hooks use TanStack Query for data fetching instead of useEffect
3. Hooks don't duplicate types from schemas or Supabase types

## Architectural Guidelines

### Hooks Should Call Actions, Not Services

**Guideline**: Hooks should only call actions, not services or Supabase directly.

**What to check**:

- Ensure there are no direct imports of Supabase client in hook files
- Ensure there are no direct imports of service files in hook files

**Anti-pattern**:

```typescript
// BAD: Hook directly imports Supabase client
import { createClient } from "@/lib/supabase/client";

export function useProviders() {
  const [providers, setProviders] = useState([]);

  useEffect(() => {
    const supabase = createClient();
    // ...
  }, []);
}
```

**Recommended pattern**:

```typescript
// GOOD: Hook calls an action
import { getProvidersAction } from "@/lib/actions/provider.actions";

export function useProviders() {
  const { data: providers } = useQuery({
    queryKey: ["providers"],
    queryFn: () => getProvidersAction(),
  });
}
```

### Hooks Should Use TanStack Query

**Guideline**: Hooks should use TanStack Query for data fetching instead of useEffect.

**What to check**:

- Look for useEffect with async functions inside (likely for data fetching)
- Ensure TanStack Query is used for data fetching operations

**Anti-pattern**:

```typescript
// BAD: Using useEffect for data fetching
export function useProviders() {
  const [providers, setProviders] = useState([]);

  useEffect(() => {
    async function fetchProviders() {
      const result = await getProvidersAction();
      setProviders(result.data);
    }
    fetchProviders();
  }, []);
}
```

**Recommended pattern**:

```typescript
// GOOD: Using TanStack Query for data fetching
export function useProviders() {
  const { data: providers = [] } = useQuery({
    queryKey: ["providers"],
    queryFn: () => getProvidersAction(),
  });
}
```

### Avoid Duplicate Types in Hooks

**Guideline**: Avoid type duplication in hooks by importing types from schemas, actions, or Supabase types.

**What to check**:

- Look for type definitions in hook files
- Ensure the hook imports types from schemas, actions, or Supabase types
- Avoid defining types in the hook that might duplicate existing types

**Anti-pattern**:

```typescript
// BAD: Defining types in hooks that might duplicate schemas
export type Provider = {
  id: string;
  name: string;
  status: string;
  verified: boolean;
};

export function useProviders() {
  // ...
}
```

**Recommended pattern**:

```typescript
// GOOD: Importing types from actions
import { getProvidersAction } from "@/lib/actions/provider.actions";
import type { Provider } from "@/lib/actions/provider.actions";

export function useProviders() {
  // ...
}
```

## Code Review Checklist

When reviewing hook-related code, check for the following:

1. ✓ Hooks only call actions, not services or Supabase directly
2. ✓ Data fetching uses TanStack Query instead of useEffect
3. ✓ Types are imported from appropriate sources, not duplicated
4. ✓ File locations follow appropriate structure (in `/hooks/` or `/lib/hooks/`)

## Troubleshooting

### Common Issues

If you encounter architectural issues in your code:

1. Check that the file organization follows the project structure guidelines
2. Review the recommendations in this document for the specific pattern
3. Consult the broader architecture documentation for guidance

### Getting Help

If you're unsure about architectural decisions:

1. Discuss with senior developers or the architecture team
2. Reference the examples in this document
3. Check existing implementations in the codebase for patterns to follow
