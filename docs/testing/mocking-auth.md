# Mocking Google Authentication in Playwright Tests

This document explains how to effectively mock Google OAuth authentication in Playwright tests for the Steelflow application.

## Table of Contents

1. [Overview](#overview)
2. [Implementation](#implementation)
3. [How It Works](#how-it-works)
4. [Usage Examples](#usage-examples)
5. [Testing Different User Roles](#testing-different-user-roles)
6. [Testing Error Scenarios](#testing-error-scenarios)
7. [Limitations](#limitations)
8. [Troubleshooting](#troubleshooting)

## Overview

Testing authentication flows that use Google OAuth presents several challenges:

1. **External Dependencies**: Real OAuth flows require interaction with Google's servers
2. **User Interaction**: OAuth typically requires user interaction in a popup window
3. **Tokens and Sessions**: Managing OAuth tokens and sessions across tests
4. **Different User Roles**: Testing with different user roles (admin, regular user, etc.)

Our solution uses <PERSON><PERSON>'s network interception capabilities to mock the entire OAuth flow without requiring actual Google authentication.

## Implementation

### 1. Google Auth Mock Utility

The core of our mocking solution is the `google-auth-mock.ts` utility, which provides functions to intercept and mock the Google OAuth flow:

```typescript
// tests/utils/google-auth-mock.ts
import { Page } from "@playwright/test";

export interface MockUserData {
  id: string;
  email: string;
  name: string;
  role: string;
  avatar_url?: string;
}

export interface MockGoogleAuthOptions {
  success?: boolean;
  userData?: MockUserData;
  errorMessage?: string;
}

const DEFAULT_USER_DATA: MockUserData = {
  id: "mock-user-id",
  email: "<EMAIL>",
  name: "Mock User",
  role: "user",
  avatar_url: "https://example.com/avatar.png",
};

export async function mockGoogleAuth(
  page: Page,
  options: MockGoogleAuthOptions = {},
) {
  const {
    success = true,
    userData = DEFAULT_USER_DATA,
    errorMessage = "Authentication failed",
  } = options;

  // Intercept the OAuth request
  await page.route("**/auth/signInWithOAuth**", async (route) => {
    if (success) {
      // Mock successful OAuth initiation
      await route.fulfill({
        status: 302,
        headers: {
          Location: "/auth/callback?code=mock-auth-code&state=mock-state",
        },
      });
    } else {
      // Mock failed OAuth initiation
      await route.fulfill({
        status: 302,
        headers: {
          Location:
            "/auth/callback?error=access_denied&error_description=" +
            encodeURIComponent(errorMessage),
        },
      });
    }
  });

  // Intercept the callback request
  await page.route("**/auth/callback**", async (route) => {
    if (success) {
      // Mock successful callback
      await route.fulfill({
        status: 302,
        headers: {
          Location: "/dashboard",
          "Set-Cookie": "supabase-auth-token=mock-token; Path=/; HttpOnly",
        },
      });
    } else {
      // Mock failed callback
      await route.fulfill({
        status: 302,
        headers: {
          Location: "/login?error=" + encodeURIComponent(errorMessage),
        },
      });
    }
  });

  // Intercept user data requests
  await page.route("**/api/user", async (route) => {
    if (success) {
      // Mock successful user data response
      await route.fulfill({
        status: 200,
        body: JSON.stringify({ data: userData }),
      });
    } else {
      // Mock failed user data response
      await route.fulfill({
        status: 401,
        body: JSON.stringify({ error: errorMessage }),
      });
    }
  });

  // Intercept session requests
  await page.route("**/rest/v1/auth/session**", async (route) => {
    if (success) {
      // Mock successful session response
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          access_token: "mock-access-token",
          refresh_token: "mock-refresh-token",
          expires_in: 3600,
          user: {
            id: userData.id,
            email: userData.email,
            user_metadata: {
              name: userData.name,
              avatar_url: userData.avatar_url,
            },
          },
        }),
      });
    } else {
      // Mock failed session response
      await route.fulfill({
        status: 401,
        body: JSON.stringify({ error: errorMessage }),
      });
    }
  });
}

// Convenience function for mocking admin authentication
export async function mockAdminAuth(page: Page) {
  await mockGoogleAuth(page, {
    success: true,
    userData: {
      id: "admin-user-id",
      email: "<EMAIL>",
      name: "Admin User",
      role: "admin",
      avatar_url: "https://example.com/admin-avatar.png",
    },
  });
}

// Convenience function for mocking regular user authentication
export async function mockUserAuth(page: Page) {
  await mockGoogleAuth(page, {
    success: true,
    userData: {
      id: "regular-user-id",
      email: "<EMAIL>",
      name: "Regular User",
      role: "user",
      avatar_url: "https://example.com/user-avatar.png",
    },
  });
}

// Convenience function for mocking failed authentication
export async function mockFailedAuth(
  page: Page,
  errorMessage: string = "Authentication failed",
) {
  await mockGoogleAuth(page, {
    success: false,
    errorMessage,
  });
}
```

### 2. Auth Test Fixture

Create a test fixture to make it easy to use the mock authentication in tests:

```typescript
// tests/fixtures/auth-fixture.ts
import { test as base } from "@playwright/test";
import {
  mockGoogleAuth,
  mockAdminAuth,
  mockUserAuth,
  mockFailedAuth,
  MockUserData,
} from "../utils/google-auth-mock";

// Define the fixture type
type AuthFixture = {
  mockAuth: (userData?: MockUserData) => Promise<void>;
  mockAdminAuth: () => Promise<void>;
  mockUserAuth: () => Promise<void>;
  mockFailedAuth: (errorMessage?: string) => Promise<void>;
};

// Create a test fixture
export const test = base.extend<AuthFixture>({
  mockAuth: async ({ page }, use) => {
    // Define the mockAuth function
    const mockAuthFn = async (userData?: MockUserData) => {
      await mockGoogleAuth(page, { success: true, userData });
    };

    // Provide the function to the test
    await use(mockAuthFn);
  },

  mockAdminAuth: async ({ page }, use) => {
    // Define the mockAdminAuth function
    const mockAdminAuthFn = async () => {
      await mockAdminAuth(page);
    };

    // Provide the function to the test
    await use(mockAdminAuthFn);
  },

  mockUserAuth: async ({ page }, use) => {
    // Define the mockUserAuth function
    const mockUserAuthFn = async () => {
      await mockUserAuth(page);
    };

    // Provide the function to the test
    await use(mockUserAuthFn);
  },

  mockFailedAuth: async ({ page }, use) => {
    // Define the mockFailedAuth function
    const mockFailedAuthFn = async (errorMessage?: string) => {
      await mockFailedAuth(page, errorMessage);
    };

    // Provide the function to the test
    await use(mockFailedAuthFn);
  },
});

export { expect } from "@playwright/test";
```

## How It Works

### 1. Network Interception

The mocking utility intercepts network requests to the following endpoints:

- `**/auth/signInWithOAuth**`: The initial OAuth request
- `**/auth/callback**`: The OAuth callback endpoint
- `**/api/user`: The endpoint that returns user data
- `**/rest/v1/auth/session**`: The Supabase session endpoint

### 2. Mocking Success Flow

For successful authentication, we:

1. Intercept the OAuth request and respond with a 302 redirect to the callback URL
2. Intercept the callback request and respond with a 302 redirect to the dashboard
3. Intercept user data requests and respond with mock user data
4. Intercept session requests and respond with a mock session

### 3. Mocking Error Flow

For failed authentication, we:

1. Intercept the OAuth request and respond with a 302 redirect to the callback URL with an error
2. Intercept the callback request and respond with a 302 redirect to the login page with an error
3. Intercept user data requests and respond with a 401 error
4. Intercept session requests and respond with a 401 error

## Usage Examples

### Basic Authentication Test

```typescript
// tests/auth/login.spec.ts
import { test, expect } from "../fixtures/auth-fixture";
import { LoginPage } from "../page-objects/login-page";
import { DashboardPage } from "../page-objects/dashboard-page";

test.describe("Login", () => {
  test("should log in with Google", async ({ page, mockAuth }) => {
    // Arrange
    const loginPage = new LoginPage(page);
    const dashboardPage = new DashboardPage(page);

    // Set up mock authentication
    await mockAuth({
      id: "user-id",
      email: "<EMAIL>",
      name: "Test User",
      role: "user",
    });

    // Act
    await loginPage.goto();
    await loginPage.loginWithGoogle();

    // Assert
    await expect(dashboardPage.welcomeMessage).toBeVisible();
    await expect(dashboardPage.welcomeMessage).toContainText("Test User");
  });
});
```

### Using Convenience Functions

```typescript
// tests/dashboard/admin-features.spec.ts
import { test, expect } from "../fixtures/auth-fixture";
import { LoginPage } from "../page-objects/login-page";
import { AdminPage } from "../page-objects/admin-page";

test.describe("Admin Features", () => {
  test("should show admin panel for admin users", async ({
    page,
    mockAdminAuth,
  }) => {
    // Arrange
    const loginPage = new LoginPage(page);
    const adminPage = new AdminPage(page);

    // Set up mock admin authentication
    await mockAdminAuth();

    // Act
    await loginPage.goto();
    await loginPage.loginWithGoogle();
    await adminPage.goto();

    // Assert
    await expect(adminPage.adminPanel).toBeVisible();
  });
});
```

## Testing Different User Roles

You can test different user roles by providing different user data:

```typescript
// tests/dashboard/role-based-access.spec.ts
import { test, expect } from "../fixtures/auth-fixture";
import { LoginPage } from "../page-objects/login-page";
import { AdminPage } from "../page-objects/admin-page";

test.describe("Role-Based Access Control", () => {
  test("should show admin panel for admin users", async ({
    page,
    mockAuth,
  }) => {
    // Arrange
    const loginPage = new LoginPage(page);
    const adminPage = new AdminPage(page);

    // Set up mock admin authentication
    await mockAuth({
      id: "admin-user-id",
      email: "<EMAIL>",
      name: "Admin User",
      role: "admin",
    });

    // Act
    await loginPage.goto();
    await loginPage.loginWithGoogle();
    await adminPage.goto();

    // Assert
    await expect(adminPage.adminPanel).toBeVisible();
  });

  test("should hide admin panel for regular users", async ({
    page,
    mockAuth,
  }) => {
    // Arrange
    const loginPage = new LoginPage(page);
    const adminPage = new AdminPage(page);

    // Set up mock regular user authentication
    await mockAuth({
      id: "regular-user-id",
      email: "<EMAIL>",
      name: "Regular User",
      role: "user",
    });

    // Act
    await loginPage.goto();
    await loginPage.loginWithGoogle();
    await adminPage.goto();

    // Assert
    await expect(adminPage.adminPanel).not.toBeVisible();
    await expect(adminPage.accessDeniedMessage).toBeVisible();
  });
});
```

## Testing Error Scenarios

You can test error scenarios by setting `success: false`:

```typescript
// tests/auth/login-errors.spec.ts
import { test, expect } from "../fixtures/auth-fixture";
import { LoginPage } from "../page-objects/login-page";

test.describe("Login Errors", () => {
  test("should show error message when authentication fails", async ({
    page,
    mockFailedAuth,
  }) => {
    // Arrange
    const loginPage = new LoginPage(page);

    // Set up mock failed authentication
    await mockFailedAuth("Access denied by user");

    // Act
    await loginPage.goto();
    await loginPage.loginWithGoogle();

    // Assert
    await expect(loginPage.errorMessage).toBeVisible();
    await expect(loginPage.errorMessage).toContainText("Access denied by user");
  });
});
```

## Limitations

This mocking approach has some limitations:

1. **Visual Differences**: The mocked flow bypasses the Google login UI, so visual elements of the OAuth flow are not tested
2. **Token Validation**: The mock doesn't validate token formats or signatures
3. **Real Google API Changes**: Changes to Google's OAuth API won't be caught by these tests

## Troubleshooting

### Common Issues

1. **Authentication not being mocked**: Ensure the route patterns match your application's actual routes
2. **Redirects not working**: Check that the redirect URLs match your application's expected URLs
3. **User data not being mocked**: Verify that the user data endpoint is correctly intercepted

### Debugging Tips

1. **Enable Playwright debugging**: Run tests with `--debug` flag to see network requests
2. **Log intercepted routes**: Add console logs in the route handlers to verify they're being called
3. **Check route patterns**: Ensure the route patterns match your application's actual routes

## Related Documentation

- [Testing Guidelines](./guidelines.md)
- [Playwright Network Interception](https://playwright.dev/docs/network)
