# Playwright Testing Guidelines

This document outlines the standards, structure, and best practices for writing Playwright tests for the Steelflow application. Following these guidelines will ensure consistent, maintainable, and reliable tests across the codebase.

## Table of Contents

1. [Test Structure](#test-structure)
2. [Code Format and Style](#code-format-and-style)
3. [Base Test Fixtures](#base-test-fixtures)
4. [Page Object Model](#page-object-model)
5. [Test Data Management](#test-data-management)
6. [Authentication Patterns](#authentication-patterns)
7. [Best Practices](#best-practices)
8. [Running Tests](#running-tests)

## Test Structure

### Directory Organization

Tests are organized by feature/page to match the application structure:

```
tests/
├── auth/                  # Authentication-related tests
│   ├── login.spec.ts
│   └── signup.spec.ts
├── landing/               # Landing page tests
│   └── landing.spec.ts
├── dashboard/             # Dashboard tests
│   ├── dashboard.spec.ts
│   ├── rfqs/              # RFQ module tests
│   │   ├── list.spec.ts
│   │   ├── create.spec.ts
│   │   └── details.spec.ts
│   ├── providers/         # Provider module tests
│   │   ├── list.spec.ts
│   │   └── details.spec.ts
│   └── sidebar.spec.ts
├── components/            # Reusable component tests
│   ├── data-table.spec.ts
│   └── form-elements.spec.ts
├── utils/                 # Test utilities
│   ├── auth-utils.ts
│   ├── test-data.ts
│   └── api-utils.ts
└── fixtures/              # Test fixtures
    ├── auth-fixture.ts
    └── mock-data.ts
```

### Test File Naming

- Use `.spec.ts` extension for test files
- Use kebab-case for file names
- Name files after the feature or component being tested

### Test Organization

Each test file should follow this structure:

```typescript
import { test, expect } from "@playwright/test";
// Import page objects, fixtures, and utilities as needed

// Group tests by feature or functionality
test.describe("Feature Name", () => {
  // Setup that applies to all tests in this describe block
  test.beforeEach(async ({ page }) => {
    // Common setup
  });

  // Individual test cases
  test("should do something specific", async ({ page }) => {
    // Test implementation
  });

  // More test cases...
});

// Another feature group if needed
test.describe("Another Feature", () => {
  // More tests...
});
```

## Code Format and Style

### General Guidelines

- Use TypeScript for all test files
- Follow consistent indentation (2 spaces)
- Use async/await for all asynchronous operations
- Add meaningful comments for complex logic
- Keep test cases focused and concise

### Naming Conventions

- **Test Descriptions**: Use clear, descriptive names that explain what is being tested

  - Start with "should" followed by the expected behavior
  - Example: `test('should display error message when login fails')`

- **Variables**: Use descriptive names that indicate purpose

  - Example: `const loginButton = page.getByRole('button', { name: 'Log In' });`

- **Page Objects**: Use PascalCase for page object classes

  - Example: `class LoginPage { ... }`

- **Selectors**: Use semantic selectors when possible
  - Prefer role-based selectors: `page.getByRole('button', { name: 'Submit' })`
  - Use test IDs for elements without semantic roles: `page.getByTestId('user-menu')`

## Base Test Fixtures

Use test fixtures to share common setup and teardown logic across tests:

```typescript
// tests/fixtures/auth-fixture.ts
import { test as base } from "@playwright/test";
import { mockGoogleAuth, MockUserData } from "../utils/google-auth-mock";

// Define the fixture type
type AuthFixture = {
  mockAuth: (userData?: MockUserData) => Promise<void>;
  mockAdminAuth: () => Promise<void>;
};

// Create a test fixture
export const test = base.extend<AuthFixture>({
  mockAuth: async ({ page }, use) => {
    // Define the mockAuth function
    const mockAuthFn = async (userData?: MockUserData) => {
      await mockGoogleAuth(page, { success: true, userData });
    };

    // Provide the function to the test
    await use(mockAuthFn);
  },

  mockAdminAuth: async ({ page }, use) => {
    // Define the mockAdminAuth function
    const mockAdminAuthFn = async () => {
      await mockGoogleAuth(page, {
        success: true,
        userData: {
          id: "admin-user-id",
          email: "<EMAIL>",
          name: "Admin User",
          role: "admin",
        },
      });
    };

    // Provide the function to the test
    await use(mockAdminAuthFn);
  },
});

export { expect } from "@playwright/test";
```

## Page Object Model

Use the Page Object Model pattern to encapsulate page interactions:

```typescript
// tests/page-objects/login-page.ts
import { Page } from "@playwright/test";

export class LoginPage {
  constructor(private page: Page) {}

  // Navigation
  async goto() {
    await this.page.goto("/login");
  }

  // Actions
  async login(email: string, password: string) {
    await this.emailInput.fill(email);
    await this.passwordInput.fill(password);
    await this.loginButton.click();
  }

  async loginWithGoogle() {
    await this.googleLoginButton.click();
  }

  // Elements
  get emailInput() {
    return this.page.getByLabel("Email");
  }

  get passwordInput() {
    return this.page.getByLabel("Password");
  }

  get loginButton() {
    return this.page.getByRole("button", { name: "Log In" });
  }

  get googleLoginButton() {
    return this.page.getByRole("button", { name: "Continue with Google" });
  }

  get errorMessage() {
    return this.page.getByTestId("login-error");
  }
}
```

## Test Data Management

### Test Data Principles

- Tests should be independent and not rely on existing data
- Use test fixtures to generate test data
- Clean up test data after tests complete
- Use unique identifiers to avoid conflicts between tests

### Test Data Utilities

```typescript
// tests/utils/test-data.ts
import { createAdminClient } from "@/lib/supabase/admin";

// Generate a unique identifier
export function generateUniqueId(prefix: string = "test"): string {
  return `${prefix}-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
}

// Create a test provider
export async function createTestProvider(overrides: Partial<Provider> = {}) {
  const supabase = createAdminClient();

  const provider = {
    name: `Test Provider ${generateUniqueId()}`,
    email: `test-${generateUniqueId()}@example.com`,
    ...overrides,
  };

  const { data, error } = await supabase
    .from("providers")
    .insert([provider])
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create test provider: ${error.message}`);
  }

  return data;
}

// Clean up test data
export async function cleanupTestData() {
  const supabase = createAdminClient();

  // Delete test providers
  await supabase.from("providers").delete().like("name", "Test Provider%");

  // Delete other test data...
}
```

## Authentication Patterns

### Mocking Google Authentication

Use the Google Auth Mock utility to simulate Google OAuth authentication:

```typescript
// tests/auth/login.spec.ts
import { test, expect } from "../fixtures/auth-fixture";
import { LoginPage } from "../page-objects/login-page";
import { DashboardPage } from "../page-objects/dashboard-page";

test.describe("Login", () => {
  test("should log in with Google", async ({ page, mockAuth }) => {
    // Arrange
    const loginPage = new LoginPage(page);
    const dashboardPage = new DashboardPage(page);

    // Set up mock authentication
    await mockAuth({
      id: "user-id",
      email: "<EMAIL>",
      name: "Test User",
      role: "user",
    });

    // Act
    await loginPage.goto();
    await loginPage.loginWithGoogle();

    // Assert
    await expect(dashboardPage.welcomeMessage).toBeVisible();
    await expect(dashboardPage.welcomeMessage).toContainText("Test User");
  });
});
```

## Best Practices

### General Best Practices

1. **Keep tests independent**: Each test should be able to run in isolation
2. **Use descriptive test names**: Test names should describe the behavior being tested
3. **Follow the AAA pattern**: Arrange, Act, Assert
4. **Use page objects**: Encapsulate page interactions in page object classes
5. **Use test fixtures**: Share common setup and teardown logic
6. **Clean up test data**: Tests should clean up after themselves
7. **Use semantic selectors**: Prefer role-based selectors over CSS selectors
8. **Add test IDs when needed**: Add data-testid attributes for elements without semantic roles
9. **Handle async operations properly**: Use await for all async operations
10. **Add meaningful assertions**: Assertions should verify the expected behavior

### Performance Best Practices

1. **Reuse browser context**: Use the same browser context for related tests
2. **Minimize page navigation**: Navigate to new pages only when necessary
3. **Use request interception**: Mock API responses when appropriate
4. **Parallelize tests**: Run tests in parallel when possible
5. **Use test sharding**: Split tests across multiple workers

## Running Tests

### Command Line Options

```bash
# Run all tests
pnpm test

# Run specific test file
pnpm exec playwright test tests/auth/login.spec.ts

# Run tests in a specific directory
pnpm exec playwright test tests/dashboard/

# Run tests with specific tag
pnpm exec playwright test --grep @smoke

# Run tests in headed mode (visible browser)
pnpm test:headed

# Run tests in specific browser
pnpm exec playwright test --project=chromium
```

### Debug Mode

```bash
# Run in debug mode
pnpm test:debug

# Run with UI mode
pnpm test:ui
```

### CI/CD Integration

Configure Playwright to run in CI/CD pipelines:

```yaml
# .github/workflows/playwright.yml
name: Playwright Tests
on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      - name: Install Playwright Browsers
        run: pnpm exec playwright install --with-deps
      - name: Run Playwright tests
        run: pnpm test
      - uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30
```

## Related Documentation

- [Mocking Google Auth](./mocking-auth.md)
- [Playwright Official Documentation](https://playwright.dev/docs/intro)
