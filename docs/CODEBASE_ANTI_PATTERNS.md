# Codebase Anti-Patterns and Improvement Plan

This document outlines the anti-patterns identified in our codebase, provides a structured plan to address them, and includes detailed implementation guides with examples. Each issue is categorized by area and prioritized by severity.

## Table of Contents

1. [Priority Levels](#priority-levels)
2. [Architecture Violations](#1-architecture-violations)
3. [React/Next.js Anti-Patterns](#2-reactnextjs-anti-patterns)
4. [TypeScript Issues](#3-typescript-issues)
5. [Performance Bottlenecks](#4-performance-bottlenecks)
6. [Security Concerns](#5-security-concerns)
7. [Code Duplication and DRY Violations](#6-code-duplication-and-dry-violations)
8. [Inconsistent Styling or Component Usage](#7-inconsistent-styling-or-component-usage)
9. [Additional Critical Issues](#8-additional-critical-issues)
10. [Recommended Improvements](#9-recommended-improvements)
11. [Progress Tracking](#progress-tracking)

## Priority Levels

- **Critical**: Must be fixed immediately; affects security, stability, or core functionality
- **High**: Should be fixed in the next sprint; affects performance or maintainability
- **Medium**: Should be addressed soon; affects developer experience or code quality
- **Low**: Can be addressed when convenient; minor inconsistencies or improvements

## 1. Architecture Violations

### 1.1. Direct Database Access in API Routes (Critical)

- **Issue**: API routes directly access the database instead of using the service layer
- **Files Affected**:
  - `src/app/api/v1/email/messages/route.ts`
  - `src/app/api/v1/email/messages/[id]/route.ts`
  - `src/app/api/v1/email/messages/[id]/attachments/route.ts`
  - `src/app/api/v1/email/accounts/route.ts`
  - `src/app/api/v1/email/accounts/[id]/route.ts`
- **Solution**: Refactor API routes to use service functions
- **Status**: � Completed

**Current Pattern (Incorrect):**

```typescript
// src/app/api/v1/providers/route.ts
export async function GET(request: NextRequest) {
  const supabase = createAdminClient();
  const { data, error } = await supabase.from("providers").select("*");

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  return NextResponse.json({ data });
}
```

**Correct Implementation:**

```typescript
// src/app/api/v1/providers/route.ts
import { fetchProviders } from "@/lib/services/provider.service";
import { formatApiResponse, handleApiError } from "@/lib/utils/api";

export const GET = createUnkeyHandler(async (request: NextRequest) => {
  try {
    const searchParams = request.nextUrl.searchParams;
    const params = {
      page: searchParams.get("page")
        ? parseInt(searchParams.get("page") || "1")
        : 1,
      pageSize: searchParams.get("pageSize")
        ? parseInt(searchParams.get("pageSize") || "10")
        : 10,
    };

    const providers = await fetchProviders(params);
    return formatApiResponse(providers);
  } catch (error) {
    return handleApiError(error, "Failed to fetch providers");
  }
});
```

### 1.2. Duplicated Business Logic (High)

- **Issue**: Business logic duplicated between API routes and server actions
- **Files Affected**:
  - API routes and corresponding server actions
- **Solution**: Extract all business logic to service functions
- **Status**: 🔴 Not Started

**Current Pattern (Incorrect):**

```typescript
// In API route
export async function GET() {
  const supabase = createAdminClient();
  const { data, error } = await supabase.from("providers").select("*");
  // Business logic here...
}

// In server action
export async function getProviders() {
  const supabase = createAdminClient();
  const { data, error } = await supabase.from("providers").select("*");
  // Same business logic duplicated here...
}
```

**Correct Implementation:**

```typescript
// src/lib/services/provider.service.ts
export async function fetchProviders(
  params: GetProvidersParams,
): Promise<Provider[]> {
  const supabase = createAdminClient();
  // Implement business logic once
  // ...
}

// src/lib/actions/provider.actions.ts
export async function getProvidersAction(
  params: GetProvidersParams,
): Promise<ActionResponse<Provider[]>> {
  try {
    const providers = await fetchProviders(params);
    return { success: true, data: providers };
  } catch (error) {
    // Error handling
  }
}

// src/app/api/v1/providers/route.ts
export const GET = createUnkeyHandler(async (request: NextRequest) => {
  try {
    const params = parseQueryParams(request);
    const providers = await fetchProviders(params);
    return formatApiResponse(providers);
  } catch (error) {
    return handleApiError(error, "Failed to fetch providers");
  }
});
```

### 1.3. Direct API Calls from UI Components (Critical)

- **Issue**: UI components directly call API endpoints instead of using server actions
- **Files Affected**:
  - Client components making fetch calls to API endpoints
- **Solution**: Use server actions for all data mutations and fetching
- **Status**: 🔴 Not Started

**Current Pattern (Incorrect):**

```typescript
// Client component
const handleSubmit = async () => {
  const response = await fetch("/api/v1/providers", {
    method: "POST",
    body: JSON.stringify(data),
  });
  // ...
};
```

**Correct Implementation:**

```typescript
// src/lib/actions/provider.actions.ts
"use server";

export async function createProviderAction(
  data: CreateProviderInput,
): Promise<ActionResponse<Provider>> {
  try {
    const provider = await createProvider(data);
    revalidatePath("/dashboard/providers");
    return { success: true, data: provider };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to create provider",
    };
  }
}

// Client component
import { createProviderAction } from "@/lib/actions/provider.actions";

const handleSubmit = async () => {
  const result = await createProviderAction(data);
  if (result.success) {
    // Handle success
  } else {
    // Handle error
  }
};
```

## 2. React/Next.js Anti-Patterns

### 2.1. Improper Use of Server vs. Client Components (Critical)

- **Issue**: Mixing server and client concerns or using client components unnecessarily
- **Files Affected**:
  - Components with `"use client"` directive that don't need it
  - Client components trying to use server-only features
- **Solution**: Follow the "server-first" approach and split components properly
- **Status**: 🔴 Not Started

**Current Pattern (Incorrect):**

```typescript
// Unnecessary "use client" directive
"use client";
import { fetchData } from "@/lib/services/data.service"; // Server-side service

export default function Component() {
  // This won't work because fetchData is server-side
  const data = fetchData();
  return <div>{data}</div>;
}
```

**Correct Implementation:**

```typescript
// Parent component (server component)
import { fetchData } from "@/lib/services/data.service";
import { ClientInteractiveComponent } from "./client-component";

export default async function Component() {
  const data = await fetchData();
  return <ClientInteractiveComponent initialData={data} />;
}

// ./client-component.tsx
"use client";
import { useState } from "react";

export function ClientInteractiveComponent({ initialData }) {
  const [data, setData] = useState(initialData);
  // Client-side interactivity here
  return <div>{/* UI with data */}</div>;
}
```

### 2.2. Inefficient Re-renders (Medium)

- **Issue**: Components re-rendering unnecessarily due to improper state management
- **Files Affected**:
  - `src/app/dashboard/rfqs/_components/provider-selection.tsx` (setTimeout usage)
  - Other components with inefficient state updates
- **Solution**: Use TanStack Query and Zustand for proper state management
- **Status**: � In Progress

**Current Pattern (Incorrect):**

```typescript
// Using setTimeout for data refetching
setTimeout(async () => {
  console.log("Re-fetching data...");
  const result = await getDataAction();
  // Update state
}, 1000);
```

**Correct Implementation:**

```typescript
// Using SWR for data fetching
import useSWR from 'swr';

function DataComponent({ id }) {
  const { data, error, mutate } = useSWR(
    `/api/data/${id}`,
    () => getDataAction(id)
  );

  const handleAction = async () => {
    await performAction();
    // Revalidate data after action
    mutate();
  };

  if (error) return <div>Error loading data</div>;
  if (!data) return <div>Loading...</div>;

  return (
    <div>
      {/* Render data */}
      <button onClick={handleAction}>Perform Action</button>
    </div>
  );
}
```

### 2.3. Improper Error Handling in React Components (High)

- **Issue**: Inconsistent or missing error handling in components
- **Files Affected**:
  - Components with direct console.error calls without user feedback
- **Solution**: Implement consistent error handling with error boundaries and toast notifications
- **Status**: 🔴 Not Started

**Current Pattern (Incorrect):**

```typescript
try {
  await action();
} catch (error) {
  console.error("Error:", error);
  // No user feedback
}
```

**Correct Implementation:**

```typescript
import { toast } from "@/components/ui/sonner";

try {
  await action();
  toast.success("Action completed successfully");
} catch (error) {
  console.error("Error:", error);
  toast.error("Failed to perform action: " + (error instanceof Error ? error.message : "Unknown error"));
}

// Add error boundaries
// src/app/dashboard/error.tsx
"use client";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] gap-4">
      <h2 className="text-xl font-semibold">Something went wrong!</h2>
      <p className="text-muted-foreground">{error.message}</p>
      <Button onClick={reset}>Try again</Button>
    </div>
  );
}
```

## 3. TypeScript Issues

### 3.1. Unsafe Type Assertions (High)

- **Issue**: Using type assertions (`as`) without proper validation
- **Files Affected**:
  - Code with patterns like `return data as RFQ` or `as unknown as X`
- **Solution**: Use proper type guards and validation
- **Status**: 🔴 Not Started

**Current Pattern (Incorrect):**

```typescript
return data as RFQ;
// or
return transformedData as unknown as RFQProvider[];
```

**Correct Implementation:**

```typescript
// Using type guards
function isRFQ(data: any): data is RFQ {
  return (
    data &&
    typeof data === "object" &&
    typeof data.id === "string" &&
    typeof data.status === "string"
  );
}

if (!data) {
  throw new Error("No data returned from database");
}

if (!isRFQ(data)) {
  throw new Error("Invalid RFQ data structure");
}

return data; // Now TypeScript knows this is an RFQ
```

### 3.2. Excessive Use of `any` Type (Medium)

- **Issue**: Using `any` type instead of proper TypeScript types
- **Files Affected**:
  - Functions with `any` parameters or return types
- **Solution**: Replace `any` with proper types
- **Status**: 🔴 Not Started

**Current Pattern (Incorrect):**

```typescript
function processData(data: any): any {
  // ...
}
```

**Correct Implementation:**

```typescript
interface DataInput {
  id: string;
  value: number;
}

interface DataOutput {
  processed: boolean;
  result: string;
}

function processData(data: DataInput): DataOutput {
  // ...
  return {
    processed: true,
    result: `Processed ${data.id} with value ${data.value}`,
  };
}
```

### 3.3. Inconsistent Interface Definitions (Medium)

- **Issue**: Similar data structures defined differently across the codebase
- **Files Affected**:
  - Multiple type definitions for the same entities
- **Solution**: Centralize type definitions and use consistent naming
- **Status**: 🔴 Not Started

**Current Pattern (Incorrect):**

```typescript
// In one file
interface Provider {
  id: string;
  name: string;
  // ...
}

// In another file
type ProviderData = {
  id: string;
  provider_name: string; // Inconsistent naming
  // ...
};
```

**Correct Implementation:**

```typescript
// src/lib/types/index.ts
export interface Provider {
  id: string;
  name: string;
  // ...
}

// Use throughout the codebase
import { Provider } from "@/lib/types";
```

## 4. Performance Bottlenecks

### 4.1. Inefficient Data Fetching (High)

- **Issue**: Fetching all data without pagination or fetching unnecessary data
- **Files Affected**:
  - Service functions without pagination
- **Solution**: Implement pagination for all list operations
- **Status**: 🔴 Not Started

**Current Pattern (Incorrect):**

```typescript
const { data, error } = await supabase.from("rfqs").select("*");
```

**Correct Implementation:**

```typescript
export async function fetchRFQs(
  params: GetRFQsParams = { page: 1, pageSize: 10 },
): Promise<PaginatedResponse<RFQ>> {
  const supabase = createAdminClient();
  const { page = 1, pageSize = 10 } = params;

  // Calculate pagination values
  const from = (page - 1) * pageSize;
  const to = from + pageSize - 1;

  // Apply pagination
  const { data, error, count } = await supabase
    .from("rfqs")
    .select("*", { count: "exact" })
    .range(from, to)
    .order("created_at", { ascending: false });

  if (error) {
    throw new Error(`Failed to fetch RFQs: ${error.message}`);
  }

  // Calculate total pages
  const totalPages = Math.ceil((count || 0) / pageSize);

  return {
    data: data as RFQ[],
    pagination: {
      page,
      pageSize,
      totalPages,
      totalCount: count || 0,
    },
  };
}
```

### 4.2. Unoptimized Database Queries (High)

- **Issue**: Database queries that could be optimized with better indexing or query structure
- **Files Affected**:
  - `provider_routes` table and related queries
- **Solution**: Add appropriate indexes and optimize query patterns
- **Status**: 🔴 Not Started

**Current Pattern (Incorrect):**

```typescript
// Inefficient query without proper indexing
const { data, error } = await supabase
  .from("provider_routes")
  .select("*")
  .eq("provider_id", providerId);
```

**Correct Implementation:**

```sql
-- Add index to frequently queried columns
CREATE INDEX IF NOT EXISTS idx_provider_routes_provider_id ON provider_routes(provider_id);

-- Add clustering to improve query performance
ALTER TABLE provider_routes CLUSTER ON idx_provider_routes_provider_id;
```

```typescript
// Service function with optimized query
export async function fetchProviderRoutes(params: {
  provider_id: string;
}): Promise<ProviderRoute[]> {
  const supabase = createAdminClient();

  // This query will now use the index
  const { data, error } = await supabase
    .from("provider_routes")
    .select("*")
    .eq("provider_id", params.provider_id)
    .order("created_at", { ascending: false });

  if (error) {
    throw new Error(`Failed to fetch provider routes: ${error.message}`);
  }

  return data || [];
}
```

### 4.3. Unnecessary Re-fetching of Data (Medium)

- **Issue**: Re-fetching data unnecessarily instead of using caching
- **Files Affected**:
  - Components making multiple calls to the same data fetching function
- **Solution**: Implement proper caching with React Query, SWR, or Next.js cache
- **Status**: 🔴 Not Started

**Current Pattern (Incorrect):**

```typescript
// Multiple components fetching the same data
function ComponentA() {
  const [data, setData] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      const result = await fetchDataAction();
      setData(result);
    };
    fetchData();
  }, []);

  // ...
}

function ComponentB() {
  const [data, setData] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      const result = await fetchDataAction(); // Same data fetched again
      setData(result);
    };
    fetchData();
  }, []);

  // ...
}
```

**Correct Implementation:**

```typescript
// src/lib/actions/data.actions.ts
import { cache } from "react";

// Create a cached version of the service function
export const getCachedData = cache(async (params: any) => {
  return await fetchData(params);
});

// In server component
async function ParentComponent() {
  // This will be cached and reused
  const data = await getCachedData({ status: "active" });

  return (
    <>
      <ComponentA data={data} />
      <ComponentB data={data} />
    </>
  );
}

// Or using SWR in client components
import useSWR from 'swr';

function ClientComponent() {
  // SWR will deduplicate requests with the same key
  const { data } = useSWR('dataKey', fetchDataAction);

  // ...
}
```

## 5. Security Concerns

### 5.1. Improper Authentication in API Routes (Critical)

- **Issue**: Inconsistent authentication validation across API routes
- **Files Affected**:
  - API routes with inconsistent auth checks
- **Solution**: Use a consistent authentication middleware for all API routes
- **Status**: 🔴 Not Started

**Current Pattern (Incorrect):**

```typescript
// Inconsistent auth checks
export async function GET(request: NextRequest) {
  // Missing authentication check
  const data = await fetchData();
  return NextResponse.json({ data });
}
```

**Correct Implementation:**

```typescript
// src/lib/utils/unkey.ts
export function createUnkeyHandler(
  handler: (req: NextRequest, keyData: any) => Promise<Response> | Response,
  options: UnkeyHandlerOptions = {},
) {
  const { requiredPermissions = [] } = options;

  return withUnkey(
    async (req: NextRequestWithUnkeyContext) => {
      try {
        // Check if the API key is valid
        if (!req.unkey?.valid) {
          return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // Check if the API key has the required permissions
        if (requiredPermissions.length > 0 && req.unkey) {
          const hasAllPermissions = requiredPermissions.every((permission) =>
            req.unkey?.permissions?.includes(permission),
          );

          if (!hasAllPermissions) {
            return NextResponse.json(
              {
                error: "Insufficient permissions",
                requiredPermissions,
              },
              { status: 403 },
            );
          }
        }

        // Call the handler with the request and key data
        return await handler(req, req.unkey);
      } catch (error) {
        return handleApiError(error, "Failed to process request");
      }
    },
    {
      apiId: process.env.UNKEY_API_ID,
      // ...
    },
  );
}

// Use consistently in all API routes
export const GET = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const data = await fetchData();
      return formatApiResponse(data);
    } catch (error) {
      return handleApiError(error, "Failed to fetch data");
    }
  },
  { requiredPermissions: ["read:data"] },
);
```

### 5.2. Client-Side Exposure of Sensitive Keys (Critical)

- **Issue**: Potential exposure of sensitive API keys in client-side code
- **Files Affected**:
  - Any client components using API keys directly
- **Solution**: Create server-side proxy endpoints for third-party APIs
- **Status**: 🔴 Not Started

**Current Pattern (Incorrect):**

```typescript
// Client component with exposed API key
"use client";

const API_KEY = "sk_12345"; // Exposed in client-side code

const handleAction = async () => {
  const response = await fetch("https://api.example.com/data", {
    headers: {
      Authorization: `Bearer ${API_KEY}`,
    },
  });
  // ...
};
```

**Correct Implementation:**

```typescript
// src/app/api/proxy/external-api/route.ts
import { NextRequest } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Use server-side API key
    const response = await fetch("https://api.example.com/data", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${process.env.EXTERNAL_API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();
    return Response.json(data);
  } catch (error) {
    return Response.json({ error: "Failed to fetch data" }, { status: 500 });
  }
}

// Client component
("use client");

const handleAction = async () => {
  const response = await fetch("/api/proxy/external-api", {
    method: "POST",
    body: JSON.stringify({
      /* request data */
    }),
  });
  // ...
};
```

### 5.3. Improper Input Validation (High)

- **Issue**: Inconsistent input validation across endpoints
- **Files Affected**:
  - API routes and server actions with manual validation
- **Solution**: Use Zod schemas consistently for all input validation
- **Status**: 🔴 Not Started

**Current Pattern (Incorrect):**

```typescript
// Manual validation without schema
export async function POST(request: NextRequest) {
  const body = await request.json();

  if (!body.name) {
    return NextResponse.json({ error: "Name is required" }, { status: 400 });
  }

  // More manual validation...

  // Process data
}
```

**Correct Implementation:**

```typescript
// src/lib/utils/api.ts
export function validateWithSchema<T>(
  schema: ZodSchema<T>,
  data: unknown,
): [boolean, T | null, NextResponse] {
  const result = schema.safeParse(data);

  if (!result.success) {
    const errorResponse = NextResponse.json(
      {
        error: "Validation failed",
        details: result.error.flatten().fieldErrors,
      },
      { status: 400 },
    );

    return [false, null, errorResponse];
  }

  return [true, result.data, new NextResponse()];
}

// In API route
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate with schema
    const [isValid, validatedData, errorResponse] = validateWithSchema(
      CreateEntitySchema,
      body,
    );

    if (!isValid) {
      return errorResponse;
    }

    // Process validated data
    const result = await createEntity(validatedData!);
    return formatApiResponse(result, 201);
  } catch (error) {
    return handleApiError(error, "Failed to create entity");
  }
}
```

## Progress Tracking

| Category                    | Total Issues | Completed | In Progress | Not Started |
| --------------------------- | ------------ | --------- | ----------- | ----------- |
| Architecture Violations     | 3            | 0         | 0           | 3           |
| React/Next.js Anti-Patterns | 3            | 0         | 0           | 3           |
| TypeScript Issues           | 3            | 0         | 0           | 3           |
| Performance Bottlenecks     | 3            | 0         | 0           | 3           |
| Security Concerns           | 3            | 0         | 0           | 3           |
| Code Duplication            | 3            | 0         | 0           | 3           |
| Styling/Component Usage     | 3            | 0         | 0           | 3           |
| Additional Issues           | 3            | 0         | 0           | 3           |
| Recommended Improvements    | 3            | 0         | 0           | 3           |
| **Total**                   | **27**       | **0**     | **0**       | **27**      |

## How to Use This Document

1. **Prioritize Issues**: Start with Critical issues, then move to High, Medium, and Low.
2. **Track Progress**: Update the status of each issue as you work on it:
   - 🔴 Not Started
   - 🟡 In Progress
   - 🟢 Completed
3. **Document Changes**: When fixing an issue, document what was changed and why.
4. **Review Regularly**: Review this document regularly in team meetings to track progress.

## Implementation Strategy

- By systematically addressing these issues, we'll improve the codebase's maintainability, performance, and security while ensuring consistent patterns across the application.
- Updated status after the task is compeleted.
