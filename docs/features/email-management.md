# Email Management System

This document provides a comprehensive overview of the email management system in Steelflow, including the email polling mechanism, architecture, implementation details, and maintenance guidelines.

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Implementation](#implementation)
4. [Database Schema](#database-schema)
5. [API Endpoints](#api-endpoints)
6. [Security Considerations](#security-considerations)
7. [Maintenance Tasks](#maintenance-tasks)
8. [Troubleshooting](#troubleshooting)

## Overview

The email management system enables Steelflow to:

- Monitor designated email accounts for new messages
- Process and categorize incoming emails
- Store email content and attachments in the database
- Associate emails with relevant business entities (e.g., RFQs, providers)
- Provide a user interface for viewing and managing emails

### Key Features

- **Email Polling**: Automatically check for new emails at regular intervals
- **Email Processing**: Parse email content and extract relevant information
- **Attachment Handling**: Store and process email attachments
- **Email Association**: Link emails to relevant business entities
- **Email Search**: Search for emails by content, sender, or recipient
- **Email Viewing**: View email content and attachments in the application

## Architecture

The email management system uses a polling approach with Cloud Scheduler to periodically check for new emails:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Cloud Scheduler │────▶│  Polling Endpoint │────▶│  Gmail API      │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                                │                        │
                                ▼                        │
                        ┌─────────────────┐             │
                        │  Email Processor │◀────────────┘
                        └─────────────────┘
                                │
                                ▼
                        ┌─────────────────┐
                        │  Database       │
                        └─────────────────┘
```

### Polling Flow

1. **Regular Polling**: Cloud Scheduler triggers the polling endpoint at regular intervals (typically every 15 minutes).
2. **Authentication**: The polling endpoint is secured with a secret key.
3. **Email Fetching**: The polling service fetches new emails using the Gmail API.
4. **Processing**: Emails are processed, categorized, and stored in the database.
5. **History Tracking**: The system tracks the history ID to efficiently fetch only new emails.

### Setting Up Cloud Scheduler

To set up Cloud Scheduler for email polling:

```bash
node scripts/setup-email-polling.js https://your-app-url.com
```

This script will:

1. Create a Cloud Scheduler job to trigger the polling endpoint
2. Generate a secret key for securing the polling endpoint
3. Update your `.env.local` file with the secret key

### Attachment Processing Configuration

You can control attachment processing behavior using environment variables:

```
# Email Attachment Processing
# Set to false to enable attachment processing
EMAIL_DISABLE_ATTACHMENTS=true
# Set to false to process image attachments (only applies when EMAIL_DISABLE_ATTACHMENTS=false)
EMAIL_SKIP_IMAGE_ATTACHMENTS=true
# Comma-separated list of MIME types to skip (only applies when EMAIL_DISABLE_ATTACHMENTS=false)
# Example: EMAIL_SKIP_MIME_TYPES=video/,audio/,application/pdf
EMAIL_SKIP_MIME_TYPES=
```

These settings allow you to:

1. **Completely disable attachment processing**: Set `EMAIL_DISABLE_ATTACHMENTS=true` to skip all attachments
2. **Skip image attachments**: Set `EMAIL_SKIP_IMAGE_ATTACHMENTS=true` to skip image files (jpg, png, gif, etc.)
3. **Skip specific MIME types**: Use `EMAIL_SKIP_MIME_TYPES` to specify a comma-separated list of MIME types to skip

## Implementation

### Email Polling Service

The email polling service is implemented in `src/lib/services/email-polling.service.ts`:

```typescript
// src/lib/services/email-polling.service.ts
import { google } from "googleapis";
import { createAdminClient } from "@/lib/supabase/admin";
import { EmailAccount, EmailMessage } from "@/lib/supabase/types";

export async function pollEmailAccounts() {
  const supabase = createAdminClient();

  // Get all email accounts to watch
  const { data: accounts, error } = await supabase
    .from("email_watched_accounts")
    .select("*");

  if (error) {
    throw new Error(`Failed to fetch email accounts: ${error.message}`);
  }

  // Process each account
  const results = await Promise.all(
    accounts.map((account) => pollEmailAccount(account)),
  );

  return {
    totalAccounts: accounts.length,
    totalEmails: results.reduce((sum, result) => sum + result.newEmails, 0),
    results,
  };
}

async function pollEmailAccount(account: EmailAccount) {
  // Implementation details...
}
```

### Email Processing

The email processing logic handles parsing email content, extracting attachments, and storing data in the database:

```typescript
// src/lib/services/email-processing.service.ts
import { simpleParser } from "mailparser";
import { createAdminClient } from "@/lib/supabase/admin";

export async function processEmail(rawEmail: string, accountId: string) {
  // Parse the email
  const parsed = await simpleParser(rawEmail);

  // Extract email data
  const emailData = {
    account_id: accountId,
    gmail_id: parsed.messageId,
    gmail_thread_id: parsed.threadId,
    subject: parsed.subject,
    from: parsed.from?.text,
    to: parsed.to?.text,
    cc: parsed.cc?.text,
    bcc: parsed.bcc?.text,
    date_received: parsed.date,
    html_body: parsed.html || null,
    text_body: parsed.text || null,
    // Additional fields...
  };

  // Store the email in the database
  const supabase = createAdminClient();
  const { data, error } = await supabase
    .from("email_messages")
    .insert([emailData])
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to store email: ${error.message}`);
  }

  // Process attachments
  if (parsed.attachments && parsed.attachments.length > 0) {
    await processAttachments(parsed.attachments, data.id);
  }

  return data;
}

async function processAttachments(attachments, emailId) {
  // Implementation details...
}
```

## Database Schema

The email management system uses the following database tables:

```sql
-- Email watched accounts
CREATE TABLE public.email_watched_accounts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email VARCHAR(255) NOT NULL UNIQUE,
  display_name VARCHAR(255),
  refresh_token TEXT NOT NULL,
  access_token TEXT,
  token_expiry TIMESTAMP WITH TIME ZONE,
  history_id VARCHAR(255),
  last_checked TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Email messages
CREATE TABLE public.email_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  account_id UUID NOT NULL REFERENCES public.email_watched_accounts(id),
  gmail_id VARCHAR(255) NOT NULL,
  gmail_thread_id VARCHAR(255),
  subject TEXT,
  from TEXT,
  to TEXT,
  cc TEXT,
  bcc TEXT,
  date_received TIMESTAMP WITH TIME ZONE,
  html_body TEXT,
  text_body TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Email attachments
CREATE TABLE public.email_attachments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  message_id UUID NOT NULL REFERENCES public.email_messages(id),
  filename VARCHAR(255),
  content_type VARCHAR(255),
  content_id VARCHAR(255),
  size INTEGER,
  storage_path TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add indexes for performance
CREATE INDEX idx_email_messages_account_id ON public.email_messages(account_id);
CREATE INDEX idx_email_messages_thread_id ON public.email_messages(gmail_thread_id);
CREATE INDEX idx_email_messages_received_at ON public.email_messages(date_received);
CREATE INDEX idx_email_attachments_email_id ON public.email_attachments(message_id);
```

## API Endpoints

### OAuth Flow

- `GET /api/v1/email/auth`: Initiates OAuth flow for adding a Gmail account
- `GET /api/v1/email/auth/callback`: Handles OAuth callback and token storage

### Email Polling

- `GET /api/email/poll`: Endpoint for Cloud Scheduler to trigger polling
- `POST /api/email/poll`: Endpoint for manually triggering polling from the UI

### Email Management

- `GET /api/v1/email/accounts`: Lists all watched email accounts
- `DELETE /api/v1/email/accounts/:id`: Removes an email account from watching
- `GET /api/v1/email/messages`: Lists email messages with filtering and pagination
- `GET /api/v1/email/messages/:id`: Gets a specific email message with attachments
- `GET /api/v1/email/attachments/:id`: Downloads an email attachment

## Security Considerations

### 1. OAuth Security

- Store refresh tokens securely in the database
- Encrypt sensitive data at rest
- Use HTTPS for all API endpoints
- Implement proper token refresh mechanism

### 2. API Authentication

- Secure polling endpoint with secret key
- Implement rate limiting
- Use HTTPS for all endpoints

### 3. Access Control

- Restrict email management to admin users
- Implement role-based access for email viewing
- Create audit logs for sensitive operations

### 4. Data Privacy

- Implement data retention policies
- Consider PII handling requirements
- Document compliance considerations

## Maintenance Tasks

### 1. Token Refresh

- Implement background job to refresh OAuth tokens
- Monitor token expiration
- Handle refresh failures gracefully

### 2. Email Polling

- Set up Cloud Scheduler for regular polling (every 15 minutes)
- Create monitoring for polling status
- Implement recovery for failed polling attempts

### 3. Database Maintenance

- Implement data retention policy
- Set up database backups
- Monitor database size and performance

## Troubleshooting

### Common Issues

1. **Polling Not Working**

   - Check Cloud Scheduler job status
   - Verify secret key configuration
   - Check for errors in polling endpoint logs

2. **OAuth Token Expired**

   - Check token refresh mechanism
   - Verify refresh token is valid
   - Re-authenticate the account if needed

3. **Email Processing Errors**

   - Check for malformed emails
   - Verify attachment handling
   - Check database constraints

4. **Attachment Processing Issues**
   - Check environment variables for attachment configuration
   - Verify `EMAIL_DISABLE_ATTACHMENTS` setting
   - Check logs for attachment processing errors
   - Ensure database columns for attachments are properly sized (use TEXT instead of VARCHAR for potentially long values)

### Debugging

1. **Enable Debug Logging**

   ```typescript
   // Set DEBUG=email:* in .env.local
   import debug from "debug";
   const log = debug("email:polling");
   log("Polling email accounts...");
   ```

2. **Manual Polling**

   ```bash
   # Trigger polling manually
   curl -X POST "https://your-app.com/api/email/poll" \
     -H "Authorization: Bearer YOUR_SECRET_KEY"
   ```

3. **Check Database State**

   ```sql
   -- Check last polling time
   SELECT email, last_checked, history_id
   FROM email_watched_accounts;

   -- Check recent emails
   SELECT id, subject, date_received
   FROM email_messages
   ORDER BY date_received DESC
   LIMIT 10;
   ```

## Related Documentation

- [API Protection Guide](../architecture/api-protection.md)
- [Service Layer Architecture](../architecture/service-layer.md)
