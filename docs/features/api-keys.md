# API Key Management

This document provides a comprehensive overview of the API key management system in Steelflow, including the Unkey integration, permission system, and implementation details.

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Implementation](#implementation)
4. [Permission System](#permission-system)
5. [API Endpoints](#api-endpoints)
6. [User Interface](#user-interface)
7. [Security Considerations](#security-considerations)
8. [Best Practices](#best-practices)
9. [Troubleshooting](#troubleshooting)

## Overview

The API key management system enables Steelflow to:

- Create and manage API keys for external integrations
- Assign permissions to API keys for fine-grained access control
- Authenticate and authorize API requests
- Track API key usage and revoke keys when needed

### Key Features

- **API Key Creation**: Generate secure API keys with specific permissions
- **Permission Management**: Assign granular permissions to API keys
- **API Authentication**: Verify API keys and permissions for API requests
- **Usage Tracking**: Monitor API key usage and detect abuse
- **Key Revocation**: Revoke API keys when they are no longer needed or compromised

## Architecture

The API key management system uses Unkey for API key authentication and management:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  API Client     │────▶│  API Route      │────▶│  Unkey          │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                                │                        │
                                ▼                        │
                        ┌─────────────────┐             │
                        │  Service Layer  │◀────────────┘
                        └─────────────────┘
                                │
                                ▼
                        ┌─────────────────┐
                        │  Database       │
                        └─────────────────┘
```

### Authentication Flow

1. **API Request**: Client sends a request with an API key in the Authorization header
2. **Key Verification**: API route verifies the API key with Unkey
3. **Permission Check**: API route checks if the key has the required permissions
4. **Request Processing**: If authorized, the request is processed by the service layer
5. **Response**: The response is returned to the client

## Implementation

### Unkey Integration

The Unkey integration is implemented in `src/lib/utils/unkey.ts`:

```typescript
// src/lib/utils/unkey.ts
import { NextRequest, NextResponse } from "next/server";
import { NextRequestWithUnkeyContext, withUnkey } from "@unkey/nextjs";
import { handleApiError } from "./api";

interface UnkeyHandlerOptions {
  requiredPermissions?: string[];
}

export function createUnkeyHandler(
  handler: (request: NextRequest) => Promise<NextResponse>,
  options: UnkeyHandlerOptions = {},
) {
  return withUnkey(
    async (req: NextRequestWithUnkeyContext) => {
      try {
        // Check if the API key is valid
        if (!req.unkey.valid) {
          return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // Check permissions if required
        if (
          options.requiredPermissions &&
          options.requiredPermissions.length > 0
        ) {
          const hasAllPermissions = options.requiredPermissions.every(
            (permission) => req.unkey.permissions?.includes(permission),
          );

          if (!hasAllPermissions) {
            return NextResponse.json(
              { error: "Insufficient permissions" },
              { status: 403 },
            );
          }
        }

        // Call the original handler
        return handler(req);
      } catch (error) {
        return handleApiError(error, "Failed to process request");
      }
    },
    {
      apiId: process.env.UNKEY_API_ID,
    },
  );
}
```

### API Key Service

The API key service is implemented in `src/lib/services/api-key.service.ts`:

```typescript
// src/lib/services/api-key.service.ts
import { Unkey } from "@unkey/api";
import { createAdminClient } from "@/lib/supabase/admin";
import { CreateApiKeyParams, ApiKey } from "@/lib/schemas/api-key";

const unkey = new Unkey({ rootKey: process.env.UNKEY_ROOT_KEY! });

export async function createApiKey(
  params: CreateApiKeyParams,
): Promise<ApiKey> {
  // Check if user has admin role
  const supabase = createAdminClient();
  const { data: userRole, error: roleError } = await supabase
    .from("user_roles")
    .select("role")
    .eq("user_id", params.userId)
    .single();

  if (roleError || userRole?.role !== "admin") {
    throw new Error("Only admin users can create API keys");
  }

  // Create API key in Unkey
  const { result, error } = await unkey.keys.create({
    apiId: process.env.UNKEY_API_ID!,
    name: params.name,
    ownerId: params.userId,
    meta: {
      createdBy: params.userId,
      createdAt: new Date().toISOString(),
    },
    permissions: params.permissions,
  });

  if (error || !result) {
    throw new Error(`Failed to create API key: ${error?.message}`);
  }

  // Return the API key
  return {
    id: result.keyId,
    key: result.key,
    name: params.name,
    permissions: params.permissions,
    createdAt: new Date().toISOString(),
  };
}

export async function listApiKeys(userId: string): Promise<ApiKey[]> {
  // Implementation details...
}

export async function revokeApiKey(
  keyId: string,
  userId: string,
): Promise<void> {
  // Implementation details...
}
```

## Permission System

The API key permission system uses a string-based approach to define permissions:

```typescript
// src/lib/schemas/api-key.ts
import { z } from "zod";

export const PermissionSchema = z.enum([
  // Provider permissions
  "read:providers",
  "write:providers",
  "delete:providers",

  // Cargo permissions
  "read:cargo",
  "write:cargo",
  "delete:cargo",

  // RFQ permissions
  "read:rfqs",
  "write:rfqs",
  "delete:rfqs",
  "manage:rfqs",

  // Equipment permissions
  "read:equipment",
  "write:equipment",
  "delete:equipment",

  // Admin permissions
  "admin:system",
]);

export const CreateApiKeySchema = z.object({
  name: z.string().min(1, "Name is required"),
  userId: z.string().uuid("Invalid user ID"),
  permissions: z
    .array(PermissionSchema)
    .min(1, "At least one permission is required"),
});

export type Permission = z.infer<typeof PermissionSchema>;
export type CreateApiKeyParams = z.infer<typeof CreateApiKeySchema>;
export type ApiKey = {
  id: string;
  key: string;
  name: string;
  permissions: Permission[];
  createdAt: string;
};
```

### Permission Patterns

- `read:resource`: Read-only access to a resource
- `write:resource`: Create or update a resource
- `delete:resource`: Delete a resource
- `manage:resource`: Special operations on a resource
- `admin:system`: Administrative access to the system

## API Endpoints

### API Key Management

- `POST /api/v1/keys`: Creates a new API key
- `GET /api/v1/keys`: Lists API keys for the current user
- `DELETE /api/v1/keys/:id`: Revokes an API key

### Protected API Routes

All API routes in the `/api/v1/` path (except for `/api/v1/keys`) are protected with Unkey authentication:

```typescript
// src/app/api/v1/providers/route.ts
import { NextRequest } from "next/server";
import { createUnkeyHandler } from "@/lib/utils/unkey";
import { fetchProviders } from "@/lib/services/provider.service";
import { formatApiResponse, handleApiError } from "@/lib/utils/api";

export const GET = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const providers = await fetchProviders();
      return formatApiResponse(providers);
    } catch (error) {
      return handleApiError(error, "Failed to fetch providers");
    }
  },
  { requiredPermissions: ["read:providers"] },
);
```

## User Interface

The API key management UI is implemented in the dashboard:

- **API Keys Page**: Lists all API keys for the current user
- **Create Key Form**: Form for creating a new API key
- **Permissions Selection**: UI for selecting permissions for a new key
- **Revoke Key Button**: Button for revoking an API key

## Security Considerations

### 1. API Key Security

- Never store API keys in client-side code
- Transmit API keys only over HTTPS
- Use the Authorization header for API key transmission
- Implement rate limiting to prevent abuse
- Set appropriate expiration for API keys

### 2. Permission Management

- Follow the principle of least privilege
- Assign only the permissions that are needed
- Regularly audit API key permissions
- Implement a review process for API key creation

### 3. Access Control

- Only allow admin users to create API keys
- Implement role-based access for API key management
- Create audit logs for API key operations

## Best Practices

### For API Providers

1. **Use the service layer**: All API routes should use the service layer for business logic
2. **Validate input data**: Use Zod schemas to validate input data
3. **Handle errors consistently**: Use error handling utilities for consistent responses
4. **Document API endpoints**: Provide clear documentation for API endpoints
5. **Implement rate limiting**: Protect API endpoints from abuse

### For API Consumers

1. **Secure API keys**: Store API keys securely and never expose them
2. **Request minimal permissions**: Only request the permissions you need
3. **Handle rate limiting**: Implement backoff strategies for rate limiting
4. **Validate responses**: Always validate API responses
5. **Handle errors gracefully**: Implement proper error handling for API requests

## Troubleshooting

### Common Issues

1. **401 Unauthorized**

   - Check if the API key is valid
   - Verify the API key is included in the Authorization header
   - Check if the API key has been revoked

2. **403 Forbidden**

   - Check if the API key has the required permissions
   - Verify the user has the necessary role to perform the operation
   - Check if the API key has expired

3. **429 Too Many Requests**
   - Implement backoff strategy for rate limiting
   - Reduce the frequency of API requests
   - Contact the API provider to increase rate limits

### Debugging

1. **Enable Debug Logging**

   ```typescript
   // Set DEBUG=api:* in .env.local
   import debug from "debug";
   const log = debug("api:keys");
   log("Creating API key...");
   ```

2. **Check API Key Status**
   ```typescript
   // Check API key status in Unkey
   const { result } = await unkey.keys.get({
     keyId: "key_123",
   });
   console.log(result);
   ```

## Related Documentation

- [API Protection Guide](../architecture/api-protection.md)
- [Service Layer Architecture](../architecture/service-layer.md)
- [Unkey Documentation](https://unkey.dev/docs)
