# SteelFlow Naming Conventions

## Overview

This document outlines the standardized naming conventions for the SteelFlow project. These conventions ensure consistency, maintainability, and developer productivity across the codebase.

## File Naming Conventions

### **Rule: Use kebab-case for all file names**

✅ **Correct Examples:**
```
src/components/app-sidebar.tsx
src/components/ui/enhanced-textarea.tsx
src/components/ui/floating-action-button.tsx
src/components/ui/delete-confirmation-dialog.tsx
src/app/dashboard/rfqs/_components/rfqs-data-table.tsx
```

❌ **Incorrect Examples:**
```
AppSidebar.tsx
enhancedTextarea.tsx
FloatingActionButton.tsx
RFQsDataTable.tsx
```

### **Rationale:**
- **Consistency**: Aligns with Next.js, shadcn/ui, and industry standards
- **Cross-platform compatibility**: Works on all file systems
- **Tool compatibility**: Integrates seamlessly with build tools and IDEs
- **Readability**: Clear separation of words without case sensitivity issues

## Component Naming Conventions

### **Rule: Use PascalCase for component exports**

✅ **Correct Examples:**
```typescript
export function AppSidebar() { ... }
export function EnhancedTextarea() { ... }
export function FloatingActionButton() { ... }
export function DeleteConfirmationDialog() { ... }
export function RFQsDataTable() { ... }
```

❌ **Incorrect Examples:**
```typescript
export function appSidebar() { ... }
export function enhanced_textarea() { ... }
export function floatingActionButton() { ... }
```

### **Rationale:**
- **React convention**: Standard practice in React ecosystem
- **JSX compatibility**: Required for proper JSX rendering
- **Type safety**: Aligns with TypeScript naming conventions
- **Component identification**: Clear distinction from regular functions

## Directory Structure Conventions

### **App-Specific Components**
```
src/app/[module]/_components/
├── [module]-content.tsx          # Main content component
├── [module]-data-table.tsx       # Data table component
├── [module]-skeleton.tsx         # Loading skeleton
├── dialogs/                      # Dialog components
│   ├── add-[module]-dialog.tsx
│   └── edit-[module]-dialog.tsx
├── forms/                        # Form components
│   └── [module]-form.tsx
└── tables/                       # Table-related components
    └── [module]-columns.tsx
```

### **Reusable UI Components**
```
src/components/ui/
├── [component-name].tsx         # Single-purpose components
├── [complex-component]/         # Multi-file components
│   ├── index.tsx
│   └── [sub-component].tsx
└── [category]/                  # Grouped components
    ├── [component-a].tsx
    └── [component-b].tsx
```

## Import/Export Conventions

### **Named Exports (Preferred)**
```typescript
// ✅ Preferred
export function ComponentName() { ... }

// Import
import { ComponentName } from "@/components/ui/component-name";
```

### **Default Exports (When Appropriate)**
```typescript
// ✅ For single-purpose files
export default function ComponentName() { ... }

// Import
import ComponentName from "@/components/ui/component-name";
```

## Specific Naming Patterns

### **Data Tables**
- **File**: `[entity]-data-table.tsx`
- **Component**: `[Entity]DataTable`
- **Examples**: `rfqs-data-table.tsx` → `RFQsDataTable`

### **Dialog Components**
- **File**: `[action]-[entity]-dialog.tsx`
- **Component**: `[Action][Entity]Dialog`
- **Examples**: `add-provider-dialog.tsx` → `AddProviderDialog`

### **Form Components**
- **File**: `[entity]-form.tsx`
- **Component**: `[Entity]Form`
- **Examples**: `provider-form.tsx` → `ProviderForm`

### **Content Components**
- **File**: `[entity]-content.tsx`
- **Component**: `[Entity]Content`
- **Examples**: `providers-content.tsx` → `ProvidersContent`

### **Skeleton Components**
- **File**: `[entity]-skeleton.tsx`
- **Component**: `[Entity]Skeleton`
- **Examples**: `providers-skeleton.tsx` → `ProvidersSkeleton`

## Validation Rules

### **Automated Checks**
Consider implementing these linting rules:

```json
{
  "rules": {
    "filename-case": ["error", "kebab-case"],
    "component-name": ["error", "PascalCase"],
    "export-consistency": ["error", "named-preferred"]
  }
}
```

### **Manual Review Checklist**
- [ ] File names use kebab-case
- [ ] Component names use PascalCase
- [ ] Directory structure follows conventions
- [ ] Import paths are consistent
- [ ] No naming conflicts exist

## Migration Guidelines

### **For New Components**
1. Always use kebab-case for file names
2. Always use PascalCase for component names
3. Follow directory structure conventions
4. Use descriptive, intention-revealing names

### **For Existing Components**
The current codebase already follows these conventions perfectly. No migration is needed.

## Examples from Codebase

### **Perfect Examples (Current State)**
```
✅ src/components/app-sidebar.tsx → AppSidebar
✅ src/components/ui/enhanced-textarea.tsx → EnhancedTextarea
✅ src/app/dashboard/rfqs/_components/rfqs-data-table.tsx → RFQsDataTable
✅ src/app/dashboard/providers/_components/providers-content.tsx → ProvidersContent
```

## Conclusion

The SteelFlow project already maintains excellent naming conventions. This document serves as a reference to preserve these standards and guide future development.

**Key Principles:**
1. **Consistency** - Follow established patterns
2. **Clarity** - Use descriptive, intention-revealing names
3. **Convention** - Align with industry standards
4. **Maintainability** - Support long-term codebase health
