# Validation Utility

This document explains how to use the simplified validation utility with Supazod-generated schemas.

## Overview

The validation utility provides a generic function for validating data against Zod schemas, including Supazod-generated schemas. This reduces the need for custom validation functions and provides a consistent validation pattern.

## Key Files

- `src/lib/utils/validation.ts`: Contains the generic validation utility
- `src/lib/schemas/schemas.ts`: Contains the Supazod-generated schemas
- `src/lib/types/database-types.ts`: Contains the centralized type definitions

## How to Use

### Basic Usage

```typescript
import { validateWithSchema } from "@/lib/utils/validation";
import * as schemas from "@/lib/schemas/schemas";

// Validate provider data
const validationResult = validateWithSchema(
  providerData,
  schemas.publicProvidersInsertSchemaSchema,
);

if (!validationResult.success) {
  // Handle validation error
  console.error(validationResult.error);
  return {
    success: false,
    error: validationResult.error?.message || "Validation failed",
    fieldErrors: validationResult.error?.fieldErrors,
  };
}

// Use validated data
const validatedData = validationResult.data;
```

### Custom Schema Validation

You can also use the utility with custom Zod schemas:

```typescript
import { validateWithSchema } from "@/lib/utils/validation";
import { z } from "zod";

// Create a custom schema
const customSchema = z.object({
  id: z.string().uuid("Invalid ID"),
  page: z.coerce.number().int().min(1).default(1),
  pageSize: z.coerce.number().int().min(1).max(100).default(10),
});

// Validate data against the custom schema
const validationResult = validateWithSchema(data, customSchema);
```

## Validation Result

The validation function returns a `ValidationResult<T>` object with the following structure:

```typescript
type ValidationResult<T> = {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    fieldErrors?:
      | Record<string, string[]>
      | { [key: string]: string[] | undefined };
  };
};
```

- `success`: Indicates whether validation was successful
- `data`: The validated data (only present if validation was successful)
- `error`: Error information (only present if validation failed)
  - `message`: A general error message
  - `fieldErrors`: Field-specific error messages

## Best Practices

1. **Use in Service Layer**: Validate data at the service layer before performing database operations
2. **Use in Action Layer**: Validate data at the action layer before calling service functions
3. **Return Validation Errors**: Return validation errors to the client for better user experience
4. **Use Validated Data**: Always use the validated data returned by the validation function

## Example: Action Layer

```typescript
import { validateWithSchema } from "@/lib/utils/validation";
import * as schemas from "@/lib/schemas/schemas";
import { z } from "zod";
import { ActionResponse } from "@/lib/types/database-types";

export async function createProviderAction(
  provider: unknown,
): Promise<ActionResponse<any>> {
  try {
    // Validate provider data
    const validationResult = validateWithSchema(
      provider,
      schemas.publicProvidersInsertSchemaSchema,
    );

    if (!validationResult.success) {
      return {
        success: false,
        error: validationResult.error?.message || "Validation failed",
        fieldErrors: validationResult.error?.fieldErrors,
      };
    }

    const newProvider = await addProvider(validationResult.data!);
    return { success: true, data: newProvider };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to create provider",
    };
  }
}
```
