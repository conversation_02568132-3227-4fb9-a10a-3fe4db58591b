# Typography System

This document outlines the typography system used in the SteelFlow application. The system is designed to provide consistent text styling across the application.

## Font Family

The application uses the Figtree font family from Google Fonts. This is a modern, readable sans-serif font that works well for both headings and body text.

## Type Scale

The type scale is designed to provide a clear hierarchy of text elements. The scale is defined in the `tailwind.config.js` file.

| Name | Size | Line Height | Weight | Usage |
|------|------|-------------|--------|-------|
| `display` | 3.5rem (56px) | 1.2 | 700 | Hero sections, major feature headings |
| `h1` | 2.25rem (36px) | 1.3 | 700 | Main page titles |
| `h2` | 1.875rem (30px) | 1.3 | 700 | Section headings |
| `h3` | 1.5rem (24px) | 1.4 | 600 | Subsection headings |
| `h4` | 1.25rem (20px) | 1.4 | 600 | Card titles, minor section headings |
| `h5` | 1.125rem (18px) | 1.5 | 600 | Small section headings |
| `h6` | 1rem (16px) | 1.5 | 600 | The smallest heading |
| `body-lg` | 1.125rem (18px) | 1.6 | 400 | Introductory paragraphs |
| `body` | 1rem (16px) | 1.6 | 400 | Default paragraph text |
| `body-sm` | 0.875rem (14px) | 1.5 | 400 | Secondary information |
| `caption` | 0.75rem (12px) | 1.5 | 400 | Image captions, footnotes |
| `overline` | 0.75rem (12px) | 1.5 | 500 | Labels, categories (uppercase) |

## Typography Components

The application provides a set of reusable typography components in `src/components/ui/typography.tsx`. These components should be used for all text elements in the application to ensure consistency.

### Headings

```tsx
import { Display, H1, H2, H3, H4, H5, H6 } from "@/components/ui/typography";

// Usage
<Display>Hero Heading</Display>
<H1>Page Title</H1>
<H2>Section Heading</H2>
<H3>Subsection Heading</H3>
<H4>Card Title</H4>
<H5>Small Section Heading</H5>
<H6>Smallest Heading</H6>
```

### Body Text

```tsx
import { 
  LargeText, 
  Text, 
  SmallText, 
  Caption, 
  Overline, 
  Muted, 
  Lead 
} from "@/components/ui/typography";

// Usage
<LargeText>Large body text for introductory paragraphs.</LargeText>
<Text>Regular body text for most content.</Text>
<SmallText>Smaller text for secondary information.</SmallText>
<Caption>Caption text for images or footnotes.</Caption>
<Overline>LABEL TEXT IN UPPERCASE</Overline>
<Muted>Less important text with muted color.</Muted>
<Lead>Introductory paragraph with larger size and muted color.</Lead>
```

### Other Text Elements

```tsx
import { Blockquote, InlineCode } from "@/components/ui/typography";

// Usage
<Blockquote>This is a quotation from a source.</Blockquote>
<InlineCode>const example = "code snippet";</InlineCode>
```

## Customizing Typography Components

All typography components accept a `className` prop that can be used to override the default styles:

```tsx
<H1 className="text-primary">Custom Colored Heading</H1>
<Text className="italic">Italic text</Text>
```

## Best Practices

1. **Use the appropriate component for the content**: Choose the right typography component based on the content's purpose and hierarchy.
2. **Maintain heading hierarchy**: Use headings in order (H1, H2, H3, etc.) without skipping levels for proper document structure and accessibility.
3. **Limit text width**: For optimal readability, limit the width of text containers to around 60-75 characters per line.
4. **Use color appropriately**: Ensure sufficient contrast between text and background colors for readability.
5. **Be consistent**: Use the typography components consistently throughout the application.

## Accessibility Considerations

- Maintain proper heading hierarchy for screen readers
- Ensure sufficient color contrast (WCAG AA compliance at minimum)
- Avoid using font size below 12px for readability
- Use relative units (rem) to respect user font size preferences
