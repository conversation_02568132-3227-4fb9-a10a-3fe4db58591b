import { Page } from "@playwright/test";

export class LoginPage {
  constructor(private page: Page) {}

  // Navigation
  async goto() {
    await this.page.goto("/auth/login");
  }

  // Actions
  async login(email: string, password: string) {
    await this.emailInput.fill(email);
    await this.passwordInput.fill(password);
    await this.loginButton.click();
  }

  async loginWithGoogle() {
    await this.googleLoginButton.click();
  }

  // Elements
  get emailInput() {
    return this.page.getByLabel("Email");
  }

  get passwordInput() {
    return this.page.getByLabel("Password");
  }

  get loginButton() {
    return this.page.getByRole("button", { name: "Log In" });
  }

  get googleLoginButton() {
    return this.page.getByRole("button", { name: "Continue with Google" });
  }

  get errorMessage() {
    return this.page.getByTestId("login-error");
  }
}