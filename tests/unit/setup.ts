// This file is used to set up the test environment for unit tests

// Define global mocks
// Using type assertion to avoid TypeScript errors
const globalAny = global as any;

interface MockFunction {
  (...args: any[]): any;
  mock: {
    calls: any[][];
    returnValue: any;
    implementation?: (...args: any[]) => any;
  };
  mockReturnValue: (value: any) => MockFunction;
  mockImplementation: (implementation: (...args: any[]) => any) => MockFunction;
}

globalAny.jest = {
  fn: (): MockFunction => {
    const mockFn = ((...args: any[]) => {
      mockFn.mock.calls.push(args);
      if (mockFn.mock.implementation) {
        return mockFn.mock.implementation(...args);
      }
      return mockFn.mock.returnValue;
    }) as MockFunction;

    mockFn.mock = {
      calls: [],
      returnValue: undefined,
      implementation: undefined,
    };

    mockFn.mockReturnValue = (value: any) => {
      mockFn.mock.returnValue = value;
      return mockFn;
    };

    mockFn.mockImplementation = (implementation: (...args: any[]) => any) => {
      mockFn.mock.implementation = implementation;
      mockFn.mock.returnValue = undefined;
      return mockFn;
    };

    return mockFn;
  },
  mock: (moduleName: string, factory?: () => any) => {
    // This is a simplified mock implementation
    // In a real setup, this would interact with the module system
    return {
      moduleName,
      factory: factory || (() => ({})),
    };
  },
};