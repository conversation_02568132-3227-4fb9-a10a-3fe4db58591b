import { test, expect } from "@playwright/test";
import { ProviderFormSchema } from "@/lib/schemas";

// Since we can't easily test server actions directly with <PERSON><PERSON>,
// we'll focus on testing the form schema validation which is a critical part
// of the form submission process

test.describe("Provider Form Validation", () => {
  test("should validate a complete provider form submission", () => {
    // This simulates the data that would be submitted in a form
    const formData = {
      name: "Test Provider",
      tax_id: "*********",
      full_address: "123 Test St, Test City",
      status: "active" as const,
      verified: true,
    };

    // Validate the form data using the schema
    const result = ProviderFormSchema.safeParse(formData);

    // Verify the validation result
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data).toEqual(formData);
    }
  });

  test("should validate a minimal provider form submission", () => {
    // This simulates a minimal form submission with only required fields
    const formData = {
      name: "Test Provider",
    };

    // Validate the form data using the schema
    const result = ProviderFormSchema.safeParse(formData);

    // Verify the validation result
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data).toEqual({
        name: "Test Provider",
        status: "pending",
        verified: false,
      });
    }
  });

  test("should reject an invalid provider form submission with empty name", () => {
    // This simulates an invalid form submission with empty name
    const formData = {
      name: "",
      status: "active" as const,
    };

    // Validate the form data using the schema
    const result = ProviderFormSchema.safeParse(formData);

    // Verify the validation result
    expect(result.success).toBe(false);
    if (!result.success) {
      // Check that the name field has an error
      const nameErrors = result.error.format().name?._errors;
      expect(nameErrors).toBeDefined();
      expect(nameErrors).toContain("Name is required");
    }
  });

  test("should reject an invalid provider form submission with invalid status", () => {
    // This simulates an invalid form submission with invalid status
    const formData = {
      name: "Test Provider",
      status: "invalid-status" as any,
    };

    // Validate the form data using the schema
    const result = ProviderFormSchema.safeParse(formData);

    // Verify the validation result
    expect(result.success).toBe(false);
    if (!result.success) {
      // Check that the status field has an error
      const statusErrors = result.error.format().status?._errors;
      expect(statusErrors).toBeDefined();
      expect(statusErrors!.length).toBeGreaterThan(0);
    }
  });

  test("should handle form data conversion from FormData to object", () => {
    // This simulates how form data is processed in the action
    const rawFormData = {
      name: "Test Provider",
      tax_id: "*********",
      full_address: "123 Test St",
      status: "active",
      verified: "true", // Note: this comes as a string from FormData
    };

    // Convert the form data as it would be in the action
    const processedData = {
      name: rawFormData.name,
      tax_id: rawFormData.tax_id || undefined,
      full_address: rawFormData.full_address || undefined,
      status: rawFormData.status || "pending",
      verified: rawFormData.verified === "true", // Convert string to boolean
    };

    // Validate the processed data
    const result = ProviderFormSchema.safeParse(processedData);

    // Verify the validation result
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data.name).toBe("Test Provider");
      expect(result.data.tax_id).toBe("*********");
      expect(result.data.full_address).toBe("123 Test St");
      expect(result.data.status).toBe("active");
      expect(result.data.verified).toBe(true);
    }
  });
});