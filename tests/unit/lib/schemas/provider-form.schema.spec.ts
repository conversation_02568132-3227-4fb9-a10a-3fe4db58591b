import { test, expect } from "@playwright/test";
import { ProviderFormSchema } from "@/lib/schemas";

test.describe("Provider Form Schema", () => {
  test("should validate valid provider data", () => {
    const validData = {
      name: "Test Provider",
      tax_id: "*********",
      full_address: "123 Test St, Test City, Test Country",
      status: "active" as const,
      verified: true,
    };

    const result = ProviderFormSchema.safeParse(validData);
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data).toEqual(validData);
    }
  });

  test("should validate provider data with only required fields", () => {
    const validData = {
      name: "Test Provider",
      status: "pending" as const,
    };

    const result = ProviderFormSchema.safeParse(validData);
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data).toEqual({
        name: "Test Provider",
        status: "pending",
        verified: false,
      });
    }
  });

  test("should validate provider data with default values", () => {
    const validData = {
      name: "Test Provider",
    };

    const result = ProviderFormSchema.safeParse(validData);
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data).toEqual({
        name: "Test Provider",
        status: "pending",
        verified: false,
      });
    }
  });

  test("should reject provider data with empty name", () => {
    const invalidData = {
      name: "",
      status: "active" as const,
    };

    const result = ProviderFormSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.errors[0].message).toBe("Name is required");
    }
  });

  test("should reject provider data with invalid status", () => {
    const invalidData = {
      name: "Test Provider",
      status: "invalid-status" as any,
    };

    const result = ProviderFormSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.errors[0].path).toContain("status");
    }
  });

  test("should reject provider data with invalid verified value", () => {
    const invalidData = {
      name: "Test Provider",
      verified: "yes" as any, // Should be boolean
    };

    const result = ProviderFormSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.errors[0].path).toContain("verified");
    }
  });
});