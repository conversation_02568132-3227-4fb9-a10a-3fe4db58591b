import { test, expect } from "@playwright/test";
import { retry } from "@/lib/utils/retry";

// Note: In a real setup, we would mock the logger
// For simplicity in this test, we'll use the actual logger

test.describe("Retry Utils", () => {
  test("should resolve if operation succeeds on first attempt", async () => {
    const operation = async () => "success";
    const result = await retry(operation);
    expect(result).toBe("success");
  });

  test("should retry and eventually succeed", async () => {
    let attempts = 0;
    const operation = async () => {
      attempts++;
      if (attempts < 2) {
        throw new Error("Temporary failure");
      }
      return "success after retry";
    };

    const result = await retry(operation, {
      initialDelay: 10, // Use small delays for faster tests
      maxRetries: 3,
    });

    expect(attempts).toBe(2);
    expect(result).toBe("success after retry");
  });

  test("should fail after max retries", async () => {
    let attempts = 0;
    const operation = async () => {
      attempts++;
      throw new Error("Persistent failure");
    };

    await expect(
      retry(operation, {
        initialDelay: 10,
        maxRetries: 2,
      })
    ).rejects.toThrow("Persistent failure");

    // The retry implementation counts attempts starting from 1 (after the first failure)
    // When attempt count reaches maxRetries, it stops retrying
    // So with maxRetries=2, we get initial attempt + 1 retry = 2 total attempts
    expect(attempts).toBe(2); // Initial attempt + 1 retry
  });

  test("should respect shouldRetry function", async () => {
    let attempts = 0;
    const operation = async () => {
      attempts++;
      throw new Error("Do not retry me");
    };

    await expect(
      retry(operation, {
        initialDelay: 10,
        maxRetries: 3,
        shouldRetry: (error) => error.message !== "Do not retry me",
      })
    ).rejects.toThrow("Do not retry me");

    expect(attempts).toBe(1); // Only the initial attempt, no retries
  });

  test("should call onRetry function before each retry", async () => {
    let attempts = 0;
    const retryCallbacks: Array<{ error: string; attempt: number }> = [];

    const operation = async () => {
      attempts++;
      if (attempts <= 2) {
        throw new Error(`Attempt ${attempts} failed`);
      }
      return "success";
    };

    const result = await retry(operation, {
      initialDelay: 10,
      maxRetries: 3,
      onRetry: (error, attempt) => {
        retryCallbacks.push({ error: error.message, attempt });
      },
    });

    expect(attempts).toBe(3);
    expect(result).toBe("success");
    expect(retryCallbacks).toHaveLength(2);
    expect(retryCallbacks[0].attempt).toBe(1);
    expect(retryCallbacks[0].error).toBe("Attempt 1 failed");
    expect(retryCallbacks[1].attempt).toBe(2);
    expect(retryCallbacks[1].error).toBe("Attempt 2 failed");
  });

  test("should use exponential backoff for delays", async () => {
    // This test is more complex and would typically use mocks to verify timing
    // For simplicity, we'll just test that the function completes successfully

    let attempts = 0;
    const operation = async () => {
      attempts++;
      if (attempts < 3) {
        throw new Error("Temporary failure");
      }
      return "success after backoff";
    };

    const result = await retry(operation, {
      initialDelay: 10,
      maxDelay: 100,
      factor: 2,
      maxRetries: 3,
    });

    expect(attempts).toBe(3);
    expect(result).toBe("success after backoff");
  });
});