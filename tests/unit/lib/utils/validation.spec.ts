import { test, expect } from "@playwright/test";
import { validateWithSchema } from "@/lib/utils/validation";
import { z } from "zod";

// Note: In a real setup, we would mock the logger
// For simplicity in this test, we'll use the actual logger

test.describe("Validation Utils", () => {
  test.describe("validateWithSchema", () => {
    // Define a simple test schema
    const userSchema = z.object({
      id: z.string().uuid(),
      name: z.string().min(2).max(50),
      email: z.string().email(),
      age: z.number().int().positive().optional(),
    });

    test("should return success for valid data", () => {
      const validData = {
        id: "123e4567-e89b-12d3-a456-************",
        name: "<PERSON>",
        email: "<EMAIL>",
        age: 30,
      };

      const result = validateWithSchema(validData, userSchema);
      
      expect(result.success).toBe(true);
      expect(result.data).toEqual(validData);
    });

    test("should return success for valid data with optional fields omitted", () => {
      const validData = {
        id: "123e4567-e89b-12d3-a456-************",
        name: "John Doe",
        email: "<EMAIL>",
      };

      const result = validateWithSchema(validData, userSchema);
      
      expect(result.success).toBe(true);
      expect(result.data).toEqual(validData);
    });

    test("should return failure for invalid data", () => {
      const invalidData = {
        id: "not-a-uuid",
        name: "J", // Too short
        email: "not-an-email",
        age: -5, // Negative number
      };

      const result = validateWithSchema(invalidData, userSchema);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toBe("Validation failed");
      expect(result.error?.fieldErrors).toBeDefined();
      
      // Check that all fields have errors
      const fieldErrors = result.error?.fieldErrors || {};
      expect(Object.keys(fieldErrors).length).toBe(4);
      expect(fieldErrors.id).toBeDefined();
      expect(fieldErrors.name).toBeDefined();
      expect(fieldErrors.email).toBeDefined();
      expect(fieldErrors.age).toBeDefined();
    });

    test("should return failure for missing required fields", () => {
      const incompleteData = {
        name: "John Doe",
      };

      const result = validateWithSchema(incompleteData, userSchema);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      
      // Check that missing fields have errors
      const fieldErrors = result.error?.fieldErrors || {};
      expect(fieldErrors.id).toBeDefined();
      expect(fieldErrors.email).toBeDefined();
    });

    test("should handle exceptions gracefully", () => {
      // Create a schema that might throw an exception
      const problematicSchema = z.object({
        value: z.string().refine(() => {
          throw new Error("Unexpected error");
        }),
      });

      const data = { value: "test" };
      const result = validateWithSchema(data, problematicSchema);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toBe("Unexpected error");
    });
  });
});