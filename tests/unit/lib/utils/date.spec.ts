import { test, expect } from "@playwright/test";
import { formatDate, formatDateSafely, formatDistanceToNowSafely } from "@/lib/utils/date";

// Note: In a real setup, we would mock the logger
// For simplicity in this test, we'll use the actual logger

test.describe("Date Utils", () => {
  test.describe("formatDate", () => {
    test("should format a valid ISO date string", () => {
      const result = formatDate("2023-01-15T12:30:45Z");
      // The exact format will depend on the locale, but we can check for the year
      expect(result).toContain("2023");
    });

    test("should format a date with custom format", () => {
      const result = formatDate("2023-01-15T12:30:45Z", "yyyy-MM-dd");
      expect(result).toBe("2023-01-15");
    });

    test("should return 'Not specified' for undefined input", () => {
      const result = formatDate(undefined);
      expect(result).toBe("Not specified");
    });

    test("should return 'Invalid date' for invalid date strings", () => {
      const result = formatDate("not-a-date");
      expect(result).toBe("Invalid date");
    });
  });

  test.describe("formatDateSafely", () => {
    test("should format a valid ISO date string", () => {
      const result = formatDateSafely("2023-01-15T12:30:45Z");
      // The exact format will depend on the locale, but we can check for the year and time
      expect(result).toContain("2023");
      expect(result).toMatch(/12:30/);
    });

    test("should format a valid timestamp string", () => {
      // January 15, 2023 12:30:45 UTC
      const timestamp = "1673785845000";
      const result = formatDateSafely(timestamp);
      expect(result).toContain("2023");
    });

    test("should format a date with custom format", () => {
      const result = formatDateSafely("2023-01-15T12:30:45Z", "yyyy-MM-dd");
      expect(result).toBe("2023-01-15");
    });

    test("should return 'Unknown date' for undefined input", () => {
      const result = formatDateSafely(undefined);
      expect(result).toBe("Unknown date");
    });

    test("should return 'Invalid date' for invalid date strings", () => {
      const result = formatDateSafely("not-a-date");
      expect(result).toBe("Invalid date");
    });
  });

  test.describe("formatDistanceToNowSafely", () => {
    test("should format a valid ISO date string relative to now", () => {
      // Create a date that's 2 days in the past
      const twoDaysAgo = new Date();
      twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);
      
      const result = formatDistanceToNowSafely(twoDaysAgo.toISOString());
      expect(result).toContain("day");
    });

    test("should format a valid timestamp string relative to now", () => {
      // Create a date that's 1 hour in the past
      const oneHourAgo = new Date();
      oneHourAgo.setHours(oneHourAgo.getHours() - 1);
      
      const result = formatDistanceToNowSafely(oneHourAgo.getTime().toString());
      expect(result).toContain("hour");
    });

    test("should format without suffix when addSuffix is false", () => {
      // Create a date that's 1 hour in the past
      const oneHourAgo = new Date();
      oneHourAgo.setHours(oneHourAgo.getHours() - 1);
      
      const result = formatDistanceToNowSafely(oneHourAgo.toISOString(), { addSuffix: false });
      expect(result).not.toContain("ago");
    });

    test("should return 'Unknown date' for undefined input", () => {
      const result = formatDistanceToNowSafely(undefined);
      expect(result).toBe("Unknown date");
    });

    test("should return 'Invalid date' for invalid date strings", () => {
      const result = formatDistanceToNowSafely("not-a-date");
      expect(result).toBe("Invalid date");
    });
  });
});
