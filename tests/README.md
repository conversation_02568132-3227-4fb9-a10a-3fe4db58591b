# Steelflow Tests

This directory contains tests for the Steelflow application. The tests are organized by type and feature.

## Test Structure

```
tests/
├── unit/                  # Unit tests for individual functions and components
│   ├── lib/               # Tests for utility functions in the lib directory
│   │   ├── actions/       # Tests for server actions
│   │   │   └── provider-form.actions.spec.ts
│   │   ├── schemas/       # Tests for schema validation
│   │   │   └── provider-form.schema.spec.ts
│   │   └── utils/         # Tests for utility functions
│   │       ├── date-utils.spec.ts
│   │       ├── retry.spec.ts
│   │       └── validation.spec.ts
│   └── setup.ts           # Setup file for unit tests
├── e2e/                   # End-to-end tests
│   └── auth/              # Authentication-related tests
│       └── login.spec.ts  # Login tests
├── page-objects/          # Page object models for e2e tests
│   └── login-page.ts      # Login page object
└── unit.config.ts         # Configuration for unit tests
```

## Running Tests

### Unit Tests

Unit tests focus on testing individual functions and components in isolation. They are fast and help ensure that each piece of the application works correctly on its own.

```bash
# Run all unit tests
pnpm test:unit

# Run a specific test file
pnpm exec playwright test tests/unit/lib/utils/date-utils.spec.ts
```

### End-to-End Tests (Future)

End-to-end tests will test the application as a whole, simulating user interactions and ensuring that all parts of the application work together correctly.

```bash
# Run all e2e tests (to be implemented)
pnpm test
```

## Writing Tests

### Unit Tests

Unit tests should follow these guidelines:

1. Test one function or component at a time
2. Use descriptive test names that explain what is being tested
3. Follow the AAA pattern: Arrange, Act, Assert
4. Keep tests independent and isolated
5. Mock external dependencies when necessary

Example:

```typescript
import { test, expect } from "@playwright/test";
import { myFunction } from "@/lib/utils/my-utils";

test.describe("My Utils", () => {
  test.describe("myFunction", () => {
    test("should return expected result for valid input", () => {
      // Arrange
      const input = "valid input";
      
      // Act
      const result = myFunction(input);
      
      // Assert
      expect(result).toBe("expected result");
    });
  });
});
```

## Form Testing Strategy

When testing forms, we use a multi-layered approach:

1. **Schema Validation Tests**:
   - Test the Zod schemas directly to ensure they validate correctly
   - Test both valid and invalid inputs
   - Test default values and type conversions

2. **Form Processing Tests**:
   - Test the conversion from form data to validated objects
   - Test error handling and validation error formatting

3. **End-to-End Form Tests**:
   - Test form submission in the browser
   - Test form validation feedback
   - Test successful form submission and navigation

## Server Action Testing

For server actions, we focus on testing:

1. **Input Validation**: Ensure the action properly validates inputs
2. **Error Handling**: Test how the action handles various error conditions
3. **Success Paths**: Verify the action returns the expected results on success

## Test Coverage

To increase test coverage, focus on:

1. Core utility functions that are used throughout the application
2. Complex business logic
3. Edge cases and error handling
4. Critical user flows
5. Form validation and submission
6. Server actions and API endpoints

## Continuous Integration

Tests are automatically run in CI/CD pipelines to ensure that changes don't break existing functionality.