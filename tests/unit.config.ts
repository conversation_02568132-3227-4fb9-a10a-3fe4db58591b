import { defineConfig } from "@playwright/test";
import baseConfig from "../playwright.config";

// Unit test configuration that extends the base configuration
export default defineConfig({
  ...baseConfig,
  testDir: "./tests/unit",
  testMatch: "**/*.spec.ts",

  // <PERSON><PERSON> launching a browser for unit tests
  use: {
    ...baseConfig.use,
    headless: true,
  },

  // <PERSON><PERSON> launching the web server for unit tests
  webServer: undefined,

  // Environment variables are now set in the process.env object
  // The 'env' property is not supported in the Playwright config
});