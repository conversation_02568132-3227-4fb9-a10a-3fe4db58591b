name: Deploy to <PERSON><PERSON>

on:
  push:
    branches:
      - main

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
  REGION: europe-west1
  REPOSITORY: europe-west1-docker.pkg.dev/${{ secrets.GCP_PROJECT_ID }}/steelflow

jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: 'read'
      id-token: 'write'

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ env.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT_ID }}

      - name: Configure Docker
        run: |
          gcloud auth configure-docker europe-west1-docker.pkg.dev

      - name: Create .env.production file for build
        run: |
          # Create .env.production file without indentation
          echo "NEXT_PUBLIC_SUPABASE_URL=${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}" > .env.production
          echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}" >> .env.production
          echo "SUPABASE_SERVICE_ROLE_KEY=${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}" >> .env.production
          echo "PUBLIC_APP_URL=${{ secrets.PUBLIC_APP_URL }}" >> .env.production
          echo "NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=${{ secrets.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY }}" >> .env.production
          echo "GEMINI_API_KEY=${{ secrets.GEMINI_API_KEY }}" >> .env.production
          echo "UNKEY_API_ID=${{ secrets.UNKEY_API_ID }}" >> .env.production
          echo "UNKEY_ROOT_KEY=${{ secrets.UNKEY_ROOT_KEY }}" >> .env.production
          echo "SENDGRID_API_KEY=${{ secrets.SENDGRID_API_KEY }}" >> .env.production
          echo "GMAIL_CLIENT_ID=${{ secrets.GMAIL_CLIENT_ID }}" >> .env.production
          echo "GMAIL_CLIENT_SECRET=${{ secrets.GMAIL_CLIENT_SECRET }}" >> .env.production
          echo "GMAIL_OAUTH_REDIRECT_URI=${{ secrets.GMAIL_OAUTH_REDIRECT_URI }}" >> .env.production
          echo "GOOGLE_CLOUD_PROJECT_ID=${{ secrets.GOOGLE_CLOUD_PROJECT_ID }}" >> .env.production
          echo "EMAIL_DISABLE_ATTACHMENTS=${{ secrets.EMAIL_DISABLE_ATTACHMENTS }}" >> .env.production

      - name: Create env.yaml for Cloud Run
        run: |
          cat << EOF > env.yaml
          NODE_ENV: "${{ secrets.NODE_ENV }}"
          NEXT_PUBLIC_SUPABASE_URL: "${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}"
          NEXT_PUBLIC_SUPABASE_ANON_KEY: "${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}"
          SUPABASE_SERVICE_ROLE_KEY: "${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}"
          PUBLIC_APP_URL: "${{ secrets.PUBLIC_APP_URL }}"
          NEXT_PUBLIC_GOOGLE_MAPS_API_KEY: "${{ secrets.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY }}"
          GEMINI_API_KEY: "${{ secrets.GEMINI_API_KEY }}"
          UNKEY_API_ID: "${{ secrets.UNKEY_API_ID }}"
          UNKEY_ROOT_KEY: "${{ secrets.UNKEY_ROOT_KEY }}"
          SENDGRID_API_KEY: "${{ secrets.SENDGRID_API_KEY }}"
          GMAIL_CLIENT_ID: "${{ secrets.GMAIL_CLIENT_ID }}"
          GMAIL_CLIENT_SECRET: "${{ secrets.GMAIL_CLIENT_SECRET }}"
          GMAIL_OAUTH_REDIRECT_URI: "${{ secrets.GMAIL_OAUTH_REDIRECT_URI }}"
          GOOGLE_CLOUD_PROJECT_ID: "${{ secrets.GOOGLE_CLOUD_PROJECT_ID }}"
          EMAIL_DISABLE_ATTACHMENTS: "${{ secrets.EMAIL_DISABLE_ATTACHMENTS }}"
          EMAIL_FORCE_SANDBOX: "${{ secrets.EMAIL_FORCE_SANDBOX }}"
          EOF
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and Push Container
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ env.REPOSITORY }}/app:${{ github.sha }}
          platforms: linux/amd64
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Deploy to Cloud Run
        run: |
          gcloud run deploy steelflow \
            --image ${{ env.REPOSITORY }}/app:${{ github.sha }} \
            --platform managed \
            --region ${{ env.REGION }} \
            --allow-unauthenticated \
            --env-vars-file=env.yaml