const { defineConfig } = require("eslint/config");

const typescriptEslint = require("@typescript-eslint/eslint-plugin");
// Import plugin is already included in eslint-config-next
// const _import = require("eslint-plugin-import");
// Removing architecture plugin import
// const architecture = require("eslint-plugin-architecture");

// No longer needed since we're not using fixupPluginRules
// const {
//     fixupPluginRules,
// } = require("@eslint/compat");

const tsParser = require("@typescript-eslint/parser");
const js = require("@eslint/js");

const { FlatCompat } = require("@eslint/eslintrc");

const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all,
});

module.exports = defineConfig([
  // Ignore patterns (replaces .eslintignore)
  {
    ignores: [
      // Next.js Build Output
      // These directories contain build artifacts that don't need linting
      "/.next/",
      "/out/",
      "/build/",

      // Dependencies
      // Third-party code that shouldn't be linted
      "/node_modules/",
      "/.pnp/",
      ".pnp.*",
      ".yarn/*",

      // Generated Files
      // Auto-generated files that shouldn't be linted
      "*.tsbuildinfo",
      "next-env.d.ts",
      "/src/types/supabase.ts",
      "/src/lib/schemas/schemas.ts",
      "/src/lib/schemas/schemas.d.ts",

      // Testing
      // Test coverage reports and configurations
      "/coverage/",
      "/jest.config.js",

      // Misc
      // Various configuration and system files
      ".DS_Store",
      "*.pem",
      ".env*",
      "!.env.example",

      // Debug Logs
      // Log files that don't need linting
      "npm-debug.log*",
      "yarn-debug.log*",
      "yarn-error.log*",
      ".pnpm-debug.log*",
      "logs/",
      "*.log",
      "dev-debug.log",

      // Vercel
      // Vercel deployment configuration
      ".vercel",

      // Editor directories
      // IDE and editor specific files
      ".idea/",
      ".vscode/",
      "*.suo",
      "*.ntvs*",
      "*.njsproj",
      "*.sln",
      "*.sw?",
      ".cursor/",

      // Supabase
      // Supabase temporary files and migrations
      "/supabase/.temp/",
      "/supabase/migrations/supabase/.temp/",

      // Scripts
      // Utility scripts that don't need to follow project architecture
      "/scripts/setup-email-polling.js",

      // Public directory
      // Static assets don't need linting
      "/public/",

      // Augment
      // Augment configuration files
      ".augment-guidelines",

      // Task files
      // Task management files
      "tasks.json",
      "tasks/",
      ".aider*",

      // Docker
      // Docker configuration files
      "Dockerfile",
      ".dockerignore",
    ],
  },
  {
    extends: compat.extends("next/core-web-vitals"),

    plugins: {
      "@typescript-eslint": typescriptEslint,
      // Removed import plugin to avoid duplication with eslint-config-next
      // Removing architecture plugin registration
    },

    languageOptions: {
      parser: tsParser,
      ecmaVersion: 2020,
      sourceType: "module",

      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
    },

    // Settings for import plugin are handled by eslint-config-next
    settings: {},

    rules: {

      // Removing architecture rules
      // "architecture/hooks-call-actions": "error",
      // "architecture/hooks-use-tanstack-query": "warn",
      // "architecture/no-duplicate-types-in-hooks": "warn",

      // Import rules are now handled through eslint-config-next
      // We'll need to configure these rules in a way that's compatible with the flat config
      // For now, we're removing them to fix the immediate error
    },
  },
]);
