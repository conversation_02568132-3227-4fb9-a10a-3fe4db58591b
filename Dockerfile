# syntax=docker/dockerfile:1.4
FROM node:20-alpine AS base

# Set environment variables
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=production
ENV SHELL=/bin/sh

# Install dependencies only when needed
FROM base AS deps
WORKDIR /app

# Install pnpm using npm (more reliable in Alpine)
RUN --mount=type=cache,id=npm-cache,target=/root/.npm \
    apk add --no-cache libc6-compat && \
    npm install -g pnpm@10.11.0

# Copy package.json and lockfile
COPY package.json pnpm-lock.yaml* pnpm-workspace.yaml* ./

# Install dependencies with cache mount
RUN --mount=type=cache,id=pnpm-store,target=/root/.pnpm-store \
    --mount=type=cache,id=npm-cache,target=/root/.npm \
    pnpm install --frozen-lockfile --prod=false

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app

# Install pnpm using npm (more reliable in Alpine)
RUN --mount=type=cache,id=npm-cache,target=/root/.npm \
    apk add --no-cache libc6-compat && \
    npm install -g pnpm@10.11.0

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/package.json ./package.json

# Copy source files
COPY . .

# Copy .env.production file for build-time environment variables
COPY .env.production .env.production

# Build the application
RUN pnpm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

# Install only production dependencies if needed
RUN apk add --no-cache libc6-compat

# Create non-root user
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs && \
    mkdir -p /app/.next && \
    chown -R nextjs:nodejs /app

# Set runtime environment variables
ENV NODE_ENV=production \
    PORT=3000 \
    HOSTNAME="0.0.0.0"

# Copy public folder
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# Copy standalone directory and static assets
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Copy .env.production file for runtime environment variables
COPY --from=builder --chown=nextjs:nodejs /app/.env.production ./.env.production

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 3000

# Start the application
CMD ["node", "server.js"]