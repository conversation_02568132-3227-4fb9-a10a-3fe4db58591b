# Docker files
Dockerfile
.dockerignore
docker-compose*.yml

# Dependencies
node_modules
.pnpm-store
.yarn
.pnp.*

# Build outputs
.next
out
build
dist

# Version control
.git
.github
.gitignore

# Environment variables
.env*
!.env.example
!.env.production

# Development files
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.DS_Store
*.pem
.vscode
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Testing
coverage
.nyc_output
test-results
playwright-report
playwright/.cache

# Documentation
README.md
docs
*.md
LICENSE

# Temporary files
.temp
.tmp
.cache
.cursor
.augment-guidelines

# Supabase
supabase/.temp
supabase/migrations/supabase/.temp/
.aider*

# Scripts
scripts/setup-email-polling.js

# Misc
tasks.json
tasks/