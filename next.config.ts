import type { NextConfig } from "next";

// Define the Next.js configuration
const nextConfig: NextConfig = {
  output: 'standalone',
  // Optimize output for production builds
  poweredByHeader: false,
  reactStrictMode: true,

  // @ts-ignore - Webpack config types are not fully compatible with Next.js types
  webpack: (config, { isServer }) => {
    // If client-side (browser), provide empty implementations for Node.js modules
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
        os: false,
        stream: false,
        buffer: false,
        util: false,
        http: false,
        https: false,
        zlib: false,
        net: false,
        tls: false,
        child_process: false,
      };

      // Handle @sendgrid/mail and other Node.js modules that shouldn't be included in client bundles
      config.module.rules.push({
        test: /node_modules\/@sendgrid\/helpers\/classes\/attachment\.js/,
        use: "null-loader",
      });
    }
    return config;
  },
};

export default nextConfig;
