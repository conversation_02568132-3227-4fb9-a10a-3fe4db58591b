"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Eye, RefreshCw, Trash2, KeyRound } from "lucide-react";

// Define the account type
type EmailAccount = {
  id: string;
  email: string;
  display_name: string;
  status: "active" | "paused" | "error" | "revoked" | "syncing";
  watch_expiry: string;
  last_sync_at: string;
  sync_error: string | null;
  sync_message: string | null;
  created_at: string;
};

// Format date
const formatDate = (dateString: string) => {
  if (!dateString) return "N/A";
  return new Date(dateString).toLocaleString();
};

// Create columns
export const columns: ColumnDef<EmailAccount>[] = [
  {
    accessorKey: "display_name",
    header: "Name",
    cell: ({ row }) => {
      const displayName = row.getValue("display_name") as string;
      const email = row.original.email;
      return (
        <div className="font-medium">{displayName || email.split("@")[0]}</div>
      );
    },
  },
  {
    accessorKey: "email",
    header: "Email",
    cell: ({ row }) => {
      const email = row.getValue("email") as string;
      return <div className="text-muted-foreground">{email}</div>;
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;

      switch (status) {
        case "active":
          return (
            <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
              Active
            </Badge>
          );
        case "paused":
          return (
            <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
              Paused
            </Badge>
          );
        case "error":
          return (
            <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
              Error
            </Badge>
          );
        case "revoked":
          return <Badge variant="outline">Revoked</Badge>;
        case "syncing":
          return (
            <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
              Syncing
            </Badge>
          );
        default:
          return <Badge>{status}</Badge>;
      }
    },
  },
  {
    accessorKey: "watch_expiry",
    header: "Last Poll",
    cell: ({ row }) => {
      const watchExpiry = row.getValue("watch_expiry") as string;
      return (
        <div className="text-muted-foreground">{formatDate(watchExpiry)}</div>
      );
    },
  },
  {
    accessorKey: "last_sync_at",
    header: "Last Sync",
    cell: ({ row }) => {
      const lastSync = row.getValue("last_sync_at") as string;
      return (
        <div className="text-muted-foreground">{formatDate(lastSync)}</div>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row, table }) => {
      const account = row.original;
      const refreshing =
        (table.options.meta as any)?.refreshing?.[account.id] || false;
      const isRevoked = account.status === "revoked";

      return (
        <div className="flex space-x-2 justify-end">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => (table.options.meta as any)?.onView?.(account)}
            title="View Details"
            className="h-8 w-8"
          >
            <Eye className="h-4 w-4" />
          </Button>

          {/* Re-authenticate button for all accounts */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => (table.options.meta as any)?.onReauth?.(account.id)}
            title="Re-authenticate"
            className="h-8 w-8 text-amber-600"
          >
            <KeyRound className="h-4 w-4" />
          </Button>

          {/* Poll button (only for non-revoked accounts) */}
          {!isRevoked && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() =>
                (table.options.meta as any)?.onRefresh?.(account.id)
              }
              disabled={refreshing}
              title="Poll Now"
              className="h-8 w-8"
            >
              <RefreshCw
                className={`h-4 w-4 ${refreshing ? "animate-spin" : ""}`}
              />
            </Button>
          )}

          <Button
            variant="ghost"
            size="icon"
            onClick={() => (table.options.meta as any)?.onDelete?.(account.id)}
            title="Delete Account"
            className="h-8 w-8"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      );
    },
  },
];
