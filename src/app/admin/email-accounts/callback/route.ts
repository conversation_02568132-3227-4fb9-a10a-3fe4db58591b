import { NextRequest, NextResponse } from "next/server";
import { emailPollingService } from "@/lib/services/email/email-polling.service";
import { emailStorageService } from "@/lib/services/email/email-storage.service";
import { getCurrentUser, isAdmin } from "@/lib/services/auth.service";
import { createLogger } from "@/lib/utils/logger/logger";
import { OAuth2Client } from "google-auth-library";

// Create a logger instance for this module
const logger = createLogger("EmailAccountsCallbackRoute");

/**
 * OAuth callback handler for Gmail
 *
 * This route handler receives the authorization code from Google after the user
 * authorizes our application. It exchanges the code for access and refresh
 * tokens, triggers initial email polling, and saves the account to the database.
 *
 * This follows the service layer architecture by using service functions for
 * business logic and database access.
 */
export async function GET(request: NextRequest) {
  try {
    logger.info("Gmail OAuth callback handler called");

    // Get the authorization code and state from the query parameters
    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get("code");
    const error = searchParams.get("error");
    const state = searchParams.get("state"); // This will contain the account ID for re-authentication

    // Check for errors
    if (error) {
      logger.error("OAuth error:", error);
      // Use PUBLIC_APP_URL environment variable if available, otherwise fallback to request URL
      const baseUrl = process.env.PUBLIC_APP_URL || request.url;
      return NextResponse.redirect(
        new URL("/admin/email-accounts?error=auth_failed", baseUrl),
      );
    }

    // Check for code
    if (!code) {
      logger.error("No authorization code provided");
      const baseUrl = process.env.PUBLIC_APP_URL || request.url;
      return NextResponse.redirect(
        new URL("/admin/email-accounts?error=no_code", baseUrl),
      );
    }

    // Get the current user using the auth service
    const user = await getCurrentUser();

    if (!user) {
      logger.error("No authenticated user found");
      const baseUrl = process.env.PUBLIC_APP_URL || request.url;
      return NextResponse.redirect(
        new URL("/admin/email-accounts?error=unauthorized", baseUrl),
      );
    }

    // Check if the user has admin role using the auth service
    const hasAdminRole = await isAdmin();

    if (!hasAdminRole) {
      logger.warn(
        `User ${user.id} attempted to add email account without admin role`,
      );
      const baseUrl = process.env.PUBLIC_APP_URL || request.url;
      return NextResponse.redirect(
        new URL("/admin/email-accounts?error=forbidden", baseUrl),
      );
    }

    // Exchange the code for tokens using the service
    logger.info("Exchanging code for tokens");
    const tokens = await emailPollingService.getTokens(code);

    if (!tokens.access_token || !tokens.refresh_token) {
      logger.error("Failed to get tokens");
      const baseUrl = process.env.PUBLIC_APP_URL || request.url;
      return NextResponse.redirect(
        new URL("/admin/email-accounts?error=token_failed", baseUrl),
      );
    }

    // Get the user's email address
    logger.info("Getting user profile from Gmail");
    const oauth2Client = new OAuth2Client();
    oauth2Client.setCredentials({
      ...tokens,
      expiry_date: Date.now() + 10 * 365 * 24 * 60 * 60 * 1000, // 10 years expiry
    });

    const gmail = (await import("googleapis")).google.gmail({
      version: "v1",
      auth: oauth2Client,
    });
    const profile = await gmail.users.getProfile({ userId: "me" });

    if (!profile.data.emailAddress) {
      logger.error("Failed to get email address");
      const baseUrl = process.env.PUBLIC_APP_URL || request.url;
      return NextResponse.redirect(
        new URL("/admin/email-accounts?error=profile_failed", baseUrl),
      );
    }

    const email = profile.data.emailAddress;
    const displayName = email.split("@")[0]; // Use the local part as display name

    // Calculate token expiry
    // If tokens.expiry_date is provided, it's likely milliseconds since epoch
    // Otherwise, set expiry to maximum (10 years)
    const expiryDate = tokens.expiry_date
      ? new Date(tokens.expiry_date)
      : new Date(Date.now() + 10 * 365 * 24 * 60 * 60 * 1000);

    logger.info(
      `Token expiry date set to: ${expiryDate.toISOString()}, original value: ${tokens.expiry_date}`,
    );

    let accountId: string;

    // Check if this is a re-authentication
    if (state) {
      try {
        // Update the existing account with new tokens
        logger.info(`Re-authenticating existing account with ID ${state}`);

        // Get the existing account to verify it exists
        const existingAccount = await emailStorageService.getAccountById(state);

        if (!existingAccount) {
          logger.error(
            `Account with ID ${state} not found for re-authentication`,
          );
          const baseUrl = process.env.PUBLIC_APP_URL || request.url;
          return NextResponse.redirect(
            new URL(
              "/admin/email-accounts?error=account_not_found",
              baseUrl,
            ),
          );
        }

        // Update the account with new tokens
        const updatedAccount = await emailStorageService.updateAccount(state, {
          access_token: tokens.access_token,
          refresh_token: tokens.refresh_token,
          token_expiry: expiryDate.toISOString(),
          status: "active", // Reset status to active
          sync_error: null, // Clear any previous errors
          last_full_sync_at: null, // Reset last_full_sync_at to force a full sync on next poll
        });

        accountId = updatedAccount.id;
        logger.info(`Account ${accountId} re-authenticated successfully`);
      } catch (updateError) {
        logger.error(
          `Error updating account for re-authentication: ${updateError}`,
        );
        const baseUrl = process.env.PUBLIC_APP_URL || request.url;
        return NextResponse.redirect(
          new URL("/admin/email-accounts?error=update_failed", baseUrl),
        );
      }
    } else {
      // Create a new account
      logger.info(`Creating new email account for ${email}`);
      const newAccount = await emailStorageService.createAccount(
        email,
        displayName,
        tokens.access_token,
        tokens.refresh_token,
        expiryDate,
        user.id,
      );

      accountId = newAccount.id;
      logger.info(`New account ${accountId} created successfully`);
    }

    // Check if this is a re-authentication or a new account
    const isReauth = !!state;
    const successMessage = isReauth ? "reauth_success" : "add_success";

    // For new accounts, start initial polling in the background
    if (!isReauth) {
      logger.info(
        `Starting background initial polling for new account ${accountId}`,
      );

      // Update account status to indicate sync is in progress
      await emailStorageService.updateAccount(accountId, {
        status: "syncing",
        sync_message: "Initial sync in progress",
      });

      // Start polling in the background without awaiting it
      // This allows us to redirect the user immediately
      emailPollingService
        .pollAccount(accountId, tokens.refresh_token)
        .then((result) => {
          logger.info(
            `Background polling completed for account ${accountId}, processed ${result.processed || 0} messages`,
          );
          // Update account status after polling completes
          return emailStorageService.updateAccount(accountId, {
            status: "active",
            sync_message: null,
          });
        })
        .catch((error) => {
          logger.error(
            `Error in background polling for account ${accountId}:`,
            error,
          );
          // Update account status to error if polling fails
          return emailStorageService.updateAccount(accountId, {
            status: "error",
            sync_error: error.message || "Unknown error during initial sync",
          });
        });

      // Redirect with syncing parameter for new accounts
      logger.info(
        `Email account added successfully, redirecting to email accounts page`,
      );
      const baseUrl = process.env.PUBLIC_APP_URL || request.url;
      return NextResponse.redirect(
        new URL(
          `/admin/email-accounts?success=${successMessage}&syncing=true`,
          baseUrl,
        ),
      );
    } else {
      // For re-authentication, just redirect without starting a full sync
      logger.info(
        `Email account re-authenticated successfully, redirecting to email accounts page`,
      );
      const baseUrl = process.env.PUBLIC_APP_URL || request.url;
      return NextResponse.redirect(
        new URL(`/admin/email-accounts?success=${successMessage}`, baseUrl),
      );
    }
  } catch (error) {
    logger.error("Error in Gmail OAuth callback:", error);
    const baseUrl = process.env.PUBLIC_APP_URL || request.url;
    return NextResponse.redirect(
      new URL("/admin/email-accounts?error=server_error", baseUrl),
    );
  }
}
