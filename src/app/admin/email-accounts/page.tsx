"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  AlertCircle,
  CheckCircle,
  Mail,
  Plus,
  RefreshCw,
  RotateCw,
  Trash2,
  KeyRound,
} from "lucide-react";
import { toast } from "sonner";
import { DataTable } from "./data-table";
import { columns } from "./columns";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  getEmailAccountsAction,
  getEmailAuthUrlAction,
  refreshWatchAction,
  deleteEmailAccountAction,
} from "@/lib/actions/email-accounts.actions";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("EmailAccountsPage");

// Define the account type
type EmailAccount = {
  id: string;
  email: string;
  display_name: string;
  status: "active" | "paused" | "error" | "revoked" | "syncing";
  watch_expiry: string;
  last_sync_at: string;
  sync_error: string | null;
  sync_message: string | null;
  created_at: string;
};

export default function EmailAccountsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [accounts, setAccounts] = useState<EmailAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedAccount, setSelectedAccount] = useState<EmailAccount | null>(
    null,
  );
  const [refreshing, setRefreshing] = useState<Record<string, boolean>>({});

  // Check for success or error messages from the OAuth flow
  useEffect(() => {
    const success = searchParams.get("success");
    const errorMsg = searchParams.get("error");
    const syncing = searchParams.get("syncing");

    if (success) {
      switch (success) {
        case "true":
        case "add_success":
          if (syncing === "true") {
            toast.success("Email account added successfully", {
              description:
                "Initial sync is in progress. This may take some time depending on the number of emails.",
              duration: 8000,
            });
          } else {
            toast.success("Email account added successfully");
          }
          break;
        case "reauth_success":
          // For re-authentication, we don't do a full sync, so we don't need to show the syncing message
          toast.success("Email account re-authenticated successfully", {
            description: "Authentication tokens have been refreshed.",
            duration: 5000,
          });
          break;
        default:
          toast.success("Operation completed successfully");
      }
    }

    if (errorMsg) {
      let message = "Failed to add email account";

      switch (errorMsg) {
        case "auth_failed":
          message = "Authentication failed. Please try again.";
          break;
        case "no_code":
          message = "No authorization code provided.";
          break;
        case "unauthorized":
          message = "You are not authorized to perform this action.";
          break;
        case "token_failed":
          message = "Failed to get access tokens.";
          break;
        case "profile_failed":
          message = "Failed to get email profile.";
          break;
        case "account_not_found":
          message = "Account not found for re-authentication.";
          break;
        case "update_failed":
          message = "Failed to update account during re-authentication.";
          break;
        case "server_error":
          message = "Server error occurred. Please try again.";
          break;
      }

      toast.error(message);
    }
  }, [searchParams]);

  // Fetch accounts
  const fetchAccounts = async () => {
    try {
      setLoading(true);
      const result = await getEmailAccountsAction();

      if (result.success) {
        setAccounts(result.data);
        setError(null);
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      logger.error("Error fetching accounts:", err);
      setError("Failed to load email accounts. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchAccounts();
  }, []);

  // Add a new account
  const handleAddAccount = async () => {
    try {
      // Get the OAuth URL from the server action
      const result = await getEmailAuthUrlAction();

      if (result.success) {
        // Navigate to the OAuth URL
        router.push(result.data);
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      logger.error("Error getting auth URL:", err);
      toast.error("Failed to add account. Please try again.");
    }
  };

  // Re-authenticate an existing account
  const handleReauthAccount = async (accountId: string) => {
    try {
      // Get the OAuth URL from the server action with the account ID
      const result = await getEmailAuthUrlAction(accountId);

      if (result.success) {
        // Navigate to the OAuth URL
        router.push(result.data);
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      logger.error("Error getting re-auth URL:", err);
      toast.error("Failed to re-authenticate account. Please try again.");
    }
  };

  // Trigger a full sync after re-authentication
  const handleFullSyncAfterReauth = async (accountId: string) => {
    try {
      setRefreshing((prev) => ({ ...prev, [accountId]: true }));

      // Call the refresh action with forceFullSync=true
      const result = await refreshWatchAction(accountId, true);

      if (!result.success) {
        toast.error(result.error || "Failed to perform full sync");
        logger.error("Error performing full sync:", result.error);
        return;
      }

      toast.success("Full sync completed successfully");
      fetchAccounts(); // Refresh the account list
    } catch (err) {
      logger.error("Error performing full sync:", err);
      toast.error("Failed to perform full sync. Please try again.");
    } finally {
      setRefreshing((prev) => ({ ...prev, [accountId]: false }));
    }
  };

  // Poll for new emails for an account
  const handleRefreshWatch = async (accountId: string) => {
    try {
      setRefreshing((prev) => ({ ...prev, [accountId]: true }));

      const result = await refreshWatchAction(accountId);

      if (!result.success) {
        // Display the specific error message from the server action
        toast.error(result.error || "Failed to poll for new emails", {
          description: result.error?.includes("reconnect")
            ? "You need to re-authenticate this account to continue using it."
            : undefined,
          action: result.error?.includes("reconnect")
            ? {
                label: "Re-authenticate",
                onClick: () => handleReauthAccount(accountId),
              }
            : undefined,
          duration: 8000, // Show longer for important errors
        });
        logger.error("Error polling for new emails:", result.error);
        fetchAccounts(); // Refresh the account list to show updated status
        return;
      }

      toast.success(result.message || "Polling completed successfully");
      fetchAccounts(); // Refresh the account list
    } catch (err) {
      logger.error("Error polling for new emails:", err);

      // Show a more specific error message if available
      const errorMessage =
        err instanceof Error
          ? err.message
          : "Failed to poll for new emails. Please try again.";
      toast.error(errorMessage);
    } finally {
      setRefreshing((prev) => ({ ...prev, [accountId]: false }));
    }
  };

  // Delete an account
  const handleDeleteAccount = async (accountId: string) => {
    if (
      !confirm(
        "Are you sure you want to delete this account? This will stop monitoring emails for this account.",
      )
    ) {
      return;
    }

    try {
      const result = await deleteEmailAccountAction(accountId);

      if (!result.success) {
        // Display the specific error message from the server action
        toast.error(result.error || "Failed to delete account");
        logger.error("Error deleting account:", result.error);
        return;
      }

      toast.success("Account deleted successfully");
      fetchAccounts(); // Refresh the account list

      if (selectedAccount?.id === accountId) {
        setSelectedAccount(null);
      }
    } catch (err) {
      logger.error("Error deleting account:", err);

      // Show a more specific error message if available
      const errorMessage =
        err instanceof Error
          ? err.message
          : "Failed to delete account. Please try again.";
      toast.error(errorMessage);
    }
  };

  // View account details
  const handleViewAccount = (account: EmailAccount) => {
    setSelectedAccount(account);
  };

  // Render status badge
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            Active
          </Badge>
        );
      case "paused":
        return (
          <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
            Paused
          </Badge>
        );
      case "error":
        return (
          <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
            Error
          </Badge>
        );
      case "revoked":
        return (
          <div className="flex flex-col gap-1">
            <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
              Access Revoked
            </Badge>
            <span className="text-xs text-red-600">Needs reconnection</span>
          </div>
        );
      case "syncing":
        return (
          <div className="flex flex-col gap-1">
            <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
              Syncing
            </Badge>
            <span className="text-xs text-blue-600">
              Initial sync in progress
            </span>
          </div>
        );
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleString();
  };

  // Render loading state
  if (loading && accounts.length === 0) {
    return (
      <div className="container mx-auto py-6 space-y-4">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Email Accounts</h1>
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="bg-white rounded-lg shadow">
          <Skeleton className="h-[400px] w-full" />
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Email Accounts</h1>
        <Button
          onClick={handleAddAccount}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Account
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="bg-white rounded-lg shadow">
        <DataTable
          columns={columns}
          data={accounts}
          onRefresh={handleRefreshWatch}
          onDelete={handleDeleteAccount}
          onView={handleViewAccount}
          onReauth={handleReauthAccount}
          refreshing={refreshing}
        />
      </div>

      {selectedAccount && (
        <div className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Mail className="mr-2 h-5 w-5" />
                {selectedAccount.display_name || selectedAccount.email}
              </CardTitle>
              <CardDescription>{selectedAccount.email}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium">Status</h3>
                  <div className="mt-1">
                    {renderStatusBadge(selectedAccount.status)}
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-medium">Last Poll</h3>
                  <div className="mt-1">
                    {formatDate(selectedAccount.watch_expiry)}
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-medium">Last Sync</h3>
                  <div className="mt-1">
                    {formatDate(selectedAccount.last_sync_at)}
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-medium">Added On</h3>
                  <div className="mt-1">
                    {formatDate(selectedAccount.created_at)}
                  </div>
                </div>
              </div>

              {selectedAccount.sync_error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Sync Error</AlertTitle>
                  <AlertDescription>
                    {selectedAccount.sync_error}
                  </AlertDescription>
                </Alert>
              )}

              {selectedAccount.sync_message && !selectedAccount.sync_error && (
                <Alert>
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  <AlertTitle>Sync Status</AlertTitle>
                  <AlertDescription>
                    {selectedAccount.sync_message}
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
            <CardFooter className="flex flex-wrap gap-2 justify-between">
              <div className="flex flex-wrap gap-2">
                {/* Re-authenticate button for all accounts */}
                <Button
                  variant="default"
                  className="bg-amber-600 hover:bg-amber-700"
                  onClick={() => handleReauthAccount(selectedAccount.id)}
                >
                  <KeyRound className="mr-2 h-4 w-4" />
                  Re-authenticate
                </Button>

                {/* Poll button (only for non-revoked and non-syncing accounts) */}
                {selectedAccount.status !== "revoked" &&
                  selectedAccount.status !== "syncing" && (
                    <>
                      <Button
                        variant="outline"
                        onClick={() => handleRefreshWatch(selectedAccount.id)}
                        disabled={refreshing[selectedAccount.id]}
                      >
                        {refreshing[selectedAccount.id] ? (
                          <>
                            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                            Polling...
                          </>
                        ) : (
                          <>
                            <RefreshCw className="mr-2 h-4 w-4" />
                            Poll Now
                          </>
                        )}
                      </Button>

                      <Button
                        variant="outline"
                        onClick={() =>
                          handleFullSyncAfterReauth(selectedAccount.id)
                        }
                        disabled={refreshing[selectedAccount.id]}
                      >
                        {refreshing[selectedAccount.id] ? (
                          <>
                            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                            Full Sync...
                          </>
                        ) : (
                          <>
                            <RotateCw className="mr-2 h-4 w-4" />
                            Force Full Sync
                          </>
                        )}
                      </Button>
                    </>
                  )}
              </div>

              <Button
                variant="destructive"
                onClick={() => handleDeleteAccount(selectedAccount.id)}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Account
              </Button>
            </CardFooter>
          </Card>
        </div>
      )}
    </div>
  );
}
