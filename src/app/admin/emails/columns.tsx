"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, Paperclip, Star } from "lucide-react";

// Define the email message type
type EmailMessage = {
  id: string;
  account_id: string;
  gmail_id: string;
  gmail_thread_id: string;
  subject: string;
  snippet: string;
  body_html: string;
  body_text: string;
  from_email: string;
  from_name: string;
  to_recipients: { name: string; email: string }[];
  cc_recipients: { name: string; email: string }[];
  bcc_recipients: { name: string; email: string }[];
  reply_to: string;
  date_received: string;
  labels: string[];
  is_read: boolean;
  is_starred: boolean;
  is_important: boolean;
  is_trash: boolean;
  rfq_id: string | null;
  provider_id: string | null;
  category: string | null;
  created_at: string;
  has_attachments?: boolean;
};

// Format date
const formatDate = (dateString: string) => {
  if (!dateString) return "N/A";

  const date = new Date(dateString);
  const now = new Date();
  const yesterday = new Date(now);
  yesterday.setDate(yesterday.getDate() - 1);

  // If today, show time only
  if (date.toDateString() === now.toDateString()) {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  }

  // If yesterday, show "Yesterday"
  if (date.toDateString() === yesterday.toDateString()) {
    return "Yesterday";
  }

  // If this year, show month and day
  if (date.getFullYear() === now.getFullYear()) {
    return date.toLocaleDateString([], { month: "short", day: "numeric" });
  }

  // Otherwise, show full date
  return date.toLocaleDateString([], {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

// Create columns
export const columns: ColumnDef<EmailMessage>[] = [
  {
    id: "starred",
    cell: ({ row }) => {
      const isStarred = row.original.is_starred;

      return isStarred ? (
        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
      ) : null;
    },
    size: 40,
  },
  {
    id: "from",
    header: "From",
    cell: ({ row }) => {
      const fromName = row.original.from_name;
      const fromEmail = row.original.from_email;

      return (
        <div className="font-medium">{fromName || fromEmail.split("@")[0]}</div>
      );
    },
    size: 200,
  },
  {
    id: "subject",
    header: "Subject",
    cell: ({ row }) => {
      const subject = row.original.subject;
      const snippet = row.original.snippet;
      const hasAttachments = row.original.has_attachments;

      return (
        <div className="flex flex-col">
          <div className="font-medium">{subject || "(No Subject)"}</div>
          <div className="text-sm text-muted-foreground truncate">
            {snippet}
          </div>
          {hasAttachments && (
            <div className="flex items-center mt-1">
              <Paperclip className="h-3 w-3 mr-1" />
              <span className="text-xs">Attachment</span>
            </div>
          )}
        </div>
      );
    },
    size: 400,
  },
  {
    accessorKey: "date_received",
    header: "Date",
    cell: ({ row }) => {
      const date = row.getValue("date_received") as string;
      return <div className="text-right">{formatDate(date)}</div>;
    },
    size: 100,
  },
  {
    id: "actions",
    cell: ({ row, table }) => {
      const email = row.original;

      return (
        <div className="flex justify-end">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => (table.options.meta as any)?.onView?.(email)}
            title="View Email"
          >
            <Eye className="h-4 w-4" />
          </Button>
        </div>
      );
    },
    size: 50,
  },
];
