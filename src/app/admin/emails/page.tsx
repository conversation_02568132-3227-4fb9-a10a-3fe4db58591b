"use client";

import { useState, useEffect, useCallback } from "react";
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  AlertCircle,
  Download,
  FileText,
  Mail,
  Paperclip,
  RefreshCw,
  Reply,
  Star,
  Trash2,
} from "lucide-react";
import { toast } from "sonner";
import { DataTable } from "./data-table";
import { columns } from "./columns";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { getEmailAccountsAction } from "@/lib/actions/email-accounts.actions";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("EmailsPage");

// Define the email message type
type EmailMessage = {
  id: string;
  account_id: string;
  gmail_id: string;
  gmail_thread_id: string;
  subject: string;
  snippet: string;
  body_html: string;
  body_text: string;
  from_email: string;
  from_name: string;
  to_recipients: { name: string; email: string }[];
  cc_recipients: { name: string; email: string }[];
  bcc_recipients: { name: string; email: string }[];
  reply_to: string;
  date_received: string;
  labels: string[];
  is_read: boolean;
  is_starred: boolean;
  is_important: boolean;
  is_trash: boolean;
  rfq_id: string | null;
  provider_id: string | null;
  category: string | null;
  created_at: string;
};

// Define the attachment type
type EmailAttachment = {
  id: string;
  message_id: string;
  gmail_attachment_id: string;
  filename: string;
  content_type: string;
  size: number;
  storage_path: string | null;
  created_at: string;
};

// Define the account type
type EmailAccount = {
  id: string;
  email: string;
  display_name: string;
};

export default function EmailsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [emails, setEmails] = useState<EmailMessage[]>([]);
  const [accounts, setAccounts] = useState<EmailAccount[]>([]);
  const [selectedAccount, setSelectedAccount] = useState<string>("all");
  const [selectedEmail, setSelectedEmail] = useState<EmailMessage | null>(null);
  const [attachments, setAttachments] = useState<EmailAttachment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalEmails, setTotalEmails] = useState(0);

  // Fetch accounts
  const fetchAccounts = async () => {
    try {
      const result = await getEmailAccountsAction();

      if (result.success) {
        setAccounts(result.data);
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      logger.error("Error fetching accounts:", err);
      toast.error("Failed to load email accounts");
    }
  };

  // Fetch emails
  const fetchEmails = useCallback(async () => {
    try {
      setLoading(true);

      let url = `/api/v1/email/messages?page=${page}&limit=20`;
      if (selectedAccount !== "all") {
        url += `&accountId=${selectedAccount}`;
      }

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error("Failed to fetch emails");
      }

      const data = await response.json();
      setEmails(data.messages);
      setTotalPages(data.totalPages);
      setTotalEmails(data.total);
      setError(null);
    } catch (err) {
      logger.error("Error fetching emails:", err);
      setError("Failed to load emails. Please try again.");
    } finally {
      setLoading(false);
    }
  }, [page, selectedAccount, setLoading, setEmails, setTotalPages, setTotalEmails, setError]);

  // Fetch email details
  const fetchEmailDetails = async (emailId: string) => {
    try {
      const response = await fetch(`/api/v1/email/messages/${emailId}`);

      if (!response.ok) {
        throw new Error("Failed to fetch email details");
      }

      const data = await response.json();
      setSelectedEmail(data);

      // Fetch attachments
      fetchAttachments(emailId);
    } catch (err) {
      logger.error("Error fetching email details:", err);
      toast.error("Failed to load email details");
    }
  };

  // Fetch attachments
  const fetchAttachments = async (emailId: string) => {
    try {
      const response = await fetch(
        `/api/v1/email/messages/${emailId}/attachments`,
      );

      if (!response.ok) {
        throw new Error("Failed to fetch attachments");
      }

      const data = await response.json();
      setAttachments(data);
    } catch (err) {
      logger.error("Error fetching attachments:", err);
      toast.error("Failed to load attachments");
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchAccounts();
  }, []);

  // Fetch emails when account or page changes
  useEffect(() => {
    fetchEmails();
  }, [selectedAccount, page, fetchEmails]);

  // Handle account change
  const handleAccountChange = (value: string) => {
    setSelectedAccount(value);
    setPage(1); // Reset to first page
  };

  // Handle email selection
  const handleViewEmail = (email: EmailMessage) => {
    fetchEmailDetails(email.id);
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleString();
  };

  // Format recipients
  const formatRecipients = (recipients: { name: string; email: string }[]) => {
    if (!recipients || recipients.length === 0) return "None";

    return recipients
      .map((r) => {
        if (r.name) {
          return `${r.name} <${r.email}>`;
        }
        return r.email;
      })
      .join(", ");
  };

  // Download attachment
  const handleDownloadAttachment = async (
    attachmentId: string,
    filename: string,
  ) => {
    try {
      const response = await fetch(
        `/api/v1/email/attachments/${attachmentId}/download`,
      );

      if (!response.ok) {
        throw new Error("Failed to download attachment");
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      logger.error("Error downloading attachment:", err);
      toast.error("Failed to download attachment");
    }
  };

  // Render loading state
  if (loading && emails.length === 0) {
    return (
      <div className="container mx-auto py-6 space-y-4">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Emails</h1>
          <Skeleton className="h-10 w-32" />
        </div>
        <Skeleton className="h-[400px] w-full" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Emails</h1>
        <div className="flex items-center space-x-2">
          <Label htmlFor="account-select">Account:</Label>
          <Select value={selectedAccount} onValueChange={handleAccountChange}>
            <SelectTrigger id="account-select" className="w-[200px]">
              <SelectValue placeholder="Select account" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Accounts</SelectItem>
              {accounts.map((account) => (
                <SelectItem key={account.id} value={account.id}>
                  {account.display_name || account.email}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="emails">
        <TabsList>
          <TabsTrigger value="emails">Emails</TabsTrigger>
          {selectedEmail && (
            <TabsTrigger value="details">Email Details</TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="emails" className="space-y-4">
          <DataTable
            columns={columns}
            data={emails}
            onView={handleViewEmail}
            totalPages={totalPages}
            currentPage={page}
            onPageChange={setPage}
            totalItems={totalEmails}
          />
        </TabsContent>

        {selectedEmail && (
          <TabsContent value="details">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>
                    {selectedEmail.subject || "(No Subject)"}
                  </CardTitle>
                  <div className="flex space-x-2">
                    {selectedEmail.is_starred && (
                      <Badge variant="outline">
                        <Star className="h-3 w-3 mr-1 fill-yellow-400 text-yellow-400" />
                        Starred
                      </Badge>
                    )}
                    {selectedEmail.labels?.includes("IMPORTANT") && (
                      <Badge variant="outline" className="bg-red-50">
                        Important
                      </Badge>
                    )}
                  </div>
                </div>
                <CardDescription>
                  From:{" "}
                  {selectedEmail.from_name
                    ? `${selectedEmail.from_name} <${selectedEmail.from_email}>`
                    : selectedEmail.from_email}
                </CardDescription>
                <div className="text-sm text-muted-foreground">
                  {formatDate(selectedEmail.date_received)}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div>
                    <span className="font-medium">To:</span>{" "}
                    {formatRecipients(selectedEmail.to_recipients)}
                  </div>
                  {selectedEmail.cc_recipients?.length > 0 && (
                    <div>
                      <span className="font-medium">CC:</span>{" "}
                      {formatRecipients(selectedEmail.cc_recipients)}
                    </div>
                  )}
                </div>

                <div className="border-t pt-4">
                  {selectedEmail.body_html ? (
                    <div
                      dangerouslySetInnerHTML={{
                        __html: selectedEmail.body_html,
                      }}
                    />
                  ) : (
                    <pre className="whitespace-pre-wrap">
                      {selectedEmail.body_text}
                    </pre>
                  )}
                </div>

                {attachments.length > 0 && (
                  <div className="border-t pt-4">
                    <h3 className="font-medium mb-2">
                      Attachments ({attachments.length})
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {attachments.map((attachment) => (
                        <div
                          key={attachment.id}
                          className="flex items-center p-2 border rounded-md"
                        >
                          <Paperclip className="h-4 w-4 mr-2" />
                          <span className="flex-1 truncate">
                            {attachment.filename}
                          </span>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() =>
                              handleDownloadAttachment(
                                attachment.id,
                                attachment.filename,
                              )
                            }
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
