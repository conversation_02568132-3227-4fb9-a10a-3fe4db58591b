import { <PERSON>ada<PERSON> } from "next";
import { EmailSettingsForm } from "./_components/email-settings-form";
import { getEmailSettingsAction } from "@/lib/actions/email-settings.actions";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";

// Mark this route as dynamically rendered since it uses cookies
export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: "Email Settings | Admin Dashboard",
  description: "Manage email settings for the application",
};

/**
 * Email Settings Page
 *
 * This page allows administrators to manage email settings.
 */
export default async function EmailSettingsPage() {
  // Get current email settings
  const settingsResponse = await getEmailSettingsAction();

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Email Settings</h1>
        <p className="text-muted-foreground">
          Configure email settings for the application
        </p>
      </div>

      {!settingsResponse.success || !settingsResponse.data ? (
        <Alert variant="destructive" className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Email functionality is disabled</AlertTitle>
          <AlertDescription>
            Email settings are not configured. Please complete the form below to enable email functionality.
            Until settings are configured, all email-related features will be unavailable.
          </AlertDescription>
        </Alert>
      ) : null}

      <Card>
        <CardHeader>
          <CardTitle>Email Configuration</CardTitle>
          <CardDescription>
            Manage email settings such as sender information, sandbox mode, and email content
          </CardDescription>
        </CardHeader>
        <CardContent>
          <EmailSettingsForm initialData={settingsResponse.success ? settingsResponse.data : null} />
        </CardContent>
      </Card>
    </div>
  );
}
