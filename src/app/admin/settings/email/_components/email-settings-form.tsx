"use client";

import { useState } from "react";
import { use<PERSON>out<PERSON> } from "next/navigation";
import { use<PERSON><PERSON>, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "@/components/ui/sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { updateEmailSettingsAction } from "@/lib/actions/email-settings.actions";
import { EmailSettings, EmailSettingsFormSchema, EmailSettingsFormValues } from "@/lib/schemas";
import { Loader2 } from "lucide-react";

// No longer importing defaults as we're enforcing explicit configuration

interface EmailSettingsFormProps {
  initialData: EmailSettings | null;
}

/**
 * Email Settings Form
 *
 * This component renders a form for updating email settings.
 * It handles form validation, submission, and error/success messages.
 */
export function EmailSettingsForm({ initialData }: EmailSettingsFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with React Hook Form

  // Define form values with proper types
  // We need to cast the form values to the correct types
  // The bcc_recipients field is a special case - it's a string in the form but transforms to string[] in the schema
  const defaultValues: Partial<EmailSettingsFormValues> = {
    // Use initialData if available, otherwise use empty values
    // This forces the admin to explicitly configure settings
    company_name: initialData?.company_name || "",
    company_logo_url: initialData?.company_logo_url || "",
    sandbox_mode: initialData?.sandbox_mode ?? true,
    sandbox_recipient_email: initialData?.sandbox_recipient_email || "",
    default_sender_email: initialData?.default_sender_email || "",
    default_sender_name: initialData?.default_sender_name || "",
    reply_to_email: initialData?.reply_to_email || "",
    // For bcc_recipients, we'll handle it in the form's render method
    // The schema will transform the string to an array
    bcc_recipients: initialData?.bcc_recipients ? initialData.bcc_recipients.join('\n') : "",
    email_signature: initialData?.email_signature || "",
    email_footer_text: initialData?.email_footer_text || "",
  } as any;

  const form = useForm<EmailSettingsFormValues>({
    resolver: zodResolver(EmailSettingsFormSchema),
    defaultValues,
    mode: "onChange",
  });

  // Handle form submission
  const onSubmit: SubmitHandler<EmailSettingsFormValues> = async (data) => {
    setIsSubmitting(true);

    try {
      // Create FormData object
      const formData = new FormData();

      // Add all form fields to FormData
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (typeof value === 'boolean') {
            formData.append(key, value.toString());
          } else if (Array.isArray(value)) {
            // Handle array values (like bcc_recipients)
            formData.append(key, JSON.stringify(value));
          } else {
            formData.append(key, value);
          }
        }
      });

      // Add ID if we're updating existing settings
      if (initialData?.id) {
        formData.append("id", initialData.id);
      }

      // Submit the form
      const result = await updateEmailSettingsAction(formData);

      if (result.success) {
        toast.success("Email settings updated successfully");
        router.refresh(); // Refresh the page to show updated data
      } else {
        toast.error(result.error || "Failed to update email settings");

        // Handle field errors
        if (result.fieldErrors) {
          Object.entries(result.fieldErrors).forEach(([field, messages]) => {
            if (messages && messages.length > 0) {
              form.setError(field as any, {
                type: "manual",
                message: messages[0],
              });
            }
          });
        }
      }
    } catch (error) {
      toast.error("An unexpected error occurred");
      console.error("Email settings update error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit as any)} className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Company Information</h3>

          <FormField
            control={form.control as any}
            name="company_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel required>Company Name</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Your Company" required />
                </FormControl>
                <FormDescription>
                  This name will appear in email headers and footers
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control as any}
            name="company_logo_url"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company Logo URL</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    value={field.value || ''}
                    placeholder="https://example.com/logo.png"
                  />
                </FormControl>
                <FormDescription>
                  URL to your company logo image (optional)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Email Configuration</h3>

          <FormField
            control={form.control as any}
            name="sandbox_mode"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Sandbox Mode</FormLabel>
                  <FormDescription>
                    When enabled, emails will be redirected to a test address
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control as any}
            name="sandbox_recipient_email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Sandbox Recipient Email</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    value={field.value || ''}
                    placeholder="<EMAIL>"
                  />
                </FormControl>
                <FormDescription>
                  Email address to redirect all emails to when sandbox mode is enabled
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control as any}
            name="default_sender_email"
            render={({ field }) => (
              <FormItem>
                <FormLabel required>Default Sender Email</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="<EMAIL>" required />
                </FormControl>
                <FormDescription>
                  Email address that will appear as the sender
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control as any}
            name="default_sender_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel required>Default Sender Name</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Your Company" required />
                </FormControl>
                <FormDescription>
                  Name that will appear as the sender
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control as any}
            name="reply_to_email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Reply-To Email</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    value={field.value || ''}
                    placeholder="<EMAIL>"
                  />
                </FormControl>
                <FormDescription>
                  Email address for recipients to reply to (optional)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control as any}
            name="bcc_recipients"
            render={({ field }) => (
              <FormItem>
                <FormLabel>BCC Recipients</FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    value={field.value || ''}
                    placeholder="<EMAIL>&#10;<EMAIL>"
                    rows={3}
                  />
                </FormControl>
                <FormDescription>
                  Email addresses to BCC on all outgoing emails (one per line or comma-separated, optional)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Email Content</h3>

          <FormField
            control={form.control as any}
            name="email_signature"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email Signature</FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    value={field.value || ''}
                    placeholder="Best regards, Your Company Team"
                    rows={3}
                  />
                </FormControl>
                <FormDescription>
                  Signature text to include in emails (optional)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control as any}
            name="email_footer_text"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email Footer Text</FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    value={field.value || ''}
                    placeholder="This email was sent by Your Company. All rights reserved."
                    rows={3}
                  />
                </FormControl>
                <FormDescription>
                  Text to display in email footers
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            "Save Settings"
          )}
        </Button>
      </form>
    </Form>
  );
}
