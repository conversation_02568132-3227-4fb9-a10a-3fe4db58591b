import { <PERSON>ada<PERSON> } from "next";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { redirect } from "next/navigation";

export const metadata: Metadata = {
  title: "Settings | Admin Dashboard",
  description: "Manage application settings",
};

/**
 * Admin Settings Layout
 * 
 * This layout provides a tabbed interface for different settings sections.
 */
export default function AdminSettingsLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { slug?: string[] };
}) {
  // Get the current tab from the URL
  const currentTab = params.slug?.[0] || "email";
  
  // Define available tabs
  const tabs = [
    { id: "email", label: "Email" },
    // Add more tabs here as needed
  ];
  
  // Validate the current tab
  const isValidTab = tabs.some(tab => tab.id === currentTab);
  
  // Redirect to the first tab if the current tab is invalid
  if (!isValidTab) {
    redirect("/admin/settings/email");
  }
  
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground">
          Manage application settings
        </p>
      </div>
      
      <Tabs defaultValue={currentTab} className="space-y-4">
        <TabsList>
          {tabs.map(tab => (
            <TabsTrigger 
              key={tab.id} 
              value={tab.id}
              asChild
            >
              <a href={`/admin/settings/${tab.id}`}>{tab.label}</a>
            </TabsTrigger>
          ))}
        </TabsList>
        
        <TabsContent value={currentTab} className="space-y-4">
          {children}
        </TabsContent>
      </Tabs>
    </div>
  );
}
