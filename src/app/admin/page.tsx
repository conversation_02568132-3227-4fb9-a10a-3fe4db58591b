import { Metadata } from "next";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { listApiKeysAction } from "@/lib/actions/admin-keys.actions";
import { getCurrentUserAction } from "@/lib/actions/auth.actions";
import { Key, Users, ShieldCheck, Mail, MailQuestion } from "lucide-react";
import { createClient } from "@/lib/supabase/server/server";

// Mark this route as dynamically rendered since it uses cookies
export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: "Admin Dashboard | Steelflow",
  description: "Admin dashboard for Steelflow platform",
};

export default async function AdminDashboardPage() {
  // Get current user
  const userResponse = await getCurrentUserAction();
  const user = userResponse.success ? userResponse.data : null;

  // Get API keys count
  const apiKeysResponse = await listApiKeysAction();
  const apiKeysCount = apiKeysResponse.success
    ? apiKeysResponse.data.length
    : 0;

  // Get email accounts and messages count
  const supabase = await createClient();

  // Get email accounts count
  const { count: emailAccountsCount } = await supabase
    .from("watched_email_accounts")
    .select("*", { count: "exact", head: true });

  // Get email messages count
  const { count: emailMessagesCount } = await supabase
    .from("email_messages")
    .select("*", { count: "exact", head: true });

  return (
    <div>
      <h1 className="text-3xl font-bold tracking-tight mb-6">
        Admin Dashboard
      </h1>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">API Keys</CardTitle>
            <Key className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{apiKeysCount}</div>
            <p className="text-xs text-muted-foreground">
              Active API keys in the system
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Email Accounts
            </CardTitle>
            <MailQuestion className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{emailAccountsCount || 0}</div>
            <p className="text-xs text-muted-foreground">
              Watched email accounts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Email Messages
            </CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{emailMessagesCount || 0}</div>
            <p className="text-xs text-muted-foreground">
              Stored email messages
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Admin Access</CardTitle>
            <ShieldCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">Enabled</div>
            <p className="text-xs text-muted-foreground">
              You have admin privileges
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">User</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{user?.email}</div>
            <p className="text-xs text-muted-foreground">
              Logged in as administrator
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>Admin Functions</CardTitle>
            <CardDescription>
              Use the sidebar to navigate to different admin functions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="list-disc pl-5 space-y-2">
              <li>
                <strong>API Keys:</strong> Manage API keys for the platform
              </li>
              <li>
                <strong>Users:</strong> Manage user accounts and roles
              </li>
              <li>
                <strong>Email Accounts:</strong> Manage watched email accounts
              </li>
              <li>
                <strong>Emails:</strong> View and manage email messages
              </li>
              <li>
                <strong>Settings:</strong> Configure system settings
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
