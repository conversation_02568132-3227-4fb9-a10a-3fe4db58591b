"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils/tailwind";
import {
  LayoutDashboard,
  Key,
  Settings,
  Users,
  ChevronLeft,
  Bug,
  Mail,
  MailQuestion,
} from "lucide-react";
import { ThemeToggle } from "@/components/theme-toggle";
import { Button } from "@/components/ui/button";
import { brand } from "@/lib/constants/brand";

const navItems = [
  {
    name: "Dashboard",
    href: "/admin",
    icon: LayoutDashboard,
  },
  {
    name: "API Keys",
    href: "/admin/keys",
    icon: Key,
  },
  {
    name: "Users",
    href: "/admin/users",
    icon: Users,
  },
  {
    name: "Email Accounts",
    href: "/admin/email-accounts",
    icon: MailQuestion,
  },
  {
    name: "Emails",
    href: "/admin/emails",
    icon: Mail,
  },
  {
    name: "Setting<PERSON>",
    href: "/admin/settings",
    icon: Settings,
  },
  {
    name: "Debug",
    href: "/admin/debug",
    icon: Bug,
  },
];

export function AdminSidebar() {
  const pathname = usePathname();

  return (
    <div className="flex h-screen w-64 flex-col border-r bg-background">
      <div className="flex h-14 items-center border-b px-4">
        <Link href="/admin" className="flex items-center gap-2 font-semibold">
          <brand.lucideIcon className="h-6 w-6" />
          <span>{brand.name} Admin</span>
        </Link>
      </div>
      <div className="flex-1 overflow-auto py-2">
        <nav className="grid items-start px-2 text-sm font-medium">
          {navItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-foreground",
                pathname === item.href && "bg-muted text-foreground",
              )}
            >
              <item.icon className="h-4 w-4" />
              {item.name}
            </Link>
          ))}
        </nav>
      </div>
      <div className="mt-auto p-4 flex items-center justify-between">
        <Button variant="outline" size="sm" asChild>
          <Link href="/dashboard">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to App
          </Link>
        </Button>
        <ThemeToggle />
      </div>
    </div>
  );
}
