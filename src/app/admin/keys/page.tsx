import { Metadata } from "next";
import { listApiKeysAction } from "@/lib/actions/admin-keys.actions";
import { ApiKeysTable } from "./_components/api-keys-table";
import { CreateApiKeyButton } from "./_components/create-api-key-button";

// Mark this route as dynamically rendered since it uses cookies
export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: "API Keys Management | Admin Dashboard",
  description: "Manage API keys for the Steelflow platform",
};

export default async function ApiKeysPage() {
  const apiKeysResponse = await listApiKeysAction();
  const apiKeys = apiKeysResponse.success ? apiKeysResponse.data : [];
  const error = apiKeysResponse.success ? null : apiKeysResponse.error;

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold tracking-tight">
          API Keys Management
        </h1>
        <CreateApiKeyButton />
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Error loading API keys
              </h3>
              <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                <p>{error}</p>
                <p className="mt-1">
                  Please check the{" "}
                  <a href="/admin/debug" className="underline font-medium">
                    debug page
                  </a>{" "}
                  for more information.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      <ApiKeysTable apiKeys={apiKeys} />
    </div>
  );
}
