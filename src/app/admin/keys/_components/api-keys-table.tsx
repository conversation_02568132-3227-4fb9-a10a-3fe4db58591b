"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, Trash, Eye } from "lucide-react";
import { toast } from "sonner";
import { revokeApiKeyAction } from "@/lib/actions/admin-keys.actions";
import { ApiKeyDetailsDialog } from "./api-key-details-dialog";
import { formatDistanceToNowSafely } from "@/lib/utils/date";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("ApiKeysTable");

// Define the ApiKeyInfo type here based on what's needed in this component
type ApiKeyInfo = {
  keyId: string;
  name: string;
  ownerId: string;
  createdAt: string;
  expires?: string;
  revokedAt?: string;
  permissions: string[];
  meta: Record<string, any>;
};

interface ApiKeysTableProps {
  apiKeys: ApiKeyInfo[];
}

export function ApiKeysTable({ apiKeys }: ApiKeysTableProps) {
  const router = useRouter();
  const [isRevokeDialogOpen, setIsRevokeDialogOpen] = useState(false);
  const [keyToRevoke, setKeyToRevoke] = useState<ApiKeyInfo | null>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [selectedKey, setSelectedKey] = useState<ApiKeyInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleRevoke = async () => {
    if (!keyToRevoke) return;

    setIsLoading(true);
    try {
      const response = await revokeApiKeyAction(keyToRevoke.keyId);

      // Check if response exists before accessing its properties
      if (response && response.success) {
        toast.success("API key revoked successfully");
        router.refresh();
      } else {
        const errorMessage = response?.error || "Failed to revoke API key";
        logger.error("Failed to revoke API key:", errorMessage);
        toast.error(errorMessage);
      }
    } catch (error) {
      logger.error("Error in handleRevoke:", error);
      toast.error("An error occurred while revoking the API key");
    } finally {
      setIsLoading(false);
      setIsRevokeDialogOpen(false);
      setKeyToRevoke(null);
    }
  };

  const handleViewDetails = (key: ApiKeyInfo) => {
    setSelectedKey(key);
    setIsDetailsDialogOpen(true);
  };

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Owner</TableHead>
              <TableHead>Created</TableHead>
              <TableHead>Permissions</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="w-[80px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  <div className="flex justify-center items-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 dark:border-white"></div>
                    <span className="ml-2">Loading...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : apiKeys.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  No API keys found. Try creating one with the &quot;Create API Key&quot;
                  button above.
                </TableCell>
              </TableRow>
            ) : (
              apiKeys.map((key) => (
                <TableRow key={key.keyId}>
                  <TableCell className="font-medium">{key.name}</TableCell>
                  <TableCell>{key.meta?.email || key.ownerId}</TableCell>
                  <TableCell>
                    {formatDistanceToNowSafely(key.createdAt)}
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {key.permissions.length === 0 ? (
                        <span className="text-muted-foreground text-sm">
                          None
                        </span>
                      ) : (
                        key.permissions.map((permission) => (
                          <Badge key={permission} variant="outline">
                            {permission}
                          </Badge>
                        ))
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    {key.revokedAt ? (
                      <Badge variant="destructive">Revoked</Badge>
                    ) : key.expires ? (
                      <Badge variant="outline">
                        Expires{" "}
                        {formatDistanceToNowSafely(key.expires)}
                      </Badge>
                    ) : (
                      <Badge variant="default">Active</Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => handleViewDetails(key)}
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                        {!key.revokedAt && (
                          <DropdownMenuItem
                            onClick={() => {
                              setKeyToRevoke(key);
                              setIsRevokeDialogOpen(true);
                            }}
                            className="text-destructive"
                          >
                            <Trash className="mr-2 h-4 w-4" />
                            Revoke Key
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Revoke API Key Dialog */}
      <AlertDialog
        open={isRevokeDialogOpen}
        onOpenChange={setIsRevokeDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Revoke API Key</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to revoke the API key &quot;{keyToRevoke?.name}&quot;?
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleRevoke}
              className="bg-destructive text-destructive-foreground"
            >
              Revoke
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* API Key Details Dialog */}
      <ApiKeyDetailsDialog
        open={isDetailsDialogOpen}
        onOpenChange={setIsDetailsDialogOpen}
        apiKey={selectedKey}
      />
    </>
  );
}
