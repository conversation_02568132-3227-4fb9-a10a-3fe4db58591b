"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { formatDateSafely } from "@/lib/utils/date";

// Define the ApiKeyInfo type here based on what's needed in this component
type ApiKeyInfo = {
  keyId: string;
  name: string;
  ownerId: string;
  createdAt: string;
  expires?: string;
  revokedAt?: string;
  permissions: string[];
  meta: Record<string, any>;
};

interface ApiKeyDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  apiKey: ApiKeyInfo | null;
}

export function ApiKeyDetailsDialog({
  open,
  onOpenChange,
  apiKey,
}: ApiKeyDetailsDialogProps) {
  if (!apiKey) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>API Key Details</DialogTitle>
          <DialogDescription>
            Detailed information about the API key.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <div className="font-medium">Name:</div>
            <div className="col-span-3">{apiKey.name}</div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <div className="font-medium">Key ID:</div>
            <div className="col-span-3 font-mono text-sm">{apiKey.keyId}</div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <div className="font-medium">Owner:</div>
            <div className="col-span-3">
              {apiKey.meta?.email || apiKey.ownerId}
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <div className="font-medium">Created:</div>
            <div className="col-span-3">
              {formatDateSafely(apiKey.createdAt)}
            </div>
          </div>

          {apiKey.expires && (
            <div className="grid grid-cols-4 items-center gap-4">
              <div className="font-medium">Expires:</div>
              <div className="col-span-3">
                {formatDateSafely(apiKey.expires)}
              </div>
            </div>
          )}

          {apiKey.revokedAt && (
            <div className="grid grid-cols-4 items-center gap-4">
              <div className="font-medium">Revoked:</div>
              <div className="col-span-3">
                {formatDateSafely(apiKey.revokedAt)}
              </div>
            </div>
          )}

          <div className="grid grid-cols-4 items-start gap-4">
            <div className="font-medium">Status:</div>
            <div className="col-span-3">
              {apiKey.revokedAt ? (
                <Badge variant="destructive">Revoked</Badge>
              ) : apiKey.expires ? (
                <Badge variant="outline">Expires</Badge>
              ) : (
                <Badge variant="default">Active</Badge>
              )}
            </div>
          </div>

          <div className="grid grid-cols-4 items-start gap-4">
            <div className="font-medium">Permissions:</div>
            <div className="col-span-3">
              {apiKey.permissions.length === 0 ? (
                <span className="text-muted-foreground">None</span>
              ) : (
                <div className="flex flex-wrap gap-1">
                  {apiKey.permissions.map((permission) => (
                    <Badge key={permission} variant="outline">
                      {permission}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="grid grid-cols-4 items-start gap-4">
            <div className="font-medium">Metadata:</div>
            <div className="col-span-3">
              <pre className="rounded bg-muted p-2 text-xs overflow-auto max-h-32">
                {JSON.stringify(apiKey.meta, null, 2)}
              </pre>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
