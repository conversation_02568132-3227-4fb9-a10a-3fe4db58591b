"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import { createApiKeyAction } from "@/lib/actions/admin-keys.actions";
import { PlusCircle } from "lucide-react";
import { CopyButton } from "./copy-button";
import { ALL_PERMISSIONS } from "@/lib/constants/permissions";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("CreateApiKeyButton");

// Form schema
const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  permissions: z.string().optional(),
  metadata: z.string().optional(),
  useAllPermissions: z.boolean().default(false),
});

export function CreateApiKeyButton() {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [createdKey, setCreatedKey] = useState<{
    key: string;
    keyId: string;
  } | null>(null);
  const [useAllPermissions, setUseAllPermissions] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      permissions: "",
      metadata: "{}",
      useAllPermissions: false,
    },
  });

  // Update permissions field when the "All Permissions" checkbox is toggled
  useEffect(() => {
    if (useAllPermissions) {
      form.setValue("permissions", ALL_PERMISSIONS.join(","));
    } else if (form.getValues("permissions") === ALL_PERMISSIONS.join(",")) {
      // Only clear if it's the exact match of all permissions
      form.setValue("permissions", "");
    }
  }, [useAllPermissions, form]);

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsCreating(true);

    try {
      // Parse permissions and metadata
      let permissions: string[] = [];

      // If useAllPermissions is checked, use all available permissions
      if (values.useAllPermissions) {
        permissions = [...ALL_PERMISSIONS];
      } else if (values.permissions) {
        permissions = values.permissions
          .split(",")
          .map((p) => p.trim())
          .filter(Boolean);
      }

      let metadata = {};
      try {
        metadata = values.metadata ? JSON.parse(values.metadata) : {};
      } catch (e) {
        form.setError("metadata", {
          type: "manual",
          message: "Invalid JSON format",
        });
        return;
      }

      const response = await createApiKeyAction({
        name: values.name,
        permissions,
        metadata,
      });

      if (response.success) {
        setCreatedKey(response.data);
        toast.success("API key created successfully");
      } else {
        toast.error(response.error || "Failed to create API key");

        // Set field errors if available
        if (response.fieldErrors) {
          Object.entries(response.fieldErrors).forEach(([field, fieldErrors]) => {
            if (field in form.formState.errors && fieldErrors && Array.isArray(fieldErrors) && fieldErrors.length > 0) {
              form.setError(field as any, {
                type: "manual",
                message: fieldErrors[0],
              });
            }
          });
        }
      }
    } catch (error) {
      toast.error("An error occurred while creating the API key");
      logger.error("Error creating API key:", error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleClose = () => {
    if (createdKey) {
      // If we're closing after creating a key, refresh the page
      router.refresh();
    }

    // Reset form and state
    form.reset();
    setCreatedKey(null);
    setUseAllPermissions(false);
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button>
          <PlusCircle className="mr-2 h-4 w-4" />
          Create API Key
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {createdKey ? "API Key Created" : "Create New API Key"}
          </DialogTitle>
          <DialogDescription>
            {createdKey
              ? "Your API key has been created. Copy it now as you won't be able to see it again."
              : "Create a new API key for accessing the platform's API."}
          </DialogDescription>
        </DialogHeader>

        {createdKey ? (
          <div className="space-y-4 py-4">
            <div className="rounded-md bg-muted p-4">
              <div className="text-sm font-medium mb-2">API Key</div>
              <div className="flex items-center gap-2">
                <code className="flex-1 rounded bg-background p-2 text-sm font-mono overflow-x-auto">
                  {createdKey.key}
                </code>
                <CopyButton value={createdKey.key} />
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                Make sure to copy this key now. You won&apos;t be able to see it
                again!
              </p>
            </div>

            <div>
              <div className="text-sm font-medium mb-2">Key ID</div>
              <div className="flex items-center gap-2">
                <code className="flex-1 rounded bg-background p-2 text-sm font-mono">
                  {createdKey.keyId}
                </code>
                <CopyButton value={createdKey.keyId} />
              </div>
            </div>
          </div>
        ) : (
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="space-y-4 py-4"
            >
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="My API Key" {...field} />
                    </FormControl>
                    <FormDescription>
                      A descriptive name for this API key
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="useAllPermissions"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={(checked) => {
                          field.onChange(checked);
                          setUseAllPermissions(!!checked);
                        }}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>All Permissions</FormLabel>
                      <FormDescription>
                        Grant this key all available permissions
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="permissions"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Permissions (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="read:providers,write:rfqs"
                        {...field}
                        disabled={useAllPermissions}
                      />
                    </FormControl>
                    <FormDescription>
                      {useAllPermissions
                        ? "All permissions will be granted to this key"
                        : "Comma-separated list of permissions"}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="metadata"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Metadata (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='{"app": "my-app", "environment": "production"}'
                        className="font-mono"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      JSON metadata to associate with this key
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button type="button" variant="outline" onClick={handleClose}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isCreating}>
                  {isCreating ? "Creating..." : "Create API Key"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}

        {createdKey && (
          <DialogFooter>
            <Button onClick={handleClose}>Done</Button>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
}
