import { Metadata } from "next";
import { createClient } from "@/lib/supabase/server/server";
import { checkRole } from "@/lib/supabase/server/auth";
import { getCurrentUserAction } from "@/lib/actions/auth.actions";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

// Mark this route as dynamically rendered since it uses cookies
export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: "Debug Information | Admin Dashboard",
  description: "Debug information for troubleshooting",
};

export default async function DebugPage() {
  // Get current user
  const userResponse = await getCurrentUserAction();
  const user = userResponse.success ? userResponse.data : null;

  // Check admin role
  const supabase = await createClient();
  const isAdmin = user ? await checkRole(supabase, ["admin"]) : false;

  // Get environment variables (only show if they exist, not the actual values)
  const envVars = {
    UNKEY_API_ID: !!process.env.UNKEY_API_ID,
    UNKEY_ROOT_KEY: !!process.env.UNKEY_ROOT_KEY,
    NEXT_PUBLIC_SUPABASE_URL: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
    NEXT_PUBLIC_SUPABASE_ANON_KEY: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  };

  return (
    <div>
      <h1 className="text-3xl font-bold tracking-tight mb-6">
        Debug Information
      </h1>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>User Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div>
                <strong>User ID:</strong> {user?.id || "Not authenticated"}
              </div>
              <div>
                <strong>Email:</strong> {user?.email || "N/A"}
              </div>
              <div>
                <strong>Is Admin:</strong> {isAdmin ? "Yes" : "No"}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Environment Variables</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {Object.entries(envVars).map(([key, exists]) => (
                <div key={key}>
                  <strong>{key}:</strong> {exists ? "Set" : "Not set"}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>API Key Service Test</CardTitle>
          </CardHeader>
          <CardContent>
            <p>
              Visit the{" "}
              <a href="/admin/keys" className="text-blue-500 hover:underline">
                API Keys page
              </a>{" "}
              to test API key management.
            </p>
            <p className="mt-2">
              If you&apos;re having issues, check the browser console for errors and
              make sure all environment variables are set correctly.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
