import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server/server";
import { checkRole } from "@/lib/supabase/server/auth";
import { AdminSidebar } from "./_components/admin-sidebar";

// Mark this route as dynamically rendered since it uses cookies
export const dynamic = 'force-dynamic';

export const metadata = {
  title: "Admin Dashboard | Steelflow",
  description: "Admin dashboard for Steelflow platform",
};

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Check if the user has admin role
  const supabase = await createClient();
  const isAdmin = await checkRole(supabase, ["admin"]);

  if (!isAdmin) {
    redirect("/unauthorized?from=/admin");
  }

  return (
    <div className="flex min-h-screen">
      <AdminSidebar />
      <div className="flex-1 p-8">
        <div className="mx-auto max-w-7xl">{children}</div>
      </div>
    </div>
  );
}
