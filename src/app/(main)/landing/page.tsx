import Link from "next/link";
import {
  Truck,
  Box,
  Route,
  FileText,
  BarChart3,
  Users,
  ArrowRight,
  Layers,
  Factory,
  ShieldCheck,
  Clock,
  Workflow,
} from "lucide-react";

import { brand } from "@/lib/constants/brand";
import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { AuthButtons } from "./_components/auth-buttons";

export default function LandingPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section with Background */}
      <div className="relative bg-gradient-to-b from-primary/10 to-background py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <div className="inline-block px-3 py-1 rounded-full bg-primary/10 text-primary font-medium text-sm mb-2">
                Internal Logistics Platform
              </div>
              <h1 className="text-4xl md:text-5xl font-bold tracking-tight leading-tight">
                Streamline Your{" "}
                <span className="text-primary">Steel Logistics</span> Operations
              </h1>
              <p className="text-xl text-muted-foreground max-w-lg">
                {brand.description}
              </p>
              <AuthButtons />
            </div>
            <div className="relative h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl hidden lg:block">
              <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-primary/5 rounded-lg flex items-center justify-center">
                <div className="relative w-full h-full flex items-center justify-center">
                  <div className="absolute w-40 h-40 bg-primary/10 rounded-full"></div>
                  <Truck className="h-24 w-24 text-primary relative z-10" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="flex flex-col items-center text-center p-6 bg-background rounded-lg shadow-sm">
              <div className="bg-primary/10 p-3 rounded-full mb-4">
                <Truck className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-3xl font-bold">100+</h3>
              <p className="text-muted-foreground">Logistics Providers</p>
            </div>
            <div className="flex flex-col items-center text-center p-6 bg-background rounded-lg shadow-sm">
              <div className="bg-primary/10 p-3 rounded-full mb-4">
                <Route className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-3xl font-bold">50+</h3>
              <p className="text-muted-foreground">Global Routes</p>
            </div>
            <div className="flex flex-col items-center text-center p-6 bg-background rounded-lg shadow-sm">
              <div className="bg-primary/10 p-3 rounded-full mb-4">
                <FileText className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-3xl font-bold">1,000+</h3>
              <p className="text-muted-foreground">RFQs Processed</p>
            </div>
          </div>
        </div>
      </div>

      {/* Key Features Section */}
      <div className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Platform Features</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Our comprehensive suite of tools designed specifically for steel
              logistics management
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="border border-border/40 shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <div className="bg-primary/10 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                  <Users className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Providers Management</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Manage company profiles, multiple contacts, and track provider
                  capabilities and certifications.
                </p>
              </CardContent>
              <CardFooter>
                <Button variant="ghost" size="sm" className="gap-1" asChild>
                  <Link href="/dashboard/providers">
                    Manage Providers
                    <ArrowRight className="h-4 w-4" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            <Card className="border border-border/40 shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <div className="bg-primary/10 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                  <Route className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Routes Management</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Handle country pair service routes, transport types, and
                  route-specific requirements.
                </p>
              </CardContent>
              <CardFooter>
                <Button variant="ghost" size="sm" className="gap-1" asChild>
                  <Link href="/dashboard/routes">
                    Manage Routes
                    <ArrowRight className="h-4 w-4" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            <Card className="border border-border/40 shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <div className="bg-primary/10 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                  <Layers className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Cargo Types</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Comprehensive steel product type management including coils,
                  beams, plates, and more.
                </p>
              </CardContent>
              <CardFooter>
                <Button variant="ghost" size="sm" className="gap-1" asChild>
                  <Link href="/dashboard/cargo">
                    Manage Cargo
                    <ArrowRight className="h-4 w-4" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </div>

      {/* Quick Access Section */}
      <div className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Quick Access</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Frequently used tools and resources
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <Link href="/dashboard/rfqs/new" className="group">
              <div className="bg-background p-6 rounded-lg border border-border/40 shadow-sm flex flex-col items-center text-center gap-4 hover:border-primary/30 hover:shadow-md transition-all">
                <div className="bg-primary/10 p-3 rounded-full">
                  <FileText className="h-6 w-6 text-primary" />
                </div>
                <h3 className="font-semibold">Create RFQ</h3>
                <p className="text-sm text-muted-foreground">
                  Create a new request for quote
                </p>
              </div>
            </Link>

            <Link href="/dashboard/providers" className="group">
              <div className="bg-background p-6 rounded-lg border border-border/40 shadow-sm flex flex-col items-center text-center gap-4 hover:border-primary/30 hover:shadow-md transition-all">
                <div className="bg-primary/10 p-3 rounded-full">
                  <Users className="h-6 w-6 text-primary" />
                </div>
                <h3 className="font-semibold">View Providers</h3>
                <p className="text-sm text-muted-foreground">
                  Browse and manage logistics providers
                </p>
              </div>
            </Link>

            <Link href="/dashboard/cargo" className="group">
              <div className="bg-background p-6 rounded-lg border border-border/40 shadow-sm flex flex-col items-center text-center gap-4 hover:border-primary/30 hover:shadow-md transition-all">
                <div className="bg-primary/10 p-3 rounded-full">
                  <Box className="h-6 w-6 text-primary" />
                </div>
                <h3 className="font-semibold">Cargo Types</h3>
                <p className="text-sm text-muted-foreground">
                  Manage steel product categories
                </p>
              </div>
            </Link>

            <Link href="/dashboard/equipment" className="group">
              <div className="bg-background p-6 rounded-lg border border-border/40 shadow-sm flex flex-col items-center text-center gap-4 hover:border-primary/30 hover:shadow-md transition-all">
                <div className="bg-primary/10 p-3 rounded-full">
                  <Truck className="h-6 w-6 text-primary" />
                </div>
                <h3 className="font-semibold">Equipment</h3>
                <p className="text-sm text-muted-foreground">
                  Manage transportation equipment
                </p>
              </div>
            </Link>
          </div>
        </div>
      </div>

      {/* Platform Benefits */}
      <div className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Platform Benefits</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Designed to streamline your steel logistics operations
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="flex gap-4">
              <div className="flex-shrink-0">
                <div className="bg-primary/10 p-2 rounded-lg">
                  <ShieldCheck className="h-6 w-6 text-primary" />
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">
                  Secure & Reliable
                </h3>
                <p className="text-muted-foreground">
                  Enterprise-grade security with role-based access control and
                  data encryption.
                </p>
              </div>
            </div>

            <div className="flex gap-4">
              <div className="flex-shrink-0">
                <div className="bg-primary/10 p-2 rounded-lg">
                  <Clock className="h-6 w-6 text-primary" />
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">Time Efficiency</h3>
                <p className="text-muted-foreground">
                  Streamlined workflows reduce manual tasks and accelerate quote
                  processing.
                </p>
              </div>
            </div>

            <div className="flex gap-4">
              <div className="flex-shrink-0">
                <div className="bg-primary/10 p-2 rounded-lg">
                  <BarChart3 className="h-6 w-6 text-primary" />
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">Data Insights</h3>
                <p className="text-muted-foreground">
                  Comprehensive analytics and reporting for better decision
                  making.
                </p>
              </div>
            </div>

            <div className="flex gap-4">
              <div className="flex-shrink-0">
                <div className="bg-primary/10 p-2 rounded-lg">
                  <Factory className="h-6 w-6 text-primary" />
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">
                  Industry Specific
                </h3>
                <p className="text-muted-foreground">
                  Tailored specifically for the steel industry&apos;s unique
                  logistics requirements.
                </p>
              </div>
            </div>

            <div className="flex gap-4">
              <div className="flex-shrink-0">
                <div className="bg-primary/10 p-2 rounded-lg">
                  <Workflow className="h-6 w-6 text-primary" />
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">
                  Streamlined Workflows
                </h3>
                <p className="text-muted-foreground">
                  Optimized processes for RFQ creation, provider matching, and
                  bid management.
                </p>
              </div>
            </div>

            <div className="flex gap-4">
              <div className="flex-shrink-0">
                <div className="bg-primary/10 p-2 rounded-lg">
                  <Users className="h-6 w-6 text-primary" />
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">Provider Network</h3>
                <p className="text-muted-foreground">
                  Access to a curated network of qualified logistics providers
                  for steel transportation.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-16 bg-primary/5">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">
              Ready to Streamline Your Logistics?
            </h2>
            <p className="text-muted-foreground mb-8">
              Access the platform to manage your steel logistics operations more
              efficiently.
            </p>
            <div className="flex justify-center">
              <AuthButtons />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
