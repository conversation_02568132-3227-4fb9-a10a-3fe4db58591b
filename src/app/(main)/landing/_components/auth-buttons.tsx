"use client";

import Link from "next/link";
import { ArrowR<PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/lib/hooks/use-auth";

export function AuthButtons() {
  const { isAuthenticated } = useAuth();

  return (
    <div className="flex flex-col sm:flex-row gap-4 pt-4">
      {isAuthenticated ? (
        <Button size="lg" asChild>
          <Link href="/dashboard">
            Go to Dashboard
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      ) : (
        <>
          <Button size="lg" asChild>
            <Link href="/auth/login">
              Log In
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
          <Button variant="outline" size="lg" asChild>
            <Link href="/auth/signup">Sign Up</Link>
          </Button>
        </>
      )}
    </div>
  );
}
