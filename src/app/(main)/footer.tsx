import Link from "next/link";

import {
  Github,
  Twitter,
  Linkedin,
  Instagram,
  Facebook,
  Youtube,
  Twitch,
} from "lucide-react";

import { brand } from "@/lib/constants/brand";

import { Logo } from "@/components/logo";

// No sections needed for internal tool

const socialIcons = {
  github: <Github className="size-5" />,
  twitter: <Twitter className="size-5" />,
  linkedin: <Linkedin className="size-5" />,
  instagram: <Instagram className="size-5" />,
  facebook: <Facebook className="size-5" />,
  youtube: <Youtube className="size-5" />,
  twitch: <Twitch className="size-5" />,
};

export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="border-t bg-background mt-12">
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <div className="flex items-center gap-3">
            <Logo />
            <p className="text-sm text-muted-foreground">
              © {currentYear} {brand.name}. All rights reserved.
            </p>
          </div>

          <div className="flex items-center gap-6">
            {Object.entries(brand.social).map(([platform, href]) => {
              if (href !== "https://example.com") {
                return (
                  <Link
                    key={platform}
                    href={href}
                    className="text-muted-foreground hover:text-primary"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <span className="sr-only">{platform}</span>
                    {socialIcons[platform as keyof typeof socialIcons]}
                  </Link>
                );
              }
              return null;
            })}
          </div>
        </div>
      </div>
    </footer>
  );
}
