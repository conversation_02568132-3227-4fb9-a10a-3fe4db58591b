"use client";

import Link from "next/link";
import { useRouter, usePathname } from "next/navigation";
import { LogOutIcon, LayoutDashboardIcon } from "lucide-react";
import { useAuth } from "@/lib/hooks/use-auth";
import { createClient } from "@/lib/supabase/client";
import { Logo } from "@/components/logo";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DropdownMenu } from "@/components/ui/dropdown-menu";
import { ThemeToggle } from "@/components/theme-toggle";
import { Skeleton } from "@/components/ui/skeleton";

const AuthNav = ({
  authUser,
  loading,
}: {
  authUser: any;
  loading: boolean;
}) => {
  const router = useRouter();
  const supabase = createClient();

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.refresh();
  };

  return (
    <div className="flex items-center gap-4">
      <ThemeToggle variant="ghost" />

      {loading ? (
        // Skeleton loading state
        <div className="flex items-center gap-2">
          <Skeleton className="h-10 w-10 rounded-full" />
        </div>
      ) : authUser ? (
        <div className="flex items-center gap-4">
          <DropdownMenu>
            <DropdownMenuTrigger asChild className="cursor-pointer">
              <Avatar className="border size-10">
                <AvatarImage src={authUser.user_metadata?.avatar_url} />
                <AvatarFallback className="text-muted-foreground font-semibold">
                  {authUser.email?.[0].toUpperCase() || "U"}
                </AvatarFallback>
              </Avatar>
            </DropdownMenuTrigger>

            <DropdownMenuContent>
              <DropdownMenuItem>
                <LayoutDashboardIcon className="size-4 text-foreground" />
                <Link href="/dashboard">Dashboard</Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleSignOut}
                className="cursor-pointer"
              >
                <LogOutIcon className="size-4 text-destructive" />
                Sign out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ) : (
        <div className="flex gap-2">
          <Link href="/auth/login">
            <Button variant="outline">Log In</Button>
          </Link>
          <Link href="/auth/signup">
            <Button>Sign Up</Button>
          </Link>
        </div>
      )}
    </div>
  );
};

/**
 * Add routes where the header should not be sticky. I personally use this
 * for the landing page, but you can add more routes as needed.
 */
const nonStickyRoutes = ["/landing"];

export function Header() {
  const pathname = usePathname();
  const { authUser, loading } = useAuth();

  return (
    <header
      className={`border-b bg-background ${
        !nonStickyRoutes.includes(pathname) ? "sticky top-0" : ""
      } z-50`}
    >
      <div className="container mx-auto py-4">
        <div className="flex items-center justify-between">
          <Link href="/" className="flex items-center">
            <Logo />
          </Link>

          <AuthNav authUser={authUser} loading={loading} />
        </div>
      </div>
    </header>
  );
}
