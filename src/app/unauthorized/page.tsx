import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ShieldAlert } from "lucide-react";

export const metadata: Metadata = {
  title: "Unauthorized | Steelflow",
  description: "You don't have permission to access this page",
};

export default async function UnauthorizedPage({
  searchParams,
}: {
  searchParams: Promise<{ from?: string }>;
}) {
  const params = await searchParams;
  const previousPath = params.from || "/";
  const isAdminRoute = previousPath.startsWith("/admin");

  return (
    <div className="flex min-h-svh flex-col items-center justify-center bg-muted p-6 md:p-10">
      <div className="w-full max-w-md">
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col items-center text-center gap-6">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-destructive/10">
                <ShieldAlert className="h-8 w-8 text-destructive" />
              </div>
              <h1 className="text-3xl font-bold">Unauthorized</h1>
              <p className="text-balance text-muted-foreground">
                {isAdminRoute
                  ? "You don't have permission to access the admin area. This area requires admin privileges."
                  : "You are not authorized to access this page."}
              </p>
              <div className="flex flex-col w-full gap-2">
                <Button asChild className="w-full">
                  <Link href="/">Go to Home</Link>
                </Button>
                {!isAdminRoute && (
                  <Button variant="outline" asChild className="w-full">
                    <Link href={previousPath}>Try Again</Link>
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
