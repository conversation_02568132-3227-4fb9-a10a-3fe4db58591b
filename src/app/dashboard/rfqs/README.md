# RFQ Module Implementation

This directory contains the implementation of the Request for Quote (RFQ) module for the Steelflow platform.

## Overview

The RFQ module enables logistics managers to create detailed RFQs, match them with suitable providers, distribute them via AI-assisted emails, capture and compare bids, and make informed decisions.

## Architecture

The RFQ module follows Steelflow's established service layer architecture:

```
UI Layer → Server Actions → Service Layer → Database
           API Routes   ↗
```

## Components

### 1. Database Schema

The RFQ module uses the following tables:

- `rfqs`: Stores the main RFQ information
- `rfq_providers`: Links RFQs with providers and tracks their status
- `rfq_bids`: Stores bids submitted by providers
- `rfq_outgoing_emails`: Stores emails sent to providers
- `rfq_incoming_emails`: Stores emails received from providers

The database schema is defined in the migration file: `supabase/migrations/20250505000000_create_rfq_tables.sql`

### 2. Types

The TypeScript types for the RFQ module are defined in:

- `src/lib/supabase/types.ts`: Database types generated from the Supabase schema
- `src/lib/schemas/rfq.schema.ts`: Zod schemas for validation and TypeScript types

### 3. Service Layer

The service layer contains all business logic and database access:

- `src/lib/services/rfq.service.ts`: CRUD operations, provider matching, bid processing

### 4. API Routes

API routes handle external HTTP requests:

- `src/app/api/rfqs/route.ts`: Main RFQ endpoints
- `src/app/api/rfqs/[id]/providers/route.ts`: Provider matching endpoints
- `src/app/api/rfqs/[id]/bids/route.ts`: Bid submission endpoints

### 5. Server Actions

Server actions handle UI interactions:

- `src/lib/actions/rfq.actions.ts`: Form submission, validation, cache invalidation

### 6. UI Components

- `src/app/dashboard/rfqs/page.tsx`: RFQ listing page
- `src/app/dashboard/rfqs/[id]/page.tsx`: RFQ detail page
- `src/app/dashboard/rfqs/new/page.tsx`: RFQ creation page
- `src/app/dashboard/rfqs/_components/rfqs-data-table.tsx`: Enhanced RFQ data table component with business insights
- `src/app/dashboard/rfqs/_components/forms/rfq-form.tsx`: Consolidated RFQ form
- `src/app/dashboard/rfqs/_components/provider-selection.tsx`: Provider selection interface
- `src/app/dashboard/rfqs/_components/email-composition-dialog.tsx`: Email composition dialog
- `src/app/dashboard/rfqs/_components/rfq-summary-panel.tsx`: RFQ summary panel

## Implementation Status

The RFQ module is being implemented in phases:

### Phase 1: Foundation (Completed)

- ✅ Define RFQ data schemas
- ✅ Implement RFQ service layer (CRUD operations)
- ✅ Develop RFQ API routes
- ✅ Create RFQ server actions
- ✅ Create basic UI components
- ✅ Create database migration
- ✅ Design RFQ creation UI components
- ✅ Implement consolidated RFQ form
- ✅ Add validation
- ✅ Create RFQ detail view

### Phase 2: Provider Selection and RFQ Distribution (In Progress)

- ✅ Create provider selection interface in RFQ detail view
- ✅ Implement provider search and filtering
- ✅ Add manual selection/deselection of providers
- ✅ Create simple email template for RFQs
- ✅ Implement email composition dialog
- ✅ Implement email data storage in database
- ✅ Update RFQ and provider status after sending
- ✅ Implement actual email sending functionality
- ✅ Display sent email history in RFQ detail view

### Phase 3: Manual Bid Management (Planned)

- ✅ Create bid entry form for manually recording bids
- ⬜ Implement bid listing and details view
- ⬜ Add bid status tracking
- ⬜ Create bid comparison interface
- ⬜ Add functionality to accept/reject bids

### Phase 4: Advanced Features (Partially Implemented)

- ✅ Implement basic provider matching algorithm
- ✅ Integrate with Gemini AI for email enhancement
- ⬜ Refine provider matching algorithm with quality indicators
- ⬜ Implement AI-assisted bid extraction from emails
- ⬜ Create analytics dashboard

## Usage

### Creating an RFQ

To create a new RFQ:

1. Navigate to the RFQs page at `/dashboard/rfqs`
2. Click the "Create RFQ" button
3. Fill in the required information in the form
4. Click "Save as Draft" or "Submit"

### Matching and Inviting Providers

To match and invite providers for an RFQ:

1. Navigate to the RFQ detail page
2. Click on the "Providers" tab (default selection)
3. The system automatically matches providers based on the RFQ details
4. Use the search and filter options to find specific providers
5. Select the providers you want to invite
6. Click "Send RFQ to Selected"
7. Customize the email content in the dialog (optional)
8. Click "Send RFQ" to invite the selected providers

### Managing Bids

To manage bids for an RFQ:

1. Navigate to the RFQ detail page
2. Click on the "Providers" tab
3. In the "Invited Providers & Bids" section, you can see the bid status for each provider
4. Use the "View Bid" or "Add Bid" button to manage bids for each provider
5. Review and compare bids from different providers
6. Update bid status as needed

### Viewing Email History

To view email history for an RFQ:

1. Navigate to the RFQ detail page
2. Click on the "Email History" tab
3. View all emails sent to providers for this RFQ
4. Click "View" to see the full email content
5. Expand email rows to see additional details

## Development

To continue development on the RFQ module:

1. Run the migration to create the database tables:

   ```
   npx supabase migration up
   ```

2. Update the Supabase types if needed:

   ```
   npx supabase gen types typescript --local > src/lib/supabase/types.ts
   ```

3. Implement the remaining components according to the implementation plan in `IMPLEMENTATION.md`

## Recent Improvements

### Component Consolidation

- **Table Components**: The RFQ data table implementation has been consolidated into a single, enhanced data table component (`rfqs-data-table.tsx`) with business insights (invited providers count, matched providers count, bids received count, response rate).

- **UI Enhancements**: The consolidated data table now includes tooltips on column headers, color-coded response rate indicators, and relative timestamps for better user experience.

### RFQ Detail Page Restructuring

- **Details Tab Consolidation**: Consolidated sparse Details tab content into the main header area for better information density
- **Route Integration**: Integrated route information (origin/destination with country flags) directly into the enhanced summary panel
- **Enhanced Summary Panel**: Extended RFQSummaryPanel to include status, expiration date, and route information in a unified grid layout
- **Email History Tab**: Added dedicated "Email History" tab for better organization of email-related functionality
- **Simplified Special Requirements**: Reduced visual prominence of special requirements section with minimal styling
- **Two-Tab Structure**: Implemented "Providers" (default) and "Email History" tabs for organized navigation
- **Maintained Information Density**: Preserved consolidated improvements while providing organized access to all functionality

- **Performance Optimizations**: The table layout has been optimized to prevent horizontal scrolling with whitespace-nowrap and default column visibility settings that hide less important columns.
