import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { RFQForm } from "../_components/forms/rfq-form";
import { H1, Lead } from "@/components/ui/typography";

export const metadata: Metadata = {
  title: "Create RFQ | Steelflow",
  description: "Create a new Request for Quote",
};

export default function CreateRFQPage() {
  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <Button variant="outline" size="sm" asChild>
          <Link href="/dashboard/rfqs">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to RFQs
          </Link>
        </Button>
      </div>

      <div className="flex justify-between items-center mb-6">
        <div>
          <H1>Create RFQ</H1>
          <Lead>
            Create a new Request for Quote for transportation services
          </Lead>
        </div>
      </div>

      <RFQForm />
    </div>
  );
}
