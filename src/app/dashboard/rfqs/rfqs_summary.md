# RFQs Module

## 1. Overview

The RFQs (Request for Quotes) module is a core component of the Steelflow application that allows users to create, manage, and track requests for transportation services. It provides functionality for creating RFQs, matching them with suitable providers, sending RFQ emails, receiving and managing bids, and tracking the entire RFQ lifecycle.

## 2. Directory Structure

```
src/app/dashboard/rfqs/
├── _components/                  # Module-specific components
│   ├── dialogs/                  # Dialog components
│   │   ├── bid-entry-dialog.tsx
│   │   ├── bid-update-dialog.tsx
│   │   ├── email-composition-dialog.tsx
│   │   └── manual-provider-dialog.tsx
│   ├── forms/                    # Form components
│   │   ├── bid-entry-form.tsx
│   │   ├── bid-update-form.tsx
│   │   └── rfq-form.tsx          # Consolidated RFQ form
│   ├── collapsible-section.tsx   # Collapsible section component
│   ├── email-history.tsx         # Email history component
│   ├── provider-selection.tsx    # Provider selection component
│   ├── rfqs-content.tsx          # Server component for data fetching
│   ├── rfqs-data-table.tsx       # RFQ data table component
│   ├── rfqs-skeleton.tsx         # Loading skeleton for RFQs list
│   └── rfq-summary-panel.tsx     # RFQ summary panel component
├── [id]/                         # Detail page
│   ├── _components/              # Detail page components
│   │   ├── rfq-detail-content.tsx
│   │   └── rfq-detail-skeleton.tsx
│   ├── edit/                     # Edit page
│   │   └── page.tsx
│   └── page.tsx                  # Detail page component
├── new/                          # Create page
│   └── page.tsx
├── page.tsx                      # Main listing page
└── rfqs_summary.md               # This documentation file
```

## 3. Components

### Page Components

- **RFQsPage**: Main page component that displays a list of RFQs
- **RFQDetailPage**: Detail page component that displays and manages a specific RFQ
- **CreateRFQPage**: Page component for creating a new RFQ

### Server Components

- **RFQsContent**: Fetches RFQ data and renders the appropriate UI
- **RFQDetailContent**: Fetches RFQ detail data and renders the appropriate UI

### Client Components

- **RFQsDataTable**: Enhanced data table component that displays RFQs with business insights, sorting, filtering, and pagination
- **RFQForm**: Consolidated form for creating and editing RFQs
- **ProviderSelection**: Interface for selecting and managing providers for an RFQ
- **EmailCompositionDialog**: Dialog for composing and sending RFQ emails
- **BidEntryDialog**: Dialog for entering bid details
- **BidUpdateDialog**: Dialog for updating bid details
- **ManualProviderDialog**: Dialog for manually selecting providers
- **RFQSummaryPanel**: Displays a summary of key RFQ information
- **CollapsibleSection**: Reusable component for collapsible sections

### Loading States

- **RFQsSkeleton**: Loading skeleton for the RFQs list
- **RFQDetailSkeleton**: Loading skeleton for the RFQ detail page

## 4. Data Flow

### Listing Page

```
RFQsPage
  ↓
Suspense
  ↓
RFQsContent (Server Component)
  ↓ getRFQsAction()
  ↓
RFQsDataTable (Client Component)
```

### Detail Page

```
RFQDetailPage
  ↓
Suspense
  ↓
RFQDetailContent (Server Component)
  ↓ getRFQAction()
  ↓
RFQSummaryPanel (with route info), Tabs → [ProviderSelection, EmailHistory] (Client Components)
```

### Provider Selection Flow

```
ProviderSelection
  ↓
matchProvidersAction() → Find matching providers
  ↓
selectProvidersAction() → Select providers and send RFQ
  ↓
EmailCompositionDialog → Compose and send emails
  ↓
BidEntryDialog/BidUpdateDialog → Manage bids
```

## 5. Key Features

- **RFQ Creation**: Consolidated form for creating RFQs with validation
- **Provider Matching**: Automatic matching of providers based on RFQ requirements
- **Email Communication**: Sending RFQs to providers via email
- **Bid Management**: Recording and managing bids from providers

## 6. Integration Points

- **Providers Module**: Uses provider data for matching and selection
- **Equipment Module**: Uses equipment types for RFQ requirements
- **Cargo Module**: Uses cargo types for RFQ specifications
- **Email Service**: Sends RFQ emails to providers

## 7. Recent Improvements

### Component Consolidation

- **Table Components**: The RFQ data table implementation has been consolidated into a single, enhanced data table component (`rfqs-data-table.tsx`).

### Business Insights

The enhanced RFQ data table now includes the following business insights:

- **Invited Providers Count**: Number of providers that have been sent an invitation email (providers with a non-null invited_at timestamp)
- **Matched Providers Count**: Number of providers that match the RFQ criteria based on equipment type and route
- **Bids Received Count**: Number of bids submitted by providers for each RFQ
- **Response Rate**: Percentage of invited providers who submitted bids, with color-coded indicators:
  - Green (75-100%): Excellent
  - Yellow (50-74%): Good
  - Orange (25-49%): Fair
  - Red (0-24%): Poor

### UI Enhancements

- **Tooltips**: Added tooltips to column headers to provide more context about what each metric means and how it's calculated
- **Relative Timestamps**: Improved timestamp display with human-readable relative times (e.g., "4 hours ago")
- **Layout Optimization**: Optimized table layout to prevent horizontal scrolling with whitespace-nowrap and default column visibility settings

## 8. Future Enhancements

- **Bid Comparison**: Enhanced tools for comparing bids from different providers
- **Automated Reminders**: Automated reminders for providers who haven't responded
- **Analytics Dashboard**: Analytics on RFQ performance and provider response rates
- **Document Generation**: Generate PDF documents for RFQs and bids
