import { Metada<PERSON> } from "next";
import { Suspense } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { PlusIcon } from "lucide-react";
import Link from "next/link";
import { RFQsContent } from "./_components/rfqs-content";
import { RFQsSkeleton } from "./_components/rfqs-skeleton";
import { createLogger } from "@/lib/utils/logger/logger";
import { H1, Lead } from "@/components/ui/typography";

const logger = createLogger("RFQsPage");

export const metadata: Metadata = {
  title: "RFQs | Steelflow",
  description: "Manage Request for Quotes",
};

export default async function RFQsPage() {
  logger.info("RFQs page loaded");

  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <div>
          <H1>
            Request for Quotes
          </H1>
          <Lead>
            Create and manage RFQs for transportation services
          </Lead>
        </div>
        <Button asChild>
          <Link href="/dashboard/rfqs/new">
            <PlusIcon className="mr-2 h-4 w-4" />
            Create RFQ
          </Link>
        </Button>
      </div>

      <Suspense fallback={<RFQsSkeleton />}>
        <RFQsContent />
      </Suspense>
    </>
  );
}
