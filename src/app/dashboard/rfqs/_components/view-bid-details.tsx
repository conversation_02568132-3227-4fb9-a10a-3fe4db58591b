"use client";

import { useState, useEffect, useCallback } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { toast } from "@/components/ui/sonner";
import { createLogger } from "@/lib/utils/logger/logger";
import { getRFQBidByProviderAction } from "@/lib/actions/rfq.actions";

const logger = createLogger("ViewBidDetails");

interface ViewBidDetailsProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  rfqId: string;
  providerId: string;
  providerName: string;
}

// Import the RFQBid type from schemas
import { RFQBid } from "@/lib/schemas";

// Use RFQBid as our Bid type
type Bid = RFQBid;

export function ViewBidDetails({
  open,
  onOpenChange,
  rfqId,
  providerId,
  providerName,
}: ViewBidDetailsProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [bid, setBid] = useState<Bid | null>(null);

  const fetchBidDetails = useCallback(async () => {
    try {
      setIsLoading(true);

      logger.info(
        `Fetching bid details for RFQ: ${rfqId}, Provider: ${providerId}`,
      );

      // Use the server action to fetch bid details
      const result = await getRFQBidByProviderAction(rfqId, providerId);

      if (!result.success) {
        throw new Error(`Failed to fetch bid: ${result.error}`);
      }

      const data = result.data;
      logger.info("Bid data received:", data);

      // Check if we have data and it's an array
      if (!data || !Array.isArray(data) || data.length === 0) {
        logger.error("No bid data returned or invalid format:", data);
        toast.error(
          <div className="space-y-1">
            <p className="font-semibold text-base">Not Found</p>
            <p className="text-sm text-muted-foreground">
              No bid found for this provider
            </p>
          </div>,
          {
            className:
              "p-4 rounded-lg border border-red-200 dark:border-red-900/30 bg-white dark:bg-card shadow-lg",
            duration: 5000,
          },
        );
        onOpenChange(false);
        return;
      }

      // Since we're filtering by provider_id in the action, we should have exactly one bid
      // But we'll still check to be safe
      const providerBid = data[0];

      if (providerBid) {
        // Set the bid data directly from the response
        setBid(providerBid);
        logger.info("Bid found and set:", providerBid);
      } else {
        logger.error(
          "No bid found in the response for provider ID:",
          providerId,
        );
        toast.error(
          <div className="space-y-1">
            <p className="font-semibold text-base">Not Found</p>
            <p className="text-sm text-muted-foreground">
              No bid found for this provider
            </p>
          </div>,
          {
            className:
              "p-4 rounded-lg border border-red-200 dark:border-red-900/30 bg-white dark:bg-card shadow-lg",
            duration: 5000,
          },
        );
        onOpenChange(false);
      }
    } catch (error) {
      logger.error("Error fetching bid details:", error);
      toast.error(
        <div className="space-y-1">
          <p className="font-semibold text-base">Error</p>
          <p className="text-sm text-muted-foreground">
            Failed to load bid details
          </p>
        </div>,
        {
          className:
            "p-4 rounded-lg border border-red-200 dark:border-red-900/30 bg-white dark:bg-card shadow-lg",
          duration: 5000,
        },
      );
    } finally {
      setIsLoading(false);
    }
  }, [rfqId, providerId, onOpenChange]);

  useEffect(() => {
    if (open) {
      logger.info(
        `ViewBidDetails opened for RFQ: ${rfqId}, Provider: ${providerId}, Name: ${providerName}`,
      );
      fetchBidDetails();
    }
  }, [open, rfqId, providerId, fetchBidDetails, providerName]);

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[650px] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="pb-2">
          <DialogTitle>Bid from {providerName}</DialogTitle>
          <DialogDescription className="text-xs">
            Review the bid details submitted by this provider.
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center items-center py-4">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
          </div>
        ) : bid ? (
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-muted/50 p-2 rounded-md">
                <h3 className="text-xs font-medium text-muted-foreground">
                  Bid Amount
                </h3>
                <p className="text-base font-semibold">
                  {bid.price.toLocaleString()} EUR
                </p>
              </div>
              <div className="bg-muted/50 p-2 rounded-md">
                <h3 className="text-xs font-medium text-muted-foreground">
                  Status
                </h3>
                <p className="text-base font-semibold capitalize">
                  {bid.status.replace("_", " ")}
                </p>
              </div>
            </div>

            <div className="bg-muted/50 p-2 rounded-md">
              <h3 className="text-xs font-medium text-muted-foreground">
                Submitted At
              </h3>
              <p className="text-sm">{formatDate(bid.submitted_at)}</p>
            </div>

            {bid.notes && (
              <div className="bg-muted/50 p-2 rounded-md">
                <h3 className="text-xs font-medium text-muted-foreground">
                  Notes
                </h3>
                <p className="text-sm whitespace-pre-wrap mt-1">{bid.notes}</p>
              </div>
            )}
          </div>
        ) : (
          <div className="py-3 text-center text-muted-foreground">
            No bid information available.
          </div>
        )}

        <DialogFooter className="pt-2">
          <Button onClick={() => onOpenChange(false)} size="sm">
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
