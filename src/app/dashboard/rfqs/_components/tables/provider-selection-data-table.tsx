"use client";

import { use<PERSON>emo, Component, ReactNode } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { DataTable } from "@/components/ui/tables";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { RFQProviderForTable, ManualProviderType } from "@/lib/schemas";
import { useProviderSelectionData } from "@/hooks/use-provider-selection-data";

// Render provider status badge
export const renderStatusBadge = (status: string | null) => {
  if (!status) return null;

  let variant: "default" | "secondary" | "outline" | "destructive" = "outline";
  let className = "bg-muted/50";

  switch (status) {
    case "matched":
      variant = "outline";
      className = "bg-muted/50";
      break;
    case "selected":
      variant = "secondary";
      className = "bg-secondary/50";
      break;
    case "invited":
      variant = "default";
      className = "";
      break;
    case "declined":
      variant = "destructive";
      className = "";
      break;
    case "bid_submitted":
      variant = "default";
      className = "bg-green-100 text-green-800 border-green-200";
      break;
  }

  return (
    <Badge variant={variant} className={className}>
      {status.replace("_", " ")}
    </Badge>
  );
};

export function getProviderSelectionColumns(): ColumnDef<RFQProviderForTable>[] {
  return [
    // Standard selection column - DataTable will handle this automatically
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="translate-y-[2px]"
        />
      ),
      cell: ({ row }) => {
        const rfqStatus = row.original.rfq_status;
        const actualStatus = row.original.status;
        const displayStatus = rfqStatus || actualStatus;
        const cannotBeSelected =
          displayStatus === "invited" || displayStatus === "bid_submitted";

        return (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
            className="translate-y-[2px]"
            disabled={cannotBeSelected}
          />
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
    {
      id: "provider_name",
      accessorFn: (row) => row.provider?.name,
      header: "Provider",
      cell: ({ row }) => {
        const provider = row.original.provider;
        return (
          <div className="font-medium">
            {provider?.name || "Unknown Provider"}
          </div>
        );
      },
      filterFn: "includesString",
    },
    {
      id: "provider_status",
      accessorFn: (row) => row.provider?.status,
      header: "Status",
      cell: ({ row }) => {
        const provider = row.original.provider;
        return (
          <Badge variant="outline" className="bg-muted/50">
            {provider?.status || "unknown"}
          </Badge>
        );
      },
      filterFn: "includesString",
    },
    {
      id: "provider_verified",
      accessorFn: (row) => row.provider?.verified,
      header: () => (
        <div className="text-center">Verified</div>
      ),
      cell: ({ row }) => {
        const provider = row.original.provider;
        const verified = provider?.verified || false;

        return (
          <div className="flex justify-center">
            {verified ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : (
              <XCircle className="h-5 w-5 text-muted-foreground" />
            )}
          </div>
        );
      },
      filterFn: (row, id, value) => {
        const verified = row.getValue(id) as boolean;
        return value.includes(verified);
      },
    },
    {
      accessorKey: "rfq_status",
      header: "RFQ Status",
      cell: ({ row }) => {
        const rfqStatus = row.original.rfq_status;
        const actualStatus = row.original.status; // The actual RFQ provider status

        // Use rfq_status for UI display, fallback to actual status
        const displayStatus = rfqStatus || actualStatus;

        // Show appropriate badges based on status
        if (displayStatus === "matched") {
          return (
            <Badge
              variant="outline"
              className="bg-green-100 text-green-800 border-green-200"
            >
              Matched
            </Badge>
          );
        } else if (displayStatus === "invited") {
          return <Badge variant="default">Invited</Badge>;
        } else if (displayStatus === "bid_submitted") {
          return (
            <Badge
              variant="default"
              className="bg-green-100 text-green-800 border-green-200"
            >
              Bid Submitted
            </Badge>
          );
        } else if (displayStatus === "selected") {
          return (
            <Badge variant="secondary" className="bg-secondary/50">
              Selected
            </Badge>
          );
        } else if (displayStatus === "declined") {
          return <Badge variant="destructive">Declined</Badge>;
        } else if (rfqStatus === "manually_added") {
          return (
            <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200">
              Manually Added
            </Badge>
          );
        }

        // For any other status or null
        return (
          <Badge variant="outline" className="bg-muted/50">
            Available
          </Badge>
        );
      },
    },
  ];
}

interface ProviderSelectionTableProps {
  data: RFQProviderForTable[];
  onRowSelectionChange?: (selectedProviderIds: string[]) => void;
}

function ProviderSelectionTable({
  data,
  onRowSelectionChange,
}: ProviderSelectionTableProps) {
  // Define columns without custom parameters
  const columns = useMemo(() => getProviderSelectionColumns(), []);

  return (
    <div className="space-y-4">
      <DataTable
        columns={columns}
        data={data}
        filterConfig={{
          search: {
            placeholder: "Search providers...",
            columnId: "provider_name",
          },
          facets: [
            {
              columnId: "rfq_status",
              title: "RFQ Status",
              options: [
                { label: "Available", value: "" },
                { label: "Matched", value: "matched" },
                { label: "Manually Added", value: "manually_added" },
                { label: "Selected", value: "selected" },
              ],
            },
          ],
        }}
        enableRowSelection={true} // Enable built-in selection and count
        initialPageSize={10}
        onRowSelectionChange={(selectedRowIndices) => {
          // Convert row indices to provider IDs
          const selectedProviderIds = Object.keys(selectedRowIndices)
            .filter(index => selectedRowIndices[index])
            .map(index => data[parseInt(index)]?.provider_id)
            .filter(Boolean);

          onRowSelectionChange?.(selectedProviderIds);
        }}
      />
    </div>
  );
}

/**
 * Loading skeleton for the provider selection data table
 *
 * Matches the exact layout of the ProviderSelectionDataTable:
 * - Search bar and filters
 * - Table with 4 columns (select, provider, status, verified, rfq status)
 * - Selection summary area
 * - Supply chain-themed styling
 */
export function ProviderSelectionTableSkeleton() {
  return (
    <div className="space-y-4">
      {/* Search and filter toolbar skeleton */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-10 w-64" /> {/* Search input */}
        <div className="flex items-center gap-2">
          <Skeleton className="h-10 w-32" /> {/* RFQ Status filter */}
          <Skeleton className="h-10 w-10" /> {/* Column visibility */}
        </div>
      </div>

      {/* Table skeleton with supply chain-themed styling */}
      <div className="rounded-md border bg-card shadow-sm">
        {/* Table header */}
        <div className="border-b border-border/50 p-4">
          <div className="flex items-center gap-4">
            <Skeleton className="h-5 w-5" /> {/* Select checkbox */}
            <Skeleton className="h-5 w-32" /> {/* Provider column */}
            <Skeleton className="h-5 w-20" /> {/* Status column */}
            <Skeleton className="h-5 w-20" /> {/* Verified column */}
            <Skeleton className="h-5 w-24" /> {/* RFQ Status column */}
          </div>
        </div>

        {/* Table rows */}
        <div className="divide-y divide-border/30">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="p-4">
              <div className="flex items-center gap-4">
                <Skeleton className="h-5 w-5" /> {/* Select checkbox */}
                <Skeleton className="h-5 w-32" /> {/* Provider name */}
                <Skeleton className="h-6 w-16 rounded-full" /> {/* Status badge */}
                <Skeleton className="h-5 w-5 rounded-full" /> {/* Verified icon */}
                <Skeleton className="h-6 w-20 rounded-full" /> {/* RFQ Status badge */}
              </div>
            </div>
          ))}
        </div>

        {/* Pagination skeleton */}
        <div className="border-t border-border/50 p-4">
          <div className="flex items-center justify-between">
            <Skeleton className="h-5 w-32" /> {/* Results count */}
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-8" /> {/* Previous button */}
              <Skeleton className="h-8 w-8" /> {/* Page number */}
              <Skeleton className="h-8 w-8" /> {/* Next button */}
            </div>
          </div>
        </div>
      </div>

      {/* Selection summary skeleton */}
      <Skeleton className="h-5 w-40" />
    </div>
  );
}

/**
 * Error boundary for provider selection data table
 */
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class ProviderSelectionErrorBoundary extends Component<
  { children: ReactNode; onRetry?: () => void },
  ErrorBoundaryState
> {
  constructor(props: { children: ReactNode; onRetry?: () => void }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error("Provider selection data table error:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex flex-col items-center justify-center p-8 text-center space-y-4">
          <AlertCircle className="h-12 w-12 text-destructive" />
          <div>
            <h3 className="text-lg font-semibold">Failed to load providers</h3>
            <p className="text-sm text-muted-foreground mt-1">
              There was an error loading the provider data. Please try again.
            </p>
          </div>
          <Button
            onClick={() => {
              this.setState({ hasError: false });
              this.props.onRetry?.();
            }}
            variant="outline"
          >
            Try Again
          </Button>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Internal component that fetches data with suspense
 */
interface ProviderSelectionDataFetcherProps {
  rfqId: string;
  searchQuery: string;
  statusFilter: string;
  manuallyAddedProviders: ManualProviderType[];
  onRowSelectionChange?: (selectedProviderIds: string[]) => void;
}

function ProviderSelectionDataFetcher({
  rfqId,
  searchQuery,
  statusFilter,
  manuallyAddedProviders,
  onRowSelectionChange,
}: ProviderSelectionDataFetcherProps) {
  // Use the consolidated hook to fetch data
  const { transformedProviders, isLoading, error } = useProviderSelectionData({
    rfqId,
    searchQuery,
    statusFilter,
    manuallyAddedProviders,
  });

  // Handle loading state
  if (isLoading) {
    return <ProviderSelectionTableSkeleton />;
  }

  // Handle error state
  if (error) {
    throw error; // Let error boundary handle this
  }

  return (
    <ProviderSelectionTable
      data={transformedProviders}
      onRowSelectionChange={onRowSelectionChange}
    />
  );
}

/**
 * Enhanced wrapper for the provider selection data table
 *
 * Provides proper loading states and eliminates the flash of "No results found"
 * during initial data loading. Uses regular TanStack Query to avoid setState-during-render errors.
 * Maintains all existing functionality.
 */
interface ProviderSelectionDataTableProps {
  rfqId: string;
  searchQuery: string;
  statusFilter: string;
  manuallyAddedProviders: ManualProviderType[];
  onRowSelectionChange?: (selectedProviderIds: string[]) => void;
}

export function ProviderSelectionDataTable({
  rfqId,
  searchQuery,
  statusFilter,
  manuallyAddedProviders,
  onRowSelectionChange,
}: ProviderSelectionDataTableProps) {
  return (
    <ProviderSelectionErrorBoundary>
      <ProviderSelectionDataFetcher
        rfqId={rfqId}
        searchQuery={searchQuery}
        statusFilter={statusFilter}
        manuallyAddedProviders={manuallyAddedProviders}
        onRowSelectionChange={onRowSelectionChange}
      />
    </ProviderSelectionErrorBoundary>
  );
}
