"use client";

import { useState, useMemo } from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  RowSelectionState,
  FilterFn,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "@/components/ui/sonner";
import { deleteRFQAction } from "@/lib/actions/rfq.actions";
import {
  MoreHorizontal,
  Eye,
  Edit,
  Trash,
  Copy,
  Calendar,
  MapPin,
  Search,
  Users,
  Mail,
  FileText,
  BarChart,
  Clock,
} from "lucide-react";
import Link from "next/link";
import { RFQ, PaginatedResponse, EnrichedRFQ } from "@/lib/schemas";

import { Badge } from "@/components/ui/badge";
import { useRouter } from "next/navigation";
import { DataTableFacetedFilter } from "@/components/ui/tables/faceted-filter";
import { formatDistanceToNowSafely } from "@/lib/utils/date";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import {
  Cross2Icon,
  MixerHorizontalIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  CaretSortIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  DoubleArrowLeftIcon,
  DoubleArrowRightIcon
} from "@radix-ui/react-icons";
import ReactCountryFlag from "react-country-flag";

interface RFQsDataTableProps {
  rfqs: EnrichedRFQ[];
  pagination: PaginatedResponse<RFQ>["pagination"];
}

export function RFQsDataTable({
  rfqs,
}: RFQsDataTableProps) {
  const router = useRouter();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [rfqToDelete, setRfqToDelete] = useState<RFQ | null>(null);

  // Table state
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
    created_at: false, // Hide created_at column by default
    updated_at: true, // Hide updated_at column by default
    origin_country_name: false, // Hide origin_country_name column by default
    destination_country_name: false, // Hide destination_country_name column by default
  });
  const [globalFilter, setGlobalFilter] = useState<string>("");
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  const handleDelete = async () => {
    if (!rfqToDelete) return;

    try {
      const result = await deleteRFQAction(rfqToDelete.id);

      if (result.success) {
        toast.success(
          <div className="space-y-1">
            <p className="font-semibold text-base">Success</p>
            <p className="text-sm text-muted-foreground">
              RFQ deleted successfully
            </p>
          </div>,
          {
            className:
              "p-4 rounded-lg border border-green-200 dark:border-green-900/30 bg-white dark:bg-card shadow-lg",
            duration: 5000,
          },
        );
        router.refresh();
      } else {
        toast.error(
          <div className="space-y-1">
            <p className="font-semibold text-base">Error</p>
            <p className="text-sm text-muted-foreground">
              {result.error || "Failed to delete RFQ"}
            </p>
          </div>,
          {
            className:
              "p-4 rounded-lg border border-red-200 dark:border-red-900/30 bg-white dark:bg-card shadow-lg",
            duration: 5000,
          },
        );
      }
    } catch (error) {
      toast.error(
        <div className="space-y-1">
          <p className="font-semibold text-base">Error</p>
          <p className="text-sm text-muted-foreground">
            An unexpected error occurred
          </p>
        </div>,
        {
          className:
            "p-4 rounded-lg border border-red-200 dark:border-red-900/30 bg-white dark:bg-card shadow-lg",
          duration: 5000,
        },
      );
    } finally {
      setDeleteDialogOpen(false);
      setRfqToDelete(null);
    }
  };

  // Define columns with enhanced features
  const columns = useMemo<ColumnDef<EnrichedRFQ>[]>(
    () => [
      {
        id: "select",
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
            aria-label="Select all"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
          />
        ),
        enableSorting: false,
        enableHiding: false,
        size: 40,
      },
      {
        accessorKey: "sequence_number",
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-medium"
          >
            RFQ #
            {column.getIsSorted() === "asc" ? (
              <ArrowUpIcon className="ml-2 h-4 w-4" />
            ) : column.getIsSorted() === "desc" ? (
              <ArrowDownIcon className="ml-2 h-4 w-4" />
            ) : (
              <CaretSortIcon className="ml-2 h-4 w-4" />
            )}
          </Button>
        ),
        cell: ({ row }) => (
          <div className="font-medium">
            <Link
              href={`/dashboard/rfqs/${row.original.id}`}
              className="hover:underline"
            >
              {row.original.sequence_number}
            </Link>
          </div>
        ),
        enableSorting: true,
        enableHiding: false,
      },
      {
        accessorKey: "status",
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-medium"
          >
            Status
            {column.getIsSorted() === "asc" ? (
              <ArrowUpIcon className="ml-2 h-4 w-4" />
            ) : column.getIsSorted() === "desc" ? (
              <ArrowDownIcon className="ml-2 h-4 w-4" />
            ) : (
              <CaretSortIcon className="ml-2 h-4 w-4" />
            )}
          </Button>
        ),
        cell: ({ row }) => {
          const status = row.original.status;
          let variant: "default" | "secondary" | "outline" | "destructive" =
            "default";
          let statusText = status.charAt(0).toUpperCase() + status.slice(1);

          switch (status) {
            case "draft":
              variant = "outline";
              break;
            case "ready":
              variant = "secondary";
              break;
            case "sent":
              variant = "default";
              break;
            case "closed":
              variant = "destructive";
              break;
          }

          return <Badge variant={variant}>{statusText}</Badge>;
        },
        enableSorting: true,
        filterFn: (row, id, value) => {
          return value.includes(row.getValue(id));
        },
      },

      {
        id: "origin_location",
        accessorFn: (row) => {
          const city = row.origin_city;
          const countryCode = row.origin_country_code;
          return `${city}${countryCode ? `, ${countryCode}` : ''}`;
        },
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-medium"
          >
            Origin
            {column.getIsSorted() === "asc" ? (
              <ArrowUpIcon className="ml-2 h-4 w-4" />
            ) : column.getIsSorted() === "desc" ? (
              <ArrowDownIcon className="ml-2 h-4 w-4" />
            ) : (
              <CaretSortIcon className="ml-2 h-4 w-4" />
            )}
          </Button>
        ),
        cell: ({ row }) => {
          const countryCode = row.original.origin_country_code;
          const city = row.original.origin_city;
          return (
            <div className="flex items-center">
              {countryCode ? (
                <ReactCountryFlag
                  countryCode={countryCode}
                  svg
                  className="mr-2"
                  style={{
                    width: '1.2em',
                    height: '1.2em',
                  }}
                  title={`${row.original.origin_country_name} Flag`}
                />
              ) : (
                <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
              )}
              <span>{city}{countryCode ? `, ${countryCode}` : ''}</span>
            </div>
          );
        },
        sortingFn: (rowA, rowB) => {
          const cityA = rowA.original.origin_city;
          const cityB = rowB.original.origin_city;
          return cityA.localeCompare(cityB);
        },
        enableSorting: true,
        filterFn: "includesString",
      },
      {
        accessorKey: "origin_country_name",
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-medium"
          >
            Origin Country
            {column.getIsSorted() === "asc" ? (
              <ArrowUpIcon className="ml-2 h-4 w-4" />
            ) : column.getIsSorted() === "desc" ? (
              <ArrowDownIcon className="ml-2 h-4 w-4" />
            ) : (
              <CaretSortIcon className="ml-2 h-4 w-4" />
            )}
          </Button>
        ),
        cell: ({ row }) => {
          const countryCode = row.original.origin_country_code;
          const countryName = row.original.origin_country_name;
          return (
            <div className="flex items-center">
              {countryCode ? (
                <ReactCountryFlag
                  countryCode={countryCode}
                  svg
                  className="mr-2"
                  style={{
                    width: '1.2em',
                    height: '1.2em',
                  }}
                  title={`${countryName} Flag`}
                />
              ) : (
                <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
              )}
              <span>{countryName} {countryCode ? `(${countryCode})` : ''}</span>
            </div>
          );
        },
        sortingFn: (rowA, rowB) => {
          const countryA = rowA.original.origin_country_name;
          const countryB = rowB.original.origin_country_name;
          return countryA.localeCompare(countryB);
        },
        enableSorting: true,
        filterFn: (row, _id, value) => {
          if (!value || !Array.isArray(value) || value.length === 0) return true;

          const countryName = row.original.origin_country_name;
          const countryCode = row.original.origin_country_code;

          // Check if any of the filter values match the country name
          // (filter values are now country names, but we still support code matching for search)
          return value.some(filterValue => {
            const filterValueLower = filterValue.toLowerCase();
            return (
              (countryName && countryName.toLowerCase() === filterValueLower) ||
              (countryCode && countryCode.toLowerCase() === filterValueLower)
            );
          });
        },
      },
      {
        id: "destination_location",
        accessorFn: (row) => {
          const city = row.destination_city;
          const countryCode = row.destination_country_code;
          return `${city}${countryCode ? `, ${countryCode}` : ''}`;
        },
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-medium"
          >
            Destination
            {column.getIsSorted() === "asc" ? (
              <ArrowUpIcon className="ml-2 h-4 w-4" />
            ) : column.getIsSorted() === "desc" ? (
              <ArrowDownIcon className="ml-2 h-4 w-4" />
            ) : (
              <CaretSortIcon className="ml-2 h-4 w-4" />
            )}
          </Button>
        ),
        cell: ({ row }) => {
          const countryCode = row.original.destination_country_code;
          const city = row.original.destination_city;
          return (
            <div className="flex items-center">
              {countryCode ? (
                <ReactCountryFlag
                  countryCode={countryCode}
                  svg
                  className="mr-2"
                  style={{
                    width: '1.2em',
                    height: '1.2em',
                  }}
                  title={`${row.original.destination_country_name} Flag`}
                />
              ) : (
                <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
              )}
              <span>{city}{countryCode ? `, ${countryCode}` : ''}</span>
            </div>
          );
        },
        sortingFn: (rowA, rowB) => {
          const cityA = rowA.original.destination_city;
          const cityB = rowB.original.destination_city;
          return cityA.localeCompare(cityB);
        },
        enableSorting: true,
        filterFn: "includesString",
      },
      {
        accessorKey: "destination_country_name",
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-medium"
          >
            Dest. Country
            {column.getIsSorted() === "asc" ? (
              <ArrowUpIcon className="ml-2 h-4 w-4" />
            ) : column.getIsSorted() === "desc" ? (
              <ArrowDownIcon className="ml-2 h-4 w-4" />
            ) : (
              <CaretSortIcon className="ml-2 h-4 w-4" />
            )}
          </Button>
        ),
        cell: ({ row }) => {
          const countryCode = row.original.destination_country_code;
          const countryName = row.original.destination_country_name;
          return (
            <div className="flex items-center">
              {countryCode ? (
                <ReactCountryFlag
                  countryCode={countryCode}
                  svg
                  className="mr-2"
                  style={{
                    width: '1.2em',
                    height: '1.2em',
                  }}
                  title={`${countryName} Flag`}
                />
              ) : (
                <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
              )}
              <span>{countryName} {countryCode ? `(${countryCode})` : ''}</span>
            </div>
          );
        },
        sortingFn: (rowA, rowB) => {
          const countryA = rowA.original.destination_country_name;
          const countryB = rowB.original.destination_country_name;
          return countryA.localeCompare(countryB);
        },
        enableSorting: true,
        filterFn: (row, _id, value) => {
          if (!value || !Array.isArray(value) || value.length === 0) return true;

          const countryName = row.original.destination_country_name;
          const countryCode = row.original.destination_country_code;

          // Check if any of the filter values match the country name
          // (filter values are now country names, but we still support code matching for search)
          return value.some(filterValue => {
            const filterValueLower = filterValue.toLowerCase();
            return (
              (countryName && countryName.toLowerCase() === filterValueLower) ||
              (countryCode && countryCode.toLowerCase() === filterValueLower)
            );
          });
        },
      },

      {
        accessorKey: "invitedProvidersCount",
        header: ({ column }) => (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                className="font-medium"
              >
                Invited
                {column.getIsSorted() === "asc" ? (
                  <ArrowUpIcon className="ml-2 h-4 w-4" />
                ) : column.getIsSorted() === "desc" ? (
                  <ArrowDownIcon className="ml-2 h-4 w-4" />
                ) : (
                  <CaretSortIcon className="ml-2 h-4 w-4" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="top" className="max-w-xs">
              <p>
                <strong>Invited Providers:</strong> The number of providers that have been sent an invitation email for this RFQ (providers with a non-null invited_at timestamp).
              </p>
            </TooltipContent>
          </Tooltip>
        ),
        cell: ({ row }) => {
          const count = row.original.invitedProvidersCount;
          return (
            <div className="flex items-center">
              <Mail className="mr-2 h-4 w-4 text-muted-foreground" />
              <Badge variant="outline" className="font-medium">
                {count}
              </Badge>
            </div>
          );
        },
        enableSorting: true,
      },
      {
        accessorKey: "matchedProvidersCount",
        header: ({ column }) => (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                className="font-medium"
              >
                Matched
                {column.getIsSorted() === "asc" ? (
                  <ArrowUpIcon className="ml-2 h-4 w-4" />
                ) : column.getIsSorted() === "desc" ? (
                  <ArrowDownIcon className="ml-2 h-4 w-4" />
                ) : (
                  <CaretSortIcon className="ml-2 h-4 w-4" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="top" className="max-w-xs">
              <p>
                <strong>Matched Providers:</strong> The number of providers that match the RFQ criteria based on equipment type and route (origin/destination). These providers are potential candidates for invitation.
              </p>
            </TooltipContent>
          </Tooltip>
        ),
        cell: ({ row }) => {
          const count = row.original.matchedProvidersCount;
          return (
            <div className="flex items-center">
              <Users className="mr-2 h-4 w-4 text-muted-foreground" />
              <Badge variant="outline" className="font-medium">
                {count}
              </Badge>
            </div>
          );
        },
        enableSorting: true,
      },

      {
        accessorKey: "bidsReceivedCount",
        header: ({ column }) => (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                className="font-medium"
              >
                Bids
                {column.getIsSorted() === "asc" ? (
                  <ArrowUpIcon className="ml-2 h-4 w-4" />
                ) : column.getIsSorted() === "desc" ? (
                  <ArrowDownIcon className="ml-2 h-4 w-4" />
                ) : (
                  <CaretSortIcon className="ml-2 h-4 w-4" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="top" className="max-w-xs">
              <p>
                <strong>Bids Received:</strong> The number of bids submitted by providers for this RFQ. Each provider can submit one bid per RFQ.
              </p>
            </TooltipContent>
          </Tooltip>
        ),
        cell: ({ row }) => {
          const count = row.original.bidsReceivedCount;
          return (
            <div className="flex items-center">
              <FileText className="mr-2 h-4 w-4 text-muted-foreground" />
              <Badge variant="outline" className="font-medium">
                {count}
              </Badge>
            </div>
          );
        },
        enableSorting: true,
      },
      {
        accessorKey: "responseRate",
        header: ({ column }) => (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                className="font-medium"
              >
                Response
                {column.getIsSorted() === "asc" ? (
                  <ArrowUpIcon className="ml-2 h-4 w-4" />
                ) : column.getIsSorted() === "desc" ? (
                  <ArrowDownIcon className="ml-2 h-4 w-4" />
                ) : (
                  <CaretSortIcon className="ml-2 h-4 w-4" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="top" className="max-w-xs">
              <p>
                <strong>Response Rate:</strong> The percentage of invited providers who submitted bids (Bids Received ÷ Invited Providers × 100%).
                <br /><br />
                <span className="text-xs">
                  Color indicates performance:
                  <br />• <span className="text-green-700">Green</span>: 75-100% (Excellent)
                  <br />• <span className="text-yellow-700">Yellow</span>: 50-74% (Good)
                  <br />• <span className="text-orange-700">Orange</span>: 25-49% (Fair)
                  <br />• <span className="text-red-700">Red</span>: 0-24% (Poor)
                </span>
              </p>
            </TooltipContent>
          </Tooltip>
        ),
        cell: ({ row }) => {
          const rate = row.original.responseRate;
          // Determine color based on response rate
          let badgeClass = "bg-red-50 text-red-700 border-red-200";
          if (rate >= 75) {
            badgeClass = "bg-green-50 text-green-700 border-green-200";
          } else if (rate >= 50) {
            badgeClass = "bg-yellow-50 text-yellow-700 border-yellow-200";
          } else if (rate >= 25) {
            badgeClass = "bg-orange-50 text-orange-700 border-orange-200";
          }

          return (
            <div className="flex items-center">
              <BarChart className="mr-2 h-4 w-4 text-muted-foreground" />
              <Badge
                variant="outline"
                className={`font-medium ${badgeClass}`}
              >
                {rate.toFixed(0)}%
              </Badge>
            </div>
          );
        },
        enableSorting: true,
      },

      {
        accessorKey: "created_at",
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-medium"
          >
            Created
            {column.getIsSorted() === "asc" ? (
              <ArrowUpIcon className="ml-2 h-4 w-4" />
            ) : column.getIsSorted() === "desc" ? (
              <ArrowDownIcon className="ml-2 h-4 w-4" />
            ) : (
              <CaretSortIcon className="ml-2 h-4 w-4" />
            )}
          </Button>
        ),
        cell: ({ row }) => {
          return (
            <div className="flex items-center">
              <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
              <span>{formatDistanceToNowSafely(row.original.created_at)}</span>
            </div>
          );
        },
        enableSorting: true,
      },
      {
        accessorKey: "updated_at",
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-medium"
          >
            Updated
            {column.getIsSorted() === "asc" ? (
              <ArrowUpIcon className="ml-2 h-4 w-4" />
            ) : column.getIsSorted() === "desc" ? (
              <ArrowDownIcon className="ml-2 h-4 w-4" />
            ) : (
              <CaretSortIcon className="ml-2 h-4 w-4" />
            )}
          </Button>
        ),
        cell: ({ row }) => {
          return (
            <div className="flex items-center">
              <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
              <span>{formatDistanceToNowSafely(row.original.updated_at)}</span>
            </div>
          );
        },
        enableSorting: true,
      },
      {
        id: "actions",
        header: () => <div className="text-right">Actions</div>,
        cell: ({ row }) => {
          const rfq = row.original;

          return (
            <div className="text-right">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                  <DropdownMenuItem asChild>
                    <Link href={`/dashboard/rfqs/${rfq.id}`}>
                      <Eye className="mr-2 h-4 w-4" />
                      View
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href={`/dashboard/rfqs/${rfq.id}/edit`}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href={`/dashboard/rfqs/${rfq.id}/duplicate`}>
                      <Copy className="mr-2 h-4 w-4" />
                      Duplicate
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    className="text-destructive"
                    onClick={() => {
                      setRfqToDelete(rfq);
                      setDeleteDialogOpen(true);
                    }}
                  >
                    <Trash className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          );
        },
        enableSorting: false,
        enableHiding: false,
      },
    ],
    []
  );

  // Create a custom global filter function that searches across multiple columns
  const fuzzyFilter: FilterFn<EnrichedRFQ> = (row, _columnId, filterValue) => {
    // Skip filtering if no filter value is provided
    if (!filterValue || typeof filterValue !== "string") return true;

    const searchValue = filterValue.toLowerCase();

    // Search in sequence_number
    const sequenceNumber = row.getValue("sequence_number") as string;
    if (sequenceNumber?.toLowerCase().includes(searchValue)) return true;

    // Search in origin data
    // Access directly from the row.original since these are not accessors in the table
    const originCity = row.original.origin_city;
    if (originCity?.toLowerCase().includes(searchValue)) return true;

    const originCountry = row.original.origin_country_name;
    if (originCountry?.toLowerCase().includes(searchValue)) return true;

    const originCountryCode = row.original.origin_country_code;
    if (originCountryCode?.toLowerCase().includes(searchValue)) return true;

    // Search in destination data
    // Access directly from the row.original since these are not accessors in the table
    const destinationCity = row.original.destination_city;
    if (destinationCity?.toLowerCase().includes(searchValue)) return true;

    const destinationCountry = row.original.destination_country_name;
    if (destinationCountry?.toLowerCase().includes(searchValue)) return true;

    const destinationCountryCode = row.original.destination_country_code;
    if (destinationCountryCode?.toLowerCase().includes(searchValue)) return true;

    // Search in status
    const status = row.getValue("status") as string;
    if (status?.toLowerCase().includes(searchValue)) return true;

    // Search in numeric fields by converting to string
    // Invited Providers Count
    const invitedCount = row.getValue("invitedProvidersCount") as number;
    if (invitedCount?.toString().includes(searchValue)) return true;

    // Matched Providers Count
    const matchedCount = row.getValue("matchedProvidersCount") as number;
    if (matchedCount?.toString().includes(searchValue)) return true;

    // Bids Received Count
    const bidsCount = row.getValue("bidsReceivedCount") as number;
    if (bidsCount?.toString().includes(searchValue)) return true;

    // Response Rate
    const responseRate = row.getValue("responseRate") as number;
    if (responseRate?.toString().includes(searchValue)) return true;

    return false;
  };

  // No custom filter function needed as we're using inline filter functions

  // Create table instance with all features
  const table = useReactTable({
    data: rfqs,
    columns,
    state: {
      sorting,
      columnFilters,
      globalFilter,
      rowSelection,
      columnVisibility,
      pagination,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onColumnVisibilityChange: setColumnVisibility,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    globalFilterFn: fuzzyFilter,
  });

  // Get unique values for filter options
  const statusOptions = useMemo(() => {
    return [
      { label: "Draft", value: "draft" },
      { label: "Ready", value: "ready" },
      { label: "Sent", value: "sent" },
      { label: "Closed", value: "closed" },
    ];
  }, []);

  // Create filter options for origin locations
  const originLocationOptions = useMemo(() => {
    // Create a set of unique origin cities with country codes
    const uniqueLocations = new Set<string>();

    // Collect all unique cities with country codes in the new format
    rfqs.forEach((rfq) => {
      if (rfq.origin_city) {
        const city = rfq.origin_city;
        const countryCode = rfq.origin_country_code;
        uniqueLocations.add(`${city}${countryCode ? `, ${countryCode}` : ''}`);
      }
    });

    return Array.from(uniqueLocations).map((location) => ({
      label: location,
      value: location,
    }));
  }, [rfqs]);

  // Create filter options for origin countries
  const originCountryOptions = useMemo(() => {
    // Create a map to store unique countries with their names and codes
    const uniqueCountries = new Map<string, { name: string; code: string }>();

    // Collect all unique countries
    rfqs.forEach((rfq) => {
      if (rfq.origin_country_name && rfq.origin_country_code) {
        const key = rfq.origin_country_name; // Use country name as the key
        uniqueCountries.set(key, {
          name: rfq.origin_country_name,
          code: rfq.origin_country_code,
        });
      }
    });

    // Create options showing country name with code, but value is just the country name
    return Array.from(uniqueCountries.values())
      .map((country) => ({
        label: `${country.name} (${country.code})`,
        value: country.name,
      }))
      .sort((a, b) => a.label.localeCompare(b.label));
  }, [rfqs]);

  // Create filter options for destination locations
  const destinationLocationOptions = useMemo(() => {
    // Create a set of unique destination cities with country codes
    const uniqueLocations = new Set<string>();

    // Collect all unique cities with country codes in the new format
    rfqs.forEach((rfq) => {
      if (rfq.destination_city) {
        const city = rfq.destination_city;
        const countryCode = rfq.destination_country_code;
        uniqueLocations.add(`${city}${countryCode ? `, ${countryCode}` : ''}`);
      }
    });

    return Array.from(uniqueLocations).map((location) => ({
      label: location,
      value: location,
    }));
  }, [rfqs]);

  // Create filter options for destination countries
  const destinationCountryOptions = useMemo(() => {
    // Create a map to store unique countries with their names and codes
    const uniqueCountries = new Map<string, { name: string; code: string }>();

    // Collect all unique countries
    rfqs.forEach((rfq) => {
      if (rfq.destination_country_name && rfq.destination_country_code) {
        const key = rfq.destination_country_name; // Use country name as the key
        uniqueCountries.set(key, {
          name: rfq.destination_country_name,
          code: rfq.destination_country_code,
        });
      }
    });

    // Create options showing country name with code, but value is just the country name
    return Array.from(uniqueCountries.values())
      .map((country) => ({
        label: `${country.name} (${country.code})`,
        value: country.name,
      }))
      .sort((a, b) => a.label.localeCompare(b.label));
  }, [rfqs]);

  return (
    <div className="space-y-4">
      {/* Toolbar with search and filters */}
      <div className="flex items-center justify-between gap-2">
        <div className="flex flex-grow items-center space-x-2">
          <div className="relative w-full max-w-sm">
            <Search className="absolute left-2 top-1.5 h-4 w-4 text-muted-foreground pointer-events-none" />
            <Input
              placeholder="Search RFQs..."
              value={globalFilter ?? ""}
              onChange={(e) => setGlobalFilter(e.target.value)}
              className="h-8 w-full pl-8 focus-visible:ring-1 focus-visible:ring-offset-0 border-muted"
              type="search"
              autoComplete="off"
            />
          </div>

          <DataTableFacetedFilter
            column={table.getColumn("status")}
            title="Status"
            options={statusOptions}
          />

          <DataTableFacetedFilter
            column={table.getColumn("origin_location")}
            title="Origin"
            options={originLocationOptions}
          />

          <DataTableFacetedFilter
            column={table.getColumn("origin_country_name")}
            title="Origin Country"
            options={originCountryOptions}
          />

          <DataTableFacetedFilter
            column={table.getColumn("destination_location")}
            title="Destination"
            options={destinationLocationOptions}
          />

          <DataTableFacetedFilter
            column={table.getColumn("destination_country_name")}
            title="Dest. Country"
            options={destinationCountryOptions}
          />

          {table.getState().columnFilters.length > 0 || globalFilter ? (
            <Button
              variant="ghost"
              onClick={() => {
                table.resetColumnFilters();
                setGlobalFilter("");
              }}
              className="h-8 px-2 lg:px-3"
            >
              Reset
              <Cross2Icon className="ml-2 h-4 w-4" />
            </Button>
          ) : null}
        </div>

        {/* Column visibility dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="ml-auto h-8">
              <MixerHorizontalIcon className="mr-2 h-4 w-4" />
              View
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[150px]">
            <DropdownMenuLabel>Toggle columns</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {table
              .getAllColumns()
              .filter(
                (column) => column.getCanHide(),
              )
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) => column.toggleVisibility(!!value)}
                  >
                    {column.id === "invitedProvidersCount"
                      ? "Invited"
                      : column.id === "matchedProvidersCount"
                      ? "Matched"
                      : column.id === "bidsReceivedCount"
                      ? "Bids"
                      : column.id === "responseRate"
                      ? "Response"
                      : column.id === "origin_location"
                      ? "Origin"
                      : column.id === "origin_country_name"
                      ? "Origin Country"
                      : column.id === "destination_location"
                      ? "Destination"
                      : column.id === "destination_country_name"
                      ? "Dest. Country"
                      : column.id === "sequence_number"
                      ? "RFQ #"
                      : column.id === "created_at"
                      ? "Created"
                      : column.id === "updated_at"
                      ? "Updated"
                      : column.id}
                  </DropdownMenuCheckboxItem>
                );
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Table */}
      <div className="rounded-md border overflow-x-auto">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id} className="whitespace-nowrap">
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="whitespace-nowrap">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between px-2">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="flex items-center space-x-6 lg:space-x-8">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium">Rows per page</p>
            <select
              value={table.getState().pagination.pageSize}
              onChange={(e) => {
                table.setPageSize(Number(e.target.value));
              }}
              className="h-8 w-[70px] rounded-md border border-input bg-background"
            >
              {[10, 20, 30, 40, 50].map((pageSize) => (
                <option key={pageSize} value={pageSize}>
                  {pageSize}
                </option>
              ))}
            </select>
          </div>
          <div className="flex w-[100px] items-center justify-center text-sm font-medium">
            Page {table.getState().pagination.pageIndex + 1} of{" "}
            {table.getPageCount()}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">Go to first page</span>
              <DoubleArrowLeftIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">Go to previous page</span>
              <ChevronLeftIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">Go to next page</span>
              <ChevronRightIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">Go to last page</span>
              <DoubleArrowRightIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Delete confirmation dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the RFQ &quot;
              {rfqToDelete?.sequence_number}&quot;. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}