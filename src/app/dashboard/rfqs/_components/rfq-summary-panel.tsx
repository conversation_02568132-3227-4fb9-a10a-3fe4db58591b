"use client";

import { WeightIcon, PackageIcon, TruckIcon, CalendarIcon, BoxIcon, RulerIcon, InfoIcon, Clock, MapPin } from "lucide-react";
import { cn } from "@/lib/utils/tailwind";
import { Badge } from "@/components/ui/badge";
import { CompactLocationDisplay } from "@/components/ui/location-display";

interface RFQSummaryPanelProps {
  weight: number;
  quantity: number;
  equipmentType: string;
  equipmentQuantity: number;
  preferredShippingDate?: string;
  cargoTypes?: string;
  dimensions?: string;
  status?: string;
  statusVariant?: "default" | "secondary" | "outline" | "destructive";
  expirationDate?: string;
  // Route information
  originCity?: string;
  originAddress?: string | null;
  originPostalCode?: string | null;
  originCountryName?: string | null;
  originCountryCode?: string | null;
  destinationCity?: string;
  destinationAddress?: string | null;
  destinationPostalCode?: string | null;
  destinationCountryName?: string | null;
  destinationCountryCode?: string | null;
  className?: string;
}

export function RFQSummaryPanel({
  weight,
  quantity,
  equipmentType,
  equipmentQuantity,
  preferredShippingDate,
  cargoTypes,
  dimensions,
  status,
  statusVariant = "default",
  expirationDate,
  originCity,
  originAddress,
  originPostalCode,
  originCountryName,
  originCountryCode,
  destinationCity,
  destinationAddress,
  destinationPostalCode,
  destinationCountryName,
  destinationCountryCode,
  className,
}: RFQSummaryPanelProps) {
  // Calculate the number of items to display
  const items = [
    { key: 'weight', show: true },
    { key: 'quantity', show: true },
    { key: 'equipment', show: true },
    { key: 'origin', show: !!originCity },
    { key: 'destination', show: !!destinationCity },
    { key: 'status', show: !!status },
    { key: 'cargoTypes', show: !!cargoTypes },
    { key: 'dimensions', show: !!dimensions },
    { key: 'shippingDate', show: !!preferredShippingDate },
    { key: 'expirationDate', show: !!expirationDate },
  ].filter(item => item.show);

  // Use responsive grid that adapts to content
  const getGridClass = () => {
    if (items.length <= 3) return 'grid-cols-1 md:grid-cols-3';
    if (items.length <= 4) return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';
    return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
  };

  return (
    <div
      className={cn(
        `grid ${getGridClass()} gap-4 p-4 bg-muted/30 rounded-lg`,
        className,
      )}
    >
      <div className="flex items-center gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 text-primary">
          <WeightIcon className="h-5 w-5" />
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Weight</p>
          <p className="font-medium">{weight} tonnes</p>
        </div>
      </div>

      <div className="flex items-center gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 text-primary">
          <PackageIcon className="h-5 w-5" />
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Cargo Quantity</p>
          <p className="font-medium">{quantity}</p>
        </div>
      </div>

      <div className="flex items-center gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 text-primary">
          <TruckIcon className="h-5 w-5" />
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Equipment</p>
          <p className="font-medium">
            {equipmentType} × {equipmentQuantity}
          </p>
        </div>
      </div>

      {originCity && (
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 text-primary">
            <MapPin className="h-5 w-5" />
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Origin</p>
            <CompactLocationDisplay
              city={originCity}
              address={originAddress}
              postalCode={originPostalCode}
              countryName={originCountryName}
              countryCode={originCountryCode}
              className="text-sm font-medium"
            />
          </div>
        </div>
      )}

      {destinationCity && (
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 text-primary">
            <MapPin className="h-5 w-5" />
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Destination</p>
            <CompactLocationDisplay
              city={destinationCity}
              address={destinationAddress}
              postalCode={destinationPostalCode}
              countryName={destinationCountryName}
              countryCode={destinationCountryCode}
              className="text-sm font-medium"
            />
          </div>
        </div>
      )}

      {status && (
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 text-primary">
            <InfoIcon className="h-5 w-5" />
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Status</p>
            <Badge variant={statusVariant} className="text-sm">
              {status}
            </Badge>
          </div>
        </div>
      )}

      {cargoTypes && (
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 text-primary">
            <BoxIcon className="h-5 w-5" />
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Cargo Types</p>
            <p className="font-medium text-sm leading-tight">{cargoTypes}</p>
          </div>
        </div>
      )}

      {dimensions && (
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 text-primary">
            <RulerIcon className="h-5 w-5" />
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Dimensions</p>
            <p className="font-medium">{dimensions}</p>
          </div>
        </div>
      )}

      {preferredShippingDate && (
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 text-primary">
            <CalendarIcon className="h-5 w-5" />
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Shipping Date</p>
            <p className="font-medium">{preferredShippingDate}</p>
          </div>
        </div>
      )}

      {expirationDate && (
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 text-primary">
            <Clock className="h-5 w-5" />
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Expires</p>
            <p className="font-medium">{expirationDate}</p>
          </div>
        </div>
      )}
    </div>
  );
}
