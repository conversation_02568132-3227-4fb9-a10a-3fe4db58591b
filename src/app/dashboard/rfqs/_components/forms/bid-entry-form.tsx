"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "@/components/ui/sonner";
import { z } from "zod";
import { RFQBidSchema } from "@/lib/schemas";
import { submitBidAction } from "@/lib/actions/rfq.actions";
import { Loader2 } from "lucide-react";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("BidEntryForm");

// Define bid status options
const BID_STATUS_OPTIONS = [
  { value: "received", label: "Received" },
  { value: "under_review", label: "Under Review" },
  { value: "accepted", label: "Accepted" },
  { value: "rejected", label: "Rejected" },
  { value: "negotiating", label: "Negotiating" },
];

// Create a type for the form values
type FormValues = z.infer<typeof RFQBidSchema>;

// Define the bid entry form props
interface BidEntryFormProps {
  rfqId: string;
  providerId: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function BidEntryForm({
  rfqId,
  providerId,
  onSuccess,
  onCancel,
}: BidEntryFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Helper function to get user-friendly field labels
  const getFieldLabel = (field: string): string => {
    const fieldLabels: Record<string, string> = {
      rfq_id: "RFQ ID",
      provider_id: "Provider ID",
      price: "Bid Amount",
      currency: "Currency",
      status: "Status",
      notes: "Notes",
    };

    return fieldLabels[field] || field;
  };

  // Initialize the form with default values
  const form = useForm<FormValues>({
    resolver: zodResolver(RFQBidSchema),
    defaultValues: {
      rfq_id: rfqId,
      provider_id: providerId,
      price: 0,
      currency: "EUR", // Always EUR
      status: "received",
      notes: "",
      submitted_at: new Date().toISOString(),
      is_ai_extracted: false, // Always false for manual submissions
    },
    mode: "onChange",
  });

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    try {
      setIsSubmitting(true);
      logger.debug(
        "Submitting bid form with data:",
        JSON.stringify(data, null, 2),
      );

      // Ensure all required fields are present
      const formData = new FormData();

      // Add all form values to FormData
      formData.append("rfq_id", rfqId);
      formData.append("provider_id", providerId);
      formData.append("price", data.price.toString());
      formData.append("currency", "EUR");
      formData.append("status", data.status || "received");
      formData.append(
        "submitted_at",
        data.submitted_at || new Date().toISOString(),
      );
      formData.append("is_ai_extracted", "false"); // Always false for manual submissions

      // Add optional fields
      if (data.notes) {
        formData.append("notes", data.notes);
      }

      // Log the form data for debugging
      logger.debug(
        "Form data to be submitted:",
        Object.fromEntries(formData.entries()),
      );

      // Submit the bid using the server action
      const result = await submitBidAction(formData);

      logger.debug("Server action response:", JSON.stringify(result, null, 2));

      if (result.success) {
        toast.success(
          <div className="space-y-1">
            <p className="font-semibold text-base">Success</p>
            <p className="text-sm text-muted-foreground">
              Bid submitted successfully
            </p>
          </div>,
          {
            className:
              "p-4 rounded-lg border border-green-200 dark:border-green-900/30 bg-white dark:bg-card shadow-lg",
            duration: 5000,
          },
        );
        if (onSuccess) onSuccess();
      } else {
        // Log the error for debugging
        logger.error("Bid submission failed:", result.error);

        if (result.fieldErrors) {
          // Handle field errors
          logger.error(
            "Field errors:",
            JSON.stringify(result.fieldErrors, null, 2),
          );

          // Display each field error as a separate toast
          Object.entries(result.fieldErrors).forEach(([field, messages]) => {
            if (messages && messages.length > 0) {
              const fieldLabel = getFieldLabel(field);
              toast.error(
                <div className="space-y-1">
                  <p className="font-semibold text-base">Validation Error</p>
                  <p className="text-sm text-muted-foreground">
                    {fieldLabel}: {messages[0]}
                  </p>
                </div>,
                {
                  className:
                    "p-4 rounded-lg border border-red-200 dark:border-red-900/30 bg-white dark:bg-card shadow-lg",
                  duration: 5000,
                },
              );

              // If the field is in our form, set the error
              if (field in form.getValues()) {
                form.setError(field as any, {
                  type: "manual",
                  message: messages[0],
                });
              }
            }
          });
        } else {
          // Display general error
          const errorMessage = result.error || "Failed to submit bid";
          logger.error("General error:", errorMessage);

          toast.error(
            <div className="space-y-1">
              <p className="font-semibold text-base">Error</p>
              <p className="text-sm text-muted-foreground">{errorMessage}</p>
            </div>,
            {
              className:
                "p-4 rounded-lg border border-red-200 dark:border-red-900/30 bg-white dark:bg-card shadow-lg",
              duration: 5000,
            },
          );
        }
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "An unexpected error occurred";

      logger.error("Exception during bid submission:", error);
      logger.error("Error details:", JSON.stringify(error, null, 2));

      toast.error(
        <div className="space-y-1">
          <p className="font-semibold text-base">Error</p>
          <p className="text-sm text-muted-foreground">{errorMessage}</p>
        </div>,
        {
          className:
            "p-4 rounded-lg border border-red-200 dark:border-red-900/30 bg-white dark:bg-card shadow-lg",
          duration: 5000,
        },
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {/* Hidden fields are handled by React Hook Form's defaultValues */}

        <div className="grid grid-cols-1 gap-4">
          {/* Price field */}
          <FormField
            control={form.control}
            name="price"
            render={({ field }) => (
              <FormItem>
                <FormLabel required>Bid Amount (EUR)</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="Enter bid amount in EUR"
                    {...field}
                    value={field.value === 0 ? "" : field.value.toString()}
                    onChange={(e) => {
                      const value = e.target.value;
                      field.onChange(value === "" ? 0 : parseFloat(value));
                    }}
                  />
                </FormControl>
                <FormDescription>
                  All bids are recorded in EUR currency
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Status field */}
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Status</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {BID_STATUS_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Notes field */}
        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter any additional notes"
                  className="min-h-[60px]"
                  {...field}
                  value={field.value || ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Form actions */}
        <div className="flex justify-end space-x-2 pt-2">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Submitting...
              </>
            ) : (
              "Submit Bid"
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
