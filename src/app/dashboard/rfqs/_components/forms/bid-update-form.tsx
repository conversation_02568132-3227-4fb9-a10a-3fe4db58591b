"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "@/components/ui/sonner";
import { z } from "zod";
import { UpdateRFQBidSchema } from "@/lib/schemas";
import { updateBidAction } from "@/lib/actions/rfq.actions";
import { Loader2 } from "lucide-react";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { createLogger } from "@/lib/utils/logger/logger";
import { BidUpdateData } from "@/lib/schemas";

// Create a logger instance for this component
const logger = createLogger("BidUpdateForm");

// Define bid status options
const BID_STATUS_OPTIONS = [
  { value: "received", label: "Received" },
  { value: "under_review", label: "Under Review" },
  { value: "accepted", label: "Accepted" },
  { value: "rejected", label: "Rejected" },
  { value: "negotiating", label: "Negotiating" },
];

// Create a type for the form values
type FormValues = z.infer<typeof UpdateRFQBidSchema>;

interface BidUpdateFormProps {
  bidId: string;
  initialData: BidUpdateData;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function BidUpdateForm({
  bidId,
  initialData,
  onSuccess,
  onCancel,
}: BidUpdateFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize the form with React Hook Form
  const form = useForm<FormValues>({
    resolver: zodResolver(UpdateRFQBidSchema),
    defaultValues: {
      id: bidId,
      price: initialData.price,
      status: initialData.status as
        | "received"
        | "under_review"
        | "accepted"
        | "rejected"
        | "negotiating",
      notes: initialData.notes || "",
    },
    mode: "onChange",
  });

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    try {
      setIsSubmitting(true);
      logger.debug("Submitting bid update:", data);

      // Create FormData object
      const formData = new FormData();

      // Add all form values to FormData
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          formData.append(key, value.toString());
        }
      });

      // Submit the bid update using the server action
      const result = await updateBidAction(formData);

      if (result.success) {
        toast.success(
          <div className="space-y-1">
            <p className="font-semibold text-base">Success</p>
            <p className="text-sm text-muted-foreground">
              Bid updated successfully
            </p>
          </div>,
          {
            className:
              "p-4 rounded-lg border border-green-200 dark:border-green-900/30 bg-white dark:bg-card shadow-lg",
            duration: 5000,
          },
        );

        // Call onSuccess callback if provided
        if (onSuccess) {
          onSuccess();
        }
      } else {
        // Handle validation errors
        if (result.fieldErrors) {
          // Set field errors in the form
          Object.entries(result.fieldErrors).forEach(([field, errors]) => {
            if (errors && errors.length > 0) {
              // Type-safe error setting
              if (
                field === "id" ||
                field === "price" ||
                field === "status" ||
                field === "notes"
              ) {
                form.setError(field, {
                  type: "manual",
                  message: errors[0],
                });
              }
            }
          });
        }

        toast.error(
          <div className="space-y-1">
            <p className="font-semibold text-base">Error</p>
            <p className="text-sm text-muted-foreground">
              {result.error || "Failed to update bid"}
            </p>
          </div>,
          {
            className:
              "p-4 rounded-lg border border-red-200 dark:border-red-900/30 bg-white dark:bg-card shadow-lg",
            duration: 5000,
          },
        );
      }
    } catch (error) {
      logger.error("Error updating bid:", error);
      toast.error(
        <div className="space-y-1">
          <p className="font-semibold text-base">Error</p>
          <p className="text-sm text-muted-foreground">
            An unexpected error occurred
          </p>
        </div>,
        {
          className:
            "p-4 rounded-lg border border-red-200 dark:border-red-900/30 bg-white dark:bg-card shadow-lg",
          duration: 5000,
        },
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 gap-4">
          {/* Price field */}
          <FormField
            control={form.control}
            name="price"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Bid Amount (EUR)</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="Enter bid amount in EUR"
                    {...field}
                    value={field.value?.toString() || ""}
                    onChange={(e) =>
                      field.onChange(
                        e.target.value === ""
                          ? undefined
                          : parseFloat(e.target.value),
                      )
                    }
                  />
                </FormControl>
                <FormDescription>
                  All bids are recorded in EUR currency
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Status field */}
          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel required>Status</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select bid status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {BID_STATUS_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>Current status of the bid</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Notes field */}
        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter any additional notes"
                  className="min-h-[60px]"
                  {...field}
                  value={field.value || ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Form actions */}
        <div className="flex justify-end space-x-2 pt-2">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Updating...
              </>
            ) : (
              "Update Bid"
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
