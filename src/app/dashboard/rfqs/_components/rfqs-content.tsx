import { getRFQsAction } from "@/lib/actions/rfq.actions";
import { RFQsDataTable } from "./rfqs-data-table";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";
import { createLogger } from "@/lib/utils/logger/logger";
import { fetchRFQProviders, fetchRFQBids, matchProvidersForRFQ } from "@/lib/services/rfq.service";
import { fetchCountries } from "@/lib/services/country.service";

const logger = createLogger("RFQsContent");

/**
 * Server Component for fetching RFQs data with enhanced business insights
 *
 * This component is responsible for:
 * - Fetching all RFQs data using the getRFQsAction with a large page size
 * - Fetching additional data for each RFQ (invited providers, matched providers, bids)
 * - Handling error states
 * - Passing enriched data to the client-side RFQsDataTable component
 */
export async function RFQsContent() {
  // Fetch all RFQs with a large page size to get the entire dataset
  // Client-side pagination will handle displaying the appropriate page
  const result = await getRFQsAction({
    page: 1,
    pageSize: 1000, // Large page size to get all data
  });

  if (!result.success) {
    logger.error("Error fetching RFQs data:", result.error);
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{result.error}</AlertDescription>
      </Alert>
    );
  }

  // Enrich RFQs with provider and bid information
  const rfqs = result.data.data;
  const enrichedRFQs = await Promise.all(
    rfqs.map(async (rfq) => {
      try {
        // Get providers for this RFQ
        const providers = await fetchRFQProviders(rfq.id);

        // Filter to only count providers that have been invited (invited_at is not null)
        const invitedProviders = providers.filter(provider => provider.invited_at !== null);

        // Get matched providers for this RFQ
        const matchedProviders = await matchProvidersForRFQ(rfq.id);

        // Get bids for this RFQ
        const bids = await fetchRFQBids(rfq.id);

        // Calculate response rate (bids received / invited providers)
        const invitedCount = invitedProviders.length;
        const bidsCount = bids.length;
        const responseRate = invitedCount > 0 ? (bidsCount / invitedCount) * 100 : 0;

        // Return enriched RFQ with additional data
        return {
          ...rfq,
          invitedProvidersCount: invitedCount,
          matchedProvidersCount: matchedProviders.length,
          bidsReceivedCount: bidsCount,
          responseRate: responseRate,
        };
      } catch (error) {
        logger.error(`Error enriching RFQ ${rfq.id}:`, error);
        // Return original RFQ with default values if enrichment fails
        return {
          ...rfq,
          invitedProvidersCount: 0,
          matchedProvidersCount: 0,
          bidsReceivedCount: 0,
          responseRate: 0,
        };
      }
    })
  );

  // Fetch countries for filters
  const countriesResult = await fetchCountries();
  const countries = countriesResult.data;

  // Create maps of country IDs to country names and codes for easier lookup
  const countryNameMap = new Map();
  const countryCodeMap = new Map();
  countries.forEach(country => {
    countryNameMap.set(country.id, country.name);
    countryCodeMap.set(country.id, country.alpha2_code);
  });

  // Add country names and codes to enriched RFQs
  const rfqsWithCountryData = enrichedRFQs.map(rfq => ({
    ...rfq,
    origin_country_name: countryNameMap.get(rfq.origin_country_id) || "Unknown",
    origin_country_code: countryCodeMap.get(rfq.origin_country_id) || "",
    destination_country_name: countryNameMap.get(rfq.destination_country_id) || "Unknown",
    destination_country_code: countryCodeMap.get(rfq.destination_country_id) || "",
  }));

  return (
    <RFQsDataTable
      rfqs={rfqsWithCountryData}
      pagination={result.data.pagination}
    />
  );
}
