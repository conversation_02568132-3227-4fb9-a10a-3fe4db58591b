import { Skeleton } from "@/components/ui/skeleton";

/**
 * Loading skeleton for the RFQs table
 *
 * This component displays a skeleton UI while the RFQs data is being loaded.
 * It shows a placeholder for the table header and rows.
 */
export function RFQsSkeleton() {
  return (
    <div className="space-y-4">
      {/* Search and filter bar skeleton */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-10 w-64" />
        <div className="flex items-center gap-2">
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-10 w-10" />
        </div>
      </div>

      {/* Table skeleton */}
      <div className="border rounded-md">
        <div className="h-12 border-b px-6 flex items-center">
          <Skeleton className="h-4 w-[250px]" />
        </div>
        {[...Array(5)].map((_, i) => (
          <div
            key={i}
            className="h-16 px-6 flex items-center border-b last:border-b-0"
          >
            <Skeleton className="h-4 w-full" />
          </div>
        ))}

        {/* Pagination skeleton */}
        <div className="h-12 border-t px-6 flex items-center justify-between">
          <Skeleton className="h-4 w-[100px]" />
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-8 rounded-md" />
            <Skeleton className="h-8 w-8 rounded-md" />
            <Skeleton className="h-8 w-8 rounded-md" />
          </div>
        </div>
      </div>
    </div>
  );
}
