# React Hook Form and Next.js Server Actions Refactoring Guide

This guide explains how to refactor existing components to use React Hook Form and Next.js Server Actions, based on the successful refactoring of the `provider-selection.tsx` component.

## Table of Contents

1. [Benefits of Refactoring](#benefits-of-refactoring)
2. [Prerequisites](#prerequisites)
3. [Step-by-Step Refactoring Guide](#step-by-step-refactoring-guide)
4. [Best Practices](#best-practices)
5. [Testing and Validation](#testing-and-validation)
6. [Examples](#examples)
7. [Troubleshooting](#troubleshooting)

## Benefits of Refactoring

Refactoring components to use React Hook Form and Next.js Server Actions provides several benefits:

- **Simplified State Management**: Centralize form state in React Hook Form instead of multiple useState hooks
- **Improved Form Validation**: Leverage Zod schemas for robust form validation
- **Better Server Integration**: Use Next.js Server Actions for direct server communication
- **Reduced Boilerplate**: Less code to maintain and fewer opportunities for bugs
- **Enhanced Performance**: Fewer re-renders and more efficient updates
- **Type Safety**: Better TypeScript integration with form values and validation

## Prerequisites

Before starting the refactoring process, ensure you have:

1. Created a schema file for form validation (e.g., `src/lib/schemas/your-component.schema.ts`)
2. Created server actions for form submission (e.g., `src/lib/actions/your-component.actions.ts`)
3. Identified all form fields and their types
4. Understood the current component's state management and data flow

## Step-by-Step Refactoring Guide

### 1. Create Form Schema

First, create a schema file for form validation using Zod:

```typescript
// src/lib/schemas/your-component.schema.ts
import { z } from "zod";
import { ActionResponse } from "./common.schema";

// Define form values type
export const YourComponentFormSchema = z.object({
  field1: z.string(),
  field2: z.number().optional(),
  field3: z.boolean(),
  // Add more fields as needed
});

export type YourComponentFormValues = z.infer<typeof YourComponentFormSchema>;

// Define action state type
export interface YourComponentActionState {
  success: boolean;
  values: YourComponentFormValues;
  data?: any[]; // Replace with your data type
  error?: string;
}
```

### 2. Create Server Actions

Next, create server actions for form submission:

```typescript
// src/lib/actions/your-component.actions.ts
"use server";

import { z } from "zod";
import { YourComponentFormSchema } from "@/lib/schemas/your-component.schema";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("YourComponentActions");

export async function yourComponentAction(formData: FormData) {
  try {
    // Parse form data
    const rawFormData = Object.fromEntries(formData.entries());

    // Validate form data
    const validatedData = YourComponentFormSchema.parse({
      field1: rawFormData.field1,
      field2: rawFormData.field2 ? Number(rawFormData.field2) : undefined,
      field3: rawFormData.field3 === "true",
      // Add more fields as needed
    });

    // Process the data (e.g., save to database)
    // ...

    // Return success response
    return {
      success: true,
      values: validatedData,
      data: [], // Replace with your data
    };
  } catch (error) {
    logger.error("Error in yourComponentAction:", error);
    return {
      success: false,
      values: {},
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
```

### 3. Update Component Imports

Update your component imports to include React Hook Form and the new schema:

```typescript
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useActionState } from "next-safe-action/hooks";
import { Form } from "@/components/ui/form";
import {
  YourComponentFormSchema,
  YourComponentFormValues,
} from "@/lib/schemas/your-component.schema";
import { yourComponentAction } from "@/lib/actions/your-component.actions";
```

### 4. Replace State with React Hook Form

Replace useState hooks with React Hook Form:

```typescript
// Before
const [field1, setField1] = useState("");
const [field2, setField2] = useState<number | undefined>(undefined);
const [field3, setField3] = useState(false);

// After
const form = useForm<YourComponentFormValues>({
  resolver: zodResolver(YourComponentFormSchema),
  defaultValues: {
    field1: "",
    field2: undefined,
    field3: false,
  },
  mode: "onChange",
});

// Get form methods
const { watch, setValue } = form;

// Watch form values for reactive updates
const formValues = watch();
```

### 5. Set Up Action State

Set up the action state for server actions:

```typescript
const [state, formAction, isPending] = useActionState(yourComponentAction, {
  success: false,
  values: {
    field1: "",
    field2: undefined,
    field3: false,
  },
  data: [],
});
```

### 6. Update Form Submission

Update the form submission to use server actions:

```typescript
const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
  e.preventDefault();

  // Create a FormData object
  const formData = new FormData(e.currentTarget);

  // Submit the form using the formAction
  formAction(formData);
};
```

### 7. Update Input Components

Update input components to use form state:

```typescript
// Before
<Input
  name="field1"
  value={field1}
  onChange={(e) => setField1(e.target.value)}
/>

// After
<Input
  name="field1"
  value={formValues.field1}
  onChange={(e) => setValue("field1", e.target.value)}
/>
```

### 8. Update Event Handlers

Update event handlers to use form state:

```typescript
// Before
const handleSomeAction = () => {
  if (field1 === "") {
    // Show error
    return;
  }

  // Do something with field1, field2, field3
};

// After
const handleSomeAction = () => {
  const currentValues = form.getValues();

  if (currentValues.field1 === "") {
    // Show error
    return;
  }

  // Do something with currentValues.field1, currentValues.field2, currentValues.field3
};
```

### 9. Update JSX

Update the JSX to use the Form component and form state:

```tsx
return (
  <Form action={formAction} onSubmit={handleFormSubmit}>
    <input type="hidden" name="someId" value={someId} />

    {/* Form fields */}
    <div className="space-y-4">
      <Input
        name="field1"
        value={formValues.field1}
        onChange={(e) => setValue("field1", e.target.value)}
      />

      <Input
        name="field2"
        type="number"
        value={formValues.field2 || ""}
        onChange={(e) =>
          setValue(
            "field2",
            e.target.value ? Number(e.target.value) : undefined,
          )
        }
      />

      <Checkbox
        name="field3"
        checked={formValues.field3}
        onCheckedChange={(checked) => setValue("field3", !!checked)}
      />

      <Button type="submit" disabled={isPending}>
        {isPending ? "Submitting..." : "Submit"}
      </Button>
    </div>
  </Form>
);
```

## Best Practices

- **Type Safety**: Use TypeScript types for form values and validation
- **Error Handling**: Implement proper error handling for server actions
- **Loading States**: Use isPending from useActionState to show loading indicators
- **Form Validation**: Use Zod schemas for form validation

## Testing and Validation

After refactoring, verify that:

1. The component renders correctly
2. Form fields update correctly when changed
3. Form validation works as expected
4. Form submission works correctly
5. Server actions are called with the correct data
6. Error handling works correctly
7. Loading states are displayed correctly

## Examples

See the refactored `provider-selection.tsx` component for a complete example of this refactoring pattern.

## Troubleshooting

### Common Issues

1. **Form values not updating**: Make sure you're using `watch()` to get reactive form values
2. **Form submission not working**: Check that you're using the correct form action and preventing default form submission
3. **Validation errors not showing**: Ensure you're using the zodResolver correctly
4. **Server actions not being called**: Verify that you're using the correct form action and that the form is being submitted correctly
5. **Type errors**: Check that your form values match the schema types

### Solutions

1. Use `console.log` to debug form values and state
2. Check the browser console for errors
3. Use the React DevTools to inspect component state
4. Verify that server actions are being called with the correct data
5. Check that form fields have the correct name attributes
