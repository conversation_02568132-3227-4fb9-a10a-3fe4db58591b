"use client";

import { useState, useEffect, startTransition } from "react";
import { useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useActionState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "@/components/ui/sonner";
import { Button } from "@/components/ui/button";
import {
  Loader2,
  Send,
  CheckCircle,
  UserPlus,
} from "lucide-react";
import {
  getRFQAction,
  sendRFQAction,
} from "@/lib/actions/rfq.actions";
import { InvitedProvidersTable } from "./tables/invited-providers-table";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { EmailCompositionDialog } from "./dialogs/email-composition-dialog";
import { BidEntryDialog } from "./dialogs/bid-entry-dialog";
import { BidUpdateDialog } from "./dialogs/bid-update-dialog";
import { ManualProviderDialog } from "./dialogs/manual-provider-dialog";
import { ProviderSelectionDataTable } from "./tables/provider-selection-data-table";
import {
  RFQBid,
  ProviderSelectionFormSchema,
  ProviderSelectionFormValues,
  EmailData,
  ManualProviderType,
  RFQDisplayDetails,
} from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";


// Create a logger instance for this component
const logger = createLogger("ProviderSelection");

// Note: Column definitions and status badge rendering moved to provider-selection-data-table.tsx

// Manual provider state management - simplified with useState
// These providers are UI-only until they are actually invited via email

// Note: Using RFQProviderWithBid type directly from schemas
// Note: Using RFQDisplayDetails type from centralized schemas

interface ProviderSelectionProps {
  rfqId: string;
  rfqStatus: string;
  initialInvitedProviders?: any[];
}

export function ProviderSelection({
  rfqId,
  rfqStatus,
  initialInvitedProviders = [],
}: ProviderSelectionProps) {
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  // Get current page and page size from URL (keeping for form initialization)
  const currentPage = Number(searchParams.get("page") || "1");
  const pageSize = Number(searchParams.get("pageSize") || "10");

  // Initialize form with React Hook Form
  const form = useForm<ProviderSelectionFormValues>({
    resolver: zodResolver(ProviderSelectionFormSchema),
    defaultValues: {
      searchQuery: "",
      statusFilter: "all",
      selectedProviders: [],
      page: currentPage,
      pageSize: pageSize,
    },
    mode: "onChange",
  });

  // Get form methods
  const { watch, setValue } = form;

  // Watch form values for reactive updates
  const formValues = watch();

  // Setup action state for sending RFQ (keeping only what's needed)
  // Note: Removed provider selection action state as we now use TanStack Query

  // Setup action state for sending RFQ
  const [, sendFormAction, isSending] = useActionState(sendRFQAction, {
    success: false,
    values: {
      searchQuery: "",
      statusFilter: "all",
      selectedProviders: [],
      page: currentPage,
      pageSize: pageSize,
    },
  });

  // Component state
  const [rfqDetails, setRfqDetails] = useState<RFQDisplayDetails | null>(null);
  const [manuallyAddedProviders, setManuallyAddedProviders] = useState<ManualProviderType[]>([]);

  // Note: Replaced useRFQDataRefresh with TanStack Query cache invalidation for better real-time updates

  // Note: Data fetching is now handled by the Suspense-enabled component

  // Dialog state
  const [isEmailDialogOpen, setIsEmailDialogOpen] = useState(false);
  const [isBidDialogOpen, setIsBidDialogOpen] = useState(false);
  const [isBidUpdateDialogOpen, setIsBidUpdateDialogOpen] = useState(false);
  const [isManualProviderDialogOpen, setIsManualProviderDialogOpen] =
    useState(false);
  const [selectedProviderForBid, setSelectedProviderForBid] = useState<{
    id: string;
    name: string;
  } | null>(null);
  const [selectedBidForUpdate, setSelectedBidForUpdate] = useState<
    (RFQBid & { provider_name?: string }) | null
  >(null);

  // Initialize component with RFQ status
  useEffect(() => {
    // Initialize form with URL parameters
    setValue("page", currentPage);
    setValue("pageSize", pageSize);
    setValue("searchQuery", "");
    setValue("statusFilter", "all");

    // Always reset selected providers when component initializes or RFQ status changes
    // This ensures we don't have stale selections from previous interactions
    setValue("selectedProviders", []);
    logger.info("Reset selected providers on component initialization");
  }, [rfqStatus, setValue, currentPage, pageSize]);

  // Fetch RFQ details for email template (keeping only what's needed)
  useEffect(() => {
    const fetchRFQDetails = async () => {
      try {
        // Get RFQ details for email template
        const rfqResult = await getRFQAction(rfqId);
        if (rfqResult.success) {
          const rfq = rfqResult.data;

          // Get country names from the RFQ data
          const originCountry = rfq.countries_origin
            ? rfq.countries_origin.name
            : "Unknown";
          const destinationCountry = rfq.countries_destination
            ? rfq.countries_destination.name
            : "Unknown";

          // Get cargo types from the RFQ data
          const cargoTypeNames = rfq.cargo_types && rfq.cargo_types.length > 0
            ? rfq.cargo_types.map((ct: any) => ct.name).filter(Boolean)
            : ["Unknown"];

          setRfqDetails({
            title: rfq.title || "RFQ",
            sequence_number: rfq.sequence_number || "",
            origin: rfq.origin_city || "Origin",
            origin_country: originCountry,
            origin_postal_code: rfq.origin_postal_code || "",
            destination: rfq.destination_city || "Destination",
            destination_country: destinationCountry,
            destination_postal_code: rfq.destination_postal_code || "",
            equipmentType: rfq.equipment_type_name || "Equipment",
            equipmentQuantity: rfq.equipment_quantity || 1,
            weight: rfq.weight || 0,
            cargoTypes: cargoTypeNames,
            // Database fields (nullable) - keep as-is from RFQ type
            preferred_shipping_date: rfq.preferred_shipping_date,
            special_requirements: rfq.special_requirements,
            notes: rfq.notes,
            // Optional formatted versions for display
            preferredShippingDate: rfq.preferred_shipping_date || undefined,
            specialRequirements: rfq.special_requirements || undefined,
          });
        }
      } catch (error) {
        logger.error("Error fetching RFQ details:", error);
        toast.error("Failed to fetch RFQ details");
      }
    };

    fetchRFQDetails();
  }, [rfqId]);

  // Note: Removed server-side pagination logic as we now use client-side TanStack Table

  // Track filtered providers count for UI

  // Note: Provider selection is now handled by DataTable's built-in selection mechanism

  // Handle opening the email dialog
  const handleOpenEmailDialog = () => {
    const formValues = form.getValues();
    const selectedProviders = formValues.selectedProviders || [];

    if (selectedProviders.length === 0) {
      toast.error("Please select at least one provider", {
        description: "Selection Required",
      });
      return;
    }

    setIsEmailDialogOpen(true);
  };

  // Handle opening the bid dialog
  const handleAddBid = (providerId: string, providerName: string) => {
    setSelectedProviderForBid({ id: providerId, name: providerName });
    setIsBidDialogOpen(true);
  };

  // Handle opening the bid update dialog
  const handleUpdateBid = (bid: RFQBid & { provider_name?: string }) => {
    // Ensure the bid has an id property
    if (!bid.id) {
      logger.error("Cannot update bid: Missing bid ID");
      toast.error("Cannot update bid: Missing bid ID");
      return;
    }

    setSelectedBidForUpdate(bid);
    setIsBidUpdateDialogOpen(true);
  };

  // Handle manually selected providers
  const handleManualProvidersSelected = async (
    selectedProviders: ManualProviderType[],
  ) => {
    logger.info(`Adding ${selectedProviders.length} manual providers to temporary UI state`);

    // Add providers to temporary UI state (not database)
    // These will appear in the provider selection table until page refresh
    setManuallyAddedProviders(prev => {
      // Avoid duplicates by filtering out providers that are already added
      const existingIds = prev.map(p => p.id);
      const newProviders = selectedProviders.filter(p => !existingIds.includes(p.id));
      return [...prev, ...newProviders];
    });

    // Invalidate provider selection query to refresh the table with new manual providers
    await queryClient.invalidateQueries({
      queryKey: ["provider-selection-unified", rfqId]
    });

    logger.info(`Total manually added providers in UI state: ${manuallyAddedProviders.length + selectedProviders.length}`);
    logger.info("Cache invalidated after manual providers added");
  };

  // Handle sending RFQ to selected providers
  const handleSendRFQ = async (emailData: EmailData) => {
    const formValues = form.getValues();
    const selectedProviders = formValues.selectedProviders || [];

    if (selectedProviders.length === 0) {
      toast.error("Please select at least one provider", {
        description: "Selection Required",
      });
      return;
    }

    try {
      // Create a FormData object for the server action
      const formData = new FormData();
      formData.append("rfqId", rfqId);
      selectedProviders.forEach((providerId) => {
        formData.append("selectedProviders", providerId);
      });
      formData.append("subject", emailData.subject);
      formData.append("body", emailData.body);
      formData.append("isCustom", emailData.isCustom ? "true" : "false");
      if (emailData.templateId) {
        formData.append("templateId", emailData.templateId);
      }

      // Submit the form using the sendFormAction inside a startTransition
      startTransition(() => {
        sendFormAction(formData);

        // Reset the selected providers in the form state immediately
        setValue("selectedProviders", []);
        logger.info("Reset selected providers after email send");
      });

      // Show success message and close dialog
      toast.success(`RFQ sent to ${selectedProviders.length} providers`);
      setIsEmailDialogOpen(false);

      // Invalidate relevant queries to trigger real-time UI updates
      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: ["provider-selection-unified", rfqId]
        }),
        queryClient.invalidateQueries({
          queryKey: ["invited-providers", rfqId]
        }),
        queryClient.invalidateQueries({
          queryKey: ["rfq-emails", rfqId]
        }),
      ]);

      logger.info("Cache invalidated after RFQ sent to providers");

    } catch (error) {
      logger.error("Error sending RFQ:", error);
      toast.error("Failed to send RFQ");
    }
  };

  // Note: Removed form submission handler as we now use client-side filtering

  return (
    <div className="space-y-4" data-component="provider-selection">
      {/* Email Composition Dialog */}
      {rfqDetails && (
        <EmailCompositionDialog
          open={isEmailDialogOpen}
          onOpenChange={setIsEmailDialogOpen}
          onSend={handleSendRFQ}
          rfqId={rfqId}
          selectedProviders={formValues.selectedProviders || []}
          rfqDetails={rfqDetails}
          isSending={isSending}
        />
      )}

      {/* Manual Provider Selection Dialog */}
      <ManualProviderDialog
        open={isManualProviderDialogOpen}
        onOpenChange={setIsManualProviderDialogOpen}
        rfqId={rfqId}
        onProvidersSelected={handleManualProvidersSelected}
        // Simplified exclusion logic - only exclude manually added providers
        // The service layer will handle excluding already invited providers
        providersWithBids={[
          // Exclude manually added providers (temporary UI state)
          ...manuallyAddedProviders.map((p) => p.id),
        ]}
      />

      <Accordion type="single" defaultValue="provider-selection" collapsible className="overflow-hidden">
        <AccordionItem value="provider-selection" className="border-b-0">
          <AccordionTrigger className="flex items-center justify-between w-full p-3 text-left bg-background hover:bg-muted/50 transition-colors border-b hover:no-underline">
            <div className="flex items-center gap-2">
              <div className="text-primary">
                <Send className="h-5 w-5" />
              </div>
              <h3 className="text-base font-medium">Provider Selection</h3>
            </div>
          </AccordionTrigger>
          <AccordionContent className="pt-4 pb-0">
            <div className="space-y-4">
          <div className="flex justify-end items-center gap-2 mb-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsManualProviderDialogOpen(true)}
              className="whitespace-nowrap"
            >
              <UserPlus className="mr-2 h-4 w-4" />
              Add Providers
            </Button>
            <Button
              type="button"
              onClick={handleOpenEmailDialog}
              disabled={
                (formValues.selectedProviders?.length || 0) === 0 ||
                isSending
              }
              className="whitespace-nowrap"
            >
              {isSending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Send RFQ to Selected
                </>
              )}
            </Button>
          </div>

          <ProviderSelectionDataTable
            rfqId={rfqId}
            searchQuery={formValues.searchQuery || ""}
            statusFilter={formValues.statusFilter || "all"}
            manuallyAddedProviders={manuallyAddedProviders}
            onRowSelectionChange={(selectedProviderIds) => {
              setValue("selectedProviders", selectedProviderIds);
            }}
          />
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <Accordion type="single" defaultValue="invited-providers" collapsible className="overflow-hidden">
        <AccordionItem value="invited-providers" className="border-b-0">
          <AccordionTrigger className="flex items-center justify-between w-full p-3 text-left bg-background hover:bg-muted/50 transition-colors border-b hover:no-underline">
            <div className="flex items-center gap-2">
              <div className="text-primary">
                <CheckCircle className="h-5 w-5" />
              </div>
              <h3 className="text-base font-medium">Invited Providers & Bids</h3>
            </div>
          </AccordionTrigger>
          <AccordionContent className="pt-4 pb-0">
            <InvitedProvidersTable
              rfqStatus={rfqStatus}
              onAddBid={handleAddBid}
              onUpdateBid={handleUpdateBid}
              initialInvitedProviders={initialInvitedProviders}
            />
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      {/* Bid Entry Dialog */}
      {selectedProviderForBid && (
        <BidEntryDialog
          open={isBidDialogOpen}
          onOpenChange={setIsBidDialogOpen}
          rfqId={rfqId}
          providerId={selectedProviderForBid.id}
          providerName={selectedProviderForBid.name}
          onSuccess={async () => {
            // Invalidate relevant queries to trigger real-time UI updates
            await queryClient.invalidateQueries({
              queryKey: ["invited-providers", rfqId]
            });
          }}
        />
      )}

      {/* Bid Update Dialog */}
      {selectedBidForUpdate && selectedBidForUpdate.id && (
        <BidUpdateDialog
          open={isBidUpdateDialogOpen}
          onOpenChange={setIsBidUpdateDialogOpen}
          bid={{
            id: selectedBidForUpdate.id,
            rfq_id: selectedBidForUpdate.rfq_id,
            provider_id: selectedBidForUpdate.provider_id,
            price: selectedBidForUpdate.price,
            currency: selectedBidForUpdate.currency,
            status: selectedBidForUpdate.status,
            notes: selectedBidForUpdate.notes,
            provider_name: selectedBidForUpdate.provider_name,
          }}
          onSuccess={async () => {
            // Invalidate relevant queries to trigger real-time UI updates
            await queryClient.invalidateQueries({
              queryKey: ["invited-providers", rfqId]
            });
          }}
        />
      )}
    </div>
  );
}
