"use client";

import * as React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Loader2,
  InfoIcon,
  CalendarIcon,
  MapPinIcon,
  TruckIcon,
  PackageIcon,
  BoxesIcon
} from "lucide-react";
import { CreateRFQInput, Country, CargoType, EquipmentType } from "@/lib/schemas";
import { format } from "date-fns";

// Format date helper function
const formatDate = (dateString?: string, formatStr: string = "PPP"): string => {
  if (!dateString) return "Not specified";

  try {
    const date = new Date(dateString);
    return format(date, formatStr);
  } catch (error) {
    console.error("Error formatting date:", error);
    return "Invalid date";
  }
};

interface RFQConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  formData: CreateRFQInput;
  onConfirm: () => Promise<void>;
  isSubmitting: boolean;
  countries: Country[];
  cargoTypes: CargoType[];
  equipmentTypes: EquipmentType[];
}

export function RFQConfirmationDialog({
  open,
  onOpenChange,
  formData,
  onConfirm,
  isSubmitting,
  countries,
  cargoTypes,
  equipmentTypes,
}: RFQConfirmationDialogProps) {
  // Helper function to get country name by ID
  const getCountryName = (id?: string) => {
    if (!id) return "Not specified";
    const country = countries.find((c) => c.id === id);
    return country?.name || "Unknown";
  };

  // Helper function to get cargo type names by IDs
  const getCargoTypeNames = (ids?: string[]) => {
    if (!ids || ids.length === 0) return "Not specified";
    return ids
      .map((id) => {
        const cargoType = cargoTypes.find((c) => c.id === id);
        return cargoType?.name || "Unknown";
      })
      .join(", ");
  };

  // Helper function to get equipment type name by ID
  const getEquipmentTypeName = (id?: string) => {
    if (!id) return "Not specified";
    const equipmentType = equipmentTypes.find((e) => e.id === id);
    return equipmentType?.name || "Unknown";
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md max-h-[85vh] overflow-y-auto">
        <DialogHeader className="pb-2">
          <DialogTitle className="text-lg">Confirm RFQ Submission</DialogTitle>
          <DialogDescription className="text-xs">
            Please review the RFQ details before final submission
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-2">
          {/* Basic Information - Simplified */}
          <div>
            <h3 className="text-sm font-semibold mb-1.5 flex items-center">
              <InfoIcon className="h-3.5 w-3.5 mr-1.5 text-primary/70" />
              <span>Basic Information</span>
            </h3>
            <div className="grid grid-cols-2 gap-2 rounded-md border p-2 bg-muted/10 text-sm">
              <div>
                <p className="text-xs font-medium text-muted-foreground flex items-center">
                  <CalendarIcon className="h-3 w-3 mr-1 text-muted-foreground/70" />
                  Expiration Date
                </p>
                <p>{formData.expiration_date ? formatDate(formData.expiration_date) : "Not specified"}</p>
              </div>
              <div>
                <p className="text-xs font-medium text-muted-foreground flex items-center">
                  <CalendarIcon className="h-3 w-3 mr-1 text-muted-foreground/70" />
                  Preferred Shipping Date
                </p>
                <p>{formData.preferred_shipping_date ? formatDate(formData.preferred_shipping_date) : "Not specified"}</p>
              </div>
              {formData.notes && (
                <div className="col-span-2 mt-1">
                  <p className="text-xs font-medium text-muted-foreground">Notes</p>
                  <p className="text-sm line-clamp-2">{formData.notes}</p>
                </div>
              )}
            </div>
          </div>

          {/* Shipping Details - Compact */}
          <div>
            <h3 className="text-sm font-semibold mb-1.5 flex items-center">
              <MapPinIcon className="h-3.5 w-3.5 mr-1.5 text-primary/70" />
              <span>Shipping Details</span>
            </h3>
            <div className="grid grid-cols-2 gap-2">
              {/* Origin - Simplified */}
              <div className="rounded-md border p-2 bg-green-50/5 dark:bg-green-950/10 border-green-100 dark:border-green-800 text-sm">
                <h4 className="text-xs font-medium text-muted-foreground mb-1 flex items-center">
                  <MapPinIcon className="h-3 w-3 mr-1 text-green-600 dark:text-green-400" />
                  Origin
                </h4>
                <div className="space-y-1">
                  <p className="text-sm">{getCountryName(formData.origin_country_id)}</p>
                  <p className="text-sm">{formData.origin_city || "Not specified"}</p>
                  <p className="text-sm">{formData.origin_postal_code || "Not specified"}</p>
                  {formData.origin_address && (
                    <p className="text-sm line-clamp-2">{formData.origin_address}</p>
                  )}
                </div>
              </div>

              {/* Destination - Simplified */}
              <div className="rounded-md border p-2 bg-blue-50/5 dark:bg-blue-950/10 border-blue-100 dark:border-blue-800 text-sm">
                <h4 className="text-xs font-medium text-muted-foreground mb-1 flex items-center">
                  <MapPinIcon className="h-3 w-3 mr-1 text-blue-600 dark:text-blue-400" />
                  Destination
                </h4>
                <div className="space-y-1">
                  <p className="text-sm">{getCountryName(formData.destination_country_id)}</p>
                  <p className="text-sm">{formData.destination_city || "Not specified"}</p>
                  <p className="text-sm">{formData.destination_postal_code || "Not specified"}</p>
                  {formData.destination_address && (
                    <p className="text-sm line-clamp-2">{formData.destination_address}</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Cargo & Equipment - Combined and Simplified */}
          <div>
            <h3 className="text-sm font-semibold mb-1.5 flex items-center">
              <PackageIcon className="h-3.5 w-3.5 mr-1.5 text-primary/70" />
              <span>Cargo & Equipment</span>
            </h3>
            <div className="rounded-md border p-2 bg-muted/10 text-sm">
              <div className="grid grid-cols-2 gap-x-4 gap-y-1">
                <div className="col-span-2 mb-1">
                  <span className="text-xs font-medium text-muted-foreground flex items-center inline-flex">
                    <BoxesIcon className="h-3 w-3 mr-1 text-muted-foreground/70" />
                    Cargo Types:
                  </span>
                  <span className="ml-4">{getCargoTypeNames(formData.cargo_type_ids)}</span>
                </div>
                <div>
                  <span className="text-xs font-medium text-muted-foreground">Weight: </span>
                  <span>{formData.weight || "Not specified"} tonnes</span>
                </div>
                <div>
                  <span className="text-xs font-medium text-muted-foreground">Quantity: </span>
                  <span>{formData.quantity || "1"}</span>
                </div>
                <div className="col-span-2 mt-2 pt-2 border-t border-border/30">
                  <span className="text-xs font-medium text-muted-foreground flex items-center inline-flex">
                    <TruckIcon className="h-3 w-3 mr-1 text-muted-foreground/70" />
                    Equipment:
                  </span>
                  <span className="ml-4">
                    {getEquipmentTypeName(formData.equipment_type_id)}
                    {formData.equipment_quantity && formData.equipment_quantity > 1 && (
                      <span className="ml-1">({formData.equipment_quantity})</span>
                    )}
                  </span>
                </div>
              </div>
              {formData.special_requirements && (
                <div className="mt-2 pt-2 border-t border-border/30">
                  <p className="text-xs font-medium text-muted-foreground">Special Requirements</p>
                  <p className="text-sm line-clamp-2">{formData.special_requirements}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        <DialogFooter className="mt-2 flex justify-end space-x-4">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
            size="sm"
            className="px-4"
          >
            Edit
          </Button>
          <Button
            onClick={onConfirm}
            disabled={isSubmitting}
            size="sm"
            className="px-4"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-1.5 h-3.5 w-3.5 animate-spin" />
                <span>Creating...</span>
              </>
            ) : (
              <span>Confirm Submission</span>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
