"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { BidEntryForm } from "../forms/bid-entry-form";

interface BidEntryDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  rfqId: string;
  providerId: string;
  providerName: string;
  onSuccess?: () => void;
}

export function BidEntryDialog({
  open,
  onOpenChange,
  rfqId,
  providerId,
  providerName,
  onSuccess,
}: BidEntryDialogProps) {
  const handleSuccess = () => {
    // Close the dialog and call the onSuccess callback if provided
    onOpenChange(false);
    if (onSuccess) onSuccess();
  };

  const handleCancel = () => {
    // Close the dialog
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[650px] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="pb-2">
          <DialogTitle>Record Bid from {providerName}</DialogTitle>
          <DialogDescription className="text-xs">
            Enter the bid details received from the provider for this RFQ.
          </DialogDescription>
        </DialogHeader>
        <BidEntryForm
          rfqId={rfqId}
          providerId={providerId}
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </DialogContent>
    </Dialog>
  );
}
