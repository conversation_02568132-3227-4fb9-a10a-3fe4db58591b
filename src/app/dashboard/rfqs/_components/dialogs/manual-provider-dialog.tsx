"use client";

import { useState, useMemo, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { createLogger } from "@/lib/utils/logger/logger";
import { showSuccessToast, showErrorToast } from "@/lib/utils/toast";
import { useManualProviderDialogData } from "@/hooks/use-manual-provider-dialog";
import { ManualProviderDataTable } from "../tables/manual-provider-data-table";
import type { ManualProviderType } from "@/lib/schemas";

// Create a logger instance for this component
const logger = createLogger("ManualProviderDialog");

interface ManualProviderDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  rfqId: string;
  onProvidersSelected: (providers: ManualProviderType[]) => void;
  providersWithBids?: string[];
}

export function ManualProviderDialog({
  open,
  onOpenChange,
  rfqId,
  onProvidersSelected,
  providersWithBids = [],
}: ManualProviderDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedProviders, setSelectedProviders] = useState<string[]>([]);

  // Fetch data using TanStack Query
  const {
    providers,
    rfqProviders,
    isLoading,
    isError,
    error,
    hasNonCriticalError,
    nonCriticalError,
    refetch,
    providersQuery,
    rfqProvidersQuery
  } = useManualProviderDialogData(rfqId, open);

  // Log component state for debugging
  logger.info("ManualProviderDialog state:", {
    open,
    rfqId,
    providersLength: providers.length,
    rfqProvidersLength: rfqProviders.length,
    isLoading,
    isError,
    hasNonCriticalError,
    error: error?.message,
    nonCriticalError: nonCriticalError?.message,
  });

  // Get excluded provider IDs from RFQ providers and providers with bids
  const excludedProviderIds = useMemo(() => {
    const rfqProviderIds = rfqProviders.map(rfqProvider => rfqProvider.provider_id);
    const excluded = [...rfqProviderIds, ...providersWithBids];
    logger.info("Excluded provider IDs:", {
      rfqProviderIds: rfqProviderIds.length,
      providersWithBids: providersWithBids.length,
      totalExcluded: excluded.length,
    });
    return excluded;
  }, [rfqProviders, providersWithBids]);

  // Reset selected providers when dialog opens
  const resetSelection = () => {
    setSelectedProviders([]);
  };

  // Handle provider selection change
  const handleSelectionChange = (selectedIds: string[]) => {
    setSelectedProviders(selectedIds);
  };

  // Reset selection when dialog opens
  useEffect(() => {
    if (open) {
      resetSelection();
    }
  }, [open]);

  // Handle adding selected providers
  const handleAddProviders = () => {
    if (selectedProviders.length === 0) {
      showErrorToast({
        title: "Selection Required",
        description: "Please select at least one provider",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Get the selected provider objects for UI state
      const selectedProviderObjects = providers.filter((provider) =>
        selectedProviders.includes(provider.id),
      );

      logger.info("Adding providers to UI selection:", {
        rfqId,
        providerIds: selectedProviders,
        providerNames: selectedProviderObjects.map(p => p.name),
      });

      // Pass the selected providers back to the parent component for UI-only updates
      // This will add them to the temporary UI state, not the database
      onProvidersSelected(selectedProviderObjects);

      showSuccessToast({
        title: "Success",
        description: `${selectedProviders.length} provider${selectedProviders.length !== 1 ? "s" : ""} added to selection`,
      });

      // Reset selection and close dialog
      resetSelection();
      onOpenChange(false);
    } catch (error) {
      logger.error("Error adding providers to selection:", error);
      showErrorToast({
        title: "Error",
        description: "Failed to add providers to selection",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show error state if there's a critical error
  if (isError) {
    logger.error("Critical error in ManualProviderDialog:", error);

    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[85vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle>Add Providers</DialogTitle>
          </DialogHeader>
          <div className="flex items-center justify-center p-8 flex-1 overflow-auto">
            <div className="text-center space-y-4">
              <p className="text-muted-foreground">Failed to load providers</p>
              {error && (
                <p className="text-sm text-red-600 bg-red-50 p-2 rounded">
                  Error: {error.message}
                </p>
              )}
              <div className="flex gap-2 justify-center">
                <Button
                  variant="outline"
                  onClick={() => {
                    logger.info("Retrying data fetch...");
                    refetch();
                  }}
                >
                  Retry
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    logger.info("Reloading page...");
                    window.location.reload();
                  }}
                >
                  Reload Page
                </Button>
              </div>
              <details className="text-left text-xs text-muted-foreground">
                <summary className="cursor-pointer">Debug Information</summary>
                <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                  {JSON.stringify({
                    rfqId,
                    providersError: providersQuery.error?.message,
                    rfqProvidersError: rfqProvidersQuery.error?.message,
                    providersLoading: providersQuery.isLoading,
                    rfqProvidersLoading: rfqProvidersQuery.isLoading,
                  }, null, 2)}
                </pre>
              </details>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Show warning for non-critical errors (RFQ providers failed but providers loaded)
  if (hasNonCriticalError) {
    logger.warn("Non-critical error in ManualProviderDialog:", nonCriticalError);
    showErrorToast({
      title: "Warning",
      description: "Some data failed to load, but you can still add providers",
    });
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl h-[85vh] sm:max-w-4xl flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>Add Providers</DialogTitle>
        </DialogHeader>

        <div className="flex-1 min-h-0 overflow-auto">
          {isLoading ? (
            <div className="flex flex-col justify-center items-center p-8 space-y-4">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Loading providers...</p>
                <p className="text-xs text-muted-foreground mt-1">
                  Providers: {providersQuery.isLoading ? "Loading..." : "✓"} |
                  RFQ Data: {rfqProvidersQuery.isLoading ? "Loading..." : "✓"}
                </p>
              </div>
            </div>
          ) : (
            <>
              {providers.length === 0 ? (
                <div className="flex justify-center items-center p-8">
                  <div className="text-center">
                    <p className="text-muted-foreground">No providers found</p>
                    <p className="text-sm text-muted-foreground mt-2">
                      There are no providers available to add to this RFQ.
                    </p>
                    <Button
                      variant="outline"
                      onClick={() => {
                        logger.info("Retrying data fetch from empty state...");
                        refetch();
                      }}
                      className="mt-4"
                    >
                      Retry
                    </Button>
                  </div>
                </div>
              ) : (
                <ManualProviderDataTable
                  data={providers}
                  selectedProviders={selectedProviders}
                  onSelectionChange={handleSelectionChange}
                  excludedProviderIds={excludedProviderIds}
                />
              )}
            </>
          )}
        </div>

        <DialogFooter className="flex-shrink-0 mt-4 border-t pt-4">
          <div className="flex items-center justify-between w-full">
            <div className="text-sm text-muted-foreground">
              {selectedProviders.length > 0 && (
                <span>
                  {selectedProviders.length} provider
                  {selectedProviders.length !== 1 ? "s" : ""} selected
                </span>
              )}
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleAddProviders}
                disabled={selectedProviders.length === 0 || isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Adding...
                  </>
                ) : (
                  <>Add Selected Providers</>
                )}
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
