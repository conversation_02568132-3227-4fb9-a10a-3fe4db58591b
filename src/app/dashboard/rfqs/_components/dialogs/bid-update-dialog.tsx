"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { BidUpdateForm } from "../forms/bid-update-form";
import { createLogger } from "@/lib/utils/logger/logger";
import { BidDisplayData } from "@/lib/schemas";

// Create a logger instance for this component
const logger = createLogger("BidUpdateDialog");

interface BidUpdateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  bid: BidDisplayData;
  onSuccess?: () => void;
}

export function BidUpdateDialog({
  open,
  onOpenChange,
  bid,
  onSuccess,
}: BidUpdateDialogProps) {
  const handleSuccess = () => {
    // Close the dialog and call the onSuccess callback if provided
    onOpenChange(false);
    if (onSuccess) onSuccess();
  };

  const handleCancel = () => {
    // Close the dialog
    onOpenChange(false);
  };

  // Log when the dialog opens with the bid details
  useEffect(() => {
    if (open) {
      logger.debug("BidUpdateDialog opened with bid:", bid);
    }
  }, [open, bid]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[650px] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="pb-2">
          <DialogTitle>
            Update Bid from {bid.provider_name || "Provider"}
          </DialogTitle>
          <DialogDescription className="text-xs">
            Update the bid details for this RFQ.
          </DialogDescription>
        </DialogHeader>
        <BidUpdateForm
          bidId={bid.id}
          initialData={{
            price: bid.price,
            status: bid.status,
            notes: bid.notes,
            rfq_id: bid.rfq_id,
          }}
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </DialogContent>
    </Dialog>
  );
}
