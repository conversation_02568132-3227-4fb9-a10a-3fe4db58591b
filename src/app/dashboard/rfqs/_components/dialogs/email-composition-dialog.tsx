"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Loader2,
  Send,
  Bold,
  Italic,
  List,
  ListOrdered,
  AlertCircle,
} from "lucide-react";
import { TextEnhancer } from "@/lib/gemini";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { createLogger } from "@/lib/utils/logger/logger";
import { RFQDisplayDetails, EmailCompositionData } from "@/lib/schemas";

// Create a logger instance for this component
const logger = createLogger("EmailCompositionDialog");

interface EmailCompositionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSend: (emailData: EmailCompositionData) => Promise<void>;
  rfqId: string;
  selectedProviders: string[];
  rfqDetails: RFQDisplayDetails;
  isSending: boolean;
}

export function EmailCompositionDialog({
  open,
  onOpenChange,
  onSend,
  rfqId,
  selectedProviders,
  rfqDetails,
  isSending,
}: EmailCompositionDialogProps) {
  const [subject, setSubject] = useState<string>("");
  const [body, setBody] = useState<string>("");
  const [aiError, setAiError] = useState<string | null>(null);

  // Generate default email content based on RFQ details
  useEffect(() => {
    if (open && rfqDetails) {
      // Format the RFQ number for the subject
      const rfqNumber = rfqDetails.sequence_number
        ? `RFQ #${rfqDetails.sequence_number}`
        : "RFQ";

      // Format origin and destination for subject
      const originText = rfqDetails.origin_country
        ? `${rfqDetails.origin}, ${rfqDetails.origin_country}`
        : rfqDetails.origin;
      const destinationText = rfqDetails.destination_country
        ? `${rfqDetails.destination}, ${rfqDetails.destination_country}`
        : rfqDetails.destination;

      // Format the subject line
      const defaultSubject = `Quote Request - ${originText} to ${destinationText}`;
      setSubject(defaultSubject);

      // Format the preferred shipping date if available
      let shippingDateText = "Not specified";
      if (rfqDetails.preferredShippingDate) {
        const date = new Date(rfqDetails.preferredShippingDate);
        shippingDateText = date.toLocaleDateString("en-US", {
          year: "numeric",
          month: "long",
          day: "numeric",
        });
      }

      // Format cargo types as a comma-separated list
      const cargoTypesText = rfqDetails.cargoTypes.join(", ");

      // Format weight with proper unit (tonnes)
      const weightText = `${rfqDetails.weight} tonnes`;

      // Prepare section content first to determine if sections should be included
      const shipmentDetails = [
        rfqDetails.origin && rfqDetails.origin_country
          ? `- Origin: ${rfqDetails.origin}${rfqDetails.origin_postal_code ? `, ${rfqDetails.origin_postal_code}` : ""}${rfqDetails.origin_country ? `, ${rfqDetails.origin_country}` : ""}`
          : "",
        rfqDetails.destination && rfqDetails.destination_country
          ? `- Destination: ${rfqDetails.destination}${rfqDetails.destination_postal_code ? `, ${rfqDetails.destination_postal_code}` : ""}${rfqDetails.destination_country ? `, ${rfqDetails.destination_country}` : ""}`
          : "",
        shippingDateText !== "Not specified"
          ? `- Preferred Shipping Date: ${shippingDateText}`
          : "",
      ]
        .filter(Boolean)
        .join("\n");

      const cargoAndWeightInfo = [
        cargoTypesText && cargoTypesText !== "Unknown"
          ? `- Cargo Type: ${cargoTypesText}`
          : "",
        rfqDetails.weight ? `- Weight: ${weightText}` : "",
      ]
        .filter(Boolean)
        .join("\n");

      const equipmentRequirements = [
        rfqDetails.equipmentType && rfqDetails.equipmentType !== "Equipment"
          ? `- Equipment Type: ${rfqDetails.equipmentType}`
          : "",
        rfqDetails.equipmentQuantity
          ? `- Quantity: ${rfqDetails.equipmentQuantity} unit(s)`
          : "",
      ]
        .filter(Boolean)
        .join("\n");

      // Build the email body with all available details
      // Using an array to build sections and then join with proper spacing
      const sections = [
        "Dear Provider,",
        "We are requesting a quote for the following shipment:",
        shipmentDetails,
        cargoAndWeightInfo,
        equipmentRequirements,
        rfqDetails.specialRequirements ? `**Special Requirements:**\n${rfqDetails.specialRequirements}` : '',
        rfqDetails.notes ? `**Additional Notes:**\n${rfqDetails.notes}` : '',
        "Please review the details and provide your best quote at your earliest convenience.",
        "Best regards"
      ];

      // Filter out empty sections and join with consistent spacing
      const defaultBody = sections
        .filter(section => section && section.trim() !== '')
        .join("\n\n")
        .trim();

      setBody(defaultBody);
    }
  }, [open, rfqDetails]);

  const handleSend = async () => {
    await onSend({
      subject,
      body,
      isCustom: false,
    });
  };

  // Simple text formatting functions
  const insertFormatting = (format: string) => {
    const textarea = document.getElementById(
      "email-body",
    ) as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);
    let formattedText = "";

    switch (format) {
      case "bold":
        formattedText = `**${selectedText}**`;
        break;
      case "italic":
        formattedText = `*${selectedText}*`;
        break;
      case "list":
        formattedText = selectedText
          .split("\n")
          .map((line) => `- ${line}`)
          .join("\n");
        break;
      case "ordered-list":
        formattedText = selectedText
          .split("\n")
          .map((line, i) => `${i + 1}. ${line}`)
          .join("\n");
        break;
      default:
        formattedText = selectedText;
    }

    const newText =
      textarea.value.substring(0, start) +
      formattedText +
      textarea.value.substring(end);
    setBody(newText);

    // Set cursor position after the inserted text
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(
        start + formattedText.length,
        start + formattedText.length,
      );
    }, 0);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto p-6">
        <DialogHeader>
          <DialogTitle>
            Send RFQ to {selectedProviders.length} Provider
            {selectedProviders.length !== 1 ? "s" : ""}
          </DialogTitle>
          <DialogDescription>
            Customize your email before sending.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 mt-4">
          <div className="space-y-2">
            <Label htmlFor="email-subject">Subject</Label>
            <Input
              id="email-subject"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              placeholder="Email subject"
            />
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="email-body">Email Body</Label>
              <div className="flex items-center space-x-2">
                <TextEnhancer
                  onEnhance={(enhancedText) => {
                    setBody(enhancedText);
                    setAiError(null);
                  }}
                  currentText={body}
                />
                <div className="flex items-center space-x-1">
                  <Button
                    variant="outline"
                    size="icon"
                    type="button"
                    onClick={() => insertFormatting("bold")}
                    title="Bold"
                  >
                    <Bold className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    type="button"
                    onClick={() => insertFormatting("italic")}
                    title="Italic"
                  >
                    <Italic className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    type="button"
                    onClick={() => insertFormatting("list")}
                    title="Bullet List"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    type="button"
                    onClick={() => insertFormatting("ordered-list")}
                    title="Numbered List"
                  >
                    <ListOrdered className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
            {aiError && (
              <Alert variant="destructive" className="mb-2">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{aiError}</AlertDescription>
              </Alert>
            )}
            <Textarea
              id="email-body"
              value={body}
              onChange={(e) => setBody(e.target.value)}
              placeholder="Email content"
              className="min-h-[250px] whitespace-pre-wrap"
            />
          </div>
          <div className="text-sm text-muted-foreground">
            This email will be sent to all selected providers with the same
            content.
          </div>
        </div>

        <DialogFooter className="flex items-center justify-between sm:justify-end mt-6">
          <div className="text-sm text-muted-foreground sm:mr-auto">
            Sending to {selectedProviders.length} provider
            {selectedProviders.length !== 1 ? "s" : ""}
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button onClick={handleSend} disabled={isSending}>
              {isSending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Send RFQ
                </>
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
