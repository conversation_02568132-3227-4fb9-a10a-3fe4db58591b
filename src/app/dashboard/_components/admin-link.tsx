"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { ShieldCheck } from "lucide-react";
import { SidebarMenuItem } from "@/components/ui/sidebar";
import { isAdminAction } from "@/lib/actions/auth.actions";
import { createLogger } from "@/lib/utils/logger/logger";
import { usePathname } from "next/navigation";

const logger = createLogger("AdminLink");

export function AdminLink() {
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const pathname = usePathname();

  useEffect(() => {
    async function checkAdminRole() {
      try {
        const result = await isAdminAction();
        setIsAdmin(result.success && result.data);
      } catch (error) {
        logger.error("Error checking admin role:", error);
        setIsAdmin(false);
      } finally {
        setIsLoading(false);
      }
    }

    checkAdminRole();
  }, []);

  if (isLoading || !isAdmin) {
    return null;
  }

  return (
    <SidebarMenuItem>
      <Link href="/admin">
        <div
          className={`flex items-center gap-2 rounded-md px-3 py-1.5 text-xs ${
            pathname === "/admin"
              ? "bg-sidebar-active text-sidebar-active-foreground"
              : "hover:bg-sidebar-hover hover:text-sidebar-hover-foreground"
          }`}
        >
          <ShieldCheck className="h-3.5 w-3.5" />
          <span>Admin</span>
        </div>
      </Link>
    </SidebarMenuItem>
  );
}
