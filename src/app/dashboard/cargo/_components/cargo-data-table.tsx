"use client";

import { useState, useCallback } from "react";
import type { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/ui/tables";
import { DataTableColumnHeader } from "@/components/ui/tables/column-header";
import {
  PencilIcon,
  TrashIcon,
  PlusCircle,
  MoreHorizontal,
} from "lucide-react";
import { toast } from "@/components/ui/sonner";
import { deleteCargoAction } from "@/lib/actions/cargo";
import { type CargoType } from "@/lib/schemas";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { AddCargoDialog } from "./dialogs/add-cargo-dialog";
import { EditCargoDialog } from "./dialogs/edit-cargo-dialog";
import { createLogger } from "@/lib/utils/logger/logger";
import { useRouter, usePathname, useSearchParams } from "next/navigation";

const logger = createLogger("CargoDataTable");

interface CargoDataTableProps {
  cargo: CargoType[];
  totalCount?: number;
  currentPage?: number;
  pageSize?: number;
  onCargoUpdated?: () => void;
}

export function CargoDataTable({
  cargo,
  totalCount,
  currentPage = 1,
  pageSize = 10,
  onCargoUpdated,
}: CargoDataTableProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingCargo, setEditingCargo] = useState<CargoType | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletingCargo, setDeletingCargo] = useState<CargoType | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Create a function to handle pagination changes
  const handlePaginationChange = useCallback(
    (page: number, newPageSize: number) => {
      const params = new URLSearchParams(searchParams.toString());
      params.set("page", page.toString());
      params.set("pageSize", newPageSize.toString());
      router.push(`${pathname}?${params.toString()}`);
    },
    [pathname, router, searchParams],
  );

  const handleEdit = (cargoItem: CargoType) => {
    setEditingCargo(cargoItem);
    setIsEditDialogOpen(true);
  };

  const handleCreate = () => {
    setIsAddDialogOpen(true);
  };

  const handleDelete = (cargoItem: CargoType) => {
    setDeletingCargo(cargoItem);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!deletingCargo) return;

    setIsDeleting(true);
    try {
      const formData = new FormData();
      formData.append("id", deletingCargo.id);

      const result = await deleteCargoAction(null, formData);

      if (result.success) {
        toast.success("Cargo deleted successfully!");
        if (onCargoUpdated) {
          onCargoUpdated();
        }
      } else {
        toast.error(result.error || "Failed to delete cargo");
      }
    } catch (error) {
      logger.error("Error deleting cargo:", error);
      toast.error("An error occurred while deleting the cargo");
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
      setDeletingCargo(null);
    }
  };

  const columns: ColumnDef<CargoType>[] = [
    {
      accessorKey: "name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Name" />
      ),
      cell: ({ row }) => <div>{row.getValue("name")}</div>,
    },
    {
      accessorKey: "description",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Description" />
      ),
      cell: ({ row }) => (
        <div className="max-w-[500px] truncate">
          {row.getValue("description") || "—"}
        </div>
      ),
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const cargo = row.original;

        return (
          <div className="flex justify-end">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleEdit(cargo)}>
                  <PencilIcon className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="text-destructive focus:text-destructive"
                  onClick={() => handleDelete(cargo)}
                >
                  <TrashIcon className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    },
  ];

  return (
    <div>
      <div className="flex justify-end mb-4">
        <Button onClick={handleCreate}>
          <PlusCircle className="mr-2 h-4 w-4" /> Create Cargo Type
        </Button>
      </div>

      <DataTable
        columns={columns}
        data={cargo}
        filterConfig={{
          search: {
            placeholder: "Filter by name...",
            columnId: "name",
          },
        }}
        paginationConfig={{
          totalItems: totalCount,
          manualPagination: true,
          initialPage: currentPage - 1, // Convert to 0-based index
          initialPageSize: pageSize,
          onPaginationChange: handlePaginationChange,
        }}
      />

      {/* Add Cargo Dialog */}
      <AddCargoDialog
        open={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        onSuccess={onCargoUpdated}
      />

      {/* Edit Cargo Dialog */}
      <EditCargoDialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        cargo={editingCargo}
        onSuccess={onCargoUpdated}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the cargo type{" "}
              <span className="font-semibold">{deletingCargo?.name}</span>. This
              action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
