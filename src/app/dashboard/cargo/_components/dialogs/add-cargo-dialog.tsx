"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { CargoForm } from "../cargo-form";

interface AddCargoDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function AddCargoDialog({
  open,
  onOpenChange,
  onSuccess,
}: AddCargoDialogProps) {
  const handleSuccess = () => {
    if (onSuccess) {
      onSuccess();
    }
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New Cargo Type</DialogTitle>
        </DialogHeader>
        <CargoForm onSuccess={handleSuccess} inModal />
      </DialogContent>
    </Dialog>
  );
}
