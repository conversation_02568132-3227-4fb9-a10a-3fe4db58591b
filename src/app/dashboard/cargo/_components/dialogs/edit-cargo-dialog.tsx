"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { CargoForm } from "../cargo-form";
import { type CargoType } from "@/lib/schemas";

interface EditCargoDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  cargo: CargoType | null;
  onSuccess?: () => void;
}

export function EditCargoDialog({
  open,
  onOpenChange,
  cargo,
  onSuccess,
}: EditCargoDialogProps) {
  const handleSuccess = () => {
    if (onSuccess) {
      onSuccess();
    }
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Cargo Type</DialogTitle>
        </DialogHeader>
        <CargoForm initialData={cargo} onSuccess={handleSuccess} inModal />
      </DialogContent>
    </Dialog>
  );
}
