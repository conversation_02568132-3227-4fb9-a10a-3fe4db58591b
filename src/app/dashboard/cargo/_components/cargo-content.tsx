import { getCargo } from "@/lib/actions/cargo";
import { CargoDataTable } from "./cargo-data-table";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("CargoContent");

interface CargoContentProps {
  page: number;
  pageSize: number;
}

export async function CargoContent({ page, pageSize }: CargoContentProps) {
  logger.info(
    `Fetching cargo data with pagination: page=${page}, pageSize=${pageSize}`,
  );
  const { data: cargo, error, totalCount } = await getCargo({ page, pageSize });

  if (error) {
    logger.error("Error fetching cargo data:", error);
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <CargoDataTable
      cargo={cargo}
      totalCount={totalCount}
      currentPage={page}
      pageSize={pageSize}
    />
  );
}
