"use client";

import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "@/components/ui/sonner";
import { createCargoAction, updateCargoAction } from "@/lib/actions/cargo";
import { CreateCargoTypeSchema, type CargoType } from "@/lib/schemas";
import { Button } from "@/components/ui/button";
import { SubmitButton } from "@/components/ui/submit-button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

interface CargoFormProps {
  initialData?: CargoType | null;
  onSuccess?: () => void;
  inModal?: boolean;
}

export function CargoForm({
  initialData,
  onSuccess,
  inModal = false,
}: CargoFormProps) {
  const router = useRouter();

  // Initialize form with React Hook Form
  const form = useForm({
    resolver: zodResolver(CreateCargoTypeSchema),
    defaultValues: {
      name: initialData?.name || "",
      description: initialData?.description || "",
      image_url: initialData?.image_url || "",
    },
  });

  // Handle form submission
  const onSubmit = async (data: any) => {
    const formData = new FormData();

    if (initialData && initialData.id) {
      formData.append("id", initialData.id);
    }

    formData.append("name", data.name);
    formData.append("description", data.description || "");
    formData.append("image_url", data.image_url || "");

    // Submit the form using the appropriate Server Action
    try {
      const result = initialData
        ? await updateCargoAction(null, formData)
        : await createCargoAction(null, formData);

      if (result.success) {
        toast.success(
          initialData
            ? "Cargo updated successfully!"
            : "Cargo added successfully!",
        );

        if (onSuccess) {
          onSuccess();
        } else if (!inModal) {
          router.push("/dashboard/cargo");
          router.refresh();
        }
      } else {
        toast.error(result.error || "An error occurred. Please try again.");

        // Set form errors if they exist
        if (result.fieldErrors) {
          Object.entries(result.fieldErrors).forEach(([field, errors]) => {
            if (Array.isArray(errors) && errors.length > 0) {
              form.setError(field as any, {
                type: "server",
                message: errors[0],
              });
            }
          });
        }
      }
    } catch (error) {
      toast.error("An error occurred. Please try again.");
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Name field */}
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter cargo name" {...field} />
              </FormControl>
              <FormDescription>The name of this cargo type</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        {/* Description field */}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description (Optional)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter a description of this cargo"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                A brief description explaining this cargo
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        {/* Image URL field */}
        <FormField
          control={form.control}
          name="image_url"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Image URL (Optional)</FormLabel>
              <FormControl>
                <Input
                  placeholder="https://..."
                  {...field}
                  value={field.value || ""}
                />
              </FormControl>
              <FormDescription>
                Optional image URL for this cargo
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        {/* Form actions */}
        <div className="flex justify-end space-x-4">
          {!inModal && (
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
            >
              Cancel
            </Button>
          )}
          <SubmitButton
            pendingText={initialData ? "Updating..." : "Creating..."}
          >
            {initialData ? "Update Cargo" : "Create Cargo"}
          </SubmitButton>
        </div>
      </form>
    </Form>
  );
}
