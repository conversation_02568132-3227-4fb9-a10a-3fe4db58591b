# Cargo Module

## Module Purpose

The Cargo module manages cargo types for logistics operations in the Steelflow application. It provides functionality to create, read, update, and delete cargo types, which represent different categories of steel products that can be transported.

## Component Structure

The Cargo module follows the standardized module structure:

```
src/app/dashboard/cargo/
├── _components/                  # Module-specific components
│   ├── cargo-content.tsx         # Server component for data fetching
│   ├── cargo-data-table.tsx      # Data table component
│   ├── cargo-form.tsx            # Form component
│   ├── cargo-skeleton.tsx        # Loading skeleton
│   └── dialogs/                  # Dialog components
│       ├── add-cargo-dialog.tsx  # Dialog for adding cargo
│       └── edit-cargo-dialog.tsx # Dialog for editing cargo
├── layout.tsx                    # Module-specific layout
├── page.tsx                      # Main listing page
└── cargo_summary.md              # This documentation file
```

## Data Flow Diagram

```
Server Component (page.tsx)
  ↓
Async Data Fetching (cargo-content.tsx)
  ↓
Pass Data to Client Component (cargo-data-table.tsx)
  ↓
Client-side Interactivity (dialogs, forms)
  ↓
Server Actions (cargo.ts)
  ↓
Service Layer (cargo.service.ts)
  ↓
Database (Supabase)
```

## API Endpoints

- `GET /api/v1/cargo` - Get all cargo types or a specific cargo type by ID
- `POST /api/v1/cargo` - Create a new cargo type
- `PUT /api/v1/cargo` - Update an existing cargo type
- `DELETE /api/v1/cargo` - Delete a cargo type

## Server Actions

The Cargo module uses the following server actions from `src/lib/actions/cargo.ts`:

- `getCargo()` - Fetches all cargo types
- `createCargoAction()` - Creates a new cargo type
- `updateCargoAction()` - Updates an existing cargo type
- `deleteCargoAction()` - Deletes a cargo type

## Service Layer

The service layer is implemented in `src/lib/services/cargo.service.ts` and includes:

- `fetchCargo()` - Fetches cargo with optional pagination or by ID
- `addCargo()` - Creates new cargo
- `modifyCargo()` - Updates existing cargo
- `removeCargo()` - Deletes cargo by ID

## Schemas

The Cargo module uses the following schemas from `src/lib/schemas/cargo.schema.ts`:

- `GetCargoTypesParamsSchema` - For fetching cargo with pagination or by ID
- `CreateCargoTypeSchema` - For creating new cargo
- `UpdateCargoTypeSchema` - For updating existing cargo
- `DeleteCargoTypeParamsSchema` - For deleting cargo
- `CargoTypeSchema` - Base schema for cargo type entity
