import { Suspense } from "react";
import { Metadata } from "next";
import { CargoContent } from "./_components/cargo-content";
import { CargoSkeleton } from "./_components/cargo-skeleton";
import { createLogger } from "@/lib/utils/logger/logger";
import { redirect } from "next/navigation";
import { H1, Lead } from "@/components/ui/typography";

const logger = createLogger("CargoPage");

export const metadata: Metadata = {
  title: "Cargo | Steelflow",
  description: "Manage cargo types for logistics operations",
};

export default async function CargoPage({
  searchParams,
}: {
  searchParams: Promise<{ page?: string; pageSize?: string }>;
}) {
  // Await searchParams before accessing its properties
  const { page: pageParam, pageSize: pageSizeParam } = await searchParams;

  const page = pageParam ? parseInt(pageParam) : 1;
  const pageSize = pageSizeParam ? parseInt(pageSizeParam) : 10;

  // Log the parsed search parameters for debugging
  logger.info(
    `Cargo page - Parsed URL parameters: page=${page}, pageSize=${pageSize}`,
  );

  // Force page to be at least 1
  if (page < 1) {
    logger.warn("Invalid page number detected, forcing to page 1");
    const params = new URLSearchParams();
    params.set("page", "1");
    params.set("pageSize", pageSize.toString());
    return redirect(`/dashboard/cargo?${params.toString()}`);
  }

  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <div>
          <H1>Cargo Types</H1>
          <Lead>
            Manage steel product categories for your logistics operations
          </Lead>
        </div>
      </div>

      <Suspense fallback={<CargoSkeleton />}>
        <CargoContent page={page} pageSize={pageSize} />
      </Suspense>
    </>
  );
}
