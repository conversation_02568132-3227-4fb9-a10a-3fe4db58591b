"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "@/components/ui/sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { updateUserProfileAction } from "@/lib/actions/user.actions";
import { User, UserProfileFormSchema, type UserProfileFormValues } from "@/lib/schemas";
import { Loader2 } from "lucide-react";

interface ProfileFormProps {
  initialData: User | null;
}

/**
 * Profile form component
 *
 * This component renders a form for updating the user's profile information.
 * It handles form validation, submission, and error/success messages.
 */
export function ProfileForm({ initialData }: ProfileFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with React Hook Form
  const form = useForm<UserProfileFormValues>({
    resolver: zodResolver(UserProfileFormSchema),
    defaultValues: {
      name: initialData?.name || "",
      phone_number: initialData?.phone_number || "",
    },
    mode: "onChange",
  });

  // Handle form submission
  const onSubmit = async (data: UserProfileFormValues) => {
    setIsSubmitting(true);

    try {
      // Create FormData object
      const formData = new FormData();
      formData.append("name", data.name);

      // Only append phone_number if it exists
      if (data.phone_number) {
        formData.append("phone_number", data.phone_number);
      }

      // Submit the form
      const result = await updateUserProfileAction(formData);

      if (result.success) {
        toast.success("Profile updated successfully");
        router.refresh(); // Refresh the page to show updated data
      } else {
        toast.error(result.error || "Failed to update profile");

        // Handle field errors
        if (result.fieldErrors) {
          Object.entries(result.fieldErrors).forEach(([field, messages]) => {
            if (messages && messages.length > 0) {
              form.setError(field as any, {
                type: "manual",
                message: messages[0],
              });
            }
          });
        }
      }
    } catch (error) {
      toast.error("An unexpected error occurred");
      console.error("Profile update error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Name</FormLabel>
              <FormControl>
                <Input {...field} placeholder="Enter your name" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phone_number"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Phone Number</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  value={field.value || ''}
                  placeholder="Enter your phone number"
                />
              </FormControl>
              <FormDescription className="text-xs text-muted-foreground">
                Accepted formats: +49 170 1234567, +49 30 12345678, 0170 123 4567, 030 12345678, +33 1 23 45 67 89
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            "Save Changes"
          )}
        </Button>
      </form>
    </Form>
  );
}
