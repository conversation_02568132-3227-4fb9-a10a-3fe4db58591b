import { getCurrentUserProfileAction } from "@/lib/actions/user.actions";
import { ProfileForm } from "./profile-form";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { H1, H4, Lead } from "@/components/ui/typography";

/**
 * Profile content component
 *
 * This component fetches the current user's profile data and renders the profile form.
 * It handles loading states and error messages.
 */
export async function ProfileContent() {
  // Fetch the current user's profile
  const profileResponse = await getCurrentUserProfileAction();

  return (
    <div className="space-y-6">
      <div>
        <H1>Profile Settings</H1>
        <Lead>
          Manage your profile information
        </Lead>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            <H4 className="m-0">Personal Information</H4>
          </CardTitle>
          <CardDescription>
            Update your personal information and contact details
          </CardDescription>
        </CardHeader>
        <CardContent>
          {profileResponse.success ? (
            <ProfileForm initialData={profileResponse.data} />
          ) : (
            <div className="space-y-4">
              <div className="space-y-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-10 w-full" />
              </div>
              <Skeleton className="h-10 w-28" />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
