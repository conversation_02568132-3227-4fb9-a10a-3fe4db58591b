# Equipment Module

## Module Purpose

The Equipment module manages the different types of equipment that logistics providers can offer. This includes trailers, containers, vehicles, chassis, and other equipment types. The module allows administrators to:

- View all equipment types grouped by category
- Add new equipment types
- Edit existing equipment types
- Delete equipment types that are not in use

Equipment types defined in this module can be associated with providers in the Providers module and selected in RFQs.

## Component Structure

The Equipment module follows the standardized dashboard module structure:

```
src/app/dashboard/equipment/
├── _components/                  # Module-specific components
│   ├── equipment-data-table.tsx  # Data table component
│   ├── equipment-form.tsx        # Form component
│   ├── dialogs/                  # Dialog components
│   │   └── equipment-dialog.tsx  # Add equipment dialog
│   ├── equipment-content.tsx     # Server component for data fetching
│   └── equipment-skeleton.tsx    # Loading skeleton
├── layout.tsx                    # Module-specific layout
├── page.tsx                      # Main listing page
└── equipment_summary.md          # This documentation file
```

### Key Components

- **EquipmentPage**: The main page component that sets up the layout and Suspense boundary.
- **EquipmentContent**: Server component that fetches equipment data and handles errors.
- **EquipmentDataTable**: Client component that displays equipment data grouped by category.
- **EquipmentDialog**: Dialog component for adding new equipment.
- **EquipmentForm**: Form component for creating and editing equipment.
- **EquipmentSkeleton**: Loading skeleton for the equipment table.

## Data Flow

```
EquipmentPage
  ↓
Suspense
  ↓
EquipmentContent (Server Component)
  ↓ getEquipment() action
  ↓
EquipmentDataTable (Client Component)
  ↓ User interactions (add, edit, delete)
  ↓
Server Actions (createEquipment, updateEquipment, deleteEquipment)
  ↓
Service Layer (equipment.service.ts)
  ↓
Database (Supabase)
```

## API Endpoints

The Equipment module provides the following API endpoints:

- `GET /api/v1/equipment`: Get all equipment types
- `POST /api/v1/equipment`: Create a new equipment type
- `PUT /api/v1/equipment/:id`: Update an existing equipment type
- `DELETE /api/v1/equipment/:id`: Delete an equipment type

## Server Actions

The module uses the following server actions:

- `getEquipment()`: Fetches all equipment types
- `createEquipment(data)`: Creates a new equipment type
- `updateEquipment(data)`: Updates an existing equipment type
- `deleteEquipment(id)`: Deletes an equipment type

## Database Schema

The Equipment module uses the following database tables:

- `equipment_types`: Stores equipment type information

  - `id`: UUID primary key
  - `name`: String, name of the equipment type
  - `category`: String, category of the equipment type
  - `description`: String, optional description
  - `created_at`: Timestamp
  - `updated_at`: Timestamp

- `provider_equipments`: Junction table linking providers to equipment types
  - `id`: UUID primary key
  - `provider_id`: UUID foreign key to providers
  - `equipment_type_id`: UUID foreign key to equipment_types
  - `quantity`: Integer, number of equipment units
  - `created_at`: Timestamp
  - `updated_at`: Timestamp

## Related Modules

- **Providers Module**: Uses equipment types to specify what equipment providers offer
- **RFQs Module**: Uses equipment types to specify what equipment is required for a shipment

## Future Enhancements

The following enhancements could improve the Equipment module in future iterations:

1. **Add Pagination Support**: Implement pagination for the equipment data table to improve performance when dealing with large numbers of equipment types.

2. **Implement Search and Filtering**: Add search functionality and filters to make it easier for users to find specific equipment types, especially as the database grows.

3. **Add Equipment Detail Page**: Create a dedicated detail page for viewing and editing equipment types, similar to the provider detail page, to provide a more focused editing experience.

4. **Enhance Equipment Categories**: Consider adding a dedicated table for equipment categories to make them more manageable and consistent, potentially allowing administrators to define and manage categories separately.

5. **Add Equipment Usage Statistics**: Show how many providers are using each equipment type to provide more context when managing equipment, helping administrators understand the impact of potential changes.
