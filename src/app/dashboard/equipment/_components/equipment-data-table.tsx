"use client";

import { useState } from "react";
import Link from "next/link";
import { type EquipmentType } from "@/lib/schemas";
import { toast } from "@/components/ui/sonner";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  MoreHorizontalIcon,
  PencilIcon,
  TrashIcon,
  PlusIcon,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { deleteEquipment } from "@/lib/actions/equipment";
import { EquipmentDialog } from "./dialogs/equipment-dialog";
import { useRouter } from "next/navigation";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("EquipmentDataTable");

interface EquipmentDataTableProps {
  equipment: EquipmentType[];
}

/**
 * Client component for displaying equipment data in a table
 *
 * This component is responsible for:
 * - Displaying equipment data grouped by category
 * - Handling equipment deletion
 * - Providing UI for adding new equipment
 */
export function EquipmentDataTable({ equipment }: EquipmentDataTableProps) {
  const router = useRouter();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [selectedEquipmentId, setSelectedEquipmentId] = useState<string | null>(
    null,
  );
  const [isDeleting, setIsDeleting] = useState(false);

  // Function to capitalize first letter of each word
  const capitalize = (str: string) => {
    return str
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  // Get the equipment name for the delete dialog
  const getSelectedEquipmentName = () => {
    if (!selectedEquipmentId) return "";
    const selected = equipment.find((et) => et.id === selectedEquipmentId);
    return selected?.name || "";
  };

  // Handle delete action
  const handleDelete = async () => {
    if (!selectedEquipmentId) return;

    setIsDeleting(true);

    try {
      const result = await deleteEquipment(selectedEquipmentId);

      if (result.success) {
        toast.success("Equipment deleted successfully.");
        router.refresh(); // Refresh the page data
      } else {
        toast.error(result.message || "Failed to delete equipment.");
      }
    } catch (error) {
      logger.error("Error deleting equipment:", error);
      toast.error("An error occurred while deleting the equipment.");
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
    }
  };

  // Group equipment by category
  const groupedEquipment = equipment.reduce<Record<string, EquipmentType[]>>(
    (acc, equipment) => {
      const category = equipment.category;
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(equipment);
      return acc;
    },
    {},
  );

  return (
    <div>
      <div className="flex justify-end mb-4">
        <Button onClick={() => setAddDialogOpen(true)}>
          <PlusIcon className="mr-2 h-4 w-4" />
          Add Equipment
        </Button>
      </div>

      {Object.keys(groupedEquipment).length === 0 ? (
        <div className="text-center p-6 border rounded-md">
          <p className="text-muted-foreground">No equipment found.</p>
          <Button className="mt-4" onClick={() => setAddDialogOpen(true)}>
            Add Equipment
          </Button>
        </div>
      ) : (
        Object.entries(groupedEquipment).map(([category, items]) => (
          <div key={category} className="mb-8">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <Badge variant="outline" className="uppercase px-3 py-1">
                {capitalize(category)}
              </Badge>
            </h2>

            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead className="w-24 text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {items.map((equipment) => (
                  <TableRow key={equipment.id}>
                    <TableCell className="font-medium">
                      {equipment.name}
                    </TableCell>
                    <TableCell className="text-muted-foreground">
                      {equipment.description || "No description"}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontalIcon className="h-4 w-4" />
                            <span className="sr-only">Open menu</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link
                              href={`/dashboard/equipment/${equipment.id}/edit`}
                              className="flex items-center"
                            >
                              <PencilIcon className="mr-2 h-4 w-4" />
                              Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="flex items-center text-destructive focus:text-destructive"
                            onClick={() => {
                              setSelectedEquipmentId(equipment.id);
                              setDeleteDialogOpen(true);
                            }}
                          >
                            <TrashIcon className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ))
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Equipment</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete &quot;{getSelectedEquipmentName()}&quot;?
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <EquipmentDialog open={addDialogOpen} onOpenChange={setAddDialogOpen} />
    </div>
  );
}
