"use client";

import {
  Di<PERSON>,
  <PERSON>alogContent,
  <PERSON>alogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { EquipmentForm } from "../equipment-form";

interface EquipmentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

/**
 * Dialog component for adding equipment
 *
 * This component displays a dialog with a form for adding new equipment.
 * It uses the EquipmentForm component for the form fields.
 */
export function EquipmentDialog({ open, onOpenChange }: EquipmentDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add Equipment</DialogTitle>
          <DialogDescription>
            Create a new equipment for providers
          </DialogDescription>
        </DialogHeader>
        <EquipmentForm
          mode="create"
          onSuccess={() => onOpenChange(false)}
          inModal={true}
        />
      </DialogContent>
    </Dialog>
  );
}
