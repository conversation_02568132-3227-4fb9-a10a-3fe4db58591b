import { Skeleton } from "@/components/ui/skeleton";

/**
 * Loading skeleton for the equipment table
 *
 * This component displays a skeleton UI while the equipment data is being loaded.
 * It shows a placeholder for the table header and rows.
 */
export function EquipmentSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-4">
        <Skeleton className="h-8 w-24" />
      </div>
      <div className="border rounded-md">
        <div className="h-12 border-b px-6 flex items-center">
          <Skeleton className="h-4 w-[250px]" />
        </div>
        {[...Array(5)].map((_, i) => (
          <div
            key={i}
            className="h-16 px-6 flex items-center border-b last:border-b-0"
          >
            <Skeleton className="h-4 w-full" />
          </div>
        ))}
      </div>
    </div>
  );
}
