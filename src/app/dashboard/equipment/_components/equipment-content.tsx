import { getEquipment } from "@/lib/actions/equipment";
import { EquipmentDataTable } from "./equipment-data-table";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("EquipmentContent");

/**
 * Server Component for fetching equipment data
 *
 * This component is responsible for:
 * - Fetching equipment data using the getEquipment action
 * - Handling error states
 * - Passing data to the client-side EquipmentDataTable component
 */
export async function EquipmentContent() {
  logger.info("Fetching equipment data");
  const result = await getEquipment();

  if (result.error) {
    logger.error("Error fetching equipment data:", result.error);
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{result.error}</AlertDescription>
      </Alert>
    );
  }

  return <EquipmentDataTable equipment={result.data} />;
}
