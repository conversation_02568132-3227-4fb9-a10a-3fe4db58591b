"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "@/components/ui/sonner";
import { createEquipment, updateEquipment } from "@/lib/actions/equipment";
import { CreateEquipmentTypeSchema, type EquipmentType } from "@/lib/schemas";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("EquipmentForm");

// Using EquipmentType imported from @/lib/schemas

interface EquipmentFormProps {
  equipment?: EquipmentType | null;
  mode: "create" | "edit";
  onSuccess?: () => void; // For handling success in modal context
  inModal?: boolean; // To indicate if in a modal
}

// Common equipment categories
const COMMON_EQUIPMENT_CATEGORIES = [
  "trailer",
  "container",
  "truck",
  "van",
  "forklift",
  "crane",
  "other",
];

export function EquipmentForm({
  equipment,
  mode,
  onSuccess,
  inModal = false,
}: EquipmentFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [customCategory, setCustomCategory] = useState<string>("");

  // Initialize form with equipment data if in edit mode
  const form = useForm({
    resolver: zodResolver(CreateEquipmentTypeSchema),
    defaultValues: {
      name: equipment?.name || "",
      category: equipment?.category || "",
      description: equipment?.description || "",
    },
  });

  // Handle form submission
  const onSubmit = async (data: any) => {
    setIsSubmitting(true);

    try {
      let result;

      if (mode === "create") {
        const formData = new FormData();
        formData.append("name", data.name);
        formData.append("category", data.category);
        formData.append("description", data.description || "");
        result = await createEquipment(undefined, formData);
      } else {
        if (!equipment?.id) {
          throw new Error("Equipment ID is required for editing");
        }
        const formData = new FormData();
        formData.append("id", equipment.id);
        formData.append("name", data.name);
        formData.append("category", data.category);
        formData.append("description", data.description || "");
        result = await updateEquipment(undefined, formData);
      }

      if (result.success) {
        toast.success(
          mode === "create"
            ? "Equipment added successfully!"
            : "Equipment updated successfully!",
        );

        if (onSuccess) {
          onSuccess();
        } else if (!inModal) {
          router.push("/dashboard/equipment");
          router.refresh();
        }
      } else {
        toast.error(result.message || "An error occurred. Please try again.");
      }
    } catch (error) {
      logger.error("Error submitting form:", error);
      toast.error("An error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Name field */}
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter equipment name" {...field} />
              </FormControl>
              <FormDescription>The name of this equipment type</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Category field */}
        <FormField
          control={form.control}
          name="category"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Category</FormLabel>
              <div className="space-y-4">
                <Select
                  value={
                    COMMON_EQUIPMENT_CATEGORIES.includes(field.value)
                      ? field.value
                      : "other"
                  }
                  onValueChange={(value) => {
                    if (value === "other") {
                      setCustomCategory(field.value);
                    } else {
                      field.onChange(value);
                      setCustomCategory("");
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    {COMMON_EQUIPMENT_CATEGORIES.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category.charAt(0).toUpperCase() + category.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {!COMMON_EQUIPMENT_CATEGORIES.includes(field.value) && (
                  <FormControl>
                    <Input
                      placeholder="Enter custom category"
                      value={customCategory || field.value}
                      onChange={(e) => {
                        setCustomCategory(e.target.value);
                        field.onChange(e.target.value);
                      }}
                    />
                  </FormControl>
                )}
              </div>
              <FormDescription>
                The category of equipment (e.g., trailer, container)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Description field */}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description (Optional)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter a description of this equipment"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                A brief description explaining this equipment
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Form actions */}
        <div className="flex justify-end space-x-4">
          {!inModal && (
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting
              ? "Saving..."
              : mode === "create"
                ? "Create Equipment"
                : "Update Equipment"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
