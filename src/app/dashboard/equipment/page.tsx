import { Suspense } from "react";
import { EquipmentContent } from "./_components/equipment-content";
import { EquipmentSkeleton } from "./_components/equipment-skeleton";
import { Button } from "@/components/ui/button";
import { PlusIcon } from "lucide-react";
import Link from "next/link";
import { H1, Lead } from "@/components/ui/typography";

/**
 * Equipment page component
 *
 * This is the main page for the equipment module.
 * It uses Suspense for data loading and displays the equipment content.
 */
export default function EquipmentPage() {
  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <div>
          <H1>Equipment</H1>
          <Lead>
            Manage equipment available for logistics providers
          </Lead>
        </div>
      </div>

      <Suspense fallback={<EquipmentSkeleton />}>
        <EquipmentContent />
      </Suspense>
    </>
  );
}
