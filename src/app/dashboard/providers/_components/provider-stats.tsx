import { Card, CardContent } from "@/components/ui/card";
import {
  Building2,
  Check<PERSON><PERSON>cle,
  Clock,
  ShieldCheck,
  XCircle,
} from "lucide-react";
import type { ProviderWithEquipment } from "@/lib/schemas";

interface ProviderStatsProps {
  providers: ProviderWithEquipment[];
  totalCount?: number;
  activeCount?: number;
  pendingCount?: number;
  inactiveCount?: number;
  verifiedCount?: number;
}

export function ProviderStats({
  providers,
  totalCount,
  activeCount,
  pendingCount,
  inactiveCount,
  verifiedCount,
}: ProviderStatsProps) {
  // If counts are provided, use them; otherwise calculate from the providers array
  const activeProviders =
    activeCount !== undefined
      ? activeCount
      : providers.filter((p) => p.status === "active").length;
  const pendingProviders =
    pendingCount !== undefined
      ? pendingCount
      : providers.filter((p) => p.status === "pending").length;
  const inactiveProviders =
    inactiveCount !== undefined
      ? inactiveCount
      : providers.filter(
          (p) => p.status === "inactive" || p.status === "suspended",
        ).length;
  const verifiedProviders =
    verifiedCount !== undefined
      ? verifiedCount
      : providers.filter((p) => p.verified).length;

  // Calculate total providers as the sum of active, pending, and inactive
  // If totalCount is provided, use it; otherwise calculate from individual counts
  const totalProviders =
    totalCount !== undefined
      ? totalCount
      : activeProviders + pendingProviders + inactiveProviders;

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
      <Card className="py-3">
        <CardContent className="pt-2 px-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Total Providers
              </p>
              <p className="text-3xl font-bold">{totalProviders}</p>
            </div>
            <div className="p-1.5 bg-primary/10 rounded-full">
              <Building2 className="h-5 w-5 text-primary" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="py-3">
        <CardContent className="pt-2 px-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Active Providers
              </p>
              <p className="text-3xl font-bold">{activeProviders}</p>
            </div>
            <div className="p-1.5 bg-green-100 rounded-full">
              <CheckCircle className="h-5 w-5 text-green-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="py-3">
        <CardContent className="pt-2 px-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Pending Providers
              </p>
              <p className="text-3xl font-bold">{pendingProviders}</p>
            </div>
            <div className="p-1.5 bg-yellow-100 rounded-full">
              <Clock className="h-5 w-5 text-yellow-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="py-3">
        <CardContent className="pt-2 px-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Inactive Providers
              </p>
              <p className="text-3xl font-bold">{inactiveProviders}</p>
            </div>
            <div className="p-1.5 bg-red-100 rounded-full">
              <XCircle className="h-5 w-5 text-red-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="py-3">
        <CardContent className="pt-2 px-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Verified Providers
              </p>
              <p className="text-3xl font-bold">{verifiedProviders}</p>
            </div>
            <div className="p-1.5 bg-blue-100 rounded-full">
              <ShieldCheck className="h-5 w-5 text-blue-600" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
