"use client";

import { useF<PERSON>, Form<PERSON>rovider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "@/components/ui/sonner";

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  ProviderFormValues,
  ProviderFormSchema,
} from "@/lib/schemas";

import { MultiSelect } from "@/components/ui/multi-select";
import { createLogger } from "@/lib/utils/logger/logger";
import { Loader2 } from "lucide-react";
import { useEquipmentTypeOptions } from "@/hooks/use-equipment-types";
import {
  useCreateProvider,
  useUpdateProviderEquipments,
} from "@/hooks/use-provider-queries";

const logger = createLogger("CreateProviderForm");

interface CreateProviderFormProps {
  onSuccess?: () => void;
  onSubmitting?: (isSubmitting: boolean) => void;
}

/**
 * Form for creating a new provider
 */
export function CreateProviderForm({
  onSuccess,
  onSubmitting,
}: CreateProviderFormProps) {
  const router = useRouter();

  // Fetch equipment types
  const {
    options: equipmentTypes = [],
    loading: isLoadingEquipmentTypes,
    error: equipmentTypesError,
  } = useEquipmentTypeOptions();

  // State to track selected equipment IDs
  const [selectedEquipmentIds, setSelectedEquipmentIds] = useState<string[]>([]);

  // Create provider mutation
  const createProvider = useCreateProvider();

  // Update provider equipments mutation
  const updateProviderEquipments = useUpdateProviderEquipments();

  // Initialize form with React Hook Form
  const form = useForm<ProviderFormValues>({
    resolver: zodResolver(ProviderFormSchema),
    defaultValues: {
      name: "",
      tax_id: "",
      full_address: "",
      status: "pending",
      verified: false,
    },
    mode: "onChange",
  });

  // Get form methods
  const { watch, setValue } = form;

  // Watch form values for reactive updates
  const formValues = watch();

  // Handle form submission
  const handleFormSubmit = async (values: ProviderFormValues) => {
    if (onSubmitting) {
      onSubmitting(true);
    }

    try {

      // Create provider
      const result = await createProvider.mutateAsync(values);

      if (result.success && result.data?.id) {
        // Update provider equipments if any selected
        if (selectedEquipmentIds.length > 0) {
          try {
            await updateProviderEquipments.mutateAsync({
              providerId: result.data.id,
              equipmentIds: selectedEquipmentIds,
            });

          } catch (equipmentError) {
            logger.error("Equipment update failed:", equipmentError);
            toast.error("Provider created but failed to associate equipment types. Please edit the provider to add equipment.");
            // Still continue with success flow since provider was created
          }
        }

        toast.success("Provider created successfully");

        // Reset form
        form.reset();
        setSelectedEquipmentIds([]);

        router.refresh();

        if (onSuccess) {
          onSuccess();
        }
      } else {
        logger.error("Provider creation failed:", result);
        toast.error("Failed to create provider");
      }
    } catch (error) {
      logger.error("Error creating provider:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to create provider"
      );
    } finally {
      if (onSubmitting) {
        onSubmitting(false);
      }
    }
  };

  // Handle equipment selection change
  const handleEquipmentChange = (selectedIds: string[]) => {
    setSelectedEquipmentIds(selectedIds);
  };

  // Show error if equipment types failed to load
  if (equipmentTypesError) {
    toast.error("Failed to load equipment types");
  }

  return (
    <FormProvider {...form}>
      <div className="space-y-6">
        <form onSubmit={form.handleSubmit(handleFormSubmit)}>
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={() => (
                <FormItem>
                  <FormLabel required>Name</FormLabel>
                  <FormControl>
                    <Input
                      name="name"
                      value={formValues.name}
                      onChange={(e) => setValue("name", e.target.value)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tax_id"
              render={() => (
                <FormItem>
                  <FormLabel>Tax ID</FormLabel>
                  <FormControl>
                    <Input
                      name="tax_id"
                      placeholder="Optional"
                      value={formValues.tax_id || ""}
                      onChange={(e) => setValue("tax_id", e.target.value)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-2">
              <FormLabel className="block">Provider Equipments</FormLabel>
              <MultiSelect
                options={equipmentTypes}
                selected={selectedEquipmentIds}
                onChange={handleEquipmentChange}
                placeholder="Select provider equipments..."
                disabled={isLoadingEquipmentTypes || createProvider.isPending || updateProviderEquipments.isPending}
              />
            </div>

            <FormField
              control={form.control}
              name="status"
              render={() => (
                <FormItem>
                  <FormLabel required>Status</FormLabel>
                  <Select
                    name="status"
                    onValueChange={(value) =>
                      setValue("status", value as ProviderFormValues["status"])
                    }
                    defaultValue={formValues.status}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="suspended">Suspended</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="verified"
              render={() => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 pt-2">
                  <FormControl>
                    <Checkbox
                      name="verified"
                      checked={formValues.verified}
                      onCheckedChange={(checked) =>
                        setValue("verified", !!checked)
                      }
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Verified</FormLabel>
                  </div>
                </FormItem>
              )}
            />
          </div>

          <div className="flex justify-end mt-6">
            <Button
              type="submit"
              disabled={createProvider.isPending || updateProviderEquipments.isPending}
            >
              {createProvider.isPending || updateProviderEquipments.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create Provider"
              )}
            </Button>
          </div>
        </form>
      </div>
    </FormProvider>
  );
}
