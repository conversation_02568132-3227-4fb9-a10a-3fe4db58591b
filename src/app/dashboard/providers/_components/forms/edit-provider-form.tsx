"use client";

import { useForm, Form<PERSON>rovider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { toast } from "@/components/ui/sonner";

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  ProviderFormValues,
  ProviderFormSchema,
} from "@/lib/schemas";

import { MultiSelect } from "@/components/ui/multi-select";
import { Provider } from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";
import { Loader2 } from "lucide-react";
import { useEquipmentTypeOptions } from "@/hooks/use-equipment-types";
import {
  useProviderEquipments,
  useUpdateProvider,
  useUpdateProviderEquipments,
} from "@/hooks/use-provider-queries";

const logger = createLogger("EditProviderForm");

interface EditProviderFormProps {
  initialData: Provider;
  onSuccess?: () => void;
}

/**
 * Form for editing an existing provider
 */
export function EditProviderForm({
  initialData,
  onSuccess,
}: EditProviderFormProps) {
  const router = useRouter();

  // Fetch equipment types
  const {
    options: equipmentTypes = [],
    loading: isLoadingEquipmentTypes,
    error: equipmentTypesError,
  } = useEquipmentTypeOptions();

  // Fetch provider equipments
  const {
    data: providerEquipments = [],
    isLoading: isLoadingProviderEquipments,
    error: providerEquipmentsError,
  } = useProviderEquipments(initialData.id);

  // State to track selected equipment IDs
  const [selectedEquipmentIds, setSelectedEquipmentIds] = useState<string[]>([]);

  // Sync selected equipment IDs when provider equipments data loads
  useEffect(() => {
    if (providerEquipments.length > 0) {
      setSelectedEquipmentIds(providerEquipments);
    }
  }, [providerEquipments]);

  // Update provider mutation
  const updateProvider = useUpdateProvider();

  // Update provider equipments mutation
  const updateProviderEquipments = useUpdateProviderEquipments();

  // Initialize form with React Hook Form
  const form = useForm<ProviderFormValues>({
    resolver: zodResolver(ProviderFormSchema),
    defaultValues: {
      name: initialData.name || "",
      tax_id: initialData.tax_id || "",
      full_address: initialData.full_address || "",
      status: (initialData.status as ProviderFormValues["status"]) || "pending",
      verified: initialData.verified || false,
    },
    mode: "onChange",
  });

  // Get form methods
  const { watch, setValue } = form;

  // Watch form values for reactive updates
  const formValues = watch();

  // Handle form submission
  const handleFormSubmit = async (values: ProviderFormValues) => {
    try {
      // Update provider
      const result = await updateProvider.mutateAsync({
        providerId: initialData.id,
        values,
      });

      if (result.success) {
        // Update provider equipments
        await updateProviderEquipments.mutateAsync({
          providerId: initialData.id,
          equipmentIds: selectedEquipmentIds,
        });

        toast.success("Provider updated successfully");
        router.refresh();

        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (error) {
      logger.error("Error updating provider:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to update provider"
      );
    }
  };

  // Handle equipment selection change
  const handleEquipmentChange = (selectedIds: string[]) => {
    setSelectedEquipmentIds(selectedIds);
  };

  // Show error if equipment types or provider equipments failed to load
  if (equipmentTypesError) {
    toast.error("Failed to load equipment types");
  }

  if (providerEquipmentsError) {
    toast.error("Failed to load provider equipments");
  }

  return (
    <FormProvider {...form}>
      <div className="space-y-6">
        <form onSubmit={form.handleSubmit(handleFormSubmit)}>
          <input type="hidden" name="id" value={initialData.id} />

          <div className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={() => (
                <FormItem>
                  <FormLabel required>Name</FormLabel>
                  <FormControl>
                    <Input
                      name="name"
                      value={formValues.name}
                      onChange={(e) => setValue("name", e.target.value)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tax_id"
              render={() => (
                <FormItem>
                  <FormLabel>Tax ID</FormLabel>
                  <FormControl>
                    <Input
                      name="tax_id"
                      placeholder="Optional"
                      value={formValues.tax_id || ""}
                      onChange={(e) => setValue("tax_id", e.target.value)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-2">
              <FormLabel className="block">Provider Equipments</FormLabel>
              <MultiSelect
                options={equipmentTypes}
                selected={selectedEquipmentIds}
                onChange={handleEquipmentChange}
                placeholder="Select provider equipments..."
                disabled={isLoadingEquipmentTypes || isLoadingProviderEquipments || updateProviderEquipments.isPending}
              />
            </div>

            <FormField
              control={form.control}
              name="status"
              render={() => (
                <FormItem>
                  <FormLabel required>Status</FormLabel>
                  <Select
                    name="status"
                    onValueChange={(value) =>
                      setValue("status", value as ProviderFormValues["status"])
                    }
                    defaultValue={formValues.status}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="suspended">Suspended</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="verified"
              render={() => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 pt-2">
                  <FormControl>
                    <Checkbox
                      name="verified"
                      checked={formValues.verified}
                      onCheckedChange={(checked) =>
                        setValue("verified", !!checked)
                      }
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Verified</FormLabel>
                  </div>
                </FormItem>
              )}
            />
          </div>

          <div className="flex justify-end mt-6">
            <Button
              type="submit"
              disabled={updateProvider.isPending || updateProviderEquipments.isPending}
            >
              {updateProvider.isPending || updateProviderEquipments.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Update Provider"
              )}
            </Button>
          </div>
        </form>
      </div>
    </FormProvider>
  );
}
