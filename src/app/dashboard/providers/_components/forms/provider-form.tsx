"use client";

import { Provider } from "@/lib/schemas";
import { EditProviderForm } from "./edit-provider-form";
import { CreateProviderForm } from "./create-provider-form";

interface ProviderFormProps {
  initialData?: Provider;
  onSuccess?: () => void;
  onSubmitting?: (isSubmitting: boolean) => void;
}

/**
 * Provider form component
 *
 * This component renders either the edit form or create form based on whether
 * initialData is provided.
 *
 * Note: The QueryProvider is already provided by the dashboard layout,
 * so we don't need to wrap our components in it here.
 */
export function ProviderForm({
  initialData,
  onSuccess,
  onSubmitting,
}: ProviderFormProps) {
  return initialData ? (
    <EditProviderForm initialData={initialData} onSuccess={onSuccess} />
  ) : (
    <CreateProviderForm onSuccess={onSuccess} onSubmitting={onSubmitting} />
  );
}
