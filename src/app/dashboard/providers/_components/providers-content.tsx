"use client";

import AddProviderDialog from "./dialogs/add-provider-dialog";
import { ProviderStats } from "./provider-stats";
import { RefreshButton } from "./refresh-button";
import { H1, Lead } from "@/components/ui/typography";
import { ProvidersDataTable } from "./tables/providers-data-table";
import { useProviders } from "@/hooks/use-providers";
import { getProviderCountsByStatusAction } from "@/lib/actions/provider.actions";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";

// Fallback component for the providers data table
function ProvidersTableSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Skeleton className="h-10 w-64" />
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-24" />
        </div>
        <div className="flex items-center gap-2">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-24" />
        </div>
      </div>
      <Skeleton className="h-[500px] w-full rounded-md" />
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-40" />
        <div className="flex items-center gap-2">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-8 w-32" />
        </div>
      </div>
    </div>
  );
}

// Fallback component for the provider stats
function ProviderStatsSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
      <Skeleton className="h-[100px] rounded-md" />
      <Skeleton className="h-[100px] rounded-md" />
      <Skeleton className="h-[100px] rounded-md" />
      <Skeleton className="h-[100px] rounded-md" />
      <Skeleton className="h-[100px] rounded-md" />
    </div>
  );
}

// Component that fetches and displays provider stats
function ProviderStatsData() {
  // Fetch provider counts with suspense disabled to avoid setState during render
  const { data: providerCounts, isLoading } = useQuery({
    queryKey: ["providerCounts"],
    queryFn: async () => {
      const response = await getProviderCountsByStatusAction();
      if (!response.success) {
        throw new Error(response.error || "Failed to fetch provider counts");
      }
      return response.data;
    },
  });

  // Show skeleton while loading
  if (isLoading) {
    return <ProviderStatsSkeleton />;
  }

  // Get counts from the provider counts action
  const totalCount = providerCounts?.totalCount || 0;
  const activeCount = providerCounts?.activeCount || 0;
  const pendingCount = providerCounts?.pendingCount || 0;
  const inactiveCount = providerCounts?.inactiveCount || 0;
  const verifiedCount = providerCounts?.verifiedCount || 0;

  return (
    <ProviderStats
      providers={[]}
      totalCount={totalCount}
      activeCount={activeCount}
      pendingCount={pendingCount}
      inactiveCount={inactiveCount}
      verifiedCount={verifiedCount}
    />
  );
}

// Component that fetches and displays provider data table
function ProvidersTableData() {
  // Use TanStack Query to fetch all providers with suspense disabled to avoid setState during render
  const { data: providersData, error, isLoading, isRefetching } = useProviders();

  if (error) {
    return <div className="text-destructive">Error: {String(error)}</div>;
  }

  // Show skeleton while loading
  if (isLoading) {
    return <ProvidersTableSkeleton />;
  }

  // Ensure providers is always an array
  const providers = providersData?.providers || [];

  return (
    <div className="overflow-x-auto relative">
      <ProvidersDataTable data={providers} />
      {isRefetching && (
        <div className="absolute top-0 right-0 m-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
        </div>
      )}
    </div>
  );
}

// Component that combines all provider data components
function ProvidersData() {
  return (
    <>
      <ProviderStatsData />
      <ProvidersTableData />
    </>
  );
}

export function ProvidersContent() {
  return (
    <>
      <div className="flex items-center justify-between mb-4">
        <div>
          <H1>
            Transport Providers
          </H1>
          <Lead>
            Manage your transport providers
          </Lead>
        </div>
        <div className="flex items-center gap-2">
          <RefreshButton />
          <AddProviderDialog />
        </div>
      </div>

      <ProvidersData />
    </>
  );
}
