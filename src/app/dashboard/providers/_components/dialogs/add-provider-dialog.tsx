"use client";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ProviderForm } from "../forms/provider-form"; // Updated import path
import { Plus } from "lucide-react";

interface AddProviderDialogProps {
  countries?: any[]; // Keep for backward compatibility
}

export default function AddProviderDialog({
  countries,
}: AddProviderDialogProps) {
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSuccess = () => {
    setOpen(false);
  };

  const handleOpenChange = (isOpen: boolean) => {
    // Only allow closing when not submitting
    if (!isSubmitting || !isOpen) {
      setOpen(isOpen);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button size="lg">
          <Plus className="mr-2 h-6 w-6" />
          Add Provider
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Provider</DialogTitle>
        </DialogHeader>
        <ProviderForm
          onSuccess={handleSuccess}
          onSubmitting={setIsSubmitting}
        />
      </DialogContent>
    </Dialog>
  );
}
