import { Suspense } from "react";
import { ProviderDetailsContent } from "./_components/provider-details-content";
import { ProviderDetailsSkeleton } from "./_components/provider-details-skeleton";

interface ProviderPageProps {
  params: Promise<{
    id: string;
  }>;
  searchParams?: Promise<{
    tab?: string;
    page?: string;
    pageSize?: string;
  }>;
}

export default async function ProviderPage({
  params,
  searchParams,
}: ProviderPageProps) {
  const { id } = await params;
  const {
    tab: paramTab,
    page,
    pageSize,
  } = (await searchParams) || {
    tab: undefined,
    page: undefined,
    pageSize: undefined,
  };
  const tab =
    paramTab === "routes"
      ? "routes"
      : paramTab === "contacts"
        ? "contacts"
        : "details";

  // Parse pagination parameters
  const parsedPage = page ? parseInt(page) : 1;
  const parsedPageSize = pageSize ? parseInt(pageSize) : 10;

  return (
    <Suspense fallback={<ProviderDetailsSkeleton />}>
      <ProviderDetailsContent
        id={id}
        tab={tab}
        page={parsedPage}
        pageSize={parsedPageSize}
      />
    </Suspense>
  );
}
