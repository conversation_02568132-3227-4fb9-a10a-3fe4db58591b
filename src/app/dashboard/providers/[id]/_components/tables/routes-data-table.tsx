"use client";

import * as React from "react";
import { useCallback } from "react";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { DataTable } from "@/components/ui/tables";
import { DataTableColumnHeader } from "@/components/ui/tables/column-header";
import { ProviderRouteWithCountries } from "@/lib/schemas/composite-types";
import { RouteActions } from "../route-components";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("RoutesDataTable");

interface RoutesDataTableProps {
  data: ProviderRouteWithCountries[];
  totalCount?: number;
  currentPage?: number;
  pageSize?: number;
  providerId: string;
  onEditRoute: (route: ProviderRouteWithCountries) => void;
  onDeleteRoute: (route: ProviderRouteWithCountries) => void;
  isPaginationLoading?: boolean;
}

const getColumns = (
  onEditRoute: (route: ProviderRouteWithCountries) => void,
  onDeleteRoute: (route: ProviderRouteWithCountries) => void,
): ColumnDef<ProviderRouteWithCountries>[] => [
  {
    id: "origin_country",
    accessorFn: (row) => row.origin_country?.name,
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Origin Country" />
    ),
    cell: ({ row }) => {
      const originCountry = row.original.origin_country;
      return <div className="font-medium">{originCountry?.name || "-"}</div>;
    },
    filterFn: "includesString",
    meta: {
      displayName: "Origin Country",
    },
  },
  {
    id: "destination_country",
    accessorFn: (row) => row.destination_country?.name,
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Destination Country" />
    ),
    cell: ({ row }) => {
      const destinationCountry = row.original.destination_country;
      return (
        <div className="font-medium">{destinationCountry?.name || "-"}</div>
      );
    },
    filterFn: "includesString",
    meta: {
      displayName: "Destination Country",
    },
  },
  {
    accessorKey: "notes",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Notes" />
    ),
    cell: ({ row }) => {
      return <div>{row.original.notes || "-"}</div>;
    },
    filterFn: "includesString",
    meta: {
      displayName: "Notes",
    },
  },
  {
    accessorKey: "is_active",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const isActive = row.original.is_active;
      return (
        <Badge
          variant={isActive ? "default" : "secondary"}
          className={isActive ? "bg-green-500" : ""}
        >
          {isActive ? "Active" : "Inactive"}
        </Badge>
      );
    },
    filterFn: (row, id, value) => {
      const status = row.getValue(id) ? "active" : "inactive";
      return value.includes(status);
    },
    meta: {
      displayName: "Status",
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      return (
        <RouteActions
          route={row.original}
          onEdit={onEditRoute}
          onDelete={onDeleteRoute}
        />
      );
    },
  },
];

export function RoutesDataTable({
  data,
  totalCount,
  currentPage = 1,
  pageSize = 10,
  providerId: _providerId, // Renamed to indicate it's not used
  onEditRoute,
  onDeleteRoute,
  isPaginationLoading = false,
}: RoutesDataTableProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Add debugging to check if data is being received
  React.useEffect(() => {
    logger.info(
      `RoutesDataTable received data: ${data?.length || 0} items, totalCount: ${totalCount}`,
    );
    if (data && data.length > 0) {
      logger.info(
        `First route: Origin: ${data[0]?.origin_country?.name || "unknown"}, Destination: ${
          data[0]?.destination_country?.name || "unknown"
        }`,
      );
    } else {
      logger.warn("No route data received or empty array");
    }
  }, [data, totalCount]);

  const columns = React.useMemo(
    () => getColumns(onEditRoute, onDeleteRoute),
    [onEditRoute, onDeleteRoute],
  );

  // Handle pagination changes
  const handlePaginationChange = useCallback(
    (page: number, pageSize: number) => {
      logger.info("RoutesDataTable.handlePaginationChange called with:", {
        page,
        pageSize,
        currentUrl: window.location.href,
      });

      // Create a new URLSearchParams object
      const params = new URLSearchParams(searchParams.toString());

      // Update the pagination parameters
      logger.info("Setting URL parameters:", { page, pageSize });
      params.set("page", page.toString());
      params.set("pageSize", pageSize.toString());

      // Preserve the tab parameter if it exists
      if (searchParams.has("tab")) {
        params.set("tab", searchParams.get("tab")!);
      }

      // Log the new URL
      const newUrl = `${pathname}?${params.toString()}`;
      logger.info("New URL will be:", newUrl);

      // Navigate to the new URL with replace to avoid adding to history stack
      router.push(newUrl, { scroll: false });
    },
    [pathname, searchParams, router],
  );

  // Ensure data is an array, even if it's undefined or null
  const safeData = Array.isArray(data) ? data : [];

  return (
    <DataTable
      columns={columns}
      data={safeData}
      paginationConfig={{
        totalItems: totalCount !== undefined ? totalCount : safeData.length,
        manualPagination: true,
        initialPage: currentPage - 1, // Convert to 0-based index for TanStack Table
        initialPageSize: pageSize,
        onPaginationChange: handlePaginationChange,
      }}
      // Pass the isPaginationLoading prop to show loading indicators during pagination
      isPaginationLoading={isPaginationLoading}
    />
  );
}
