import { Skeleton } from "@/components/ui/skeleton";

export function ProviderDetailsSkeleton() {
  return (
    <div className="space-y-6">
      {/* Back Button Skeleton */}
      <div className="mb-6">
        <Skeleton className="h-8 w-32" />
      </div>

      {/* Header Skeleton */}
      <div className="space-y-2">
        <Skeleton className="h-7 w-1/3" /> {/* Title */}
        <Skeleton className="h-4 w-1/2" /> {/* Subtitle */}
      </div>

      {/* Provider Summary Card Skeleton */}
      <div className="rounded-lg border mb-6">
        <div className="p-6 pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Skeleton className="h-10 w-10 rounded-full" />{" "}
              {/* Icon Container */}
              <div className="space-y-2">
                <Skeleton className="h-6 w-40" /> {/* Provider Name */}
                <Skeleton className="h-4 w-60" /> {/* Address */}
              </div>
            </div>
            <div className="flex flex-col items-end gap-2">
              <Skeleton className="h-5 w-16" /> {/* Status Badge */}
              <Skeleton className="h-5 w-24" /> {/* Verification Badge */}
            </div>
          </div>
        </div>
        <div className="border-t p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Skeleton className="h-4 w-16" /> {/* Tax ID Label */}
              <Skeleton className="h-5 w-32" /> {/* Tax ID Value */}
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-16" /> {/* Created Label */}
              <Skeleton className="h-5 w-32" /> {/* Created Value */}
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" /> {/* Updated Label */}
              <Skeleton className="h-5 w-32" /> {/* Updated Value */}
            </div>
          </div>
        </div>
      </div>

      {/* Tabs Skeleton */}
      <div className="space-y-4">
        <div className="flex border-b">
          <Skeleton className="h-10 w-1/3" />
          <Skeleton className="h-10 w-1/3" />
          <Skeleton className="h-10 w-1/3" />
        </div>

        {/* Tab Content Skeleton */}
        <div className="rounded-lg border p-6 space-y-4">
          <div className="space-y-2">
            <Skeleton className="h-6 w-1/4" /> {/* Tab Title */}
            <Skeleton className="h-4 w-1/3" /> {/* Tab Description */}
          </div>
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-2/3" />
          <Skeleton className="h-10 w-1/4 ml-auto" /> {/* Submit Button */}
        </div>
      </div>
    </div>
  );
}
