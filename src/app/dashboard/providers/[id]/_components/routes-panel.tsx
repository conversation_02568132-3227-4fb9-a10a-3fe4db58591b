"use client";

import { useState, useEffect, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { toast } from "@/components/ui/sonner";

// Custom components and hooks
import {
  RouteFormModal,
  EditRouteDialog,
  DeleteRouteDialog,
} from "./route-components";
import { useDialog } from "./hooks";
import { RoutesDataTable } from "./tables/routes-data-table";
import { RoutesTableSkeleton } from "./tables/routes-skeleton";

// Types
import { ProviderRouteWithCountries } from "@/lib/schemas/composite-types";

// Utils
import { createLogger } from "@/lib/utils/logger/logger";
import {
  deleteProviderRouteAction,
  getProviderRoutesAction,
} from "@/lib/actions/provider-routes.actions";

const logger = createLogger("RoutesPanel");

interface RoutesPanelProps {
  providerId: string;
  routes: ProviderRouteWithCountries[];
  countries: any[];
}

/**
 * RoutesPanel component displays a list of routes for a provider
 * and provides functionality to add, edit, and delete routes
 */
export default function RoutesPanel({
  providerId,
  routes: initialRoutes,
  countries,
}: RoutesPanelProps) {
  const searchParams = useSearchParams();

  // Parse pagination parameters from URL
  const page = parseInt(searchParams.get("page") || "1");
  const pageSize = parseInt(searchParams.get("pageSize") || "10");

  // Parse filter parameters from URL
  const searchQuery = searchParams.get("search") || "";
  const originCountryId = searchParams.get("origin_country_id") || "";
  const destinationCountryId = searchParams.get("destination_country_id") || "";
  const isActiveParam = searchParams.get("is_active");
  const isActive = isActiveParam ? isActiveParam === "true" : undefined;

  // State for routes data
  const [routes, setRoutes] =
    useState<ProviderRouteWithCountries[]>(initialRoutes);
  const [totalCount, setTotalCount] = useState<number>(initialRoutes.length);
  const [loading, setLoading] = useState<boolean>(false);
  const [isPaginationLoading, setIsPaginationLoading] =
    useState<boolean>(false);
  const [isInitialLoad, setIsInitialLoad] = useState<boolean>(true);

  // State for route management
  const [selectedRoute, setSelectedRoute] = useState<
    ProviderRouteWithCountries | undefined
  >(undefined);
  const [routeToDelete, setRouteToDelete] =
    useState<ProviderRouteWithCountries | null>(null);
  const [deletingRouteId, setDeletingRouteId] = useState<string | null>(null);

  // Dialog state management
  const editDialog = useDialog(false);
  const deleteDialog = useDialog(false);

  // Fetch routes with pagination and filtering
  useEffect(() => {
    const fetchRoutes = async () => {
      // Use loading state for initial load, pagination loading for subsequent loads
      if (isInitialLoad) {
        setLoading(true);
      } else {
        setIsPaginationLoading(true);
      }

      try {
        // Log the current page, pageSize, and filter parameters for debugging
        logger.info(
          `Fetching routes for page ${page}, pageSize ${pageSize}, search=${searchQuery}, originCountryId=${originCountryId}, destinationCountryId=${destinationCountryId}, isActive=${isActive}`,
        );

        // Build the request parameters
        const requestParams: any = {
          provider_id: providerId,
          page,
          pageSize,
        };

        // Add filter parameters if they exist
        if (searchQuery) {
          requestParams.search = searchQuery;
        }

        if (originCountryId) {
          requestParams.origin_country_id = originCountryId;
        }

        if (destinationCountryId) {
          requestParams.destination_country_id = destinationCountryId;
        }

        if (isActive !== undefined) {
          requestParams.is_active = isActive;
        }

        const result = await getProviderRoutesAction(requestParams);

        if (result.success) {
          setRoutes(result.data);
          setTotalCount(result.count || result.data.length);

          if (isInitialLoad) {
            setIsInitialLoad(false);
          }
        } else {
          toast.error(result.error || "Failed to fetch routes");
        }
      } catch (error) {
        logger.error("Error fetching routes:", error);
        toast.error("Failed to fetch routes");
      } finally {
        setLoading(false);
        setIsPaginationLoading(false);
      }
    };

    fetchRoutes();
  }, [
    providerId,
    page,
    pageSize,
    searchQuery,
    originCountryId,
    destinationCountryId,
    isActive,
    isInitialLoad,
  ]);

  /**
   * Handle route edit action
   */
  const handleEditRoute = (route: ProviderRouteWithCountries) => {
    setSelectedRoute(route);
    editDialog.open();
  };

  /**
   * Handle route delete action
   */
  const handleDeleteRoute = (route: ProviderRouteWithCountries) => {
    setRouteToDelete(route);
    deleteDialog.open();
  };

  /**
   * Handle successful edit
   */
  const handleEditSuccess = async () => {
    editDialog.close();

    // Fetch updated data instead of refreshing the page
    setLoading(true);
    try {
      // Build the request parameters with filters
      const requestParams: any = {
        provider_id: providerId,
        page,
        pageSize,
      };

      // Add filter parameters if they exist
      if (searchQuery) {
        requestParams.search = searchQuery;
      }

      if (originCountryId) {
        requestParams.origin_country_id = originCountryId;
      }

      if (destinationCountryId) {
        requestParams.destination_country_id = destinationCountryId;
      }

      if (isActive !== undefined) {
        requestParams.is_active = isActive;
      }

      const result = await getProviderRoutesAction(requestParams);

      if (result.success) {
        setRoutes(result.data);
        setTotalCount(result.count || result.data.length);
      } else {
        toast.error(result.error || "Failed to fetch routes");
      }
    } catch (error) {
      logger.error("Error fetching routes:", error);
      toast.error("Failed to fetch routes");
    } finally {
      setLoading(false);
    }
  };

  /**
   * Execute route deletion
   */
  const handleDelete = async () => {
    if (!routeToDelete) return;

    setDeletingRouteId(routeToDelete.id);
    try {
      const result = await deleteProviderRouteAction(
        routeToDelete.id,
        routeToDelete.provider_id,
      );

      if (!result.success) {
        toast.error(result.error || "Failed to delete route");
        return;
      }

      toast.success("Route deleted successfully");
      setRouteToDelete(null);
      deleteDialog.close();

      // Fetch updated data instead of refreshing the page
      // Build the request parameters with filters
      const requestParams: any = {
        provider_id: providerId,
        page,
        pageSize,
      };

      // Add filter parameters if they exist
      if (searchQuery) {
        requestParams.search = searchQuery;
      }

      if (originCountryId) {
        requestParams.origin_country_id = originCountryId;
      }

      if (destinationCountryId) {
        requestParams.destination_country_id = destinationCountryId;
      }

      if (isActive !== undefined) {
        requestParams.is_active = isActive;
      }

      const updatedResult = await getProviderRoutesAction(requestParams);

      if (updatedResult.success) {
        setRoutes(updatedResult.data);
        setTotalCount(updatedResult.count || updatedResult.data.length);
      }
    } catch (error) {
      logger.error("Error deleting route:", error);
      toast.error("Failed to delete route");
    } finally {
      setDeletingRouteId(null);
    }
  };

  return (
    <div className="space-y-4">
      {/* Header with add button */}
      <div className="flex items-center justify-end">
        <RouteFormModal
          providerId={providerId}
          countries={countries}
          onSuccess={async () => {
            // Fetch updated data instead of refreshing the page
            setLoading(true);
            try {
              // Build the request parameters with filters
              const requestParams: any = {
                provider_id: providerId,
                page,
                pageSize,
              };

              // Add filter parameters if they exist
              if (searchQuery) {
                requestParams.search = searchQuery;
              }

              if (originCountryId) {
                requestParams.origin_country_id = originCountryId;
              }

              if (destinationCountryId) {
                requestParams.destination_country_id = destinationCountryId;
              }

              if (isActive !== undefined) {
                requestParams.is_active = isActive;
              }

              const result = await getProviderRoutesAction(requestParams);

              if (result.success) {
                setRoutes(result.data);
                setTotalCount(result.count || result.data.length);
              } else {
                toast.error(result.error || "Failed to fetch routes");
              }
            } catch (error) {
              logger.error("Error fetching routes:", error);
              toast.error("Failed to fetch routes");
            } finally {
              setLoading(false);
            }
          }}
        />
      </div>

      {/* Routes data table with loading state */}
      <Suspense fallback={<RoutesTableSkeleton />}>
        {loading ? (
          <RoutesTableSkeleton />
        ) : (
          <RoutesDataTable
            data={routes}
            totalCount={totalCount}
            currentPage={page}
            pageSize={pageSize}
            providerId={providerId}
            onEditRoute={handleEditRoute}
            onDeleteRoute={handleDeleteRoute}
            isPaginationLoading={isPaginationLoading}
          />
        )}
      </Suspense>

      {/* Edit Route Dialog */}
      <EditRouteDialog
        isOpen={editDialog.isOpen}
        onOpenChange={(isOpen: boolean) => {
          editDialog.setIsOpen(isOpen);
          if (!isOpen) {
            setSelectedRoute(undefined);
          }
        }}
        selectedRoute={selectedRoute}
        providerId={providerId}
        countries={countries}
        onSuccess={handleEditSuccess}
      />

      {/* Delete Route Alert Dialog */}
      <DeleteRouteDialog
        isOpen={deleteDialog.isOpen}
        onOpenChange={deleteDialog.setIsOpen}
        routeToDelete={routeToDelete}
        onDelete={handleDelete}
        isDeleting={!!deletingRouteId}
      />
    </div>
  );
}
