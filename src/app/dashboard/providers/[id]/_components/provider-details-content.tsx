import Link from "next/link";
import { notFound } from "next/navigation";
import { ProviderForm } from "../../_components/forms/provider-form";
import { getProviderAction } from "@/lib/actions/provider.actions";
import { getCountriesAction } from "@/lib/actions/country.actions";
import { getProviderRoutesAction } from "@/lib/actions/provider-routes.actions";
import ContactsPanel from "./contacts-panel";
import RoutesPanel from "./routes-panel";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Building2, Users, MapPin, CheckCircle, ArrowLeft } from "lucide-react";
import { createLogger } from "@/lib/utils/logger/logger";
import { H1, H4, <PERSON>, Text, SmallText } from "@/components/ui/typography";

const logger = createLogger("ProviderDetailsContent");

interface ProviderDetailsContentProps {
  id: string;
  tab?: string;
  page?: number;
  pageSize?: number;
}

function ProviderSummary({ provider }: { provider: any }) {
  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="bg-primary/10 p-2 rounded-full">
              <Building2 className="h-6 w-6 text-primary" />
            </div>
            <div>
              <CardTitle>
                <H4 className="m-0">{provider.name}</H4>
              </CardTitle>
            </div>
          </div>
          <div className="flex flex-col items-end gap-2">
            <Badge
              variant={provider.status === "active" ? "default" : "secondary"}
              className={provider.status === "active" ? "bg-green-500" : ""}
            >
              {provider.status.charAt(0).toUpperCase() +
                provider.status.slice(1)}
            </Badge>
            {provider.verified ? (
              <Badge
                variant="outline"
                className="bg-green-50 text-green-700 border-green-200"
              >
                <CheckCircle className="h-3 w-3 mr-1" /> Verified
              </Badge>
            ) : (
              <Badge
                variant="outline"
                className="bg-yellow-50 text-yellow-700 border-yellow-200"
              >
                Not Verified
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="border-t pt-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {provider.tax_id && (
            <div>
              <SmallText>Tax ID</SmallText>
              <Text className="font-medium">{provider.tax_id}</Text>
            </div>
          )}
          <div>
            <SmallText>Created</SmallText>
            <Text className="font-medium">
              {new Date(provider.created_at).toLocaleDateString()}
            </Text>
          </div>
          <div>
            <SmallText>Last Updated</SmallText>
            <Text className="font-medium">
              {new Date(provider.updated_at).toLocaleDateString()}
            </Text>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export async function ProviderDetailsContent({
  id,
  tab,
  page = 1,
  pageSize = 10,
}: ProviderDetailsContentProps) {
  // Log the pagination parameters for debugging
  logger.info(
    `ProviderDetailsContent: Using pagination parameters: page=${page}, pageSize=${pageSize}`,
  );

  const [providerRes, countriesRes, routesRes] = await Promise.all([
    getProviderAction(id, { includeContacts: true }),
    getCountriesAction(),
    getProviderRoutesAction({
      provider_id: id,
      page,
      pageSize,
    }),
  ]);

  if (!providerRes.success) {
    if (providerRes.error?.toLowerCase().includes("not found")) {
      notFound();
    }
    return <div className="text-destructive">Error: {providerRes.error}</div>;
  }
  if (!countriesRes.success) {
    return <div className="text-destructive">Error: {countriesRes.error}</div>;
  }

  const provider = providerRes.data;
  const countries = countriesRes.data;
  const routes = routesRes.success ? routesRes.data : [];

  return (
    <>
      <div className="flex items-center mb-6">
        <Link href="/dashboard/providers">
          <Button variant="ghost" size="sm" className="gap-1">
            <ArrowLeft className="h-4 w-4" />
            Back to Providers
          </Button>
        </Link>
      </div>

      <div>
        <H1>
          Edit Provider: {provider.name}
        </H1>
        <Lead>
          Update provider details, manage contacts, and configure service routes
        </Lead>
      </div>

      <ProviderSummary provider={provider} />

      <Tabs defaultValue={tab || "details"} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="details" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            <span>Details</span>
          </TabsTrigger>
          <TabsTrigger value="contacts" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <span>Contacts</span>
          </TabsTrigger>
          <TabsTrigger value="routes" className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            <span>Routes</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>
                <H4 className="m-0">Provider Details</H4>
              </CardTitle>
              <CardDescription>
                Update the provider&apos;s basic information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ProviderForm initialData={provider} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="contacts" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>
                <H4 className="m-0">Contacts</H4>
              </CardTitle>
              <CardDescription>Manage provider contacts</CardDescription>
            </CardHeader>
            <CardContent>
              <ContactsPanel
                providerId={provider.id}
                contacts={provider.provider_contacts}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="routes" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>
                <H4 className="m-0">Routes</H4>
              </CardTitle>
              <CardDescription>Configure service routes</CardDescription>
            </CardHeader>
            <CardContent>
              <RoutesPanel
                providerId={provider.id}
                routes={routesRes.success ? routesRes.data : []}
                countries={countries}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </>
  );
}
