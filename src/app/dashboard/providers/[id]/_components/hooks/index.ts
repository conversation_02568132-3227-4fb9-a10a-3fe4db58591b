"use client";

import { useState, useMemo, useEffect } from "react";
import { type Country } from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("ProviderHooks");

/**
 * Custom hook for filtering countries based on search text
 * @param countries - List of countries to filter
 * @param initialSearch - Initial search text
 * @returns Filtered countries and search state handlers
 */
export function useCountrySearch(
  countries: Country[],
  initialSearch: string = "",
) {
  const [searchText, setSearchText] = useState(initialSearch);

  // Log the initial countries array length when the hook is first used
  // This helps us verify we're getting all countries
  useEffect(() => {
    logger.info(`useCountrySearch initialized with ${countries.length} countries`);

    // Log the first few countries to verify the data
    if (countries.length > 0) {
      const sampleCountries = countries.slice(0, Math.min(5, countries.length));
      logger.info(`Sample countries: ${JSON.stringify(sampleCountries.map(c => c.name))}`);
    }
  }, [countries]);

  const filteredCountries = useMemo(() => {
    if (!searchText.trim()) return countries;

    // Improved search logic to be more flexible
    const searchLower = searchText.toLowerCase();

    // Log search parameters for debugging
    logger.info(
      `Searching for "${searchLower}" in ${countries.length} countries`,
    );

    const filtered = countries.filter((country) => {
      const nameLower = country.name.toLowerCase();
      const alpha2Lower = country.alpha2_code?.toLowerCase() || "";
      const alpha3Lower = country.alpha3_code?.toLowerCase() || "";

      // Check if the search text is in the name or ISO codes
      return (
        nameLower.includes(searchLower) ||
        alpha2Lower.includes(searchLower) ||
        alpha3Lower.includes(searchLower)
      );
    });

    // Log filtered results
    logger.info(`Found ${filtered.length} countries matching "${searchLower}"`);

    return filtered;
  }, [countries, searchText]);

  return {
    searchText,
    setSearchText,
    filteredCountries,
  };
}

/**
 * Custom hook for managing dialog state
 * @param initialState - Initial open state of the dialog
 * @returns Dialog state and handlers
 */
export function useDialog(initialState: boolean = false) {
  const [isOpen, setIsOpen] = useState(initialState);

  const open = () => setIsOpen(true);
  const close = () => setIsOpen(false);
  const toggle = () => setIsOpen((prev) => !prev);

  return {
    isOpen,
    setIsOpen,
    open,
    close,
    toggle,
  };
}
