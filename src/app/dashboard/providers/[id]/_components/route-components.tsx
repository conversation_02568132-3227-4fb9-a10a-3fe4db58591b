"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "@/components/ui/sonner";
import { Plus, MoreHorizontal } from "lucide-react";

// UI Components
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Combobox } from "@/components/ui/combobox";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertD<PERSON>og,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";

// Actions and Schemas
import {
  createProviderRouteAction,
  updateProviderRouteAction,
  deleteProviderRouteAction,
} from "@/lib/actions/provider-routes.actions";
import {
  ProviderRouteFormValues,
  ProviderRouteFormSchema,
} from "@/lib/schemas";

// Custom hooks and types
import { useCountrySearch } from "./hooks";
import { ProviderRouteWithCountries } from "@/lib/schemas/composite-types";

// Define interface types for the components
interface RouteFormProps {
  providerId: string;
  countries: any[];
  initialData?: ProviderRouteWithCountries;
  onSuccess?: () => void;
}

interface RouteFormModalProps {
  providerId: string;
  countries: any[];
  onSuccess?: () => void;
}

interface DeleteRouteDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  routeToDelete: ProviderRouteWithCountries | null;
  onDelete: () => void;
  isDeleting: boolean;
}

interface EditRouteDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  selectedRoute: ProviderRouteWithCountries | undefined;
  providerId: string;
  countries: any[];
  onSuccess?: () => void;
}

// Utils
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("RouteComponents");

/**
 * Form component for creating or editing a provider route
 */
export function RouteForm({
  providerId,
  countries,
  initialData,
  onSuccess,
}: RouteFormProps) {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const {
    searchText: originSearch,
    setSearchText: setOriginSearch,
    filteredCountries: filteredOriginCountries,
  } = useCountrySearch(countries);

  const {
    searchText: destinationSearch,
    setSearchText: setDestinationSearch,
    filteredCountries: filteredDestinationCountries,
  } = useCountrySearch(countries);

  const form = useForm<ProviderRouteFormValues>({
    resolver: zodResolver(ProviderRouteFormSchema),
    defaultValues: {
      origin_country_id: initialData?.origin_country_id || "",
      destination_country_id: initialData?.destination_country_id || "",
      is_active: initialData?.is_active ?? true,
      notes: initialData?.notes || "",
    },
  });

  async function onSubmit(data: ProviderRouteFormValues) {
    try {
      setLoading(true);
      if (initialData) {
        // Update route
        await updateProviderRouteAction(initialData.id, providerId, {
          ...data,
        });
        toast.success("Route updated successfully");
      } else {
        // Create new route
        await createProviderRouteAction({
          ...data,
          provider_id: providerId,
          notes: data.notes || null,
        });
        toast.success("Route created successfully");
      }
      router.refresh();
      onSuccess?.();
    } catch (error) {
      logger.error("Error submitting route form:", error instanceof Error ? error.message : String(error));
      toast.error("Something went wrong");
    } finally {
      setLoading(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="origin_country_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Origin Country</FormLabel>
              <FormControl>
                <Combobox
                  items={filteredOriginCountries}
                  value={
                    field.value
                      ? countries.find((c) => c.id === field.value)?.name || ""
                      : ""
                  }
                  onValueChange={(value) => {
                    setOriginSearch(value);
                  }}
                  onSelect={(country) => {
                    field.onChange(country.id);
                    setOriginSearch("");
                  }}
                  getValue={(country) => country.id}
                  getDisplayValue={(country) => country.name}
                  placeholder="Select origin country"
                  searchText="Search countries..."
                  emptyText="No countries found"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="destination_country_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Destination Country</FormLabel>
              <FormControl>
                <Combobox
                  items={filteredDestinationCountries}
                  value={
                    field.value
                      ? countries.find((c) => c.id === field.value)?.name || ""
                      : ""
                  }
                  onValueChange={(value) => {
                    setDestinationSearch(value);
                  }}
                  onSelect={(country) => {
                    field.onChange(country.id);
                    setDestinationSearch("");
                  }}
                  getValue={(country) => country.id}
                  getDisplayValue={(country) => country.name}
                  placeholder="Select destination country"
                  searchText="Search countries..."
                  emptyText="No countries found"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Enter notes (optional)"
                  value={field.value || ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="is_active"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>Active Route</FormLabel>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end">
          <Button type="submit" disabled={loading}>
            {initialData ? "Update" : "Create"} Route
          </Button>
        </div>
      </form>
    </Form>
  );
}

/**
 * Modal component for adding a new route
 */
export function RouteFormModal({
  providerId,
  countries,
  onSuccess,
}: RouteFormModalProps) {
  const [open, setOpen] = useState(false);

  const handleSuccess = () => {
    setOpen(false);
    onSuccess?.();
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Add Route
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Route</DialogTitle>
        </DialogHeader>
        <RouteForm
          providerId={providerId}
          countries={countries}
          onSuccess={handleSuccess}
        />
      </DialogContent>
    </Dialog>
  );
}

/**
 * Dialog component for editing a route
 */
export function EditRouteDialog({
  isOpen,
  onOpenChange,
  selectedRoute,
  providerId,
  countries,
  onSuccess,
}: EditRouteDialogProps) {
  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        onOpenChange(open);
      }}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Route</DialogTitle>
        </DialogHeader>
        {selectedRoute && (
          <RouteForm
            providerId={providerId}
            countries={countries}
            initialData={selectedRoute}
            onSuccess={onSuccess}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}

/**
 * Dialog component for confirming route deletion
 */
export function DeleteRouteDialog({
  isOpen,
  onOpenChange,
  routeToDelete,
  onDelete,
  isDeleting,
}: DeleteRouteDialogProps) {
  return (
    <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This will permanently delete this route.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={onDelete} disabled={isDeleting}>
            {isDeleting ? "Deleting..." : "Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

/**
 * Component for route actions (edit, delete)
 */
export function RouteActions({
  route,
  onEdit,
  onDelete,
}: {
  route: ProviderRouteWithCountries;
  onEdit: (route: ProviderRouteWithCountries) => void;
  onDelete: (route: ProviderRouteWithCountries) => void;
}) {
  return (
    <div className="flex items-center gap-2">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onSelect={() => onEdit(route)}>
            Edit
          </DropdownMenuItem>
          <DropdownMenuItem
            onSelect={() => onDelete(route)}
            className="text-red-600"
          >
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
