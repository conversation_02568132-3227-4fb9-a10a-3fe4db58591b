# Providers Module

## 1. Overview

The Providers module manages transport service providers in the Steelflow application. It allows users to create, view, edit, and delete transport providers, as well as manage their contacts and service routes.

## 2. Directory Structure

```
src/app/dashboard/providers/
├── _components/                  # Module-specific components
│   ├── dialogs/                  # Dialog components
│   │   └── add-provider-dialog.tsx
│   ├── forms/                    # Form components
│   │   └── provider-form.tsx
│   ├── tables/                   # Table components
│   │   └── providers-data-table.tsx
│   ├── providers-content.tsx     # Server component for data fetching
│   ├── providers-skeleton.tsx    # Loading skeleton
│   ├── provider-stats.tsx        # Provider statistics component
│   └── refresh-button.tsx        # Refresh button component
├── [id]/                         # Detail page
│   ├── _components/              # Detail page components
│   │   ├── provider-details-content.tsx
│   │   └── provider-details-skeleton.tsx
│   ├── ContactsPanel.tsx         # Contacts management panel
│   ├── RoutesPanel.tsx           # Routes management panel
│   └── page.tsx                  # Detail page component
├── page.tsx                      # Main listing page
└── providers_summary.md          # This documentation file
```

## 3. Components

### Page Components

- **ProvidersPage**: Main page component that displays a list of providers
- **ProviderPage**: Detail page component that displays and edits a specific provider

### Server Components

- **ProvidersContent**: Fetches provider data and renders the appropriate UI
- **ProviderDetailsContent**: Fetches provider detail data and renders the appropriate UI

### Client Components

- **ProvidersDataTable**: Displays providers in a table with client-side sorting, filtering, and pagination
- **ProviderForm**: Form for creating and editing providers
- **AddProviderDialog**: Dialog for adding a new provider
- **ContactsPanel**: Manages contacts for a specific provider
- **RoutesPanel**: Manages routes for a specific provider
- **ProviderStats**: Displays statistics about providers
- **RefreshButton**: Button to refresh the provider list

### Loading States

- **ProvidersSkeleton**: Loading skeleton for the providers list
- **ProviderDetailsSkeleton**: Loading skeleton for the provider detail page

## 4. Data Flow

### Listing Page

1. **ProvidersPage** component renders with Suspense boundary
2. **ProvidersContent** uses TanStack Query hooks to fetch data:
   - `useProviders` hook for provider data with equipment
   - `useQuery` for provider counts by status
3. Data is passed to client components:
   - **ProviderStats** for statistics
   - **ProvidersDataTable** for the table display with client-side operations

### Detail Page

1. **ProviderPage** component renders with Suspense boundary
2. **ProviderDetailsContent** fetches data using server actions:
   - `getProviderAction`
   - `getCountriesAction`
   - `getProviderRoutesAction`
3. Data is passed to client components:
   - **ProviderForm** for editing provider details
   - **ContactsPanel** for managing contacts
   - **RoutesPanel** for managing routes

### Form Submission

1. **ProviderForm** collects user input
2. Form is submitted to server actions:
   - `createProviderOnlyAction` for new providers
   - `updateProviderAction` for existing providers
3. On success, UI is updated and notifications are shown

## 5. Server Actions

- `getProvidersAction`: Get all providers
- `getProviderAction`: Get a specific provider by ID
- `getProvidersWithEquipmentAction`: Get providers with their equipment data
- `createProviderOnlyAction`: Create a new provider
- `updateProviderAction`: Update an existing provider
- `deleteProviderAction`: Delete a provider
- `getProviderCountsByStatusAction`: Get provider counts by status
- `getProviderRFQInvitationCountsAction`: Get RFQ invitation counts for providers

## 6. Service Layer

The Provider module uses the following service files:

- `provider.service.ts`: Core provider operations, statistics and metrics
- `provider-contact.service.ts`: Contact management
- `provider-routes.service.ts`: Route management
- `provider-equipments.service.ts`: Equipment capabilities

## 7. Schema

The Provider module uses the following schema files:

- `provider.schema.ts`: Provider entity schemas and types
- `provider-form.ts`: Form validation schemas

## 8. Features

- List providers with client-side filtering, sorting, and pagination
- View provider statistics (total, active, pending, inactive, verified)
- View RFQ invitation counts for each provider
- Export providers data to CSV
- Create new providers
- Edit existing providers
- Delete providers
- Manage provider contacts
- Configure service routes between countries
- Assign equipment capabilities to providers
