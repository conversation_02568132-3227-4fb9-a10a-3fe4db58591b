import { NextRequest } from "next/server";
import {
  fetchCargo,
  addCargo,
  modifyCargo,
  removeCargo,
} from "@/lib/services/cargo.service";
import {
  GetCargoTypesParamsSchema,
  CreateCargoTypeSchema,
  UpdateCargoTypeSchema,
  DeleteCargoTypeParamsSchema,
} from "@/lib/schemas";
import {
  validateWithSchema,
  handleApiError,
  formatApiResponse,
  formatPaginatedApiResponse,
} from "@/lib/utils/api";
import { createUnkeyHandler } from "@/lib/utils/unkey";

export const dynamic = 'force-dynamic';

// Define the GET handler with Unkey protection
export const GET = createUnkeyHandler(
  async (request: NextRequest): Promise<Response> => {
    try {
      const searchParams = request.nextUrl.searchParams;
      const params = {
        id: searchParams.get("id") ?? undefined,
        page: searchParams.get("page") ?? undefined,
        pageSize: searchParams.get("pageSize") ?? undefined,
      };

      // Validate params
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        GetCargoTypesParamsSchema,
        params,
      );

      if (!isValid) {
        return errorResponse;
      }

      // Use service to fetch data
      const result = await fetchCargo({
        id: validatedData!.id,
        page: validatedData!.page || 1,
        pageSize: validatedData!.pageSize || 10,
      });

      // If fetching a single item by ID
      if (validatedData!.id) {
        return formatApiResponse(result.data);
      }

      // If fetching a paginated list
      // Ensure result.data is an array before passing to formatPaginatedApiResponse
      const dataArray = Array.isArray(result.data) ? result.data : [];

      // Ensure all pagination parameters are numbers
      const page = validatedData!.page || 1;
      const pageSize = validatedData!.pageSize || 10;
      const totalItems = result.count || 0;
      const totalPages =
        result.pagination?.totalPages || Math.ceil(totalItems / pageSize);

      return formatPaginatedApiResponse(dataArray, {
        page,
        pageSize,
        totalItems,
        totalPages,
      });
    } catch (error) {
      return handleApiError(error, "Failed to fetch cargo");
    }
  },
  { requiredPermissions: ["read:cargo"] },
);

// Define the POST handler with Unkey protection
export const POST = createUnkeyHandler(
  async (request: NextRequest): Promise<Response> => {
    try {
      const body = await request.json();

      // Validate input
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        CreateCargoTypeSchema,
        body,
      );

      if (!isValid) {
        return errorResponse;
      }

      // Use service to create cargo
      const data = await addCargo(validatedData!);

      return formatApiResponse(data, 201);
    } catch (error) {
      return handleApiError(error, "Failed to create cargo");
    }
  },
  { requiredPermissions: ["write:cargo"] },
);

// Define the PUT handler with Unkey protection
export const PUT = createUnkeyHandler(
  async (request: NextRequest): Promise<Response> => {
    try {
      const body = await request.json();

      // Validate input
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        UpdateCargoTypeSchema,
        body,
      );

      if (!isValid) {
        return errorResponse;
      }

      // Use service to update cargo
      const data = await modifyCargo(validatedData!);

      return formatApiResponse(data);
    } catch (error) {
      return handleApiError(error, "Failed to update cargo");
    }
  },
  { requiredPermissions: ["write:cargo"] },
);

// Define the DELETE handler with Unkey protection
export const DELETE = createUnkeyHandler(
  async (request: NextRequest): Promise<Response> => {
    try {
      const searchParams = request.nextUrl.searchParams;
      const id = searchParams.get("id");

      if (!id) {
        return formatApiResponse({ error: "Cargo ID is required" }, 400);
      }

      // Validate ID
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        DeleteCargoTypeParamsSchema,
        { id },
      );

      if (!isValid) {
        return errorResponse;
      }

      // Use service to delete cargo
      const result = await removeCargo(validatedData!);

      return formatApiResponse(result);
    } catch (error) {
      return handleApiError(error, "Failed to delete cargo");
    }
  },
  { requiredPermissions: ["delete:cargo"] },
);
