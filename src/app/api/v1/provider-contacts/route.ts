import { NextRequest } from "next/server";
import {
  fetchProviderContacts,
  add<PERSON>roviderContact,
  modifyProviderContact,
  removeProviderContact,
} from "@/lib/services/provider-contact.service";
import {
  GetProviderContactsParamsSchema,
  CreateProviderContactSchema,
  UpdateProviderContactSchema,
  DeleteProviderContactParamsSchema,
} from "@/lib/schemas";
import {
  validateWithSchema,
  handleApiError,
  formatApiResponse,
} from "@/lib/utils/api";
import { createUnkeyHandler } from "@/lib/utils/unkey";

export const dynamic = 'force-dynamic';

// Define the GET handler with Unkey protection
export const GET = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const searchParams = request.nextUrl.searchParams;

      // Parse and prepare params
      const provider_id = searchParams.get("provider_id");

      if (!provider_id) {
        return formatApiResponse({ error: "Provider ID is required" }, 400);
      }

      // Validate params with schema
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        GetProviderContactsParamsSchema,
        { provider_id },
      );

      if (!isValid) {
        return errorResponse;
      }

      // Call service
      const contacts = await fetchProviderContacts(validatedData!);

      return formatApiResponse(contacts);
    } catch (error) {
      return handleApiError(error, "Failed to fetch provider contacts");
    }
  },
  { requiredPermissions: ["read:providers"] },
);

// Define the POST handler with Unkey protection
export const POST = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate with schema
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        CreateProviderContactSchema,
        body,
      );

      if (!isValid) {
        return errorResponse;
      }

      // Call service to create contact with is_primary explicitly set
      const newContact = await addProviderContact({
        ...validatedData!,
        is_primary: validatedData!.is_primary === true,
      });

      return formatApiResponse(newContact, 201);
    } catch (error) {
      return handleApiError(error, "Failed to create provider contact");
    }
  },
  { requiredPermissions: ["write:providers"] },
);

// Define the PUT handler with Unkey protection
export const PUT = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate with schema
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        UpdateProviderContactSchema,
        body,
      );

      if (!isValid) {
        return errorResponse;
      }

      // Call service to update contact
      const updatedContact = await modifyProviderContact(validatedData!);

      return formatApiResponse(updatedContact);
    } catch (error) {
      return handleApiError(error, "Failed to update provider contact");
    }
  },
  { requiredPermissions: ["write:providers"] },
);

// Define the DELETE handler with Unkey protection
export const DELETE = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const searchParams = request.nextUrl.searchParams;
      const id = searchParams.get("id");
      const provider_id = searchParams.get("provider_id");

      if (!id) {
        return formatApiResponse({ error: "Contact ID is required" }, 400);
      }

      // Validate with schema
      const params = { id, provider_id: provider_id || undefined };
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        DeleteProviderContactParamsSchema,
        params,
      );

      if (!isValid) {
        return errorResponse;
      }

      // Call service to delete contact
      const result = await removeProviderContact(validatedData!);

      return formatApiResponse(result);
    } catch (error) {
      return handleApiError(error, "Failed to delete provider contact");
    }
  },
  { requiredPermissions: ["delete:providers"] },
);
