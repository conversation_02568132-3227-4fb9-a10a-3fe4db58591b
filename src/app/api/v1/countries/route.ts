import { NextRequest } from "next/server";
import {
  fetchCountries,
  addCountry,
  updateCountry,
  deleteCountry,
} from "@/lib/services/country.service";
import {
  GetCountriesParamsSchema,
  CreateCountrySchema,
  UpdateCountrySchema,
  DeleteCountryParamsSchema,
} from "@/lib/schemas";
import {
  validateWithSchema,
  handleApiError,
  formatApiResponse,
  formatPaginatedApiResponse,
} from "@/lib/utils/api";
import { createUnkeyHandler } from "@/lib/utils/unkey";

export const dynamic = 'force-dynamic';

// Define the GET handler with Unkey protection
export const GET = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const searchParams = request.nextUrl.searchParams;

      // Check if we should fetch all countries (for dropdowns, etc.)
      const fetchAll = searchParams.get("fetchAll") === "true";

      const params = {
        id: searchParams.get("id") ?? undefined,
        code: searchParams.get("code") ?? undefined,
        // Only set pagination params if not fetching all countries
        page: !fetchAll && searchParams.has("page") ? Number(searchParams.get("page")) : undefined,
        pageSize: !fetchAll && searchParams.has("pageSize") ? Number(searchParams.get("pageSize")) : undefined,
      };

      // Validate params with schema
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        GetCountriesParamsSchema,
        params,
      );

      if (!isValid) {
        return errorResponse;
      }

      // Prepare fetch parameters
      const fetchParams = {
        ...validatedData!,
      };

      // Log the fetch parameters for debugging
      console.log("Fetching countries with params:", fetchParams);

      // Use service to fetch data
      const result = await fetchCountries(fetchParams);

      // If fetching a single country by ID or alpha2_code
      if (validatedData!.id || validatedData!.alpha2_code) {
        return formatApiResponse(result.data[0]);
      }

      // For paginated list request
      return formatPaginatedApiResponse(result.data, result.pagination!);
    } catch (error) {
      return handleApiError(error, "Failed to fetch countries");
    }
  },
  { requiredPermissions: ["read:countries"] },
);

// Define the POST handler with Unkey protection
export const POST = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate with schema
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        CreateCountrySchema,
        body,
      );

      if (!isValid) {
        return errorResponse;
      }

      // Use service to create country
      const data = await addCountry(validatedData!);

      return formatApiResponse(data, 201);
    } catch (error) {
      return handleApiError(error, "Failed to create country");
    }
  },
  { requiredPermissions: ["write:countries"] },
);

// Define the PUT handler with Unkey protection
export const PUT = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate with schema
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        UpdateCountrySchema,
        body,
      );

      if (!isValid) {
        return errorResponse;
      }

      // Use service to update country
      const data = await updateCountry(validatedData!);

      return formatApiResponse(data);
    } catch (error) {
      return handleApiError(error, "Failed to update country");
    }
  },
  { requiredPermissions: ["write:countries"] },
);

// Define the DELETE handler with Unkey protection
export const DELETE = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const searchParams = request.nextUrl.searchParams;
      const id = searchParams.get("id");

      if (!id) {
        return formatApiResponse({ error: "Country ID is required" }, 400);
      }

      // Validate with schema
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        DeleteCountryParamsSchema,
        { id },
      );

      if (!isValid) {
        return errorResponse;
      }

      // Use service to delete country
      await deleteCountry(validatedData!.id);

      return formatApiResponse({ message: "Country successfully deleted" });
    } catch (error) {
      return handleApiError(error, "Failed to delete country");
    }
  },
  { requiredPermissions: ["delete:countries"] },
);
