import { NextRequest, NextResponse } from "next/server";
import { fetchUsers, getUser } from "@/lib/services/user.service";
import { GetUsersParamsSchema } from "@/lib/schemas";
import { createUnkey<PERSON>and<PERSON> } from "@/lib/utils/unkey";
import { handleApiError } from "@/lib/utils/api";
import { createLogger } from "@/lib/utils/logger/logger";

export const dynamic = 'force-dynamic';

const logger = createLogger("UsersAPI");

// Define the GET handler with Unkey protection
export const GET = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      const params = {
        id: searchParams.get("id") ?? undefined,
      };

      // Validate params
      const validation = GetUsersParamsSchema.safeParse(params);
      if (!validation.success) {
        return NextResponse.json(
          {
            error: "Invalid query parameters",
            details: validation.error.flatten().fieldErrors,
          },
          { status: 400 },
        );
      }

      // If requesting a specific user by ID
      if (validation.data.id) {
        try {
          const user = await getUser(validation.data.id);
          return NextResponse.json(user, { status: 200 });
        } catch (error) {
          if (error instanceof Error && error.message.includes("not found")) {
            return NextResponse.json(
              { error: "User not found" },
              { status: 404 },
            );
          }
          throw error;
        }
      }

      // For list request
      const users = await fetchUsers(validation.data);
      return NextResponse.json(users, { status: 200 });
    } catch (error) {
      logger.error("Error fetching users:", error);
      return handleApiError(error, "Failed to fetch users");
    }
  },
  { requiredPermissions: ["read:users"] },
);
