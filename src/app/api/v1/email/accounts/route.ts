import { NextRequest, NextResponse } from "next/server";
import { isAdmin } from "@/lib/services/auth.service";
import { emailStorageService } from "@/lib/services/email/email-storage.service";
import { createLogger } from "@/lib/utils/logger/logger";

export const dynamic = 'force-dynamic';

// Create a logger instance for this module
const logger = createLogger("EmailAccountsAPI");

/**
 * GET /api/v1/email/accounts
 *
 * Get all watched email accounts
 */
export async function GET(request: NextRequest) {
  try {
    // Check if the user has admin role using the auth service
    const hasAdminRole = await isAdmin();

    if (!hasAdminRole) {
      logger.warn("Unauthorized access attempt to email accounts API");
      return NextResponse.json(
        { error: "Forbidden: Admin role required" },
        { status: 403 },
      );
    }

    // Get all accounts using the service
    const accounts = await emailStorageService.getAccounts();

    return NextResponse.json(accounts);
  } catch (error) {
    logger.error("Error in GET /api/v1/email/accounts:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

/**
 * POST /api/v1/email/accounts
 *
 * Create a new watched email account
 *
 * Note: This endpoint is not used directly. Instead, the OAuth flow is used
 * to create new accounts.
 */
export async function POST(request: NextRequest) {
  return NextResponse.json(
    { error: "Method not allowed. Use the OAuth flow to add accounts." },
    { status: 405 },
  );
}
