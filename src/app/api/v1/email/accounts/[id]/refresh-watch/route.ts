import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server/server";
import { emailPollingService } from "@/lib/services/email/email-polling.service";
import { emailStorageService } from "@/lib/services/email/email-storage.service";
import { createLogger } from "@/lib/utils/logger/logger";

export const dynamic = 'force-dynamic';

// Create a logger instance for this module
const logger = createLogger("EmailAccountPollingRoute");

/**
 * POST /api/v1/email/accounts/[id]/refresh-watch
 *
 * Manually trigger polling for a specific email account
 *
 * Note: This endpoint was previously used to refresh the Gmail API watch,
 * but now it triggers the polling process instead. The endpoint path is
 * kept the same for backward compatibility.
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // Get the params and Supabase client and user
    const { id } = await params;
    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if the user has admin role
    const { data: userRoles, error: rolesError } = await supabase
      .from("user_roles")
      .select("*, roles:role_id(name)")
      .eq("user_id", user.id);

    if (rolesError) {
      logger.error("Error fetching user roles:", rolesError);
      return NextResponse.json(
        { error: "Error checking permissions" },
        { status: 500 },
      );
    }

    const isAdmin = userRoles.some((role) => role.roles?.name === "admin");

    if (!isAdmin) {
      logger.warn(
        `User ${user.id} attempted to trigger polling without admin role`,
      );
      return NextResponse.json(
        { error: "Forbidden: Admin role required" },
        { status: 403 },
      );
    }

    // Get the account to check if it exists and get the refresh token
    logger.info(`Fetching account details for ${id}`);
    const account = await emailStorageService.getAccountById(id);

    if (!account) {
      logger.error(`Account not found: ${id}`);
      return NextResponse.json({ error: "Account not found" }, { status: 404 });
    }

    if (!account.refresh_token) {
      logger.error(`Account has no refresh token: ${id}`);
      return NextResponse.json(
        { error: "Account has no refresh token" },
        { status: 400 },
      );
    }

    // Parse request body to check for forceFullSync option
    let forceFullSync = false;
    try {
      const body = await request.json();
      forceFullSync = !!body.forceFullSync;
    } catch (e) {
      // If there's no body or it can't be parsed, default to regular polling
      logger.debug(
        "No request body or invalid JSON, defaulting to regular polling",
      );
    }

    // Trigger polling for this account
    if (forceFullSync) {
      logger.info(`Triggering full sync for account ${id}`);
    } else {
      logger.info(`Triggering polling for account ${id}`);
    }

    const result = await emailPollingService.pollAccount(
      id,
      account.refresh_token,
      forceFullSync,
    );

    if (!result.success) {
      logger.error(
        `${forceFullSync ? "Full sync" : "Polling"} failed for account ${id}:`,
        result.error,
      );
      return NextResponse.json(
        {
          error:
            result.error || `${forceFullSync ? "Full sync" : "Polling"} failed`,
        },
        { status: 500 },
      );
    }

    const message = forceFullSync
      ? `Full sync completed successfully`
      : `Polling completed successfully`;

    logger.info(
      `${forceFullSync ? "Full sync" : "Polling"} completed for account ${id}, processed ${result.processed} messages`,
    );
    return NextResponse.json({
      success: true,
      message,
      processed: result.processed,
      lastSyncAt: new Date().toISOString(),
      fullSync: forceFullSync,
    });
  } catch (error: any) {
    // Get the id from params for error logging
    const { id } = await params;
    logger.error(
      `Error in POST /api/v1/email/accounts/${id}/refresh-watch:`,
      error,
    );

    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 },
    );
  }
}
