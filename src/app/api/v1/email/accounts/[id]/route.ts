import { NextRequest, NextResponse } from "next/server";
import { isAdmin } from "@/lib/services/auth.service";
import { emailStorageService } from "@/lib/services/email/email-storage.service";
import { createLogger } from "@/lib/utils/logger/logger";

export const dynamic = 'force-dynamic';

// Create a logger instance for this module
const logger = createLogger("EmailAccountDetailAPI");

/**
 * GET /api/v1/email/accounts/[id]
 *
 * Get a specific watched email account
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // Get the params
    const { id } = await params;

    // Check if the user has admin role using the auth service
    const hasAdminRole = await isAdmin();

    if (!hasAdminRole) {
      logger.warn("Unauthorized access attempt to email account detail API");
      return NextResponse.json(
        { error: "Forbidden: Admin role required" },
        { status: 403 },
      );
    }

    try {
      // Get the account using the service
      const account = await emailStorageService.getAccountById(id);
      return NextResponse.json(account);
    } catch (error: any) {
      // Check if the error is due to account not found
      if (error.message.includes("not found")) {
        return NextResponse.json(
          { error: "Account not found" },
          { status: 404 },
        );
      }

      // Re-throw for general error handling
      throw error;
    }
  } catch (error) {
    // Get the id from params for error logging
    const { id } = await params;
    logger.error(`Error in GET /api/v1/email/accounts/${id}:`, error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

/**
 * DELETE /api/v1/email/accounts/[id]
 *
 * Delete a watched email account
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // Get the params
    const { id } = await params;

    // Check if the user has admin role using the auth service
    const hasAdminRole = await isAdmin();

    if (!hasAdminRole) {
      logger.warn("Unauthorized access attempt to delete email account API");
      return NextResponse.json(
        { error: "Forbidden: Admin role required" },
        { status: 403 },
      );
    }

    try {
      // Delete the account using the service
      const success = await emailStorageService.deleteAccount(id);
      return NextResponse.json({ success: true });
    } catch (error: any) {
      // Check if the error is due to account not found
      if (error.message.includes("not found")) {
        return NextResponse.json(
          { error: "Account not found" },
          { status: 404 },
        );
      }

      // Re-throw for general error handling
      throw error;
    }
  } catch (error) {
    // Get the id from params for error logging
    const { id } = await params;
    logger.error(`Error in DELETE /api/v1/email/accounts/${id}:`, error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
