import { NextRequest, NextResponse } from "next/server";
import { isAdmin } from "@/lib/services/auth.service";
import { emailStorageService } from "@/lib/services/email/email-storage.service";
import { createLogger } from "@/lib/utils/logger/logger";

export const dynamic = 'force-dynamic';

// Create a logger instance for this module
const logger = createLogger("EmailMessageDetailAPI");

/**
 * GET /api/v1/email/messages/[id]
 *
 * Get a specific email message
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // Get the params
    const { id } = await params;

    // Check if the user has admin role using the auth service
    const hasAdminRole = await isAdmin();

    if (!hasAdminRole) {
      logger.warn("Unauthorized access attempt to email message detail API");
      return NextResponse.json(
        { error: "Forbidden: Admin role required" },
        { status: 403 },
      );
    }

    try {
      // Use the service to fetch the message by ID
      const message = await emailStorageService.getMessageById(id);
      return NextResponse.json(message);
    } catch (error: any) {
      // Check if the error is due to message not found
      if (
        error.message.includes("not found") ||
        error.message.includes("No rows returned")
      ) {
        return NextResponse.json(
          { error: "Message not found" },
          { status: 404 },
        );
      }

      // Re-throw for general error handling
      throw error;
    }
  } catch (error) {
    // Get the id from params for error logging
    const { id } = await params;
    logger.error(`Error in GET /api/v1/email/messages/${id}:`, error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
