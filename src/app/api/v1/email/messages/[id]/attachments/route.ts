import { NextRequest, NextResponse } from "next/server";
import { isAdmin } from "@/lib/services/auth.service";
import { emailStorageService } from "@/lib/services/email/email-storage.service";
import { createLogger } from "@/lib/utils/logger/logger";

// Create a logger instance for this module
const logger = createLogger("EmailAttachmentsAPI");

export const dynamic = 'force-dynamic';
/**
 * GET /api/v1/email/messages/[id]/attachments
 *
 * Get attachments for a specific email message
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // Get the params
    const { id } = await params;

    // Check if the user has admin role using the auth service
    const hasAdminRole = await isAdmin();

    if (!hasAdminRole) {
      logger.warn("Unauthorized access attempt to email attachments API");
      return NextResponse.json(
        { error: "Forbidden: Admin role required" },
        { status: 403 },
      );
    }

    // Use the service to fetch attachments for the message
    const attachments = await emailStorageService.getAttachments(id);

    return NextResponse.json(attachments);
  } catch (error) {
    // Get the id from params for error logging
    const { id } = await params;
    logger.error(
      `Error in GET /api/v1/email/messages/${id}/attachments:`,
      error,
    );

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
