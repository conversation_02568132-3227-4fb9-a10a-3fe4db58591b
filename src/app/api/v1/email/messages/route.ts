import { NextRequest, NextResponse } from "next/server";
import { isAdmin } from "@/lib/services/auth.service";
import { emailStorageService } from "@/lib/services/email/email-storage.service";
import { createLogger } from "@/lib/utils/logger/logger";

export const dynamic = 'force-dynamic';

// Create a logger instance for this module
const logger = createLogger("EmailMessagesAPI");

/**
 * GET /api/v1/email/messages
 *
 * Get email messages with pagination and filtering
 */
export async function GET(request: NextRequest) {
  try {
    // Check if the user has admin role using the auth service
    const hasAdminRole = await isAdmin();

    if (!hasAdminRole) {
      logger.warn("Unauthorized access attempt to email messages API");
      return NextResponse.json(
        { error: "Forbidden: Admin role required" },
        { status: 403 },
      );
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const accountId = searchParams.get("accountId");
    const search = searchParams.get("search");

    // Use the service to fetch messages with pagination and filtering
    const result = await emailStorageService.getMessagesWithPagination({
      page,
      limit,
      accountId,
      search,
    });

    // Return the formatted response
    return NextResponse.json({
      messages: result.messages,
      page: result.page,
      limit: result.limit,
      total: result.total,
      totalPages: result.totalPages,
    });
  } catch (error) {
    logger.error("Error in GET /api/v1/email/messages:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
