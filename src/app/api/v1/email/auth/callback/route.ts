import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server/server";
import { emailPollingService } from "@/lib/services/email/email-polling.service";
import { emailStorageService } from "@/lib/services/email/email-storage.service";
import { createLogger } from "@/lib/utils/logger/logger";
import { OAuth2Client } from "google-auth-library";

export const dynamic = 'force-dynamic';

// Create a logger instance for this module
const logger = createLogger("EmailAuthCallbackRoute");

/**
 * OAuth callback endpoint for Gmail
 *
 * This endpoint receives the authorization code from Google after the user
 * authorizes our application. It exchanges the code for access and refresh
 * tokens, sets up a watch on the Gmail account, and saves the account to
 * the database.
 */
export async function GET(request: NextRequest) {
  try {
    // Get the authorization code from the query parameters
    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get("code");
    const error = searchParams.get("error");

    // Check for errors
    if (error) {
      logger.error("OAuth error:", error);
      const baseUrl = process.env.PUBLIC_APP_URL || request.nextUrl.origin;
      return NextResponse.redirect(new URL("/admin/email-accounts?error=auth_failed", baseUrl));
    }

    // Check for code
    if (!code) {
      logger.error("No authorization code provided");
      const baseUrl = process.env.PUBLIC_APP_URL || request.nextUrl.origin;
      return NextResponse.redirect(new URL("/admin/email-accounts?error=no_code", baseUrl));
    }

    // Get the Supabase client and user
    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      logger.warn("Unauthorized access to OAuth callback");
      const baseUrl = process.env.PUBLIC_APP_URL || request.nextUrl.origin;
      return NextResponse.redirect(new URL("/admin/email-accounts?error=unauthorized", baseUrl));
    }

    // Exchange the code for tokens
    logger.info("Exchanging authorization code for tokens");
    const tokens = await emailPollingService.getTokens(code);

    if (!tokens.access_token || !tokens.refresh_token) {
      logger.error("Failed to get tokens from Google OAuth");
      const baseUrl = process.env.PUBLIC_APP_URL || request.nextUrl.origin;
      return NextResponse.redirect(new URL("/admin/email-accounts?error=token_failed", baseUrl));
    }

    // Get the user's email address
    const oauth2Client = new OAuth2Client();
    oauth2Client.setCredentials({
      ...tokens,
      expiry_date: Date.now() + 10 * 365 * 24 * 60 * 60 * 1000, // 10 years expiry
    });

    const gmail = (await import("googleapis")).google.gmail({
      version: "v1",
      auth: oauth2Client,
    });
    const profile = await gmail.users.getProfile({ userId: "me" });

    if (!profile.data.emailAddress) {
      logger.error("Failed to get email address");
      const baseUrl = process.env.PUBLIC_APP_URL || request.nextUrl.origin;
      return NextResponse.redirect(
        new URL("/admin/email-accounts?error=profile_failed", baseUrl),
      );
    }

    const email = profile.data.emailAddress;
    const displayName = email.split("@")[0]; // Use the local part as display name

    // Calculate token expiry
    // If tokens.expiry_date is provided, it's likely milliseconds since epoch
    // Otherwise, set expiry to maximum (10 years)
    const expiryDate = tokens.expiry_date
      ? new Date(tokens.expiry_date)
      : new Date(Date.now() + 10 * 365 * 24 * 60 * 60 * 1000);

    logger.info(
      `Token expiry date set to: ${expiryDate.toISOString()}, original value: ${tokens.expiry_date}`,
    );

    // Create the account in the database
    logger.info(`Creating email account for ${email}`);
    const account = await emailStorageService.createAccount(
      email,
      displayName,
      tokens.access_token,
      tokens.refresh_token,
      expiryDate,
      user.id,
    );

    // Start initial polling in the background without waiting for it to complete
    logger.info(
      `Starting background initial polling for account ${account.id}`,
    );

    // Update account status to indicate sync is in progress
    await emailStorageService.updateAccount(account.id, {
      status: "syncing",
      sync_message: "Initial sync in progress",
    });

    // Start polling in the background without awaiting it
    // This allows us to redirect the user immediately
    emailPollingService
      .pollAccount(account.id, tokens.refresh_token)
      .then((result) => {
        logger.info(
          `Background polling completed for account ${account.id}, processed ${result.processed || 0} messages`,
        );
        // Update account status after polling completes
        return emailStorageService.updateAccount(account.id, {
          status: "active",
          sync_message: null,
        });
      })
      .catch((error) => {
        logger.error(
          `Error in background polling for account ${account.id}:`,
          error,
        );
        // Update account status to error if polling fails
        return emailStorageService.updateAccount(account.id, {
          status: "error",
          sync_error: error.message || "Unknown error during initial sync",
        });
      });

    // Redirect to the email accounts page
    logger.info("Email account setup completed successfully");
    const baseUrl = process.env.PUBLIC_APP_URL || request.nextUrl.origin;
    return NextResponse.redirect(
      new URL("/admin/email-accounts?success=true&syncing=true", baseUrl),
    );
  } catch (error) {
    logger.error("Error in Gmail auth callback:", error);

    const baseUrl = process.env.PUBLIC_APP_URL || request.nextUrl.origin;
    return NextResponse.redirect(new URL("/admin/email-accounts?error=server_error", baseUrl));
  }
}
