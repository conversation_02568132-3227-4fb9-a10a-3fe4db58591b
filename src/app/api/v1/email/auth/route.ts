import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server/server";
import { emailPollingService } from "@/lib/services/email/email-polling.service";
import { createLogger } from "@/lib/utils/logger/logger";

export const dynamic = 'force-dynamic';

// Create a logger instance for this module
const logger = createLogger("EmailAuthRoute");

/**
 * OAuth authorization endpoint for Gmail
 *
 * This endpoint generates an OAuth URL and redirects the user to Google's
 * authorization page. After authorization, Google will redirect the user
 * back to the callback endpoint.
 */
export async function GET(request: NextRequest) {
  try {
    // Get the Supabase client and user
    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if the user has admin role
    const { data: userRoles, error: rolesError } = await supabase
      .from("user_roles")
      .select("*, roles:role_id(name)")
      .eq("user_id", user.id);

    if (rolesError) {
      logger.error("Error fetching user roles:", rolesError);
      return NextResponse.json(
        { error: "Error checking permissions" },
        { status: 500 },
      );
    }

    const isAdmin = userRoles.some((role) => role.roles?.name === "admin");

    if (!isAdmin) {
      logger.warn(
        `User ${user.id} attempted to access OAuth flow without admin role`,
      );
      return NextResponse.json(
        { error: "Forbidden: Admin role required" },
        { status: 403 },
      );
    }

    // Generate the OAuth URL
    const authUrl = emailPollingService.generateAuthUrl();
    logger.info("Generated OAuth URL for Gmail authentication");

    // Redirect to the OAuth URL
    return NextResponse.redirect(authUrl);
  } catch (error) {
    logger.error("Error in Gmail auth endpoint:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
