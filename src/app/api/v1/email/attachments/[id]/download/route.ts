import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server/server";
import { google } from "googleapis";
import { createLogger } from "@/lib/utils/logger/logger";

export const dynamic = 'force-dynamic';

const logger = createLogger("EmailAttachmentDownloadAPI");

/**
 * GET /api/v1/email/attachments/[id]/download
 *
 * Download an attachment
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // Get the params
    const { id } = await params;

    // Get the Supabase client and user
    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if the user has admin role
    const { data: userRoles, error: rolesError } = await supabase
      .from("user_roles")
      .select("*, roles:role_id(name)")
      .eq("user_id", user.id);

    if (rolesError) {
      logger.error("Error fetching user roles:", rolesError);
      return NextResponse.json(
        { error: "Error checking permissions" },
        { status: 500 },
      );
    }

    const isAdmin = userRoles.some((role) => role.roles?.name === "admin");

    if (!isAdmin) {
      return NextResponse.json(
        { error: "Forbidden: Admin role required" },
        { status: 403 },
      );
    }

    // Get the attachment with the related email message
    const { data: attachment, error: attachmentError } = await supabase
      .from("email_attachments")
      .select("*, email_messages!inner(account_id, gmail_id)")
      .eq("id", id)
      .single();

    if (attachmentError || !attachment) {
      logger.error("Error fetching attachment:", attachmentError);
      return NextResponse.json(
        { error: "Attachment not found" },
        { status: 404 },
      );
    }

    // Check if the attachment content is already stored
    if (attachment.content) {
      // Return the stored content (convert from base64 string to Buffer)
      return new Response(Buffer.from(attachment.content, 'base64'), {
        headers: {
          "Content-Type": attachment.content_type || "application/octet-stream",
          "Content-Disposition": `attachment; filename="${encodeURIComponent(attachment.filename)}"`,
        },
      });
    }

    // If not stored, fetch from Gmail API
    // Get the account
    const { data: account, error: accountError } = await supabase
      .from("watched_email_accounts")
      .select("*")
      .eq("id", attachment.email_messages.account_id)
      .single();

    if (accountError || !account) {
      logger.error("Error fetching account:", accountError);
      return NextResponse.json({ error: "Account not found" }, { status: 404 });
    }

    // Initialize the OAuth2 client
    const oauth2Client = new google.auth.OAuth2(
      process.env.GMAIL_CLIENT_ID,
      process.env.GMAIL_CLIENT_SECRET,
      process.env.GMAIL_OAUTH_REDIRECT_URI,
    );

    // Set credentials with maximum expiry time
    oauth2Client.setCredentials({
      refresh_token: account.refresh_token,
      expiry_date: Date.now() + 10 * 365 * 24 * 60 * 60 * 1000, // 10 years expiry
    });

    // Initialize the Gmail API client
    const gmail = google.gmail({
      version: "v1",
      auth: oauth2Client as any,
    });

    // Fetch the attachment from Gmail
    const response = await gmail.users.messages.attachments.get({
      userId: "me",
      messageId: attachment.email_messages.gmail_id,
      id: attachment.gmail_attachment_id,
    });

    if (!response.data.data) {
      return NextResponse.json(
        { error: "Attachment data not found" },
        { status: 404 },
      );
    }

    // Decode the attachment data
    const attachmentData = Buffer.from(response.data.data, "base64");

    // Store the attachment content in the database
    // Convert Buffer to base64 string for storage
    await supabase
      .from("email_attachments")
      .update({ content: attachmentData.toString('base64') })
      .eq("id", id);

    // Return the attachment
    return new Response(attachmentData, {
      headers: {
        "Content-Type": attachment.content_type || "application/octet-stream",
        "Content-Disposition": `attachment; filename="${encodeURIComponent(attachment.filename)}"`,
      },
    });
  } catch (error: any) {
    // Get the id from params for error logging
    const { id } = await params;
    logger.error(
      `Error in GET /api/v1/email/attachments/${id}/download:`,
      error,
    );

    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 },
    );
  }
}
