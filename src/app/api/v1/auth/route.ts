import { NextRequest, NextResponse } from "next/server";
import {
  signInWithPassword,
  signOut,
  getCurrentUser,
  isAdmin,
} from "@/lib/services/auth.service";
import { formatApiResponse, handleApiError } from "@/lib/utils/api";
import { createLogger } from "@/lib/utils/logger/logger";

export const dynamic = 'force-dynamic';

// Create a logger instance for this module
const logger = createLogger("AuthAPI");

/**
 * POST handler for user login
 */
export async function POST(request: NextRequest) {
  try {
    // Get request body
    const body = await request.json();
    const { email, password } = body;

    // Validate required fields
    if (!email || !password) {
      return formatApiResponse(
        { error: "Email and password are required" },
        400,
      );
    }

    // Use the auth service to sign in
    const result = await signInWithPassword(email, password);

    if (!result.success) {
      return formatApiResponse(
        { error: result.error || "Authentication failed" },
        401,
      );
    }

    return formatApiResponse({ user: result.user });
  } catch (error) {
    return handleApiError(error, "Authentication failed");
  }
}

/**
 * DELETE handler for user logout
 */
export async function DELETE(request: NextRequest) {
  try {
    // Use the auth service to sign out
    const result = await signOut();

    if (!result.success) {
      return formatApiResponse({ error: result.error || "Logout failed" }, 500);
    }

    return formatApiResponse({ message: "Logged out successfully" });
  } catch (error) {
    return handleApiError(error, "Logout failed");
  }
}

/**
 * GET handler to check authentication status
 */
export async function GET(request: NextRequest) {
  try {
    // Use the auth service to get the current user
    const user = await getCurrentUser();

    if (!user) {
      return formatApiResponse({ authenticated: false });
    }

    // Check if the user has admin role
    const hasAdminRole = await isAdmin();

    return formatApiResponse({
      authenticated: true,
      user: {
        id: user.id,
        email: user.email,
        isAdmin: hasAdminRole,
      },
    });
  } catch (error) {
    return handleApiError(error, "Failed to check authentication status");
  }
}
