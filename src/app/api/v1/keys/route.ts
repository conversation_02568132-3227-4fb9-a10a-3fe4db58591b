import { NextRequest } from "next/server";
import { formatApiResponse, handleApiError } from "@/lib/utils/api";
import { getCurrentUser, isAdmin } from "@/lib/services/auth.service";
import {
  createUserApiKey,
  revokeUserApi<PERSON>ey,
} from "@/lib/services/api-key.service";
import { createLogger } from "@/lib/utils/logger/logger";

export const dynamic = 'force-dynamic';

const logger = createLogger("ApiKeysEndpoint");

/**
 * POST handler to create a new API key
 */
export async function POST(request: NextRequest) {
  try {
    // Get the current user
    const user = await getCurrentUser();

    if (!user) {
      return formatApiResponse({ error: "Unauthorized" }, 401);
    }

    // Check if the user has admin role
    const hasAdminRole = await isAdmin();

    if (!hasAdminRole) {
      logger.warn(
        `User ${user.id} attempted to create API key without admin role`,
      );
      return formatApiResponse(
        {
          error: "Unauthorized: Admin role required to manage API keys",
        },
        403,
      );
    }

    // Get request body
    const body = await request.json();
    const { name, permissions, metadata = {} } = body;

    if (!name) {
      return formatApiResponse({ error: "Key name is required" }, 400);
    }

    // Create API key using the service
    const keyData = await createUserApiKey(
      user.id,
      name,
      { ...metadata, email: user.email },
      permissions || [],
    );

    if (!keyData) {
      return formatApiResponse({ error: "Failed to create API key" }, 500);
    }

    logger.info(`API key created successfully by admin user ${user.id}`);
    return formatApiResponse(
      {
        message: "API key created successfully",
        key: keyData.key,
        keyId: keyData.keyId,
      },
      201,
    );
  } catch (error) {
    return handleApiError(error, "Failed to create API key");
  }
}

/**
 * DELETE handler to revoke an API key
 */
export async function DELETE(request: NextRequest) {
  try {
    // Get the current user
    const user = await getCurrentUser();

    if (!user) {
      return formatApiResponse({ error: "Unauthorized" }, 401);
    }

    // Check if the user has admin role
    const hasAdminRole = await isAdmin();

    if (!hasAdminRole) {
      logger.warn(
        `User ${user.id} attempted to revoke API key without admin role`,
      );
      return formatApiResponse(
        {
          error: "Unauthorized: Admin role required to manage API keys",
        },
        403,
      );
    }

    // Get key ID from query params
    const keyId = request.nextUrl.searchParams.get("keyId");

    if (!keyId) {
      return formatApiResponse({ error: "Key ID is required" }, 400);
    }

    // Revoke API key using the service
    const success = await revokeUserApiKey(keyId);

    if (!success) {
      return formatApiResponse({ error: "Failed to revoke API key" }, 500);
    }

    logger.info(
      `API key ${keyId} revoked successfully by admin user ${user.id}`,
    );
    return formatApiResponse({
      message: "API key revoked successfully",
    });
  } catch (error) {
    return handleApiError(error, "Failed to revoke API key");
  }
}
