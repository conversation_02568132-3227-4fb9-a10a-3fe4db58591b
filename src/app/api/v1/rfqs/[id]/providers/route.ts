import { NextRequest } from "next/server";
import {
  fetchRFQProviders,
  matchProvidersForRFQ,
  selectProvidersForRFQ,
  validateRFQProvider,
} from "@/lib/services/rfq.service";
import {
  validateWithSchema,
  handleApiError,
  formatApiResponse,
} from "@/lib/utils/api";
import { createUnkeyHandler } from "@/lib/utils/unkey";

export const dynamic = 'force-dynamic';

/**
 * GET handler to fetch providers for an RFQ
 */
export const GET = createUnkeyHandler(
  async (request: NextRequest, { params }: { params: { id: string } }) => {
    try {
      const rfqId = params.id;

      // Use service to fetch RFQ providers
      const providers = await fetchRFQProviders(rfqId);

      return formatApiResponse(providers);
    } catch (error) {
      return handleApiError(error, "Failed to fetch RFQ providers");
    }
  },
  { requiredPermissions: ["read:rfqs"] },
);

/**
 * POST handler to match providers for an RFQ
 */
export const POST = createUnkeyHandler(
  async (request: NextRequest, { params }: { params: { id: string } }) => {
    try {
      const rfqId = params.id;
      const action = request.nextUrl.searchParams.get("action");

      // If action is "match", run the matching algorithm
      if (action === "match") {
        const providers = await matchProvidersForRFQ(rfqId);
        return formatApiResponse(providers);
      }

      // Otherwise, handle provider selection
      const body = await request.json();

      // Use centralized validation function
      const providersData = Array.isArray(body) ? body : [body];

      // Validate each provider
      for (const provider of providersData) {
        const validationResult = await validateRFQProvider({
          ...provider,
          rfq_id: rfqId,
        });

        if (!validationResult.success) {
          return formatApiResponse(
            {
              error: validationResult.error?.message || "Validation failed",
              details: validationResult.error?.fieldErrors,
            },
            400,
          );
        }
      }

      // Use service to select providers
      const providers = await selectProvidersForRFQ(
        providersData.map((p) => ({ ...p, rfq_id: rfqId })),
      );

      return formatApiResponse(providers);
    } catch (error) {
      return handleApiError(error, "Failed to process RFQ providers");
    }
  },
  { requiredPermissions: ["write:rfqs"] },
);
