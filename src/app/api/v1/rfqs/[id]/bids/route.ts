import { NextRequest } from "next/server";
import {
  fetchRFQBids,
  submitRFQBid,
  updateRFQBid,
} from "@/lib/services/rfq.service";
import { RFQBidSchema, UpdateRFQBidSchema } from "@/lib/schemas";
import {
  validateWithSchema,
  handleApiError,
  formatApiResponse,
} from "@/lib/utils/api";
import { createUnkeyHandler } from "@/lib/utils/unkey";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("RFQBidsAPI");

export const dynamic = 'force-dynamic';

/**
 * GET handler to fetch bids for an RFQ
 */
export const GET = createUnkeyHandler(
  async (request: NextRequest, { params }: { params: { id: string } }) => {
    try {
      const rfqId = params.id;
      const searchParams = request.nextUrl.searchParams;
      const providerId = searchParams.get("provider_id");

      // Use service to fetch RFQ bids, passing providerId directly to filter at the database level
      const bids = await fetchRFQBids(rfqId, providerId || undefined);

      if (bids.length === 0 && providerId) {
        logger.warn(
          `No bids found for RFQ ${rfqId} and Provider ${providerId}`,
        );
      }

      return formatApiResponse(bids);
    } catch (error) {
      logger.error("Error in GET /api/rfqs/[id]/bids:", error);
      return handleApiError(error, "Failed to fetch RFQ bids");
    }
  },
  { requiredPermissions: ["read:rfqs"] },
);

/**
 * POST handler to submit a bid for an RFQ
 */
export const POST = createUnkeyHandler(
  async (request: NextRequest, { params }: { params: { id: string } }) => {
    try {
      const rfqId = params.id;
      const body = await request.json();

      // Validate with schema
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        RFQBidSchema,
        { ...body, rfq_id: rfqId },
      );

      if (!isValid) {
        return errorResponse;
      }

      // Use service to submit bid with required status, currency and is_ai_extracted
      const bid = await submitRFQBid({
        ...validatedData!,
        status: validatedData!.status || "received",
        currency: validatedData!.currency || "EUR",
        is_ai_extracted: validatedData!.is_ai_extracted === true || validatedData!.is_ai_extracted === "true" ? true : false,
        submitted_at: new Date().toISOString(),
      });

      return formatApiResponse(bid, 201);
    } catch (error) {
      return handleApiError(error, "Failed to submit RFQ bid");
    }
  },
  { requiredPermissions: ["write:rfqs"] },
);

/**
 * PUT handler to update an existing bid
 */
export const PUT = createUnkeyHandler(
  async (request: NextRequest, { params }: { params: { id: string } }) => {
    try {
      const body = await request.json();

      // Validate with schema
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        UpdateRFQBidSchema,
        body,
      );

      if (!isValid) {
        return errorResponse;
      }

      // Use service to update bid
      const bid = await updateRFQBid(validatedData!);

      return formatApiResponse(bid);
    } catch (error) {
      return handleApiError(error, "Failed to update RFQ bid");
    }
  },
  { requiredPermissions: ["write:rfqs"] },
);
