import { NextRequest } from "next/server";
import {
  fetchRFQs,
  createRFQ,
  updateRFQ,
  deleteRFQ,
  validateCreateRFQ,
  validateUpdateRFQ,
  validateRFQParams,
} from "@/lib/services/rfq.service";
import { getCurrentUser } from "@/lib/services/auth.service";
import {
  validateWithSchema,
  handleApiError,
  formatApiResponse,
  formatPaginatedApiResponse,
} from "@/lib/utils/api";
import { createUnkeyHandler } from "@/lib/utils/unkey";

export const dynamic = 'force-dynamic';

/**
 * GET handler to fetch RFQs with optional filtering and pagination
 */
export const GET = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const searchParams = request.nextUrl.searchParams;
      const params = {
        id: searchParams.get("id") ?? undefined,
        status: searchParams.get("status") ?? undefined,
        page: searchParams.get("page")
          ? parseInt(searchParams.get("page") || "1")
          : undefined,
        pageSize: searchParams.get("pageSize")
          ? parseInt(searchParams.get("pageSize") || "10")
          : undefined,
      };

      // Use centralized validation function
      const validationResult = await validateRFQParams(params);

      if (!validationResult.success) {
        return formatApiResponse(
          {
            error: validationResult.error?.message || "Validation failed",
            details: validationResult.error?.fieldErrors,
          },
          400,
        );
      }

      // Ensure required parameters are set with defaults and correct types
      const validParams = {
        page: Number(validationResult.data!.page || 1),
        pageSize: Number(validationResult.data!.pageSize || 10),
        id: validationResult.data!.id,
        status: validationResult.data!.status,
        origin_city: validationResult.data!.origin_city,
        destination_city: validationResult.data!.destination_city,
        search: validationResult.data!.search,
        created_after: validationResult.data!.created_after,
        created_before: validationResult.data!.created_before,
      };

      // Use service to fetch data
      const result = await fetchRFQs(validParams);

      // If fetching a single item by ID
      if (validationResult.data!.id) {
        return formatApiResponse(result.data[0]);
      }

      // For paginated list request
      return formatPaginatedApiResponse(result.data, result.pagination);
    } catch (error) {
      return handleApiError(error, "Failed to fetch RFQs");
    }
  },
  { requiredPermissions: ["read:rfqs"] },
);

/**
 * POST handler to create a new RFQ
 */
export const POST = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Use centralized validation function
      const validationResult = await validateCreateRFQ(body);

      if (!validationResult.success) {
        return formatApiResponse(
          {
            error: validationResult.error?.message || "Validation failed",
            details: validationResult.error?.fieldErrors,
          },
          400,
        );
      }

      // Get the current user using the auth service
      const user = await getCurrentUser();

      if (!user) {
        return formatApiResponse({ error: "Authentication required" }, 401);
      }

      // Use service to create RFQ
      const data = await createRFQ(
        {
          ...validationResult.data!,
          status: validationResult.data!.status ?? "draft",
          quantity: validationResult.data!.quantity ?? 1,
          equipment_quantity: validationResult.data!.equipment_quantity ?? 1,
        },
        user.id,
      );

      return formatApiResponse(data, 201);
    } catch (error) {
      return handleApiError(error, "Failed to create RFQ");
    }
  },
  { requiredPermissions: ["write:rfqs"] },
);

/**
 * PUT handler to update an existing RFQ
 */
export const PUT = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Use centralized validation function
      const validationResult = await validateUpdateRFQ(body);

      if (!validationResult.success) {
        return formatApiResponse(
          {
            error: validationResult.error?.message || "Validation failed",
            details: validationResult.error?.fieldErrors,
          },
          400,
        );
      }

      // Use service to update RFQ
      const data = await updateRFQ(validationResult.data!);

      return formatApiResponse(data);
    } catch (error) {
      return handleApiError(error, "Failed to update RFQ");
    }
  },
  { requiredPermissions: ["write:rfqs"] },
);

/**
 * DELETE handler to delete an RFQ
 */
export const DELETE = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const searchParams = request.nextUrl.searchParams;
      const id = searchParams.get("id");

      if (!id) {
        return formatApiResponse({ error: "RFQ ID is required" }, 400);
      }

      // Use service to delete RFQ
      await deleteRFQ(id);

      return formatApiResponse({ success: true });
    } catch (error) {
      return handleApiError(error, "Failed to delete RFQ");
    }
  },
  { requiredPermissions: ["delete:rfqs"] },
);
