import { NextRequest } from "next/server";
import {
  fetchEquipment,
  addEquipment,
  modifyEquipment,
  removeEquipment,
} from "@/lib/services/equipment.service";
import {
  GetEquipmentTypesParamsSchema,
  CreateEquipmentTypeSchema,
  UpdateEquipmentTypeSchema,
  DeleteEquipmentTypeParamsSchema,
} from "@/lib/schemas";
import {
  validateWithSchema,
  handleApiError,
  formatApiResponse,
  formatPaginatedApiResponse,
} from "@/lib/utils/api";
import { createUnkeyHandler } from "@/lib/utils/unkey";

export const dynamic = 'force-dynamic';

// Define the GET handler with Unkey protection
export const GET = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const searchParams = request.nextUrl.searchParams;
      const params = {
        id: searchParams.get("id") ?? undefined,
        page: searchParams.get("page") ?? undefined,
        pageSize: searchParams.get("pageSize") ?? undefined,
        category: searchParams.get("category") ?? undefined,
      };

      // Validate params with schema
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        GetEquipmentTypesParamsSchema,
        params,
      );

      if (!isValid) {
        return errorResponse;
      }

      // Ensure required parameters have default values
      const page = validatedData!.page || 1;
      const pageSize = validatedData!.pageSize || 10;

      // Use service to fetch data with validated parameters
      const result = await fetchEquipment({
        id: validatedData!.id,
        page,
        pageSize,
        category: validatedData!.category,
      });

      // If fetching a single item by ID
      if (validatedData!.id) {
        return formatApiResponse(result.data);
      }

      // If fetching a list
      const { data, count } = result;

      // Ensure data is an array before passing to formatPaginatedApiResponse
      const dataArray = Array.isArray(data) ? data : [];

      return formatPaginatedApiResponse(dataArray, {
        page,
        pageSize,
        totalItems: count || 0,
        totalPages: Math.ceil((count || 0) / pageSize),
      });
    } catch (error) {
      return handleApiError(error, "Failed to fetch equipment");
    }
  },
  { requiredPermissions: ["read:equipment"] },
);

// Define the POST handler with Unkey protection
export const POST = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate with schema
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        CreateEquipmentTypeSchema,
        body,
      );

      if (!isValid) {
        return errorResponse;
      }

      // Use service to create
      const newEquipment = await addEquipment(validatedData!);
      return formatApiResponse(newEquipment, 201);
    } catch (error) {
      return handleApiError(error, "Failed to create equipment");
    }
  },
  { requiredPermissions: ["write:equipment"] },
);

// Define the PUT handler with Unkey protection
export const PUT = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate with schema
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        UpdateEquipmentTypeSchema,
        body,
      );

      if (!isValid) {
        return errorResponse;
      }

      // Use service to update
      const updatedEquipment = await modifyEquipment(validatedData!);
      return formatApiResponse(updatedEquipment);
    } catch (error) {
      return handleApiError(error, "Failed to update equipment");
    }
  },
  { requiredPermissions: ["write:equipment"] },
);

// Define the DELETE handler with Unkey protection
export const DELETE = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const searchParams = request.nextUrl.searchParams;
      const params = {
        id: searchParams.get("id") ?? undefined,
      };

      // Validate with schema
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        DeleteEquipmentTypeParamsSchema,
        params,
      );

      if (!isValid) {
        return errorResponse;
      }

      // Use service to delete
      const result = await removeEquipment(validatedData!);
      return formatApiResponse(result);
    } catch (error) {
      return handleApiError(error, "Failed to delete equipment");
    }
  },
  { requiredPermissions: ["delete:equipment"] },
);
