import { NextRequest, NextResponse } from "next/server";
import {
  fetchProviderRoutes,
  addProviderRoute,
} from "@/lib/services/provider-routes.service";
import { createUnkeyHandler } from "@/lib/utils/unkey";
import { formatApiResponse, handleApiError } from "@/lib/utils/api";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("ProviderRoutesAPI");

export const dynamic = 'force-dynamic';

/**
 * GET handler to retrieve routes for a specific provider
 */
export const GET = createUnkeyHandler(
  async (
    request: NextRequest,
    { params }: { params: { providerId: string } },
  ) => {
    try {
      const providerId = params.providerId;

      // Fetch routes using the service with default pagination
      const routes = await fetchProviderRoutes({
        provider_id: providerId,
        page: 1,
        pageSize: 10
      });

      return formatApiResponse(routes);
    } catch (error) {
      logger.error("Error in GET /api/providers/[providerId]/routes:", error);
      return handleApiError(error, "Failed to fetch provider routes");
    }
  },
  { requiredPermissions: ["read:providers"] },
);

/**
 * POST handler to create a new route
 */
export const POST = createUnkeyHandler(
  async (
    request: NextRequest,
    { params }: { params: { providerId: string } },
  ) => {
    try {
      const providerId = params.providerId;
      const body = await request.json();

      // Import the correct schema from provider-route.schema.ts
      const { CreateProviderRouteSchema } = await import('@/lib/schemas/provider-route.schema');

      // Validate the request body
      const validationResult = CreateProviderRouteSchema.safeParse({
        ...body,
        provider_id: providerId,
      });

      if (!validationResult.success) {
        return NextResponse.json(
          {
            success: false,
            error: "Invalid request data",
            fieldErrors: validationResult.error.flatten().fieldErrors,
          },
          { status: 400 },
        );
      }

      // Create the route using the service
      const newRoute = await addProviderRoute(validationResult.data);

      return formatApiResponse(newRoute, 201);
    } catch (error) {
      logger.error("Error in POST /api/providers/[providerId]/routes:", error);
      return handleApiError(error, "Failed to create provider route");
    }
  },
  { requiredPermissions: ["write:providers"] },
);
