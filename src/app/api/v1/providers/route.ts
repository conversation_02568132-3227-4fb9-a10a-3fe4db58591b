import { NextRequest } from "next/server";
import {
  fetchProviders,
  addProvider,
  modifyProvider,
  removeProvider,
} from "@/lib/services/provider.service";
import {
  GetProvidersParamsSchema,
  CreateProviderSchema,
  UpdateProviderSchema,
  DeleteProviderParamsSchema,
} from "@/lib/schemas";
import { validateWithSchema } from "@/lib/utils/validation";
import {
  handleApiError,
  formatApiResponse,
  formatPaginatedApiResponse,
} from "@/lib/utils/api";
import { createUnkeyHandler } from "@/lib/utils/unkey";
import { createLogger } from "@/lib/utils/logger/logger";

export const dynamic = 'force-dynamic';

// Create a logger instance for this module
const logger = createLogger("ProviderAPI");

// Define the GET handler with Unkey protection
export const GET = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const searchParams = request.nextUrl.searchParams;

      // Parse and prepare params
      const params = {
        id: searchParams.get("id") ?? undefined,
        page: searchParams.get("page")
          ? parseInt(searchParams.get("page") || "1")
          : undefined,
        pageSize: searchParams.get("pageSize")
          ? parseInt(searchParams.get("pageSize") || "10")
          : undefined,
      };

      // Use validateWithSchema with the appropriate schema
      const validationResult = validateWithSchema(params, GetProvidersParamsSchema);

      if (!validationResult.success) {
        logger.error(
          "Provider params validation failed:",
          validationResult.error,
        );
        return formatApiResponse(
          {
            error: validationResult.error?.message || "Validation failed",
            details: validationResult.error?.fieldErrors,
          },
          400,
        );
      }

      // Ensure required parameters have default values and are numbers
      const page = Number(validationResult.data!.page || 1);
      const pageSize = Number(validationResult.data!.pageSize || 10);

      // Call service with validated parameters
      const result = await fetchProviders({
        id: validationResult.data!.id,
        page,
        pageSize,
      });

      // Format response appropriately
      if (validationResult.data!.id) {
        // Single provider response
        return formatApiResponse(result.data);
      } else {
        // Paginated list response
        // Ensure data is an array before passing to formatPaginatedApiResponse
        const dataArray = Array.isArray(result.data) ? result.data : [];

        return formatPaginatedApiResponse(dataArray, {
          page,
          pageSize,
          totalItems: result.count || 0,
          totalPages:
            result.pagination?.totalPages ||
            Math.ceil((result.count || 0) / pageSize),
        });
      }
    } catch (error) {
      return handleApiError(error, "Failed to fetch providers");
    }
  },
  { requiredPermissions: ["read:providers"] },
);

// Define the POST handler with Unkey protection
export const POST = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Use validateWithSchema with the appropriate schema
      const validationResult = validateWithSchema(body, CreateProviderSchema);

      if (!validationResult.success) {
        logger.error(
          "Provider creation validation failed:",
          validationResult.error,
        );
        return formatApiResponse(
          {
            error: validationResult.error?.message || "Validation failed",
            details: validationResult.error?.fieldErrors,
          },
          400,
        );
      }

      // Ensure status is set to a valid value before calling the service
      const providerData = {
        ...validationResult.data!,
        status: validationResult.data!.status || "active" as const
      };

      // Call service to create provider with validated data
      const newProvider = await addProvider(providerData);

      return formatApiResponse(newProvider, 201);
    } catch (error) {
      return handleApiError(error, "Failed to create provider");
    }
  },
  { requiredPermissions: ["write:providers"] },
);

// Define the PUT handler with Unkey protection
export const PUT = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Use validateWithSchema with the appropriate schema
      const validationResult = validateWithSchema(body, UpdateProviderSchema);

      if (!validationResult.success) {
        logger.error(
          "Provider update validation failed:",
          validationResult.error,
        );
        return formatApiResponse(
          {
            error: validationResult.error?.message || "Validation failed",
            details: validationResult.error?.fieldErrors,
          },
          400,
        );
      }

      // Call service to update provider
      const updatedProvider = await modifyProvider(validationResult.data!);

      return formatApiResponse(updatedProvider);
    } catch (error) {
      return handleApiError(error, "Failed to update provider");
    }
  },
  { requiredPermissions: ["write:providers"] },
);

// Define the DELETE handler with Unkey protection
export const DELETE = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const searchParams = request.nextUrl.searchParams;
      const id = searchParams.get("id");

      if (!id) {
        return formatApiResponse({ error: "Provider ID is required" }, 400);
      }

      // Use validateWithSchema with the appropriate schema
      const validationResult = validateWithSchema({ id }, DeleteProviderParamsSchema);

      if (!validationResult.success) {
        logger.error(
          "Provider deletion validation failed:",
          validationResult.error,
        );
        return formatApiResponse(
          {
            error: validationResult.error?.message || "Validation failed",
            details: validationResult.error?.fieldErrors,
          },
          400,
        );
      }

      // Call service to delete provider
      const result = await removeProvider(validationResult.data!);

      return formatApiResponse(result);
    } catch (error) {
      return handleApiError(error, "Failed to delete provider");
    }
  },
  { requiredPermissions: ["delete:providers"] },
);
