import { NextRequest, NextResponse } from "next/server";
import {
  updateProviderRoute,
  deleteProviderRoute,
} from "@/lib/services/provider-routes.service";
import { UpdateProviderRouteSchema } from "@/lib/schemas";
import { createUnkeyHandler } from "@/lib/utils/unkey";
import { formatApiResponse, handleApiError } from "@/lib/utils/api";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("ProviderRouteAPI");

export const dynamic = 'force-dynamic';

/**
 * PUT handler to update a route
 */
export const PUT = createUnkeyHandler(
  async (request: NextRequest, { params }: { params: { id: string } }) => {
    try {
      const id = params.id;
      const body = await request.json();

      // Validate the request body
      const validationResult = UpdateProviderRouteSchema.safeParse({
        ...body,
        id,
      });

      if (!validationResult.success) {
        return NextResponse.json(
          {
            success: false,
            error: "Invalid request data",
            fieldErrors: validationResult.error.flatten().fieldErrors,
          },
          { status: 400 },
        );
      }

      // Update the route using the service
      const updatedRoute = await updateProviderRoute(validationResult.data);

      return formatApiResponse(updatedRoute);
    } catch (error) {
      logger.error("Error in PUT /api/providers/routes/[id]:", error);
      return handleApiError(error, "Failed to update provider route");
    }
  },
  { requiredPermissions: ["write:providers"] },
);

/**
 * DELETE handler to delete a route
 */
export const DELETE = createUnkeyHandler(
  async (request: NextRequest, { params }: { params: { id: string } }) => {
    try {
      const id = params.id;

      // Delete the route using the service
      const result = await deleteProviderRoute({ id });

      return formatApiResponse(result);
    } catch (error) {
      logger.error("Error in DELETE /api/providers/routes/[id]:", error);
      return handleApiError(error, "Failed to delete provider route");
    }
  },
  { requiredPermissions: ["delete:providers"] },
);
