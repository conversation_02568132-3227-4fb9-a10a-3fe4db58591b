import { NextRequest } from "next/server";
import {
  fetchProviderEquipments as fetchProviderEquipment,
  addProviderEquipments as addProviderEquipment,
  removeProviderEquipment,
} from "@/lib/services/provider-equipments.service";
import {
  GetProviderEquipmentParamsSchema,
  CreateProviderEquipmentSchema,
  UpdateProviderEquipmentSchema,
  DeleteProviderEquipmentParamsSchema,
} from "@/lib/schemas";
import {
  validateWithSchema,
  handleApiError,
  formatApiResponse,
} from "@/lib/utils/api";
import { createUnkeyHandler } from "@/lib/utils/unkey";

export const dynamic = 'force-dynamic';

// Define the GET handler with Unkey protection
export const GET = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const searchParams = request.nextUrl.searchParams;

      // Parse and prepare params
      const provider_id = searchParams.get("provider_id");

      if (!provider_id) {
        return formatApiResponse({ error: "Provider ID is required" }, 400);
      }

      // Validate params with schema
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        GetProviderEquipmentParamsSchema,
        { provider_id },
      );

      if (!isValid) {
        return errorResponse;
      }

      // Call service
      const equipment = await fetchProviderEquipment(provider_id);

      return formatApiResponse(equipment);
    } catch (error) {
      return handleApiError(error, "Failed to fetch provider equipment");
    }
  },
  { requiredPermissions: ["read:providers"] },
);

// Define the POST handler with Unkey protection
export const POST = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate with schema
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        CreateProviderEquipmentSchema,
        body,
      );

      if (!isValid) {
        return errorResponse;
      }

      // Call service to add equipment
      const newEquipment = await addProviderEquipment(
        validatedData!.provider_id,
        [validatedData!.equipment_type_id],
      );

      return formatApiResponse(newEquipment, 201);
    } catch (error) {
      return handleApiError(error, "Failed to add provider equipment");
    }
  },
  { requiredPermissions: ["write:providers"] },
);

// Define the PUT handler with Unkey protection
export const PUT = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate with schema
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        UpdateProviderEquipmentSchema,
        body,
      );

      if (!isValid) {
        return errorResponse;
      }

      // For PUT requests, we need the provider_id which isn't in the schema
      // Get it from the query params
      const searchParams = request.nextUrl.searchParams;
      const providerId = searchParams.get("provider_id");

      if (!providerId) {
        return formatApiResponse({ error: "Provider ID is required" }, 400);
      }

      // Since we don't have an update function in provider-equipments.service.ts,
      // we'll need to remove and re-add the equipment
      // First, remove the equipment
      await removeProviderEquipment({
        id: validatedData!.id,
        provider_id: providerId,
      });

      // Then add it back with the updated data
      const updatedEquipment = await addProviderEquipment(providerId, [
        validatedData!.id,
      ]);

      return formatApiResponse(updatedEquipment);
    } catch (error) {
      return handleApiError(error, "Failed to update provider equipment");
    }
  },
  { requiredPermissions: ["write:providers"] },
);

// Define the DELETE handler with Unkey protection
export const DELETE = createUnkeyHandler(
  async (request: NextRequest) => {
    try {
      const searchParams = request.nextUrl.searchParams;
      const id = searchParams.get("id");
      const provider_id = searchParams.get("provider_id");

      if (!id) {
        return formatApiResponse({ error: "Equipment ID is required" }, 400);
      }

      if (!provider_id) {
        return formatApiResponse({ error: "Provider ID is required" }, 400);
      }

      // Validate with schema
      const params = { id, provider_id };
      const [isValid, validatedData, errorResponse] = validateWithSchema(
        DeleteProviderEquipmentParamsSchema,
        params,
      );

      if (!isValid) {
        return errorResponse;
      }

      // Call service to delete equipment
      const result = await removeProviderEquipment({
        id: validatedData!.id,
        provider_id: validatedData!.provider_id || "",
      });

      return formatApiResponse(result);
    } catch (error) {
      return handleApiError(error, "Failed to delete provider equipment");
    }
  },
  { requiredPermissions: ["delete:providers"] },
);
