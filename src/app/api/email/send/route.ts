import { NextRequest, NextResponse } from "next/server";
import { createLogger } from "@/lib/utils/logger/logger";
import {
  sendEmail,
  sendRFQEmail,
  EmailData,
  RFQEmailData,
} from "@/lib/services/email/email.service";
import {
  validateEmailData,
  validateRFQEmailData,
} from "@/lib/services/email/email-core.service";

export const dynamic = 'force-dynamic';

// Create a logger instance for this module
const logger = createLogger("EmailAPI");

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Handle RFQ emails
    if (body.rfqDetails) {
      // Validate RFQ email data
      const validationResult = validateRFQEmailData(body);

      if (!validationResult.success) {
        return NextResponse.json(
          {
            error: validationResult.error?.message || "Validation failed",
            details: validationResult.error?.fieldErrors,
          },
          { status: 400 },
        );
      }

      const result = await sendRFQEmail(validationResult.data!);
      return NextResponse.json({ success: true, result });
    }
    // Handle regular emails
    else {
      // Validate email data
      const validationResult = validateEmailData(body);

      if (!validationResult.success) {
        return NextResponse.json(
          {
            error: validationResult.error?.message || "Validation failed",
            details: validationResult.error?.fieldErrors,
          },
          { status: 400 },
        );
      }

      const result = await sendEmail(validationResult.data!);
      return NextResponse.json({ success: true, result });
    }
  } catch (error) {
    logger.error("Error in email API:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 },
    );
  }
}
