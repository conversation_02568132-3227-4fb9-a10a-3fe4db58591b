import { NextRequest, NextResponse } from "next/server";
import { emailPollingService } from "@/lib/services/email/email-polling.service";
import { createLogger } from "@/lib/utils/logger/logger";
import { isAdmin } from "@/lib/services/auth.service";

export const dynamic = 'force-dynamic';

const logger = createLogger("EmailPollingEndpoint");

/**
 * GET handler for the email polling endpoint
 *
 * This endpoint is designed to be called by Cloud Scheduler to trigger
 * the polling process for all active email accounts.
 *
 * Authentication is done using a secret key that should be set in the
 * EMAIL_POLLING_SECRET_KEY environment variable.
 */
export async function GET(request: NextRequest) {
  try {
    // Check for authorization
    const authHeader = request.headers.get("Authorization");
    const expectedAuth = `Bearer ${process.env.EMAIL_POLLING_SECRET_KEY}`;

    if (!authHeader || authHeader !== expectedAuth) {
      logger.warn("Unauthorized polling attempt");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check for force full sync parameter
    const url = new URL(request.url);
    const forceFullSync = url.searchParams.get("forceFullSync") === "true";

    if (forceFullSync) {
      logger.info("Starting scheduled full sync of all email accounts");
    } else {
      logger.info("Starting scheduled email polling process");
    }

    // Poll for new emails with the forceFullSync option
    const result = await emailPollingService.pollAllAccounts({ forceFullSync });

    if (!result.success) {
      logger.error("Polling failed:", result.error);
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    const message = forceFullSync
      ? `Full sync completed successfully`
      : `Polling completed successfully`;

    logger.info(
      `Scheduled ${forceFullSync ? "full sync" : "polling"} completed successfully, processed ${result.processed} messages`,
    );

    return NextResponse.json({
      success: true,
      message,
      processed: result.processed,
      fullSync: forceFullSync,
    });
  } catch (error) {
    logger.error("Error in polling endpoint:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

/**
 * POST handler for manually triggering the polling process
 *
 * This endpoint can be used by admin users to manually trigger
 * the polling process from the UI.
 */
export async function POST(request: NextRequest) {
  try {
    // Check if the user is an admin
    const hasAdminRole = await isAdmin();

    if (!hasAdminRole) {
      logger.warn("Unauthorized polling attempt from non-admin user");
      return NextResponse.json(
        { error: "Forbidden: Admin role required" },
        { status: 403 },
      );
    }

    // Parse request body to check for forceFullSync option
    let forceFullSync = false;
    try {
      const body = await request.json();
      forceFullSync = !!body.forceFullSync;
    } catch (e) {
      // If there's no body or it can't be parsed, default to regular polling
      logger.debug(
        "No request body or invalid JSON, defaulting to regular polling",
      );
    }

    if (forceFullSync) {
      logger.info("Starting manual full sync of all email accounts");
    } else {
      logger.info("Starting manual email polling process");
    }

    // Poll for new emails with the forceFullSync option
    const result = await emailPollingService.pollAllAccounts({ forceFullSync });

    if (!result.success) {
      logger.error("Manual polling failed:", result.error);
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    const message = forceFullSync
      ? `Full sync completed successfully`
      : `Polling completed successfully`;

    logger.info(
      `Manual ${forceFullSync ? "full sync" : "polling"} completed successfully, processed ${result.processed} messages`,
    );

    return NextResponse.json({
      success: true,
      message,
      processed: result.processed,
      fullSync: forceFullSync,
    });
  } catch (error) {
    logger.error("Error in manual polling endpoint:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
