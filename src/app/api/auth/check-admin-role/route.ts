import { NextResponse } from "next/server";
import { isAdminAction } from "@/lib/actions/auth.actions";
import { createLogger } from "@/lib/utils/logger/logger";

export const dynamic = 'force-dynamic';
// Create a logger instance for this module
const logger = createLogger("CheckAdminRoleAPI");

export async function GET() {
  try {
    // Use the auth action to check if the user has admin role
    const response = await isAdminAction();
    const hasAdminRole = response.success ? response.data : false;

    return NextResponse.json({ isAdmin: hasAdminRole });
  } catch (error) {
    logger.error("Error checking admin role:", error);
    return NextResponse.json({ isAdmin: false });
  }
}
