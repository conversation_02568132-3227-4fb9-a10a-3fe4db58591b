import { NextResponse } from "next/server";

import { exchangeCodeForSession } from "@/lib/services/auth.service";
import { createUser } from "@/lib/services/user.service";
import { AuthenticationError, handleApiError } from "@/lib/utils/errors";
import { createLogger } from "@/lib/utils/logger/logger";

/**
 * This route handles the OAuth callback after authentication with providers
 * like Google. It exchanges the code for a session and ensures the user
 * exists in the database before redirecting to the dashboard.
 */
export async function GET(request: Request) {
  // Create a logger for this route
  const logger = createLogger("AuthCallbackRoute");

  logger.info("Auth callback route handler called");
  const searchParams = new URL(request.url).searchParams;
  const code = searchParams.get("code");

  try {
    if (!code) {
      logger.error("No authentication code provided in callback");
      throw new AuthenticationError("No authentication code provided");
    }

    logger.info("Exchanging code for session");

    // Use the auth service to exchange the code for a session
    const session = await exchangeCodeForSession(code);

    if (!session?.user?.id) {
      logger.error("Failed to exchange code for session");
      throw new AuthenticationError("Failed to exchange code for session");
    }

    logger.info("Session obtained successfully, user ID:", session.user.id);

    if (!session.user.email) {
      logger.error("No email found in session");
      throw new AuthenticationError("No email found in session");
    }

    try {
      logger.info("Creating or getting existing user");
      // Create or get existing user
      await createUser({
        id: session.user.id,
        name: session.user.email, // Use email as name since the schema doesn't have an email field
      });
      logger.info("User created or retrieved successfully");
    } catch (createUserError) {
      logger.error("Failed to create user:", createUserError);
      throw new AuthenticationError(
        "Failed to create user: ",
        createUserError instanceof Error
          ? createUserError.message
          : String(createUserError),
      );
    }

    logger.info("Redirecting to dashboard");
    // Use PUBLIC_APP_URL environment variable if available, otherwise fallback to request origin
    const baseUrl = process.env.PUBLIC_APP_URL || new URL(request.url).origin;
    logger.info(`Using base URL for redirect: ${baseUrl}`);
    return NextResponse.redirect(new URL("/dashboard", baseUrl));
  } catch (error) {
    logger.error("Error in auth callback:", error);
    const { error: message, status } = handleApiError(error);
    return Response.json({ error: message }, { status });
  }
}
