"use client";

import * as React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils/tailwind";

export interface CalendarProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, "onSelect"> {
  month?: Date;
  selected?: Date | Date[];
  onSelect?: (date: Date | undefined) => void;
  disabled?: (date: Date) => boolean;
  onMonthChange?: (month: Date) => void;
  mode?: "single" | "range" | "multiple";
  numberOfMonths?: number;
  fromDate?: Date;
  toDate?: Date;
  defaultMonth?: Date;
  weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6;
  showOutsideDays?: boolean;
  initialFocus?: boolean;
}

function Calendar({
  className,
  month: propMonth,
  selected,
  onSelect,
  disabled,
  onMonthChange,
  mode = "single",
  numberOfMonths = 1,
  fromDate,
  toDate,
  defaultMonth = new Date(),
  weekStartsOn = 0,
  showOutsideDays = true,
  initialFocus,
  ...props
}: CalendarProps) {
  const [month, setMonth] = React.useState(propMonth || defaultMonth);
  const [selectedDates, setSelectedDates] = React.useState<Date[]>(
    Array.isArray(selected) ? selected : selected ? [selected] : [],
  );

  // Update internal state when props change
  React.useEffect(() => {
    if (propMonth) {
      setMonth(propMonth);
    }
  }, [propMonth]);

  React.useEffect(() => {
    if (selected) {
      setSelectedDates(Array.isArray(selected) ? selected : [selected]);
    }
  }, [selected]);

  // Navigation functions
  const handlePreviousMonth = () => {
    const newMonth = new Date(month);
    newMonth.setMonth(newMonth.getMonth() - 1);
    setMonth(newMonth);
    onMonthChange?.(newMonth);
  };

  const handleNextMonth = () => {
    const newMonth = new Date(month);
    newMonth.setMonth(newMonth.getMonth() + 1);
    setMonth(newMonth);
    onMonthChange?.(newMonth);
  };

  // Date selection handler
  const handleDateSelect = (date: Date) => {
    if (disabled?.(date)) return;

    let newSelectedDates: Date[] = [];

    if (mode === "single") {
      newSelectedDates = [date];
    } else if (mode === "multiple") {
      const isSelected = selectedDates.some(
        (d) => d.toDateString() === date.toDateString(),
      );
      newSelectedDates = isSelected
        ? selectedDates.filter((d) => d.toDateString() !== date.toDateString())
        : [...selectedDates, date];
    } else if (mode === "range") {
      if (selectedDates.length === 0 || selectedDates.length === 2) {
        newSelectedDates = [date];
      } else {
        const start = selectedDates[0];
        newSelectedDates = start < date ? [start, date] : [date, start];
      }
    }

    setSelectedDates(newSelectedDates);
    onSelect?.(
      mode === "single" ? newSelectedDates[0] : (newSelectedDates as any),
    );
  };

  // Helper functions
  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay();
  };

  const isDateSelected = (date: Date) => {
    return selectedDates.some((d) => d.toDateString() === date.toDateString());
  };

  const isInRange = (date: Date) => {
    if (mode !== "range" || selectedDates.length !== 2) return false;

    const [start, end] =
      selectedDates[0] < selectedDates[1]
        ? [selectedDates[0], selectedDates[1]]
        : [selectedDates[1], selectedDates[0]];

    return date > start && date < end;
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  };

  const isDateDisabled = (date: Date) => {
    if (disabled?.(date)) return true;
    if (fromDate && date < fromDate) return true;
    if (toDate && date > toDate) return true;
    return false;
  };

  // Generate calendar grid
  const renderCalendarGrid = () => {
    const year = month.getFullYear();
    const monthIndex = month.getMonth();
    const daysInMonth = getDaysInMonth(year, monthIndex);
    const firstDayOfMonth = getFirstDayOfMonth(year, monthIndex);

    const weekdays = ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"];
    const adjustedWeekdays = [
      ...weekdays.slice(weekStartsOn),
      ...weekdays.slice(0, weekStartsOn),
    ];

    // Calculate previous month days to show
    const prevMonthDays = [];
    if (showOutsideDays) {
      const prevMonth = new Date(year, monthIndex, 0);
      const prevMonthDaysCount = (firstDayOfMonth - weekStartsOn + 7) % 7;
      for (let i = prevMonthDaysCount - 1; i >= 0; i--) {
        const day = prevMonth.getDate() - i;
        const date = new Date(year, monthIndex - 1, day);
        prevMonthDays.push(date);
      }
    }

    // Current month days
    const currentMonthDays = [];
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, monthIndex, day);
      currentMonthDays.push(date);
    }

    // Next month days to fill the grid
    const nextMonthDays = [];
    if (showOutsideDays) {
      const totalDaysShown = prevMonthDays.length + currentMonthDays.length;
      const nextMonthDaysCount = (7 - (totalDaysShown % 7)) % 7;
      for (let day = 1; day <= nextMonthDaysCount; day++) {
        const date = new Date(year, monthIndex + 1, day);
        nextMonthDays.push(date);
      }
    }

    const allDays = [...prevMonthDays, ...currentMonthDays, ...nextMonthDays];
    const weeks = [];

    for (let i = 0; i < allDays.length; i += 7) {
      weeks.push(allDays.slice(i, i + 7));
    }

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-7 gap-1 text-center">
          {adjustedWeekdays.map((day) => (
            <div
              key={day}
              className="text-muted-foreground text-xs font-medium py-1"
            >
              {day}
            </div>
          ))}
        </div>
        <div className="space-y-1">
          {weeks.map((week, weekIndex) => (
            <div key={weekIndex} className="grid grid-cols-7 gap-1">
              {week.map((date, dayIndex) => {
                const isOutsideCurrentMonth = date.getMonth() !== monthIndex;

                const isRangeStart =
                  mode === "range" &&
                  selectedDates.length === 2 &&
                  date.toDateString() === selectedDates[0].toDateString();

                const isRangeEnd =
                  mode === "range" &&
                  selectedDates.length === 2 &&
                  date.toDateString() === selectedDates[1].toDateString();

                return (
                  <Button
                    key={`${weekIndex}-${dayIndex}`}
                    variant="ghost"
                    size="icon"
                    className={cn(
                      "h-9 w-9 p-0 font-normal aria-selected:opacity-100",
                      isOutsideCurrentMonth &&
                        "text-muted-foreground opacity-50",
                      isDateSelected(date) &&
                        "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground",
                      isToday(date) &&
                        !isDateSelected(date) &&
                        "bg-accent text-accent-foreground",
                      isInRange(date) && "bg-accent/50 text-accent-foreground",
                      isRangeStart && "rounded-l-md",
                      isRangeEnd && "rounded-r-md",
                      isDateDisabled(date) &&
                        "text-muted-foreground opacity-50 cursor-not-allowed",
                    )}
                    disabled={isDateDisabled(date)}
                    onClick={() => handleDateSelect(date)}
                    aria-selected={isDateSelected(date)}
                    data-day-outside={
                      isOutsideCurrentMonth ? "true" : undefined
                    }
                    data-day-range-start={isRangeStart ? "true" : undefined}
                    data-day-range-end={isRangeEnd ? "true" : undefined}
                    data-day-today={isToday(date) ? "true" : undefined}
                  >
                    {date.getDate()}
                  </Button>
                );
              })}
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className={cn("p-3", className)} {...props} data-slot="calendar">
      <div className="flex flex-col space-y-4">
        <div className="flex justify-center pt-1 relative items-center">
          <div className="text-sm font-medium">
            {month.toLocaleDateString("en-US", {
              month: "long",
              year: "numeric",
            })}
          </div>
          <div className="space-x-1 flex items-center absolute right-1">
            <Button
              variant="outline"
              size="icon"
              className="h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"
              onClick={handlePreviousMonth}
              disabled={
                fromDate &&
                month.getMonth() === fromDate.getMonth() &&
                month.getFullYear() === fromDate.getFullYear()
              }
            >
              <ChevronLeft className="h-4 w-4" />
              <span className="sr-only">Previous month</span>
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"
              onClick={handleNextMonth}
              disabled={
                toDate &&
                month.getMonth() === toDate.getMonth() &&
                month.getFullYear() === toDate.getFullYear()
              }
            >
              <ChevronRight className="h-4 w-4" />
              <span className="sr-only">Next month</span>
            </Button>
          </div>
        </div>
        {renderCalendarGrid()}
      </div>
    </div>
  );
}

Calendar.displayName = "Calendar";

export { Calendar };
