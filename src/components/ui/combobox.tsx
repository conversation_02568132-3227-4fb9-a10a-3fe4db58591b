"use client";

import * as React from "react";
import { Check, ChevronsUpDown } from "lucide-react";

import { cn } from "@/lib/utils/tailwind";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("Combobox");

export interface ComboboxProps<T> {
  value?: string;
  onValueChange?: (value: string) => void;
  onSelect?: (item: T) => void;
  placeholder?: string;
  items: T[];
  getValue: (item: T) => string;
  getDisplayValue: (item: T) => string;
  emptyText?: string;
  searchText?: string;
  loading?: boolean;
  error?: string;
  disabled?: boolean;
}

export function Combobox<T>({
  value,
  onValueChange,
  onSelect,
  placeholder = "Select an option",
  items,
  getValue,
  getDisplayValue,
  emptyText = "No results found.",
  searchText = "Search...",
  loading,
  error,
  disabled,
}: ComboboxProps<T>) {
  const [open, setOpen] = React.useState(false);
  const [search, setSearch] = React.useState("");

  React.useEffect(() => {
    if (!open) {
      setSearch("");
    }
  }, [open]);

  // Debug logging
  React.useEffect(() => {
    if (open) {
      logger.info("Combobox opened with items:", items.length);
    }
  }, [open, items]);

  // Additional debug logging for search
  React.useEffect(() => {
    if (open && search) {
      logger.info(
        `Combobox search: "${search}" with ${items.length} items available`,
      );
    }
  }, [open, search, items.length]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between",
            error && "border-destructive",
            disabled && "opacity-50 cursor-not-allowed",
          )}
          disabled={disabled}
        >
          {value || placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
        <Command shouldFilter={false}>
          <CommandInput
            placeholder={searchText}
            value={search}
            onValueChange={(value) => {
              setSearch(value);
              onValueChange?.(value);
            }}
          />
          <CommandEmpty>{loading ? "Loading..." : emptyText}</CommandEmpty>
          <CommandGroup>
            {items.map((item, index) => (
              <CommandItem
                key={getValue(item) || index}
                value={getValue(item)}
                onSelect={() => {
                  onValueChange?.(getValue(item));
                  onSelect?.(item);
                  setOpen(false);
                }}
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    value === getValue(item) ? "opacity-100" : "opacity-0",
                  )}
                />
                {getDisplayValue(item)}
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
