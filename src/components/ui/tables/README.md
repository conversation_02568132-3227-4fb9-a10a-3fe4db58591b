# Reusable Table Components

This directory contains the standardized, reusable table components for SteelFlow that follow our established data table standards and design system.

## Components Overview

### Core Components

- **`DataTable`** - Main table component with client-side operations
- **`ServerFilteredDataTable`** - Table component for server-side filtering scenarios
- **`DataTableToolbar`** - Integrated toolbar with search and filtering
- **`DataTableColumnHeader`** - Sortable column headers with actions
- **`DataTablePagination`** - Pagination controls with loading states
- **`DataTableViewOptions`** - Column visibility management
- **`DataTableFacetedFilter`** - Multi-select filter dropdowns

## Standards Compliance

### ✅ Architecture Compliance
- **Service-Action Pattern**: Components are UI-only, business logic handled in services/actions
- **Proper Separation**: No direct database calls or business logic in components
- **Type Safety**: Full TypeScript support with proper type definitions

### ✅ Data Table Standards
- **TanStack Query Integration**: Designed for use with TanStack Query data fetching
- **TanStack Table**: Uses TanStack Table for all table logic and state management
- **Client-Side Operations**: Sorting, filtering, pagination on full dataset
- **Column Visibility**: Show/hide columns via dropdown
- **Row Selection**: Multi-select with checkboxes
- **Global Search**: Single input across all filterable columns

### ✅ Schema Integration
- **Central Imports**: All consuming components use `@/lib/schemas` imports
- **Type Safety**: Proper TypeScript integration with schema types
- **Validation**: Runtime validation support through Zod schemas

### ✅ Design System Alignment
- **Supply Chain Theme**: Professional styling with logistics industry colors
- **Information Dense**: Reduced vertical spacing, compact layouts
- **Consistent Styling**: Follows SteelFlow design tokens and patterns
- **Responsive Design**: Mobile-friendly with appropriate breakpoints

## Usage Examples

### Basic Data Table

```tsx
import { DataTable } from "@/components/ui/tables";
import { useQuery } from "@tanstack/react-query";

function MyDataTable() {
  const { data = [], isLoading } = useQuery({
    queryKey: ['my-data'],
    queryFn: () => myService.getData()
  });

  return (
    <DataTable
      columns={columns}
      data={data}
      filterConfig={{
        search: {
          placeholder: "Search across all data...",
          columnId: "name"
        },
        facets: [
          {
            columnId: "status",
            title: "Status",
            options: statusOptions
          }
        ]
      }}
      enableRowSelection={true}
      initialPageSize={20}
    />
  );
}
```

### Server-Side Filtered Table

```tsx
import { ServerFilteredDataTable } from "@/components/ui/tables";

function MyServerTable() {
  return (
    <ServerFilteredDataTable
      columns={columns}
      data={data}
      filterConfig={{
        search: {
          placeholder: "Search...",
          onSearchChange: handleSearchChange
        },
        facets: [
          {
            columnId: "status",
            title: "Status",
            options: statusOptions,
            onValueChange: handleStatusFilter
          }
        ]
      }}
      paginationConfig={{
        totalItems: totalCount,
        manualPagination: true,
        onPaginationChange: handlePaginationChange
      }}
    />
  );
}
```

## Column Definition Best Practices

### Using DataTableColumnHeader

```tsx
import { DataTableColumnHeader } from "@/components/ui/tables/column-header";

const columns: ColumnDef<MyType>[] = [
  {
    accessorKey: "name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
    meta: {
      displayName: "Company Name" // Used in column visibility dropdown
    }
  }
];
```

### Row Selection Column

```tsx
{
  id: "select",
  header: ({ table }) => (
    <Checkbox
      checked={table.getIsAllPageRowsSelected()}
      onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
      aria-label="Select all"
    />
  ),
  cell: ({ row }) => (
    <Checkbox
      checked={row.getIsSelected()}
      onCheckedChange={(value) => row.toggleSelected(!!value)}
      aria-label="Select row"
    />
  ),
  enableSorting: false,
  enableHiding: false,
  size: 40,
}
```

## Design Features

### Supply Chain/Logistics Theme
- Professional blue/gray color palette
- Subtle shadows and borders
- Clean, modern typography
- Industry-appropriate iconography

### Information Dense Layouts
- Reduced vertical spacing (`py-3` instead of `py-4`)
- Compact pagination controls
- Efficient use of horizontal space
- Minimal visual separators

### Enhanced UX
- Smooth transitions and hover states
- Loading indicators for async operations
- Clear visual feedback for interactions
- Accessible keyboard navigation

## Migration from Custom Tables

When migrating existing custom table implementations:

1. **Replace custom table logic** with `DataTable` component
2. **Move column definitions** to use `DataTableColumnHeader`
3. **Update filter implementations** to use `filterConfig` prop
4. **Ensure TanStack Query integration** for data fetching
5. **Test all functionality** maintains 100% compatibility

## Performance Considerations

- **Client-side operations** work best with datasets under 10,000 rows
- **Server-side filtering** recommended for larger datasets
- **Memoize column definitions** to prevent unnecessary re-renders
- **Use proper keys** for stable row identification

## Accessibility

All components follow WCAG AA guidelines:
- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility
- High contrast color ratios
- Focus management

## Future Enhancements

Planned improvements:
- Export functionality integration
- Advanced filtering options
- Bulk action support
- Column resizing capabilities
- Virtual scrolling for large datasets
