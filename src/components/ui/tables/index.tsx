"use client";

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { DataTablePagination } from "./pagination";
import { DataTableToolbar, type DataTableFilterConfig } from "./toolbar";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  /** Optional: Configuration for filtering capabilities */
  filterConfig?: {
    search?: {
      placeholder?: string;
      columnId?: Extract<keyof TData, string>;
    };
    facets?: DataTableFilterConfig<TData>[];
  };
  /** Optional: Enable row selection */
  enableRowSelection?: boolean;
  /** Optional: Initial page size */
  initialPageSize?: number;
  /** Optional: Pagination configuration */
  paginationConfig?: {
    totalItems?: number;
    manualPagination?: boolean;
    initialPage?: number;
    initialPageSize?: number;
    onPaginationChange?: (page: number, pageSize: number) => void;
  };
  /** Optional: Loading state for pagination */
  isPaginationLoading?: boolean;
  /** Optional: Callback for row selection changes */
  onRowSelectionChange?: (selectedRowIndices: Record<string, boolean>) => void;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  filterConfig,
  enableRowSelection = false,
  initialPageSize = 10,
  paginationConfig,
  isPaginationLoading = false,
  onRowSelectionChange,
}: DataTableProps<TData, TValue>) {
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [sorting, setSorting] = React.useState<SortingState>([]);

  // Add pagination state for client-side pagination
  const [pagination, setPagination] = React.useState({
    pageIndex: paginationConfig?.initialPage ?? 0,
    pageSize: paginationConfig?.initialPageSize ?? initialPageSize,
  });

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination,
    },
    enableRowSelection: enableRowSelection,
    onRowSelectionChange: (updater) => {
      const newSelection = typeof updater === 'function' ? updater(rowSelection) : updater;
      setRowSelection(newSelection);
      onRowSelectionChange?.(newSelection);
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    manualPagination: paginationConfig?.manualPagination ?? false,
    pageCount: paginationConfig?.totalItems
      ? Math.ceil(
          paginationConfig.totalItems /
            (paginationConfig.initialPageSize ?? initialPageSize),
        )
      : undefined,
  });

  return (
    <div className="space-y-4">
      {/* Integrate toolbar for filtering and column visibility */}
      {filterConfig && (
        <DataTableToolbar table={table} filterConfig={filterConfig} />
      )}

      <div className="rounded-md border bg-card shadow-sm">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="border-b border-border/50">
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} className="font-semibold text-foreground/90">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  className="border-b border-border/30 hover:bg-muted/40 transition-colors"
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="py-3">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center text-muted-foreground"
                >
                  No results found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination
        table={table}
        totalItems={paginationConfig?.totalItems}
        onPaginationChange={paginationConfig?.onPaginationChange}
        isLoading={isPaginationLoading}
      />
    </div>
  );
}
