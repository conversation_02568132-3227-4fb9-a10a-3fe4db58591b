"use client";

import type { Table } from "@tanstack/react-table";
import { Cross2Icon } from "@radix-ui/react-icons";
import { Search } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DataTableViewOptions } from "./view-options";
import { DataTableFacetedFilter } from "./faceted-filter";

export interface FacetFilterOption {
  label: string;
  value: string;
  icon?: React.ComponentType<{ className?: string }>;
}

export interface DataTableFilterConfig<TData> {
  columnId: Extract<keyof TData, string>;
  title: string;
  options: FacetFilterOption[];
}

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  filterConfig?: {
    search?: {
      placeholder?: string;
      columnId?: Extract<keyof TData, string>;
    };
    facets?: DataTableFilterConfig<TData>[];
  };
}

export function DataTableToolbar<TData>({
  table,
  filterConfig,
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0;

  const searchColumn = filterConfig?.search?.columnId
    ? table.getColumn(filterConfig.search.columnId)
    : null;

  return (
    <div className="flex items-center justify-between gap-3 p-1">
      <div className="flex flex-grow items-center space-x-3">
        {searchColumn && (
          <div className="relative w-full max-w-sm">
            <Search className="absolute left-3 top-1/2 h-4 w-4 text-muted-foreground pointer-events-none -translate-y-1/2" />
            <Input
              placeholder={filterConfig?.search?.placeholder ?? "Search across all data..."}
              value={(searchColumn.getFilterValue() as string) ?? ""}
              onChange={(event) =>
                searchColumn.setFilterValue(event.target.value)
              }
              className="h-9 w-full pl-9 pr-4 bg-background border-border/60 focus-visible:ring-2 focus-visible:ring-primary/20 focus-visible:border-primary transition-all duration-200"
              type="search"
              autoComplete="off"
            />
          </div>
        )}
        <div className="flex items-center space-x-2">
          {filterConfig?.facets?.map((facet) => {
            const column = table.getColumn(facet.columnId);
            if (!column) {
              return null;
            }
            return (
              <DataTableFacetedFilter
                key={facet.columnId}
                column={column}
                title={facet.title}
                options={facet.options}
              />
            );
          })}
        </div>
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={() => table.resetColumnFilters()}
            className="h-8 px-3 text-muted-foreground hover:text-foreground border border-dashed border-border/60 hover:border-border transition-colors"
          >
            Reset filters
            <Cross2Icon className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  );
}
