import * as React from "react";
import type { Column } from "@tanstack/react-table";
import { CheckIcon, PlusCircledIcon } from "@radix-ui/react-icons";

import { cn } from "@/lib/utils/tailwind";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import type { FacetFilterOption } from "./toolbar";

interface DataTableFacetedFilterProps<TData, TValue> {
  column?: Column<TData, TValue>;
  title?: string;
  options: FacetFilterOption[];
  customValue?: string;
  customOnChange?: (value: string | null) => void;
}

export function DataTableFacetedFilter<TData, TValue>({
  column,
  title,
  options,
  customValue,
  customOnChange,
}: DataTableFacetedFilterProps<TData, TValue>) {
  // For server-side filtering, column might not be a real column
  // Using useMemo to avoid dependency warning in the sortedOptions useMemo below
  const facets = React.useMemo(() => {
    return column && typeof column.getFacetedUniqueValues === 'function'
      ? column.getFacetedUniqueValues()
      : new Map();
  }, [column]);

  const selectedValues = new Set<string>();

  // If we have a custom value, use it
  if (customValue) {
    selectedValues.add(customValue);
  }
  // Otherwise use the column filter value if available
  else if (column && typeof column.getFilterValue === 'function' && column.getFilterValue()) {
    const filterValue = column.getFilterValue();
    if (Array.isArray(filterValue)) {
      filterValue.forEach(value => selectedValues.add(value));
    } else if (typeof filterValue === 'string') {
      selectedValues.add(filterValue);
    }
  }

  // Enhance options with counts and sort them
  const sortedOptions = React.useMemo(() => {
    return (
      options
        .map((option) => ({
          ...option,
          // Get count from facets map, default to 0
          count: facets?.get(option.value) ?? 0,
        }))
        // Sort by count descending, then alphabetically by label
        .sort((a, b) => {
          if (b.count !== a.count) {
            return b.count - a.count;
          }
          return a.label.localeCompare(b.label);
        })
    );
  }, [options, facets]);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="h-8 border-dashed border-border/60 hover:bg-muted/60 text-foreground/80 hover:text-foreground transition-colors"
        >
          <PlusCircledIcon className="mr-2 h-4 w-4" />
          {title}
          {selectedValues?.size > 0 && (
            <>
              <Separator orientation="vertical" className="mx-2 h-4" />
              <Badge
                variant="secondary"
                className="rounded-sm px-1.5 py-0.5 font-medium text-xs lg:hidden bg-primary/10 text-primary border-primary/20"
              >
                {selectedValues.size}
              </Badge>
              <div className="hidden space-x-1 lg:flex">
                {selectedValues.size > 2 ? (
                  <Badge
                    variant="secondary"
                    className="rounded-sm px-1.5 py-0.5 font-medium text-xs bg-primary/10 text-primary border-primary/20"
                  >
                    {selectedValues.size} selected
                  </Badge>
                ) : (
                  options
                    .filter((option) => selectedValues.has(option.value))
                    .map((option) => (
                      <Badge
                        variant="secondary"
                        key={option.value}
                        className="rounded-sm px-1.5 py-0.5 font-medium text-xs bg-primary/10 text-primary border-primary/20"
                      >
                        {option.label}
                      </Badge>
                    ))
                )}
              </div>
            </>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[220px] p-0" align="start">
        <Command>
          <CommandInput
            placeholder={`Search ${title?.toLowerCase()}...`}
            className="h-9"
          />
          <CommandList>
            <CommandEmpty className="py-6 text-center text-sm text-muted-foreground">
              No results found.
            </CommandEmpty>
            <CommandGroup>
              {sortedOptions.map((option) => {
                const isSelected = selectedValues.has(option.value);
                return (
                  <CommandItem
                    key={option.value}
                    onSelect={() => {
                      if (customOnChange) {
                        // For server-side filtering, we only support single selection
                        if (isSelected) {
                          customOnChange(null);
                        } else {
                          customOnChange(option.value);
                        }
                      } else if (column) {
                        // For client-side filtering, we support multiple selection
                        if (isSelected) {
                          selectedValues.delete(option.value);
                        } else {
                          selectedValues.add(option.value);
                        }
                        const filterValues = Array.from(selectedValues);
                        column.setFilterValue(
                          filterValues.length ? filterValues : undefined,
                        );
                      }
                    }}
                    className="cursor-pointer"
                  >
                    <div
                      className={cn(
                        "mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                        isSelected
                          ? "bg-primary text-primary-foreground"
                          : "opacity-50 [&_svg]:invisible",
                      )}
                    >
                      <CheckIcon className={cn("h-4 w-4")} />
                    </div>
                    {option.icon && (
                      <option.icon className="mr-2 h-4 w-4 text-muted-foreground" />
                    )}
                    <span className="flex-1">{option.label}</span>
                    {option.count > 0 && (
                      <span className="ml-auto flex h-5 w-5 items-center justify-center rounded-full bg-muted text-xs font-medium text-muted-foreground">
                        {option.count}
                      </span>
                    )}
                  </CommandItem>
                );
              })}
            </CommandGroup>
            {selectedValues.size > 0 && (
              <>
                <CommandSeparator />
                <CommandGroup>
                  <CommandItem
                    onSelect={() => {
                      if (customOnChange) {
                        customOnChange(null);
                      } else if (column) {
                        column.setFilterValue(undefined);
                      }
                    }}
                    className="justify-center text-center cursor-pointer text-muted-foreground hover:text-foreground"
                  >
                    Clear filters
                  </CommandItem>
                </CommandGroup>
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
