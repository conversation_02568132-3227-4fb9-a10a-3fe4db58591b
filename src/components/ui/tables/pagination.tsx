"use client";

import type { Table } from "@tanstack/react-table";
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  DoubleArrowLeftIcon,
  DoubleArrowRightIcon,
} from "@radix-ui/react-icons";
import { Loader2 } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface DataTablePaginationProps<TData> {
  table: Table<TData>;

  totalItems?: number;
  onPaginationChange?: (page: number, pageSize: number) => void;
  isLoading?: boolean;
}

export function DataTablePagination<TData>({
  table,
  totalItems,
  onPaginationChange,
  isLoading = false,
}: DataTablePaginationProps<TData>) {
  const handlePageChange = (newPage: number) => {
    if (onPaginationChange) {
      // For manual pagination (server-side), call the provided handler
      onPaginationChange(newPage + 1, table.getState().pagination.pageSize);
    } else {
      // For client-side pagination, update the table state directly
      table.setPageIndex(newPage);
    }
  };

  const handlePageSizeChange = (newPageSize: number) => {
    if (onPaginationChange) {
      // For manual pagination (server-side), call the provided handler
      onPaginationChange(1, newPageSize); // Reset to page 1 when changing page size
    } else {
      // For client-side pagination, update the table state directly
      table.setPageSize(newPageSize);
    }
  };

  return (
    <div className="flex items-center justify-between px-1 py-2">
      <div className="flex-1 text-sm text-muted-foreground">
        <span className="font-medium">
          {table.getFilteredSelectedRowModel().rows.length}
        </span>{" "}
        of{" "}
        <span className="font-medium">
          {totalItems !== undefined
            ? totalItems.toLocaleString()
            : table.getFilteredRowModel().rows.length.toLocaleString()}
        </span>{" "}
        row(s) selected
      </div>
      <div className="flex items-center space-x-4 lg:space-x-6">
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium text-foreground/80">Rows per page</p>
          <Select
            value={`${table.getState().pagination.pageSize}`}
            onValueChange={(value) => {
              const newPageSize = Number(value);
              handlePageSizeChange(newPageSize);
            }}
            disabled={isLoading}
          >
            <SelectTrigger className="h-8 w-[70px] border-border/60">
              {isLoading ? (
                <div className="flex items-center justify-center w-full">
                  <Loader2 className="h-4 w-4 animate-spin" />
                </div>
              ) : (
                <SelectValue
                  placeholder={table.getState().pagination.pageSize}
                />
              )}
            </SelectTrigger>
            <SelectContent side="top">
              {[10, 20, 30, 40, 50].map((pageSize) => (
                <SelectItem key={pageSize} value={`${pageSize}`}>
                  {pageSize}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex w-[120px] items-center justify-center text-sm font-medium text-foreground/90">
          Page {table.getState().pagination.pageIndex + 1} of{" "}
          {table.getPageCount().toLocaleString()}
        </div>
        <div className="flex items-center space-x-1">
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex border-border/60 hover:bg-muted/60"
            onClick={() => handlePageChange(0)}
            disabled={!table.getCanPreviousPage() || isLoading}
          >
            <span className="sr-only">Go to first page</span>
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <DoubleArrowLeftIcon className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0 border-border/60 hover:bg-muted/60"
            onClick={() =>
              handlePageChange(table.getState().pagination.pageIndex - 1)
            }
            disabled={!table.getCanPreviousPage() || isLoading}
          >
            <span className="sr-only">Go to previous page</span>
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <ChevronLeftIcon className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0 border-border/60 hover:bg-muted/60"
            onClick={() =>
              handlePageChange(table.getState().pagination.pageIndex + 1)
            }
            disabled={!table.getCanNextPage() || isLoading}
          >
            <span className="sr-only">Go to next page</span>
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <ChevronRightIcon className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex border-border/60 hover:bg-muted/60"
            onClick={() => handlePageChange(table.getPageCount() - 1)}
            disabled={!table.getCanNextPage() || isLoading}
          >
            <span className="sr-only">Go to last page</span>
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <DoubleArrowRightIcon className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
