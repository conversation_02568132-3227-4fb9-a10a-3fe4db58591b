"use client";

import React, { ButtonHTMLAttributes } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";

interface DeleteConfirmationDialogProps {
  trigger?: React.ReactNode; // Optional external trigger
  triggerProps?: ButtonHTMLAttributes<HTMLButtonElement>; // Use React's ButtonHTMLAttributes
  itemName?: string; // Optional item name for descriptive message
  onConfirm: () => void;
  isPending?: boolean; // Optional pending state for confirmation button
}

export function DeleteConfirmationDialog({
  trigger,
  triggerProps,
  itemName,
  onConfirm,
  isPending,
}: DeleteConfirmationDialogProps) {
  const description = itemName
    ? `This action cannot be undone. This will permanently delete the "${itemName}".`
    : "This action cannot be undone. This will permanently delete the item.";

  return (
    <AlertDialog>
      {trigger ? (
        <AlertDialogTrigger asChild>{trigger}</AlertDialogTrigger>
      ) : (
        <AlertDialogTrigger asChild>
          <Button variant="destructive" size="sm" {...triggerProps}>
            Delete
          </Button>
        </AlertDialogTrigger>
      )}
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>{description}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isPending}>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={onConfirm} disabled={isPending}>
            {isPending ? "Deleting..." : "Confirm Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
