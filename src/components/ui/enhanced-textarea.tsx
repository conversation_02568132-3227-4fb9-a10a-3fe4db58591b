"use client";

import React, { useState, useRef, useEffect } from "react";
import { Textarea } from "@/components/ui/textarea";
import { TextEnhancer } from "@/components/ui/text-enhancer";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils/tailwind";
import { Loader2, RotateCcw, Check } from "lucide-react";

interface EnhancedTextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  onValueChange?: (value: string) => void;
  label?: string;
  description?: string;
  error?: string;
  showEnhancer?: boolean;
  enhancerPosition?: "top" | "bottom";
  applyButtonText?: string;
  showApplyButton?: boolean;
}

export function EnhancedTextarea({
  value,
  onChange,
  onValueChange,
  label,
  description,
  error,
  className,
  showEnhancer = true,
  enhancerPosition = "top",
  applyButtonText = "Apply",
  showApplyButton = true,
  ...props
}: EnhancedTextareaProps) {
  const [text, setText] = useState<string>((value as string) || "");
  const [enhancedText, setEnhancedText] = useState<string>("");
  const [isEnhanced, setIsEnhanced] = useState<boolean>(false);
  const [isApplying, setIsApplying] = useState<boolean>(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Update internal state when value prop changes
  useEffect(() => {
    if (value !== undefined && value !== text) {
      setText(value as string);
    }
  }, [value, text]);

  // Handle animation effect for apply button
  useEffect(() => {
    if (isApplying) {
      // Create a cleanup function that will run after the component renders
      const timer = setTimeout(() => {
        setIsApplying(false);
      }, 500);

      // Clean up the timer if the component unmounts or isApplying changes
      return () => clearTimeout(timer);
    }
  }, [isApplying]);

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setText(newValue);

    // Reset enhanced state when text changes
    if (isEnhanced) {
      setIsEnhanced(false);
    }

    // Call both onChange handlers
    if (onChange) {
      onChange(e);
    }

    if (onValueChange) {
      onValueChange(newValue);
    }
  };

  const handleEnhance = (newText: string) => {
    setEnhancedText(newText);
    setIsEnhanced(true);
  };

  const handleApply = () => {
    setIsApplying(true);

    // Apply the enhanced text
    setText(enhancedText);

    // Call the onChange handlers
    if (onValueChange) {
      onValueChange(enhancedText);
    }

    // Reset enhanced state
    setIsEnhanced(false);
    setEnhancedText("");

    // Set isApplying to true, the useEffect will handle setting it back to false
    setIsApplying(true);
  };

  const handleReset = () => {
    setIsEnhanced(false);
    setEnhancedText("");
  };

  const renderEnhancer = () => (
    <div className="flex items-center justify-between mb-2">
      <TextEnhancer onEnhance={handleEnhance} currentText={text} />
      {isEnhanced && showApplyButton && (
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleReset}>
            <RotateCcw className="h-4 w-4 mr-1" />
            Reset
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={handleApply}
            disabled={isApplying}
          >
            {isApplying ? (
              <>
                <Check className="h-4 w-4 mr-1" />
                Applied
              </>
            ) : (
              <>{applyButtonText}</>
            )}
          </Button>
        </div>
      )}
    </div>
  );

  return (
    <div className="space-y-2">
      {label && (
        <div className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          {label}
        </div>
      )}

      {description && (
        <div className="text-sm text-muted-foreground">{description}</div>
      )}

      {showEnhancer && enhancerPosition === "top" && renderEnhancer()}

      <div className="relative">
        <Textarea
          ref={textareaRef}
          value={isEnhanced ? enhancedText : text}
          onChange={handleTextChange}
          className={cn(
            "min-h-[120px] transition-all duration-200",
            error && "border-destructive focus-visible:ring-destructive",
            isEnhanced && "border-primary focus-visible:ring-primary/20",
            className,
          )}
          readOnly={isEnhanced}
          {...props}
        />

        {isEnhanced && (
          <div className="absolute inset-0 bg-primary/5 pointer-events-none rounded-md border border-primary" />
        )}
      </div>

      {showEnhancer && enhancerPosition === "bottom" && renderEnhancer()}

      {error && (
        <div className="text-sm font-medium text-destructive">{error}</div>
      )}
    </div>
  );
}
