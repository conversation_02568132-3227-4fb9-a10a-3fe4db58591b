import * as React from "react";
import { cn } from "@/lib/utils/tailwind";

/*
 * Typography Components
 *
 * This file contains a set of reusable typography components for consistent text styling
 * across the application. Each component has a default style that can be overridden
 * with className props.
 */

// Display - Used for hero sections and major feature headings
export function Display({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLHeadingElement>) {
  return (
    <h1
      className={cn("text-display text-foreground scroll-m-20", className)}
      {...props}
    >
      {children}
    </h1>
  );
}

// Heading 1 - Main page titles
export function H1({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLHeadingElement>) {
  return (
    <h1
      className={cn("text-3xl font-bold tracking-tight text-foreground scroll-m-20", className)}
      {...props}
    >
      {children}
    </h1>
  );
}

// Heading 2 - Section headings
export function H2({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLHeadingElement>) {
  return (
    <h2
      className={cn("text-h2 text-foreground scroll-m-20 mt-10 first:mt-0", className)}
      {...props}
    >
      {children}
    </h2>
  );
}

// Heading 3 - Subsection headings
export function H3({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLHeadingElement>) {
  return (
    <h3
      className={cn("text-h3 text-foreground scroll-m-20 mt-8 first:mt-0", className)}
      {...props}
    >
      {children}
    </h3>
  );
}

// Heading 4 - Card titles and minor section headings
export function H4({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLHeadingElement>) {
  return (
    <h4
      className={cn("text-lg font-semibold text-foreground scroll-m-20 mt-6 first:mt-0", className)}
      {...props}
    >
      {children}
    </h4>
  );
}

// Heading 5 - Small section headings
export function H5({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLHeadingElement>) {
  return (
    <h5
      className={cn("text-h5 text-foreground scroll-m-20 mt-4 first:mt-0", className)}
      {...props}
    >
      {children}
    </h5>
  );
}

// Heading 6 - The smallest heading
export function H6({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLHeadingElement>) {
  return (
    <h6
      className={cn("text-h6 text-foreground scroll-m-20 mt-4 first:mt-0", className)}
      {...props}
    >
      {children}
    </h6>
  );
}

// Large Body Text - Used for introductory paragraphs
export function LargeText({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLParagraphElement>) {
  return (
    <p
      className={cn("text-body-lg text-foreground leading-7", className)}
      {...props}
    >
      {children}
    </p>
  );
}

// Regular Body Text - Default paragraph text
export function Text({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLParagraphElement>) {
  return (
    <p
      className={cn("text-body text-foreground leading-7", className)}
      {...props}
    >
      {children}
    </p>
  );
}

// Small Body Text - Used for secondary information
export function SmallText({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLParagraphElement>) {
  return (
    <p
      className={cn("text-body-sm text-muted-foreground leading-6", className)}
      {...props}
    >
      {children}
    </p>
  );
}

// Caption - Used for image captions, footnotes, etc.
export function Caption({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLParagraphElement>) {
  return (
    <p
      className={cn("text-caption text-muted-foreground", className)}
      {...props}
    >
      {children}
    </p>
  );
}

// Overline - Used for labels, categories, etc.
export function Overline({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLParagraphElement>) {
  return (
    <p
      className={cn("text-overline text-muted-foreground", className)}
      {...props}
    >
      {children}
    </p>
  );
}

// Muted - Used for less important text
export function Muted({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLParagraphElement>) {
  return (
    <p
      className={cn("text-body text-muted-foreground", className)}
      {...props}
    >
      {children}
    </p>
  );
}

// Lead - Used for introductory paragraphs
export function Lead({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLParagraphElement>) {
  return (
    <p
      className={cn("text-muted-foreground", className)}
      {...props}
    >
      {children}
    </p>
  );
}

// Blockquote - Used for quotations
export function Blockquote({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLQuoteElement>) {
  return (
    <blockquote
      className={cn("text-body border-l-4 border-border pl-6 italic", className)}
      {...props}
    >
      {children}
    </blockquote>
  );
}

// Code - Used for inline code
export function InlineCode({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLElement>) {
  return (
    <code
      className={cn(
        "relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-body-sm",
        className
      )}
      {...props}
    >
      {children}
    </code>
  );
}
