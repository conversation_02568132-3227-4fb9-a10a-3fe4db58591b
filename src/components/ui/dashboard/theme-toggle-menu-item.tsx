"use client";

import * as React from "react";
import { Moon, Sun } from "lucide-react";
import { useTheme } from "next-themes";

import {
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar";

export function ThemeToggleMenuItem() {
  const { setTheme, theme } = useTheme();

  const handleToggleTheme = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    setTheme(theme === "dark" ? "light" : "dark");
  };

  const ThemeIcon = theme === "dark" ? Moon : Sun;

  return (
    <SidebarMenuItem>
      <SidebarMenuButton asChild size="sm">
        <a href="#" onClick={handleToggleTheme}>
          <ThemeIcon />
          <span>Theme</span>
        </a>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
}
