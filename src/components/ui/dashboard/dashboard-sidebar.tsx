"use client";

import * as React from "react";
import {
  LayoutDashboard,
  FileText,
  Users,
  Package,
  Truck,
  Settings,
  HelpCircle,
} from "lucide-react";

import { NavMain, type NavMainItem } from "./nav-main";
import { NavMainSkeleton } from "./nav-main-skeleton";
import { NavSecondary, type NavSecondaryItem } from "./nav-secondary";
import { NavSecondarySkeleton } from "./nav-secondary-skeleton";
import { NavUser } from "./nav-user";
import { NavUserSkeleton } from "./nav-user-skeleton";
import { NavAdmin } from "./nav-admin";
import { ThemeToggleMenuItem } from "./theme-toggle-menu-item";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { useAuth } from "@/lib/hooks/use-auth";

// Define navigation items for our application
const navItems: NavMainItem[] = [
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: LayoutDashboard,
  },
  {
    title: "RFQs",
    url: "/dashboard/rfqs",
    icon: FileText,
  },
  {
    title: "Providers",
    url: "/dashboard/providers",
    icon: Users,
  },
  {
    title: "Cargo",
    url: "/dashboard/cargo",
    icon: Package,
  },
  {
    title: "Equipment",
    url: "/dashboard/equipment",
    icon: Truck,
  },
];

const settingsItems: NavSecondaryItem[] = [
  {
    title: "Profile",
    url: "/dashboard/settings/profile",
    icon: Settings,
  },
  {
    title: "Help",
    url: "/dashboard/help",
    icon: HelpCircle,
  },
];

export interface DashboardSidebarProps
  extends React.ComponentProps<typeof Sidebar> {}

export function DashboardSidebar({ ...props }: DashboardSidebarProps) {
  const { authUser, user: rawUser, isAuthenticated, loading } = useAuth();

  const user = {
    name: rawUser?.name || authUser?.email?.split("@")[0] || "User",
    email: authUser?.email || "",
    avatar: rawUser?.avatar_url || authUser?.user_metadata?.avatar_url || "",
  };

  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <a href="/dashboard">
                <div className="flex aspect-square size-10 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                  <Truck className="size-5" />
                </div>
                <div className="grid flex-1 text-left leading-tight">
                  <span className="truncate text-base font-semibold">SteelFlow</span>
                  <span className="truncate text-sm text-sidebar-foreground/70">Logistics Platform</span>
                </div>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        {loading ? (
          <>
            <NavMainSkeleton />
            {/* NavAdmin already has its own loading state */}
            <NavAdmin />
            <NavSecondarySkeleton className="mt-auto" />
          </>
        ) : (
          <>
            <NavMain items={navItems} />
            <NavAdmin />
            <NavSecondary
              items={settingsItems}
              className="mt-auto"
              insertAfterProfile={<ThemeToggleMenuItem />}
            />
          </>
        )}
      </SidebarContent>
      <SidebarFooter>
        {loading ? (
          <NavUserSkeleton />
        ) : isAuthenticated ? (
          <NavUser user={user} />
        ) : null}
      </SidebarFooter>
    </Sidebar>
  );
}
