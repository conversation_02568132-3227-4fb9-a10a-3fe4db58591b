"use client";

import * as React from "react";
import { type LucideIcon } from "lucide-react";
import { usePathname } from "next/navigation";

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";

export interface NavSecondaryItem {
  title: string;
  url: string;
  icon: LucideIcon | React.FC<React.ComponentProps<LucideIcon>>;
  onClick?: (e: React.MouseEvent<HTMLAnchorElement>) => void;
}

export function NavSecondary({
  items,
  insertAfterProfile,
  ...props
}: {
  items: NavSecondaryItem[];
  insertAfterProfile?: React.ReactNode;
} & React.ComponentPropsWithoutRef<typeof SidebarGroup>) {
  const pathname = usePathname();

  return (
    <SidebarGroup {...props}>
      <SidebarGroupLabel>Settings</SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu>
          {items.map((item, index) => {
            const isActive = pathname === item.url;
            const isProfile = item.url === "/dashboard/settings/profile";

            return (
              <React.Fragment key={item.title}>
                <SidebarMenuItem>
                  <SidebarMenuButton asChild size="sm" isActive={isActive}>
                    <a href={item.url} onClick={item.onClick}>
                      {React.createElement(item.icon)}
                      <span>{item.title}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                {isProfile && insertAfterProfile}
              </React.Fragment>
            );
          })}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
