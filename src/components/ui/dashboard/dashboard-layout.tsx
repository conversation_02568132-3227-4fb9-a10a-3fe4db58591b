"use client";

import * as React from "react";
import { usePathname } from "next/navigation";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { DashboardSidebar } from "./dashboard-sidebar";

// Helper function to generate breadcrumb items from pathname
function generateBreadcrumbs(pathname: string) {
  // Remove leading slash and split by slashes
  const paths = pathname.split("/").filter(Boolean);

  // Generate breadcrumb items
  return paths.map((path, index) => {
    // Create the URL for this breadcrumb
    const url = `/${paths.slice(0, index + 1).join("/")}`;

    // Format the label (capitalize first letter)
    const label = path.charAt(0).toUpperCase() + path.slice(1);

    // Check if this is the last item (current page)
    const isCurrentPage = index === paths.length - 1;

    return {
      label,
      url,
      isCurrentPage,
    };
  });
}

export interface DashboardLayoutProps {
  children: React.ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const pathname = usePathname();
  const breadcrumbs = generateBreadcrumbs(pathname);

  return (
    <SidebarProvider>
      <DashboardSidebar />
      <SidebarInset>
        <header className="group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 flex h-12 shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear">
          <div className="flex w-full items-center px-6">
            <SidebarTrigger className="mr-4" />
            <Breadcrumb>
              <BreadcrumbList>
                {breadcrumbs.map((crumb) => (
                  <BreadcrumbItem key={crumb.url}>
                    {crumb.isCurrentPage ? (
                      <BreadcrumbPage>{crumb.label}</BreadcrumbPage>
                    ) : (
                      <BreadcrumbLink href={crumb.url}>
                        {crumb.label}
                      </BreadcrumbLink>
                    )}
                    {!crumb.isCurrentPage && <BreadcrumbSeparator />}
                  </BreadcrumbItem>
                ))}
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-6 pt-4">{children}</div>
      </SidebarInset>
    </SidebarProvider>
  );
}
