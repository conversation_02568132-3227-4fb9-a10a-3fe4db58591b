"use client";

import { cn } from "@/lib/utils/tailwind";
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuSkeleton,
} from "@/components/ui/sidebar";

// Define different width classes for the skeleton items
const skeletonWidthClasses = [
  "w-[65%]", // Profile
  "w-[55%]", // Theme
  "w-[50%]", // Help
];

export interface NavSecondarySkeletonProps
  extends React.ComponentPropsWithoutRef<typeof SidebarGroup> {}

export function NavSecondarySkeleton({ ...props }: NavSecondarySkeletonProps) {
  return (
    <SidebarGroup {...props}>
      <SidebarGroupLabel>Settings</SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu>
          {/* Create 3 skeleton items to match the settings nav items */}
          {Array.from({ length: 3 }).map((_, index) => (
            <SidebarMenuSkeleton
              key={index}
              showIcon={true}
              className={cn(skeletonWidthClasses[index])}
            />
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
