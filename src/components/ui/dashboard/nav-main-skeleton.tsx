"use client";

import { cn } from "@/lib/utils/tailwind";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuSkeleton,
} from "@/components/ui/sidebar";

// Define different width classes for the skeleton items
const skeletonWidthClasses = [
  "w-[65%]", // Dashboard
  "w-[55%]", // RFQs
  "w-[70%]", // Providers
  "w-[60%]", // Cargo
  "w-[75%]", // Equipment
];

export function NavMainSkeleton() {
  return (
    <SidebarGroup>
      <SidebarGroupLabel>Main</SidebarGroupLabel>
      <SidebarMenu>
        {/* Create 5 skeleton items to match the main nav items */}
        {Array.from({ length: 5 }).map((_, index) => (
          <SidebarMenuSkeleton
            key={index}
            showIcon={true}
            className={cn(skeletonWidthClasses[index])}
          />
        ))}
      </SidebarMenu>
    </SidebarGroup>
  );
}
