"use client";

import { useEffect, useState } from "react";
import { ShieldCheck } from "lucide-react";
import { usePathname } from "next/navigation";
import { isAdminAction } from "@/lib/actions/auth.actions";
import { createLogger } from "@/lib/utils/logger/logger";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSkeleton,
} from "@/components/ui/sidebar";

const logger = createLogger("NavAdmin");

export function NavAdmin() {
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const pathname = usePathname();

  useEffect(() => {
    async function checkAdminRole() {
      try {
        const result = await isAdminAction();
        setIsAdmin(result.success && result.data);
      } catch (error) {
        logger.error("Error checking admin role:", error);
        setIsAdmin(false);
      } finally {
        setIsLoading(false);
      }
    }

    checkAdminRole();
  }, []);

  if (isLoading) {
    return (
      <SidebarGroup>
        <SidebarGroupLabel>Admin</SidebarGroupLabel>
        <SidebarMenu>
          <SidebarMenuSkeleton showIcon={true} className="w-[75%]" />
        </SidebarMenu>
      </SidebarGroup>
    );
  }

  if (!isAdmin) {
    return null;
  }

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Admin</SidebarGroupLabel>
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton asChild isActive={pathname === "/admin"}>
            <a href="/admin">
              <ShieldCheck />
              <span>Admin Panel</span>
            </a>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarGroup>
  );
}
