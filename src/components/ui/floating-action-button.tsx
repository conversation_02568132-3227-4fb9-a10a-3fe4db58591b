"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils/tailwind";
import { Wand2, X, AlertCircle } from "lucide-react";
import { enhanceTextAction } from "@/lib/actions/text-enhancement.actions";
import { EnhancementType } from "@/lib/types/text-enhancement.types";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "@/components/ui/sonner";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("FloatingActionButton");

interface FloatingActionButtonProps {
  targetTextareaId?: string;
  position?: "bottom-right" | "bottom-left" | "top-right" | "top-left";
  className?: string;
  buttonSize?: "default" | "sm" | "lg" | "icon";
  buttonVariant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
}

export function FloatingActionButton({
  targetTextareaId,
  position = "bottom-right",
  className,
  buttonSize = "icon",
  buttonVariant = "default",
}: FloatingActionButtonProps) {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [localError, setLocalError] = useState<string | null>(null);

  const positionClasses = {
    "bottom-right": "bottom-4 right-4",
    "bottom-left": "bottom-4 left-4",
    "top-right": "top-4 right-4",
    "top-left": "top-4 left-4",
  };

  const handleEnhance = async (type: EnhancementType) => {
    if (!targetTextareaId) return;

    const textarea = document.getElementById(
      targetTextareaId,
    ) as HTMLTextAreaElement;
    if (!textarea) {
      logger.error(`Textarea with id "${targetTextareaId}" not found`);
      toast.error(`Textarea with id "${targetTextareaId}" not found`);
      return;
    }

    const currentText = textarea.value;
    if (!currentText.trim()) return;

    setLocalError(null);
    setIsLoading(true);

    try {
      const result = await enhanceTextAction(currentText, type);

      if (result.success) {
        // Apply the enhanced text
        textarea.value = result.data;

        // Trigger input event to update any React state
        const event = new Event("input", { bubbles: true });
        textarea.dispatchEvent(event);

        toast.success("Text enhanced successfully");
        setOpen(false);
      } else {
        // Show the error message
        setLocalError(result.error || "Failed to enhance text");
        toast.error(result.error || "Failed to enhance text");

        // Close the popover immediately for API key errors
        if (result.error?.includes("API key")) {
          setOpen(false);
        }
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error("Error enhancing text:", errorMessage);
      setLocalError(errorMessage);

      if (errorMessage.includes("API key")) {
        toast.error("Gemini API key is invalid or expired");
        // Close the popover immediately for API key errors
        setOpen(false);
      } else {
        toast.error("Failed to enhance text");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const enhancementOptions = [
    {
      type: EnhancementType.SHORTEN,
      label: "Shorten",
      description: "Makes the text more concise",
    },
    {
      type: EnhancementType.SPELLING_GRAMMAR,
      label: "Fix Grammar",
      description: "Corrects spelling and grammar",
    },
    {
      type: EnhancementType.IMPROVE_FLOW,
      label: "Improve Flow",
      description: "Enhances readability",
    },
    {
      type: EnhancementType.PROFESSIONAL,
      label: "Professional",
      description: "Adds a professional tone",
    },
  ];

  return (
    <div className={cn("fixed z-50", positionClasses[position], className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant={buttonVariant}
            size={buttonSize}
            className="rounded-full shadow-lg"
            disabled={isLoading}
          >
            {isLoading ? (
              <span className="animate-spin">⟳</span>
            ) : (
              <Wand2 className="h-5 w-5" />
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-64 p-0" align="end">
          <div className="flex justify-between items-center p-2 border-b">
            <span className="font-medium">Enhance Text</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setOpen(false)}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {localError ? (
            <div className="p-3">
              <Alert variant="destructive" className="border-red-500 bg-red-50">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-sm">
                  {localError.includes("API key")
                    ? "Gemini API key is invalid or expired. Please check your API key."
                    : localError}
                </AlertDescription>
              </Alert>
            </div>
          ) : (
            <div className="flex flex-col">
              {enhancementOptions.map((option) => (
                <button
                  key={option.type}
                  onClick={() => handleEnhance(option.type)}
                  className="flex flex-col items-start p-3 hover:bg-muted transition-colors text-left"
                  disabled={isLoading}
                >
                  <span className="font-medium">{option.label}</span>
                  <span className="text-xs text-muted-foreground">
                    {option.description}
                  </span>
                </button>
              ))}
            </div>
          )}
        </PopoverContent>
      </Popover>
    </div>
  );
}
