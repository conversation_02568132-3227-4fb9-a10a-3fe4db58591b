"use client";

import ReactCountryFlag from "react-country-flag";
import { MapPin } from "lucide-react";
import { cn } from "@/lib/utils/tailwind";

interface LocationDisplayProps {
  city: string;
  address?: string | null;
  postalCode?: string | null;
  countryName?: string | null;
  countryCode?: string | null;
  className?: string;
  showFullAddress?: boolean;
}

/**
 * LocationDisplay Component
 *
 * Displays location information with enhanced formatting and country flags.
 * Follows the user's preference for location data format: "City, CountryCode" with flag icons.
 *
 * @param city - The city name (required)
 * @param address - Optional street address
 * @param postalCode - Optional postal code
 * @param countryName - Optional country name
 * @param countryCode - Optional country alpha2 code for flag display
 * @param className - Optional additional CSS classes
 * @param showFullAddress - Whether to show full address details (default: true)
 */
export function LocationDisplay({
  city,
  address,
  postalCode,
  countryName,
  countryCode,
  className,
  showFullAddress = true,
}: LocationDisplayProps) {
  // Format the main location line: "City, CountryCode" with flag
  const formatMainLocation = () => {
    let mainLocation = city;
    if (countryCode) {
      mainLocation += `, ${countryCode}`;
    }
    return mainLocation;
  };

  return (
    <div className={cn("space-y-1", className)}>
      {/* Main location line with flag and city */}
      <div className="flex items-center gap-2">
        {countryCode ? (
          <ReactCountryFlag
            countryCode={countryCode}
            svg
            style={{
              width: '1.2em',
              height: '1.2em',
            }}
            title={countryName ? `${countryName} Flag` : `${countryCode} Flag`}
            className="flex-shrink-0"
          />
        ) : (
          <MapPin className="h-4 w-4 text-muted-foreground flex-shrink-0" />
        )}
        <span className="text-base font-medium text-foreground">
          {formatMainLocation()}
        </span>
      </div>

      {/* Additional address details */}
      {showFullAddress && (
        <div className="ml-6 space-y-0.5">
          {address && (
            <div className="text-sm text-muted-foreground">
              {address}
            </div>
          )}
          {postalCode && (
            <div className="text-sm text-muted-foreground">
              {postalCode}
            </div>
          )}
          {countryName && !countryCode && (
            <div className="text-sm text-muted-foreground">
              {countryName}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

/**
 * Compact Location Display Component
 *
 * A more compact version that shows only the essential location information
 * in a single line format. Supports enhanced address display logic:
 * - When full address is available: Display complete address with country flag
 * - When address is not available: Display "PostalCode City, CountryCode" format
 * - Always includes country flags for better visual appeal
 */
export function CompactLocationDisplay({
  city,
  address,
  postalCode,
  countryName,
  countryCode,
  className,
}: Pick<LocationDisplayProps, 'city' | 'address' | 'postalCode' | 'countryName' | 'countryCode' | 'className'>) {
  // Format location text based on available data
  const formatLocationText = () => {
    // If full address is available, use it
    if (address && address.trim()) {
      return address;
    }

    // Otherwise, use "PostalCode City, CountryCode" format
    let locationText = '';
    if (postalCode && postalCode.trim()) {
      locationText = `${postalCode} `;
    }
    locationText += city;
    if (countryCode) {
      locationText += `, ${countryCode}`;
    } else if (countryName) {
      locationText += `, ${countryName}`;
    }

    return locationText;
  };

  return (
    <div className={cn("flex items-center gap-2", className)}>
      {countryCode ? (
        <ReactCountryFlag
          countryCode={countryCode}
          svg
          style={{
            width: '1em',
            height: '1em',
          }}
          title={countryName ? `${countryName} Flag` : `${countryCode} Flag`}
          className="flex-shrink-0"
        />
      ) : (
        <MapPin className="h-3.5 w-3.5 text-muted-foreground flex-shrink-0" />
      )}
      <span className="text-sm">
        {formatLocationText()}
      </span>
    </div>
  );
}
