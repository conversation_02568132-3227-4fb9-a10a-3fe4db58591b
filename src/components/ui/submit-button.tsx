"use client";

import { useFormStatus } from "react-dom";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils/tailwind";

interface SubmitButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  pendingText?: string;
  className?: string;
}

export function SubmitButton({
  children,
  pendingText,
  className,
  ...props
}: SubmitButtonProps) {
  const { pending } = useFormStatus();

  return (
    <Button
      type="submit"
      disabled={pending || props.disabled}
      className={cn(className)}
      {...props}
    >
      {pending ? pendingText || "Processing..." : children}
    </Button>
  );
}
