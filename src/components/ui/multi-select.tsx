"use client";

import * as React from "react";
import { X, Check, ChevronsUpDown, Search } from "lucide-react";
import { cn } from "@/lib/utils/tailwind";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("MultiSelect");

export type Option = {
  value: string;
  label: string;
};

interface MultiSelectProps {
  options: Option[];
  selected: string[];
  onChange: (values: string[]) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  error?: string;
  emptyText?: string;
  searchText?: string;
}

export function MultiSelect({
  options,
  selected,
  onChange,
  placeholder = "Select options...",
  className,
  disabled = false,
  error,
  emptyText = "No options found.",
  searchText = "Search options...",
}: MultiSelectProps) {
  const [open, setOpen] = React.useState(false);
  const [search, setSearch] = React.useState("");

  // Filter options based on search
  const filteredOptions = React.useMemo(() => {
    if (!search.trim()) return options;
    return options.filter((option) =>
      option.label.toLowerCase().includes(search.toLowerCase())
    );
  }, [options, search]);

  // Log when options change
  React.useEffect(() => {
    logger.debug(`MultiSelect has ${options.length} options available`);
  }, [options]);

  const handleSelect = React.useCallback(
    (value: string) => {
      const newSelected = selected.includes(value)
        ? selected.filter((item) => item !== value)
        : [...selected, value];
      onChange(newSelected);
      logger.debug(`Selected values updated: ${newSelected.length} items`);
    },
    [selected, onChange],
  );

  const handleRemove = React.useCallback(
    (value: string) => {
      onChange(selected.filter((item) => item !== value));
      logger.debug(`Removed value: ${value}`);
    },
    [selected, onChange],
  );

  // Reset search when dropdown closes
  React.useEffect(() => {
    if (!open) {
      setSearch("");
    }
  }, [open]);

  // Handle keyboard shortcuts
  const handleKeyDown = React.useCallback((e: React.KeyboardEvent) => {
    // Open dropdown on Alt+Down or Slash
    if ((!open && (e.altKey && e.key === "ArrowDown")) || (!open && e.key === "/")) {
      e.preventDefault();
      setOpen(true);
    }

    // Close dropdown on Escape
    if (open && e.key === "Escape") {
      e.preventDefault();
      setOpen(false);
    }
  }, [open, setOpen]);

  return (
    <div className={cn("space-y-2", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between min-h-[40px] text-left font-normal",
              error && "border-destructive",
              selected.length > 0 && "pl-3 pr-2 py-1.5"
            )}
            disabled={disabled}
            onKeyDown={handleKeyDown}
            title="Press '/' to search or Alt+Down to open"
          >
            <div className="flex flex-wrap gap-1 items-center">
              {selected.length > 0 ? (
                <>
                  {selected.length <= 3 ? (
                    // Show badges for up to 3 selected items
                    selected.map((value) => {
                      const option = options.find((option) => option.value === value);
                      return (
                        <Badge
                          key={value}
                          variant="secondary"
                          className="rounded-md px-1.5 py-0.5 text-xs"
                        >
                          {option?.label || value}
                        </Badge>
                      );
                    })
                  ) : (
                    // Show count for more than 3 selected items
                    <Badge variant="secondary" className="rounded-md px-1.5 py-0.5 text-xs">
                      {selected.length} selected
                    </Badge>
                  )}
                </>
              ) : (
                <span className="text-muted-foreground flex items-center gap-1.5">
                  <Search className="h-3.5 w-3.5 opacity-50" />
                  {placeholder}
                </span>
              )}
            </div>
            <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
          <Command shouldFilter={false}>
            <CommandInput
              placeholder={searchText}
              value={search}
              onValueChange={setSearch}
              className="border-none focus:ring-0"
            />
            <CommandList>
              <CommandEmpty>{emptyText}</CommandEmpty>
              <CommandGroup>
                {filteredOptions.map((option) => (
                  <CommandItem
                    key={option.value}
                    value={option.value}
                    onSelect={() => handleSelect(option.value)}
                    className="flex items-center gap-2"
                  >
                    <div className={cn(
                      "flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                      selected.includes(option.value) ? "bg-primary text-primary-foreground" : "opacity-50"
                    )}>
                      {selected.includes(option.value) && (
                        <Check className="h-3 w-3" />
                      )}
                    </div>
                    <span>{option.label}</span>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {selected.length > 0 && (
        <div className="flex flex-wrap gap-1.5 mt-1.5">
          {selected.map((value) => {
            const option = options.find((option) => option.value === value);
            return (
              <Badge
                key={value}
                variant="secondary"
                className="rounded-md px-2 py-1 text-sm bg-muted/60 hover:bg-muted transition-colors"
              >
                {option?.label || value}
                <button
                  type="button"
                  className="ml-1.5 rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-1"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleRemove(value);
                  }}
                  disabled={disabled}
                  aria-label={`Remove ${option?.label || value}`}
                >
                  <X className="h-3.5 w-3.5" />
                  <span className="sr-only">Remove</span>
                </button>
              </Badge>
            );
          })}
        </div>
      )}

      {error && <p className="text-sm font-medium text-destructive mt-1.5">{error}</p>}
    </div>
  );
}
