"use client";

import * as React from "react";
import { Loader2, MapPin } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils/tailwind";
import { StructuredAddress } from "@/lib/services/google-maps/types";

// Import the use-places-autocomplete hook
import usePlacesAutocomplete, {
  getGeocode,
  getLatLng,
} from "use-places-autocomplete";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("AddressAutocomplete");

// Script loading state - shared across all instances
let scriptLoaded = false;
let scriptLoading = false;
let callbacks: (() => void)[] = [];

// Global callback that will be executed when the script loads
const GLOBAL_CALLBACK_NAME = "initGoogleMapsPlacesAPI";

declare global {
  interface Window {
    google: any;
    [GLOBAL_CALLBACK_NAME]: () => void;
  }
}

export interface AddressAutocompleteProps {
  value: string;
  onChange: (value: string) => void;
  onSelect: (address: StructuredAddress) => void;
  error?: string;
  disabled?: boolean;
  label?: string;
  id?: string; // Add unique ID for each instance
  placeholder?: string;
}

export function AddressAutocomplete({
  value,
  onChange,
  onSelect,
  error,
  disabled,
  label,
  id = "default",
  placeholder = "Search for an address...",
}: AddressAutocompleteProps) {
  const [loading, setLoading] = React.useState(false);
  const [isScriptReady, setIsScriptReady] = React.useState(scriptLoaded);
  const [selectedFromDropdown, setSelectedFromDropdown] = React.useState(false);
  const containerRef = React.useRef<HTMLDivElement>(null);
  const inputRef = React.useRef<HTMLInputElement>(null);
  const instanceId = React.useRef(
    `address-${id}-${Math.random().toString(36).substring(2, 9)}`,
  );

  // Initialize the hook with a unique cache key for each instance
  const {
    ready,
    value: inputValue,
    suggestions: { status, data },
    setValue: setInputValue,
    clearSuggestions,
    init,
  } = usePlacesAutocomplete({
    callbackName: GLOBAL_CALLBACK_NAME,
    debounce: 300,
    initOnMount: false,
    cacheKey: `steelflow-address-${instanceId.current}`,
  });

  // Define the global callback only once
  React.useEffect(() => {
    // If the script is already loaded, initialize immediately
    if (scriptLoaded) {
      setIsScriptReady(true);
      return;
    }

    // If not loaded yet, add our callback to the queue
    const ourCallback = () => {
      setIsScriptReady(true);
    };

    callbacks.push(ourCallback);

    // Define the global callback if not already defined
    if (!window[GLOBAL_CALLBACK_NAME]) {
      window[GLOBAL_CALLBACK_NAME] = () => {
        scriptLoaded = true;
        callbacks.forEach((cb) => cb());
        callbacks = [];
      };
    }

    // Load the script if not already loading
    if (!scriptLoading && !scriptLoaded) {
      scriptLoading = true;

      // Create and append the script manually to avoid Next.js Script component issues
      const script = document.createElement("script");
      script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=places&callback=${GLOBAL_CALLBACK_NAME}`;
      script.async = true;
      script.defer = true;
      document.head.appendChild(script);
    }

    return () => {
      // Remove our callback from the queue on unmount
      callbacks = callbacks.filter((cb) => cb !== ourCallback);
    };
  }, []);

  // Initialize the hook when script is loaded
  React.useEffect(() => {
    if (isScriptReady && window.google?.maps?.places) {
      init();
    }
  }, [isScriptReady, init]);

  // Sync the external value with the hook's internal value
  React.useEffect(() => {
    if (value !== inputValue) {
      setInputValue(value, false);
    }
  }, [value, inputValue, setInputValue]);

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    onChange(newValue);
    setSelectedFromDropdown(false);
  };

  // Handle selecting a place from suggestions
  const handleSelectSuggestion = async (
    description: string,
    placeId: string,
  ) => {
    setLoading(true);
    setInputValue(description, false);
    onChange(description);
    setSelectedFromDropdown(true);
    clearSuggestions();

    try {
      // Get geocode data for the selected place
      const results = await getGeocode({ placeId });

      if (results[0]) {
        // Extract structured address data
        const place = results[0];
        const structuredAddress: StructuredAddress = {
          street_address: description,
        };

        // Extract components
        if (place.address_components) {
          for (const component of place.address_components) {
            if (!component.types) continue;

            if (component.types.includes("locality")) {
              structuredAddress.city = component.long_name;
            } else if (
              component.types.includes("administrative_area_level_1")
            ) {
              structuredAddress.state = component.long_name;
            } else if (component.types.includes("postal_code")) {
              structuredAddress.postal_code = component.long_name;
            } else if (component.types.includes("country")) {
              structuredAddress.country = component.long_name;
              structuredAddress.country_code = component.short_name;
            }
          }
        }

        // Get lat/lng
        const latLng = getLatLng(place);
        structuredAddress.lat = latLng.lat;
        structuredAddress.lng = latLng.lng;

        // Call onSelect with the structured address
        onSelect(structuredAddress);
      }
    } catch (error) {
      logger.error("Error selecting place:", error);
    } finally {
      setLoading(false);
    }
  };

  // Close suggestions when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        clearSuggestions();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [clearSuggestions]);

  return (
    <div className="space-y-1.5" ref={containerRef}>
      {label && (
        <Label htmlFor={`address-input-${instanceId.current}`}>{label}</Label>
      )}

      <div className="relative" style={{ zIndex: 50 }}>
        <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
          <MapPin
            size={16}
            className={cn(
              "transition-colors duration-200",
              selectedFromDropdown && inputValue ? "text-green-500" : "",
            )}
          />
        </div>

        <Input
          id={`address-input-${instanceId.current}`}
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleChange}
          placeholder={placeholder}
          disabled={disabled || !ready || loading}
          className={cn(
            "pl-10 transition-all duration-200",
            error ? "border-destructive" : "",
            selectedFromDropdown && inputValue
              ? "border-green-500 focus-visible:ring-green-500/20"
              : "",
          )}
        />

        {loading && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          </div>
        )}

        {/* Display suggestions */}
        {status === "OK" && data.length > 0 && !selectedFromDropdown && (
          <div
            className="absolute left-0 right-0 z-50 mt-1 max-h-60 overflow-auto rounded-md border border-border bg-background shadow-lg"
            style={{
              position: 'absolute',
              width: '100%',
              top: 'calc(100% + 4px)'
            }}
          >
            <ul className="py-1">
              {data.map(({ place_id, description }) => (
                <li
                  key={place_id}
                  onClick={() => handleSelectSuggestion(description, place_id)}
                  className="px-3 py-2 flex items-center gap-2 hover:bg-accent cursor-pointer"
                >
                  <MapPin className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  <span className="text-sm truncate">{description}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {error && (
        <div className="flex items-center gap-2 text-sm text-destructive">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="14"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <circle cx="12" cy="12" r="10" />
            <line x1="12" x2="12" y1="8" y2="12" />
            <line x1="12" x2="12.01" y1="16" y2="16" />
          </svg>
          <span>{error}</span>
        </div>
      )}

      {!selectedFromDropdown && inputValue && !error && (
        <div className="flex items-center gap-2 text-sm text-amber-500">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="14"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z" />
            <path d="M12 9v4" />
            <path d="M12 17h.01" />
          </svg>
          <span>Please select an address from the suggestions</span>
        </div>
      )}
    </div>
  );
}
