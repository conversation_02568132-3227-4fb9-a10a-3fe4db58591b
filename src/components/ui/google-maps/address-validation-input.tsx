"use client";

import * as React from "react";
import { Loader2, <PERSON>Circle2, AlertCircle } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  validateAddress,
  ValidateAddressResult,
} from "@/lib/services/google-maps/address-validation.service";
import { cn } from "@/lib/utils/tailwind";
import { Button } from "@/components/ui/button";
import { useDebounce } from "@/hooks/use-debounce";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("AddressValidationInput");

export interface AddressValidationInputProps {
  value: string;
  onChange: (value: string) => void;
  onValidationResult?: (result: ValidateAddressResult) => void;
  error?: string;
  disabled?: boolean;
  label?: string;
  regionCode?: string;
  placeholder?: string;
  className?: string;
}

export function AddressValidationInput({
  value,
  onChange,
  onValidationResult,
  error,
  disabled,
  label,
  regionCode,
  placeholder = "Enter address",
  className,
}: AddressValidationInputProps) {
  const [isValidating, setIsValidating] = React.useState(false);
  const [validationResult, setValidationResult] =
    React.useState<ValidateAddressResult | null>(null);
  const [showSuggestion, setShowSuggestion] = React.useState(false);
  const debouncedValue = useDebounce(value, 500);

  // Validate the address when the value changes
  React.useEffect(() => {
    if (!debouncedValue || debouncedValue.length < 5 || !regionCode) {
      setValidationResult(null);
      return;
    }

    const validate = async () => {
      setIsValidating(true);
      try {
        const result = await validateAddress(debouncedValue, regionCode);
        setValidationResult(result);
        onValidationResult?.(result);

        // Show suggestion if we have a formatted address that's different from the input
        if (
          result.formattedAddress &&
          result.formattedAddress !== debouncedValue
        ) {
          setShowSuggestion(true);
        } else {
          setShowSuggestion(false);
        }
      } catch (err) {
        logger.error("Error validating address:", err);
      } finally {
        setIsValidating(false);
      }
    };

    validate();
  }, [debouncedValue, regionCode, onValidationResult]);

  // Apply the suggested address
  const applySuggestion = () => {
    if (validationResult?.formattedAddress) {
      onChange(validationResult.formattedAddress);
      setShowSuggestion(false);
    }
  };

  return (
    <div className="space-y-2">
      {label && <Label htmlFor="address-input">{label}</Label>}

      <div className="relative">
        <Input
          id="address-input"
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          className={cn(
            validationResult?.isValid &&
              "border-green-500 focus-visible:ring-green-500",
            validationResult?.isValid === false &&
              !error &&
              "border-yellow-500 focus-visible:ring-yellow-500",
            error && "border-destructive focus-visible:ring-destructive",
            className,
          )}
        />

        {isValidating && (
          <div className="absolute right-3 top-3">
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          </div>
        )}

        {!isValidating && validationResult?.isValid && (
          <div className="absolute right-3 top-3">
            <CheckCircle2 className="h-4 w-4 text-green-500" />
          </div>
        )}

        {!isValidating && validationResult?.isValid === false && (
          <div className="absolute right-3 top-3">
            <AlertCircle className="h-4 w-4 text-yellow-500" />
          </div>
        )}
      </div>

      {error && <p className="text-sm text-destructive">{error}</p>}

      {showSuggestion && validationResult?.formattedAddress && (
        <div className="rounded-md border border-yellow-200 bg-yellow-50 p-3 dark:border-yellow-900 dark:bg-yellow-950">
          <p className="text-sm text-yellow-800 dark:text-yellow-300">
            Did you mean:{" "}
            <span className="font-medium">
              {validationResult.formattedAddress}
            </span>
            ?
          </p>
          <Button
            variant="outline"
            size="sm"
            className="mt-2 h-7 bg-yellow-100 hover:bg-yellow-200 dark:bg-yellow-900 dark:hover:bg-yellow-800"
            onClick={applySuggestion}
          >
            Use this address
          </Button>
        </div>
      )}

      {validationResult?.error && !error && (
        <p className="text-sm text-yellow-600 dark:text-yellow-400">
          {validationResult.error}
        </p>
      )}
    </div>
  );
}
