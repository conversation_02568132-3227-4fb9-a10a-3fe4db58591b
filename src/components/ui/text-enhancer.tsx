"use client";

import React, { useState } from "react";
import { enhanceTextAction } from "@/lib/actions/text-enhancement.actions";
import { EnhancementType } from "@/lib/types/text-enhancement.types";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Loader2,
  Type,
  Check,
  Wand2,
  FileText,
  ArrowDownAZ,
  Pencil,
  Briefcase,
  AlertCircle,
} from "lucide-react";
import { cn } from "@/lib/utils/tailwind";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("TextEnhancer");

interface TextEnhancerProps {
  onEnhance: (enhancedText: string) => void;
  currentText: string;
  className?: string;
}

export function TextEnhancer({
  onEnhance,
  currentText,
  className,
}: TextEnhancerProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [localError, setLocalError] = useState<string | null>(null);

  const handleEnhance = async (type: EnhancementType) => {
    if (!currentText.trim()) return;

    setLocalError(null);
    setIsLoading(true);

    try {
      const result = await enhanceTextAction(currentText, type);

      if (result.success) {
        // Apply the enhanced text
        onEnhance(result.data);
        setOpen(false);
      } else {
        // Show the error message
        setLocalError(result.error || "Failed to enhance text");

        // Close the popover immediately for API key errors
        if (result.error?.includes("API key")) {
          setOpen(false);
        }
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error("Error enhancing text:", errorMessage);
      setLocalError(errorMessage);

      // Close the popover immediately for API key errors
      if (errorMessage.includes("API key")) {
        setOpen(false);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const enhancementOptions = [
    {
      type: EnhancementType.SHORTEN,
      label: "Shorten",
      description: "Makes the response more concise by removing details",
      icon: <Type className="h-4 w-4" />,
    },
    {
      type: EnhancementType.SPELLING_GRAMMAR,
      label: "Spelling & Grammar",
      description: "Fixes any spelling or grammar issues",
      icon: <Check className="h-4 w-4" />,
    },
    {
      type: EnhancementType.SIMPLIFY,
      label: "Simplify",
      description: "Simplifies the language in the response",
      icon: <FileText className="h-4 w-4" />,
    },
    {
      type: EnhancementType.IMPROVE_FLOW,
      label: "Improve Flow",
      description: "Improves the flow of the response for readability",
      icon: <ArrowDownAZ className="h-4 w-4" />,
    },
    {
      type: EnhancementType.REWRITE,
      label: "Rewrite",
      description: "Uses the text to generate a new answer",
      icon: <Pencil className="h-4 w-4" />,
    },
    {
      type: EnhancementType.PROFESSIONAL,
      label: "Professional",
      description: "Adds a professional tone to the response",
      icon: <Briefcase className="h-4 w-4" />,
    },
    {
      type: EnhancementType.GENERIC,
      label: "Generic Answer",
      description: "Generates a general non-specific answer",
      icon: <Wand2 className="h-4 w-4" />,
    },
  ];

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn("flex items-center gap-2", className)}
          disabled={isLoading || !currentText.trim()}
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              Enhancing...
            </>
          ) : (
            <>
              <Wand2 className="h-4 w-4" />
              Writing Actions
            </>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="start">
        {localError ? (
          <div className="p-3">
            <Alert variant="destructive" className="border-red-500 bg-red-50">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-sm">
                {localError.includes("API key")
                  ? "Gemini API key is invalid or expired. Please check your API key."
                  : localError}
              </AlertDescription>
            </Alert>
          </div>
        ) : (
          <div className="flex flex-col">
            {enhancementOptions.map((option) => (
              <button
                key={option.type}
                onClick={() => handleEnhance(option.type)}
                className="flex items-start gap-3 p-3 hover:bg-muted transition-colors text-left"
                disabled={isLoading}
              >
                <div className="flex h-5 w-5 items-center justify-center text-primary">
                  {option.icon}
                </div>
                <div className="flex flex-col">
                  <span className="font-medium">{option.label}</span>
                  <span className="text-sm text-muted-foreground">
                    {option.description}
                  </span>
                </div>
              </button>
            ))}
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
}
