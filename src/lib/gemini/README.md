# Gemini AI Text Enhancement

This module provides a set of components and utilities for enhancing text using Google's Gemini AI.

## Features

- **GeminiProvider**: Context provider for Gemini AI integration
- **TextEnhancer**: But<PERSON> with dropdown menu for text enhancement options
- **EnhancedTextarea**: Textarea with built-in AI enhancement capabilities
- **FloatingActionButton**: Floating button that can enhance text in any textarea

## Installation

1. Install the Google GenAI SDK:

```bash
npm install @google/genai
```

2. Get a Gemini API key from [Google AI Studio](https://makersuite.google.com/)

## Usage

### Basic Setup

Wrap your application or component with the `GeminiProvider`:

```tsx
import { GeminiProvider } from "@/lib/gemini";

function MyApp() {
  return (
    <GeminiProvider apiKey="YOUR_GEMINI_API_KEY">
      {/* Your app content */}
    </GeminiProvider>
  );
}
```

### Using the EnhancedTextarea

```tsx
import { EnhancedTextarea } from "@/lib/gemini";

function MyForm() {
  const [content, setContent] = useState("");

  return (
    <EnhancedTextarea
      label="Email Content"
      value={content}
      onValueChange={setContent}
      placeholder="Write your content here..."
    />
  );
}
```

### Using the TextEnhancer with a custom textarea

```tsx
import { TextEnhancer } from "@/lib/gemini";
import { Textarea } from "@/components/ui/textarea";

function MyCustomForm() {
  const [content, setContent] = useState("");

  const handleEnhance = (enhancedText) => {
    setContent(enhancedText);
  };

  return (
    <div>
      <div className="flex justify-between mb-2">
        <label>Your Content</label>
        <TextEnhancer onEnhance={handleEnhance} currentText={content} />
      </div>
      <Textarea
        value={content}
        onChange={(e) => setContent(e.target.value)}
        placeholder="Write your content here..."
      />
    </div>
  );
}
```

### Using the FloatingActionButton

```tsx
import { FloatingActionButton } from "@/lib/gemini";
import { Textarea } from "@/components/ui/textarea";

function MyPage() {
  return (
    <div>
      <Textarea id="my-textarea" placeholder="Write something here..." />

      <FloatingActionButton
        targetTextareaId="my-textarea"
        position="bottom-right"
      />
    </div>
  );
}
```

## Enhancement Types

The following enhancement types are available:

- `SHORTEN`: Makes the text more concise
- `SPELLING_GRAMMAR`: Fixes spelling and grammar issues
- `SIMPLIFY`: Simplifies the language
- `IMPROVE_FLOW`: Improves readability and flow
- `REWRITE`: Rewrites the text while preserving meaning
- `PROFESSIONAL`: Adds a professional tone
- `GENERIC`: Creates a more general version

## Custom Enhancements

You can use the `useGemini` hook to create custom enhancements:

```tsx
import { useGemini, EnhancementType } from "@/lib/gemini";

function MyCustomEnhancer() {
  const { enhanceText, isLoading } = useGemini();

  const handleCustomEnhancement = async (text) => {
    try {
      const enhanced = await enhanceText(text, EnhancementType.PROFESSIONAL);
      console.log(enhanced);
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <button onClick={() => handleCustomEnhancement("Your text here")}>
      Enhance
    </button>
  );
}
```

## Environment Variables

For production use, it's recommended to store your API key in an environment variable:

```
NEXT_PUBLIC_GEMINI_API_KEY=your_api_key_here
```

Then use it in your provider:

```tsx
<GeminiProvider apiKey={process.env.NEXT_PUBLIC_GEMINI_API_KEY}>
  {/* Your app content */}
</GeminiProvider>
```

## Security Considerations

- Never expose your API key in client-side code in production
- Consider implementing a server-side proxy for API calls in production
- Use environment variables and proper secret management
