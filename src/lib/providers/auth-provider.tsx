"use client";

import * as React from "react";
import { User as AuthUser } from "@supabase/supabase-js";
import { createClient } from "@/lib/supabase/client";
import { type User } from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("AuthProvider");

export const AuthContext = React.createContext<
  | {
      authUser: AuthUser | undefined;
      user: User | undefined;
      isAuthenticated: boolean;
      loading: boolean;
      error: string | null;
      logIn: (email: string, password: string) => any;
      logOut: () => any;
      refreshUser: () => any;
    }
  | undefined
>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [authUser, setAuthUser] = React.useState<AuthUser | undefined>(
    undefined,
  );
  const [user, setUser] = React.useState<User | undefined>(undefined);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  const initAuthUser = async () => {
    try {
      setError(null);
      const supabase = createClient();

      // First try to get the user directly, which is more reliable
      const {
        data: { user },
        error: userError,
      } = await supabase.auth.getUser();

      if (userError) {
        logger.error(
          "Failed to get user during auth initialization:",
          userError,
        );

        // Fall back to session if getUser fails
        const {
          data: { session },
          error: sessionError,
        } = await supabase.auth.getSession();

        if (sessionError) {
          logger.error(
            "Failed to get session during auth initialization:",
            sessionError,
          );
          setAuthUser(undefined);
          return;
        }

        setAuthUser(session?.user || undefined);
      } else {
        // Use the user from getUser
        setAuthUser(user || undefined);
      }
    } catch (error) {
      logger.error("Error during auth initialization:", error);
      setAuthUser(undefined);
    } finally {
      setLoading(false);
    }
  };

  const fetchUser = React.useCallback(async () => {
    if (!authUser?.id) {
      setUser(undefined);
      return;
    }

    try {
      setError(null);
      const supabase = createClient();
      const { data: fetchedUser, error: fetchError } = await supabase
        .from("users")
        .select("*")
        .eq("id", authUser.id)
        .maybeSingle();

      if (fetchError) {
        setError("Failed to load user data. Please refresh the page.");
        logger.error("Failed to fetch user:", fetchError);
        setUser(undefined);
        return;
      }

      setUser(fetchedUser || undefined);
    } catch (error) {
      setError("An unexpected error occurred while loading user data.");
      logger.error("Error fetching user data:", error);
      setUser(undefined);
    } finally {
      setLoading(false);
    }
  }, [authUser, setUser, setError, setLoading]);

  React.useEffect(() => {
    initAuthUser();
  }, []);

  React.useEffect(() => {
    if (!authUser) {
      setUser(undefined);
      return;
    }

    fetchUser();
  }, [authUser, fetchUser]);

  React.useEffect(() => {
    const supabase = createClient();

    const { data: subscription } = supabase.auth.onAuthStateChange(
      (_event, session) => {
        setAuthUser(session?.user || undefined);
      },
    );

    return () => {
      subscription?.subscription.unsubscribe();
    };
  }, []);

  const logIn = React.useCallback(async (email: string, password: string) => {
    setError(null);
    setLoading(true);

    try {
      // Use the client-side login function
      const supabase = createClient();
      const { data, error: loginError } =
        await supabase.auth.signInWithPassword({
          email,
          password,
        });

      if (loginError) {
        setError("Invalid email or password. Please try again.");
        setLoading(false);
        return { error: loginError.message };
      }

      setAuthUser(data.user);
      await fetchUser();
    } catch (error) {
      setError("An unexpected error occurred. Please try again.");
      logger.error("Login error:", error);
    } finally {
      setLoading(false);
    }
  }, [setError, setLoading, setAuthUser, fetchUser]);

  const logOut = React.useCallback(async () => {
    setError(null);
    setLoading(true);

    try {
      // Use the client-side logout function
      const supabase = createClient();
      const { error } = await supabase.auth.signOut();

      if (error) {
        logger.error("Error signing out:", error);
        setError("Failed to sign out. Please try again.");
      } else {
        setAuthUser(undefined);
        setUser(undefined);
      }
    } catch (error) {
      logger.error("Logout error:", error);
      setError("An unexpected error occurred during logout.");
    } finally {
      setLoading(false);
    }
  }, [setError, setLoading, setAuthUser, setUser]);

  const isAuthenticated = !!authUser;

  const value = React.useMemo(
    () => ({
      authUser,
      user,
      isAuthenticated,
      loading,
      error,
      logIn,
      logOut,
      refreshUser: fetchUser,
    }),
    [user, authUser, loading, error, isAuthenticated, logIn, logOut, fetchUser],
  );

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
