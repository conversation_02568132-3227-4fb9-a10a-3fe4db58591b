"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { GoogleGenAI } from "@google/genai";

// Define the context type
interface GeminiContextType {
  client: GoogleGenAI | null;
  isLoading: boolean;
  error: string | null;
  enhanceText: (
    text: string,
    enhancementType: EnhancementType,
  ) => Promise<string>;
}

// Enhancement types for text
export enum EnhancementType {
  SHORTEN = "shorten",
  SPELLING_GRAMMAR = "spelling_grammar",
  SIMPLIFY = "simplify",
  IMPROVE_FLOW = "improve_flow",
  REWRITE = "rewrite",
  PROFESSIONAL = "professional",
  GENERIC = "generic",
}

// Create the context with default values
const GeminiContext = createContext<GeminiContextType>({
  client: null,
  isLoading: false,
  error: null,
  enhanceText: async () => "",
});

// Provider props
interface GeminiProviderProps {
  apiKey: string;
  children: ReactNode;
}

export function GeminiProvider({ apiKey, children }: GeminiProviderProps) {
  const [client, setClient] = useState<GoogleGenAI | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize the Gemini client
  useEffect(() => {
    if (!apiKey) {
      setError("API key is required");
      return;
    }

    try {
      const genAI = new GoogleGenAI({ apiKey });
      setClient(genAI);
      setError(null);
    } catch (err) {
      setError(
        `Failed to initialize Gemini client: ${err instanceof Error ? err.message : String(err)}`,
      );
      setClient(null);
    }
  }, [apiKey]);

  // Function to enhance text using Gemini
  const enhanceText = async (
    text: string,
    enhancementType: EnhancementType,
  ): Promise<string> => {
    if (!client) {
      throw new Error("Gemini client is not initialized");
    }

    if (!text.trim()) {
      return "";
    }

    setIsLoading(true);

    try {
      // Create the prompt based on enhancement type
      let prompt = "";

      // Base system prompt to guide the model's behavior
      const systemPrompt = `You are an email enhancement assistant that directly improves email text.

YOUR OUTPUT MUST:
- Contain ONLY the enhanced email text
- Be a single, complete email ready to send
- Maintain the original structure (greeting, body, signature)
- Preserve the core message and intent

YOUR OUTPUT MUST NOT:
- Include multiple versions or options (no "Option 1", "Option 2")
- Contain explanations of changes
- Include meta-text like "Changes Made:" or "Here are options"
- Contain bullet points explaining your process
- Include any commentary about what you changed
- Add any annotations or notes

CRITICAL: Users need the exact enhanced email text with no additional content.`;

      switch (enhancementType) {
        case EnhancementType.SHORTEN:
          prompt = `${systemPrompt}

TASK: Make this email more concise by removing unnecessary words and details.
GOAL: A shorter email that preserves all key information and maintains professionalism.
REMEMBER: Return ONLY the improved email text with no explanations.

EMAIL TO ENHANCE:
${text}

FINAL INSTRUCTION: Provide ONLY the enhanced email with no explanations, options, or annotations.`;
          break;
        case EnhancementType.SPELLING_GRAMMAR:
          prompt = `${systemPrompt}

TASK: Fix all spelling and grammar issues in this email.
GOAL: A grammatically perfect email with no spelling errors.
REMEMBER: Return ONLY the corrected email text with no explanations.

EMAIL TO ENHANCE:
${text}

FINAL INSTRUCTION: Provide ONLY the enhanced email with no explanations, options, or annotations.`;
          break;
        case EnhancementType.SIMPLIFY:
          prompt = `${systemPrompt}

TASK: Simplify the language in this email to make it clearer and easier to understand.
GOAL: An email with simpler vocabulary and sentence structure that maintains the same meaning.
REMEMBER: Return ONLY the simplified email text with no explanations.

EMAIL TO ENHANCE:
${text}

FINAL INSTRUCTION: Provide ONLY the enhanced email with no explanations, options, or annotations.`;
          break;
        case EnhancementType.IMPROVE_FLOW:
          prompt = `${systemPrompt}

TASK: Improve the flow and readability of this email.
GOAL: A more coherent email with better transitions between ideas.
REMEMBER: Return ONLY the improved email text with no explanations.

EMAIL TO ENHANCE:
${text}

FINAL INSTRUCTION: Provide ONLY the enhanced email with no explanations, options, or annotations.`;
          break;
        case EnhancementType.REWRITE:
          prompt = `${systemPrompt}

TASK: Rewrite this email while preserving its core meaning and intent.
GOAL: A fresh version of the email that communicates the same message in a new way.
REMEMBER: Return ONLY the rewritten email text with no explanations.

EMAIL TO ENHANCE:
${text}

FINAL INSTRUCTION: Provide ONLY the enhanced email with no explanations, options, or annotations.`;
          break;
        case EnhancementType.PROFESSIONAL:
          prompt = `${systemPrompt}

TASK: Make this email more professional and formal in tone.
GOAL: A business-appropriate email with formal language and structure.
REMEMBER: Return ONLY the professional email text with no explanations.

EMAIL TO ENHANCE:
${text}

FINAL INSTRUCTION: Provide ONLY the enhanced email with no explanations, options, or annotations.`;
          break;
        case EnhancementType.GENERIC:
          prompt = `${systemPrompt}

TASK: Make this email more general so it could apply to a wider audience.
GOAL: A versatile email that could be used in multiple similar situations.
REMEMBER: Return ONLY the generalized email text with no explanations.

EMAIL TO ENHANCE:
${text}

FINAL INSTRUCTION: Provide ONLY the enhanced email with no explanations, options, or annotations.`;
          break;
        default:
          prompt = `${systemPrompt}

TASK: Improve this email's overall quality and effectiveness.
GOAL: A better version of the email that maintains its core purpose.
REMEMBER: Return ONLY the improved email text with no explanations.

EMAIL TO ENHANCE:
${text}

FINAL INSTRUCTION: Provide ONLY the enhanced email with no explanations, options, or annotations.`;
      }

      try {
        // Use the Gemini model to generate content
        const model = client.models.generateContent({
          model: "gemini-2.0-flash-001",
          contents: prompt,
          config: {
            temperature: 0.01, // Extremely low temperature for highly deterministic responses
            maxOutputTokens: 1024,
          },
        });

        const result = await model;
        const enhancedText = result.text?.trim() || "";

        return enhancedText;
      } catch (apiError) {
        // Check for API key errors
        const errorMessage =
          apiError instanceof Error ? apiError.message : String(apiError);

        if (
          errorMessage.includes("API key expired") ||
          errorMessage.includes("API_KEY_INVALID") ||
          errorMessage.includes("INVALID_ARGUMENT")
        ) {
          setError(
            "Gemini API key is invalid or expired. Please check your API key.",
          );

          // Return the original text with a note about the API key issue
          return (
            text + "\n\n[Note: AI enhancement unavailable - API key issue]"
          );
        }

        // For other API errors
        throw apiError;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(`Error enhancing text: ${errorMessage}`);

      // Return the original text as a fallback
      return text;
    } finally {
      setIsLoading(false);
    }
  };

  // Provide the context value
  const contextValue: GeminiContextType = {
    client,
    isLoading,
    error,
    enhanceText,
  };

  return (
    <GeminiContext.Provider value={contextValue}>
      {children}
    </GeminiContext.Provider>
  );
}

// Custom hook to use the Gemini context
export function useGemini() {
  const context = useContext(GeminiContext);

  if (context === undefined) {
    throw new Error("useGemini must be used within a GeminiProvider");
  }

  return context;
}
