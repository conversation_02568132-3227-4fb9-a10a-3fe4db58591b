"use server";

import { revalidatePath } from "next/cache";
import {
  fetchProviderContacts,
  addProviderContact,
  modifyProviderContact,
  removeProviderContact,
} from "@/lib/services/provider-contact.service";
import {
  CreateProviderContactInput,
  UpdateProviderContactInput,
  ActionResponse,
} from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("ProviderContactActions");

/**
 * Get all contacts for a provider
 */
export async function getProviderContactsAction(
  providerId: string,
): Promise<ActionResponse<any>> {
  try {
    const contacts = await fetchProviderContacts({ provider_id: providerId });
    return { success: true, data: contacts };
  } catch (error) {
    logger.error("Error in getProviderContactsAction:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch provider contacts",
    };
  }
}

/**
 * Create a new contact for a provider
 */
export async function createProviderContactAction(
  contact: CreateProviderContactInput,
): Promise<ActionResponse<any>> {
  logger.info("Server action called with contact data:", contact);

  try {
    logger.info("Calling service function addProviderContact...");
    const newContact = await addProviderContact(contact);

    revalidatePath(`/dashboard/providers/${contact.provider_id}`);

    return { success: true, data: newContact };
  } catch (error) {
    logger.error("Error in createProviderContactAction:", error);
    // Return more detailed error information
    const errorMessage =
      error instanceof Error
        ? error.message
        : "Failed to create provider contact";
    logger.error("Returning error:", errorMessage);
    return {
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Update an existing provider contact
 */
export async function updateProviderContactAction(
  id: string,
  providerId: string,
  contact: Omit<UpdateProviderContactInput, "id" | "provider_id">,
): Promise<ActionResponse<any>> {
  try {
    const updatedContact = await modifyProviderContact({
      id,
      provider_id: providerId,
      ...contact,
    });

    revalidatePath(`/dashboard/providers/${providerId}`);

    return { success: true, data: updatedContact };
  } catch (error) {
    logger.error("Error in updateProviderContactAction:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to update provider contact",
    };
  }
}

/**
 * Delete a provider contact
 */
export async function deleteProviderContactAction(
  id: string,
  providerId: string,
): Promise<ActionResponse<any>> {
  try {
    const result = await removeProviderContact({
      id,
      provider_id: providerId,
    });

    revalidatePath(`/dashboard/providers/${providerId}`);

    return { success: true, data: result };
  } catch (error) {
    logger.error("Error in deleteProviderContactAction:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to delete provider contact",
    };
  }
}
