"use server";

import { revalidatePath } from "next/cache";
import {
  fetchRFQs,
  getRFQ,
  createRFQ,
  updateRFQ,
  deleteRFQ,
  matchProvidersForRFQ,
  selectProvidersForRFQ,
  fetchRFQProviders,
  fetchRFQProvidersWithPagination,
  submitRFQBid,
  updateRFQBid,
  fetchRFQB<PERSON>,
  createRFQEmails,
  fetchRFQEmails,
  validateCreateRFQ,
  validateUpdateRFQ,
  validateRFQ<PERSON>rovider,
  validateRFQBid,
  validateUpdateRFQBid,
  mergeProvidersWithBids as mergeProvidersWithBidsService,
  filterProviders as filterProvidersService,
  getInvitedProviders as getInvitedProvidersService,
  isProviderInvited,
  hasProviderSubmittedBid,
  getProviderSelectionData,
} from "@/lib/services/rfq.service";
import { getCurrentUser } from "@/lib/services/auth.service";
import { getCurrentUserProfile } from "@/lib/services/user.service";
import {
  GetRFQsParams,
  ActionResponse,
  PaginatedResponse,
  RFQ,
  RFQProvider,
  RFQBid,
  ProviderSelectionFormSchema,
  EmailDataSchema,
  ProviderSelectionActionState,
} from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("RFQActions");

/**
 * Get all RFQs or filter by parameters
 */
export async function getRFQsAction(
  params: Partial<GetRFQsParams> = { page: 1, pageSize: 10 },
): Promise<ActionResponse<PaginatedResponse<RFQ>>> {
  try {
    // Ensure required parameters are set
    const validParams = {
      page: Number(params.page || 1),
      pageSize: Number(params.pageSize || 10),
      id: params.id,
      status: params.status,
      origin_city: params.origin_city,
      destination_city: params.destination_city,
      search: params.search,
      created_after: params.created_after,
      created_before: params.created_before,
    };

    const result = await fetchRFQs(validParams);
    return { success: true, data: result };
  } catch (error) {
    logger.error("Error in getRFQsAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch RFQs",
    };
  }
}

/**
 * Get a single RFQ by ID
 */
export async function getRFQAction(id: string): Promise<
  ActionResponse<
    RFQ & {
      equipment_type_name?: string;
      cargo_types?: any[];
      countries_origin?: { name: string; alpha2_code: string };
      countries_destination?: { name: string; alpha2_code: string };
    }
  >
> {
  try {
    const rfq = await getRFQ(id);
    return { success: true, data: rfq };
  } catch (error) {
    logger.error("Error in getRFQAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch RFQ",
    };
  }
}

/**
 * Create a new RFQ
 */
export async function createRFQAction(
  data: FormData | Record<string, unknown>,
): Promise<ActionResponse<RFQ>> {
  try {
    // Handle both FormData and direct object input
    let parsedData;

    logger.info("createRFQAction called with data:", data);

    if (data instanceof FormData) {
      // Convert FormData to object
      const formDataObj = Object.fromEntries(data.entries());
      logger.info("FormData converted to object:", formDataObj);

      // Parse numeric values
      parsedData = {
        ...formDataObj,
        weight: formDataObj.weight
          ? parseFloat(formDataObj.weight as string)
          : undefined,
        length: formDataObj.length
          ? parseFloat(formDataObj.length as string)
          : undefined,
        width: formDataObj.width
          ? parseFloat(formDataObj.width as string)
          : undefined,
        height: formDataObj.height
          ? parseFloat(formDataObj.height as string)
          : undefined,
        quantity: formDataObj.quantity
          ? parseInt(formDataObj.quantity as string, 10)
          : undefined,
        equipment_quantity: formDataObj.equipment_quantity
          ? parseInt(formDataObj.equipment_quantity as string, 10)
          : undefined,
      };
    } else {
      // Direct object input (already parsed)
      parsedData = data;
    }

    logger.info("Parsed data for RFQ creation:", parsedData);

    // Check for required fields before validation
    if (!parsedData.origin_country_id) {
      logger.error("Missing origin_country_id in RFQ creation data");
      return {
        success: false,
        error: "Origin country is required",
        fieldErrors: { origin_country_id: ["Origin country is required"] },
      };
    }

    if (!parsedData.destination_country_id) {
      logger.error("Missing destination_country_id in RFQ creation data");
      return {
        success: false,
        error: "Destination country is required",
        fieldErrors: {
          destination_country_id: ["Destination country is required"],
        },
      };
    }

    if (
      !parsedData.cargo_type_ids ||
      (Array.isArray(parsedData.cargo_type_ids) &&
        parsedData.cargo_type_ids.length === 0)
    ) {
      logger.error("Missing cargo_type_ids in RFQ creation data");
      return {
        success: false,
        error: "At least one cargo type must be selected",
        fieldErrors: {
          cargo_type_ids: ["At least one cargo type must be selected"],
        },
      };
    }

    if (!parsedData.equipment_type_id) {
      logger.error("Missing equipment_type_id in RFQ creation data");
      return {
        success: false,
        error: "Equipment type is required",
        fieldErrors: { equipment_type_id: ["Equipment type is required"] },
      };
    }

    // Use centralized validation function
    logger.info("Validating RFQ data with schema");
    const validationResult = await validateCreateRFQ(parsedData);

    if (!validationResult.success) {
      logger.error(
        "Validation failed for RFQ creation:",
        validationResult.error,
      );
      return {
        success: false,
        error: validationResult.error?.message || "Validation failed",
        fieldErrors: validationResult.error?.fieldErrors,
      };
    }

    // Get the current user using the auth service
    logger.info("Getting current user for RFQ creation");
    const user = await getCurrentUser();

    if (!user) {
      logger.error("Authentication required for RFQ creation");
      return { success: false, error: "Authentication required" };
    }

    // Create the RFQ
    logger.info(
      "Creating RFQ with validated data:",
      JSON.stringify(validationResult.data, null, 2),
    );

    try {
      const rfq = await createRFQ(validationResult.data!, user.id);
      logger.info("RFQ created successfully:", rfq);

      // Revalidate the RFQs page
      revalidatePath("/dashboard/rfqs");

      return { success: true, data: rfq };
    } catch (createError) {
      logger.error("Error in createRFQ service call:", createError);
      return {
        success: false,
        error:
          createError instanceof Error
            ? createError.message
            : "Failed to create RFQ in database",
      };
    }
  } catch (error) {
    logger.error("Error in createRFQAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create RFQ",
    };
  }
}

/**
 * Update an existing RFQ
 */
export async function updateRFQAction(
  formData: FormData,
): Promise<ActionResponse<RFQ>> {
  try {
    // Convert FormData to object
    const formDataObj = Object.fromEntries(formData.entries());

    // Parse numeric values
    const parsedData = {
      ...formDataObj,
      weight: formDataObj.weight
        ? parseFloat(formDataObj.weight as string)
        : undefined,
      length: formDataObj.length
        ? parseFloat(formDataObj.length as string)
        : undefined,
      width: formDataObj.width
        ? parseFloat(formDataObj.width as string)
        : undefined,
      height: formDataObj.height
        ? parseFloat(formDataObj.height as string)
        : undefined,
      quantity: formDataObj.quantity
        ? parseInt(formDataObj.quantity as string, 10)
        : undefined,
      equipment_quantity: formDataObj.equipment_quantity
        ? parseInt(formDataObj.equipment_quantity as string, 10)
        : undefined,
    };

    // Use centralized validation function
    const validationResult = await validateUpdateRFQ(parsedData);

    if (!validationResult.success) {
      return {
        success: false,
        error: validationResult.error?.message || "Validation failed",
        fieldErrors: validationResult.error?.fieldErrors,
      };
    }

    // Update the RFQ
    const rfq = await updateRFQ(validationResult.data!);

    // Revalidate the RFQs page and the specific RFQ page
    revalidatePath("/dashboard/rfqs");
    revalidatePath(`/dashboard/rfqs/${rfq.id}`);

    return { success: true, data: rfq };
  } catch (error) {
    logger.error("Error in updateRFQAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update RFQ",
    };
  }
}

/**
 * Delete an RFQ
 */
export async function deleteRFQAction(
  id: string,
): Promise<ActionResponse<boolean>> {
  try {
    await deleteRFQ(id);

    // Revalidate the RFQs page
    revalidatePath("/dashboard/rfqs");

    return { success: true, data: true };
  } catch (error) {
    logger.error("Error in deleteRFQAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to delete RFQ",
    };
  }
}

/**
 * Match providers for an RFQ
 */
export async function matchProvidersAction(rfqId: string): Promise<
  ActionResponse<
    {
      id: string;
      name: string;
      status: string;
      verified: boolean;
      full_address?: string | null;
      tax_id?: string | null;
    }[]
  >
> {
  try {
    const providers = await matchProvidersForRFQ(rfqId);

    // Revalidate the RFQ detail page
    revalidatePath(`/dashboard/rfqs/${rfqId}`);

    return { success: true, data: providers };
  } catch (error) {
    logger.error("Error in matchProvidersAction:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to match providers",
    };
  }
}



/**
 * Select providers for an RFQ and update RFQ status to "sent"
 */
export async function selectProvidersAction(
  rfqId: string,
  providerIds: string[],
  emailData?: {
    subject: string;
    body: string;
    isCustom: boolean;
    templateId?: string;
  },
): Promise<ActionResponse<RFQProvider[]>> {
  try {
    logger.info(
      `selectProvidersAction called for RFQ ${rfqId} with providers:`,
      providerIds,
    );

    const timestamp = new Date().toISOString();
    const providersData = providerIds.map((providerId) => ({
      rfq_id: rfqId,
      provider_id: providerId,
      status: "invited" as const, // Change status to "invited" directly
      invited_at: timestamp,
    }));

    logger.debug("Prepared provider data:", providersData);

    // Validate each provider using centralized validation function
    for (const provider of providersData) {
      const validationResult = await validateRFQProvider(provider);

      if (!validationResult.success) {
        logger.error(
          "Validation failed for provider:",
          provider,
          validationResult.error,
        );
        return {
          success: false,
          error: validationResult.error?.message || "Validation failed",
          fieldErrors: validationResult.error?.fieldErrors,
        };
      }
    }

    // Select the providers
    await selectProvidersForRFQ(providersData);

    // Create email records if email data is provided
    if (emailData) {
      // Get current user profile information using the service function
      const userProfile = await getCurrentUserProfile();

      if (userProfile) {
        // Add sender information to email data
        const emailDataWithSender = {
          ...emailData,
          senderInfo: {
            name: userProfile.name || "",
            phone: userProfile.phone_number || "",
          }
        };

        await createRFQEmails(rfqId, providerIds, emailDataWithSender);
      } else {
        // If no user profile is found, proceed without sender information
        await createRFQEmails(rfqId, providerIds, emailData);
      }
    }

    // Update the RFQ status to "sent"
    await updateRFQ({
      id: rfqId,
      status: "sent",
      updated_at: new Date().toISOString(),
    });

    // Fetch the complete provider data with provider details
    logger.debug("Fetching complete RFQ provider data...");
    const completeProviders = await fetchRFQProviders(rfqId);

    // Revalidate the RFQ providers page and the RFQ detail page
    revalidatePath(`/dashboard/rfqs/${rfqId}`);
    revalidatePath(`/dashboard/rfqs`);

    return { success: true, data: completeProviders };
  } catch (error) {
    logger.error("Error in selectProvidersAction:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to select providers",
    };
  }
}

/**
 * Get providers for an RFQ
 */
export async function getRFQProvidersAction(
  rfqId: string,
): Promise<ActionResponse<RFQProvider[]>> {
  try {
    const providers = await fetchRFQProviders(rfqId);
    return { success: true, data: providers };
  } catch (error) {
    logger.error("Error in getRFQProvidersAction:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch RFQ providers",
    };
  }
}

/**
 * Get providers for an RFQ with pagination
 */
export async function getRFQProvidersWithPaginationAction(
  rfqId: string,
  params: { page: number; pageSize: number },
  filterParams?: { searchQuery?: string; statusFilter?: string },
): Promise<
  ActionResponse<{
    data: RFQProvider[];
    count: number;
    pagination: {
      page: number;
      pageSize: number;
      totalItems: number;
      totalPages: number;
    };
  }>
> {
  try {
    logger.info("getRFQProvidersWithPaginationAction called with:", {
      rfqId,
      params,
      filterParams,
    });
    const result = await fetchRFQProvidersWithPagination(
      rfqId,
      params,
      filterParams,
    );
    return { success: true, data: result };
  } catch (error) {
    logger.error("Error in getRFQProvidersWithPaginationAction:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch paginated RFQ providers",
    };
  }
}

/**
 * Submit a bid for an RFQ
 */
export async function submitBidAction(
  formData: FormData,
): Promise<ActionResponse<any>> {
  try {
    // Log the raw form data for debugging
    logger.debug(
      "Raw form data in submitBidAction:",
      Object.fromEntries(formData.entries()),
    );

    // Convert FormData to object
    const formDataObj = Object.fromEntries(formData.entries());

    // Parse numeric values and ensure all required fields are present
    const parsedData: Record<string, any> = {
      ...formDataObj,
      price: formDataObj.price
        ? parseFloat(formDataObj.price as string)
        : undefined,
      transit_time_days: formDataObj.transit_time_days
        ? parseInt(formDataObj.transit_time_days as string, 10)
        : undefined,
      validity_period_days: formDataObj.validity_period_days
        ? parseInt(formDataObj.validity_period_days as string, 10)
        : undefined,
      // Parse boolean values
      is_ai_extracted: formDataObj.is_ai_extracted === "true",
    };

    // Ensure submitted_at is present
    if (!parsedData.submitted_at) {
      parsedData.submitted_at = new Date().toISOString();
    }

    logger.debug("Parsed data before validation:", parsedData);

    // Use centralized validation function
    const validationResult = await validateRFQBid(parsedData);

    if (!validationResult.success) {
      logger.error(
        "Validation failed:",
        JSON.stringify(validationResult.error, null, 2),
      );

      // Log detailed field errors for debugging
      if (validationResult.error?.fieldErrors) {
        Object.entries(validationResult.error.fieldErrors).forEach(
          ([field, errors]) => {
            logger.error(`Field '${field}' validation errors:`, errors);
          },
        );
      }

      return {
        success: false,
        error: validationResult.error?.message || "Validation failed",
        fieldErrors: validationResult.error?.fieldErrors,
      };
    }

    logger.debug("Validated data:", validationResult.data);

    // Submit the bid
    const bid = await submitRFQBid(validationResult.data!);

    // Revalidate the RFQ bids page
    revalidatePath(`/dashboard/rfqs/${bid.rfq_id}/bids`);

    return { success: true, data: bid };
  } catch (error) {
    logger.error("Error in submitBidAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to submit bid",
    };
  }
}

/**
 * Get bids for an RFQ
 */
export async function getRFQBidsAction(
  rfqId: string,
): Promise<ActionResponse<RFQBid[]>> {
  try {
    const bids = await fetchRFQBids(rfqId);
    return { success: true, data: bids };
  } catch (error) {
    logger.error("Error in getRFQBidsAction:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to fetch RFQ bids",
    };
  }
}

/**
 * Get a specific bid for an RFQ by provider ID
 */
export async function getRFQBidByProviderAction(
  rfqId: string,
  providerId: string,
): Promise<ActionResponse<RFQBid[]>> {
  try {
    logger.info(`Fetching bid for RFQ: ${rfqId}, Provider: ${providerId}`);
    const bids = await fetchRFQBids(rfqId, providerId);
    return { success: true, data: bids };
  } catch (error) {
    logger.error("Error in getRFQBidByProviderAction:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch RFQ bid for provider",
    };
  }
}

/**
 * Fetches outgoing emails for an RFQ
 */
export async function getRFQEmailsAction(rfqId: string): Promise<
  ActionResponse<
    {
      id: string;
      rfq_id: string;
      provider_id: string;
      subject: string;
      body: string;
      sent_at: string;
      status: string;
      provider?: { name: string };
    }[]
  >
> {
  try {
    logger.debug(`getRFQEmailsAction called for RFQ ${rfqId}`);

    const emails = await fetchRFQEmails(rfqId);

    return { success: true, data: emails };
  } catch (error) {
    logger.error("Error in getRFQEmailsAction:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to fetch RFQ emails",
    };
  }
}

/**
 * Update a bid for an RFQ
 */
export async function updateBidAction(
  formData: FormData,
): Promise<ActionResponse<RFQBid>> {
  try {
    logger.debug("updateBidAction called with formData");

    // Convert FormData to object
    const formDataObj = Object.fromEntries(formData.entries());

    // Parse numeric values
    const parsedData = {
      ...formDataObj,
      price: formDataObj.price
        ? parseFloat(formDataObj.price as string)
        : undefined,
    };

    logger.debug("Parsed data:", parsedData);

    // Use centralized validation function
    const validationResult = await validateUpdateRFQBid(parsedData);

    if (!validationResult.success) {
      logger.error("Validation failed:", validationResult.error);
      return {
        success: false,
        error: validationResult.error?.message || "Validation failed",
        fieldErrors: validationResult.error?.fieldErrors,
      };
    }

    // Update the bid
    const bid = await updateRFQBid(validationResult.data!);

    // Revalidate the RFQ bids page
    revalidatePath(`/dashboard/rfqs/${bid.rfq_id}/bids`);
    revalidatePath(`/dashboard/rfqs/${bid.rfq_id}`);

    return { success: true, data: bid };
  } catch (error) {
    logger.error("Error in updateBidAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update bid",
    };
  }
}

/**
 * Merges RFQ providers with their bids
 *
 * This action wraps the service function to maintain the service-action layer architecture
 */
export async function mergeProvidersWithBidsAction<
  T extends { provider_id: string },
  B extends { provider_id: string },
>(providers: T[], bids: B[]): Promise<ActionResponse<(T & { bid?: B })[]>> {
  try {
    logger.debug("mergeProvidersWithBidsAction called");
    const result = await mergeProvidersWithBidsService(providers, bids);
    return { success: true, data: result };
  } catch (error) {
    logger.error("Error in mergeProvidersWithBidsAction:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to merge providers with bids",
    };
  }
}

/**
 * Provider selection form action
 *
 * This action handles the provider selection form submission using the new React 19 pattern.
 * It returns a structured response for useActionState.
 */
export async function providerSelectionAction(
  initialState: ProviderSelectionActionState,
  formData: FormData,
): Promise<ProviderSelectionActionState> {
  try {
    logger.debug("providerSelectionAction called");

    // Extract form data
    const rawData = {
      searchQuery: formData.get("searchQuery")?.toString() || "",
      statusFilter: formData.get("statusFilter")?.toString() || "all",
      selectedProviders: formData
        .getAll("selectedProviders")
        .map((p) => p.toString()),
      page: Number(formData.get("page") || initialState.values.page),
      pageSize: Number(
        formData.get("pageSize") || initialState.values.pageSize,
      ),
    };

    // Validate form data
    const validationResult = ProviderSelectionFormSchema.safeParse(rawData);

    if (!validationResult.success) {
      const errors: Record<string, { message: string }> = {};

      validationResult.error.errors.forEach((err) => {
        const path = err.path.join(".");
        errors[path] = { message: err.message };
      });

      return {
        success: false,
        message: "Validation failed",
        errors,
        values: initialState.values,
      };
    }

    const validData = validationResult.data;

    // Fetch providers with pagination
    const rfqId = formData.get("rfqId")?.toString() || "";
    if (!rfqId) {
      return {
        success: false,
        message: "RFQ ID is required",
        values: validData,
      };
    }

    const paginationResult = await fetchRFQProvidersWithPagination(
      rfqId,
      { page: validData.page, pageSize: validData.pageSize },
      {
        searchQuery: validData.searchQuery,
        statusFilter: validData.statusFilter,
      },
    );

    return {
      success: true,
      values: validData,
      providers: paginationResult.data,
      totalCount: paginationResult.count,
    };
  } catch (error) {
    logger.error("Error in providerSelectionAction:", error);
    return {
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to process provider selection",
      values: initialState.values,
    };
  }
}

/**
 * Send RFQ to selected providers action
 *
 * This action handles sending an RFQ to selected providers using the new React 19 pattern.
 * It returns a structured response for useActionState.
 */
export async function sendRFQAction(
  initialState: ProviderSelectionActionState,
  formData: FormData,
): Promise<ProviderSelectionActionState> {
  try {
    logger.debug("sendRFQAction called");

    // Extract form data
    const rfqId = formData.get("rfqId")?.toString() || "";
    const selectedProviders = formData
      .getAll("selectedProviders")
      .map((p) => p.toString());
    const emailData = {
      subject: formData.get("subject")?.toString() || "",
      body: formData.get("body")?.toString() || "",
      isCustom: formData.get("isCustom") === "true",
      templateId: formData.get("templateId")?.toString(),
    };

    // Validate RFQ ID
    if (!rfqId) {
      return {
        success: false,
        message: "RFQ ID is required",
        values: initialState.values,
      };
    }

    // Validate selected providers
    if (selectedProviders.length === 0) {
      return {
        success: false,
        message: "Please select at least one provider",
        values: initialState.values,
      };
    }

    // Validate email data
    const emailValidation = EmailDataSchema.safeParse(emailData);
    if (!emailValidation.success) {
      const errors: Record<string, { message: string }> = {};

      emailValidation.error.errors.forEach((err) => {
        const path = err.path.join(".");
        errors[path] = { message: err.message };
      });

      return {
        success: false,
        message: "Email validation failed",
        errors,
        values: initialState.values,
      };
    }

    // Send RFQ to selected providers
    const result = await selectProvidersAction(
      rfqId,
      selectedProviders,
      emailValidation.data,
    );

    // Revalidate the RFQ providers page and the RFQ detail page
    revalidatePath(`/dashboard/rfqs/${rfqId}`);
    revalidatePath(`/dashboard/rfqs`);

    return {
      success: true,
      message: `RFQ sent to ${selectedProviders.length} providers`,
      values: {
        ...initialState.values,
        selectedProviders: [],
      },
      rfqProviders: result.success ? result.data : [],
    };
  } catch (error) {
    logger.error("Error in sendRFQAction:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to send RFQ",
      values: initialState.values,
    };
  }
}

/**
 * Filters providers based on search query and status filter
 *
 * This action wraps the service function to maintain the service-action layer architecture
 */
export async function filterProvidersAction<
  T extends {
    id: string;
    name: string;
    status: string;
    verified: boolean;
    tax_id?: string | null;
  },
>(
  providers: T[],
  rfqProviders: {
    provider_id: string;
    status: string;
    invited_at?: string | null;
  }[],
  searchQuery: string,
  statusFilter: string,
): Promise<ActionResponse<T[]>> {
  try {
    logger.debug("filterProvidersAction called");
    const result = await filterProvidersService(
      providers,
      rfqProviders,
      searchQuery,
      statusFilter,
    );
    return { success: true, data: result };
  } catch (error) {
    logger.error("Error in filterProvidersAction:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to filter providers",
    };
  }
}

/**
 * Gets invited providers for an RFQ
 *
 * This action wraps the service function to maintain the service-action layer architecture
 */
export async function getInvitedProvidersAction(
  rfqProviders: RFQProvider[],
): Promise<ActionResponse<RFQProvider[]>> {
  try {
    logger.debug("getInvitedProvidersAction called");
    const result = await getInvitedProvidersService(rfqProviders);
    return { success: true, data: result };
  } catch (error) {
    logger.error("Error in getInvitedProvidersAction:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to get invited providers",
    };
  }
}

/**
 * Check if a provider has been invited for an RFQ
 *
 * This action consolidates the business logic for determining provider invitation status.
 * UI components should use this instead of implementing their own logic.
 */
export async function isProviderInvitedAction(
  rfqProviders: {
    provider_id: string;
    status: string;
    invited_at?: string | null;
    bid?: any;
  }[],
  providerId: string,
): Promise<ActionResponse<boolean>> {
  try {
    const invited = await isProviderInvited(rfqProviders, providerId);

    return {
      success: true,
      data: invited,
    };
  } catch (error) {
    console.error("Error checking provider invitation status:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Check if a provider has submitted a bid for an RFQ
 *
 * This action consolidates the business logic for determining bid submission status.
 * UI components should use this instead of implementing their own logic.
 */
export async function hasProviderSubmittedBidAction(
  rfqProviders: {
    provider_id: string;
    status: string;
  }[],
  providerId: string,
): Promise<ActionResponse<boolean>> {
  try {
    const hasBid = await hasProviderSubmittedBid(rfqProviders, providerId);

    return {
      success: true,
      data: hasBid,
    };
  } catch (error) {
    console.error("Error checking provider bid status:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Simplified Provider Selection Action
 *
 * Uses the unified service to fetch all provider selection data.
 * Replaces the complex providerSelectionAction with a cleaner implementation.
 */
export async function getProviderSelectionDataAction(
  rfqId: string,
  options: {
    searchQuery?: string;
    statusFilter?: string;
    includeManualProviders?: any[];
  } = {}
): Promise<ActionResponse<{
  availableProviders: any[];
  invitedProviders: any[];
  rfqProviders: any[];
  totalCount: number;
}>> {
  try {
    logger.info("getProviderSelectionDataAction called", { rfqId, options });

    const data = await getProviderSelectionData(rfqId, options);

    return {
      success: true,
      data,
    };
  } catch (error) {
    logger.error("Error in getProviderSelectionDataAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch provider selection data",
    };
  }
}
