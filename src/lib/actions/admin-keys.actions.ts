"use server";

import { revalidate<PERSON>ath } from "next/cache";
import {
  createUser<PERSON><PERSON><PERSON><PERSON>,
  revokeUser<PERSON><PERSON><PERSON><PERSON>,
  listAllApi<PERSON><PERSON>s,
  getApiKeyDetails,
  type ApiKeyInfo,
} from "@/lib/services/api-key.service";
import { getCurrentUser, isAdmin } from "@/lib/services/auth.service";
import { createLogger } from "@/lib/utils/logger/logger";
import { ActionResponse } from "@/lib/schemas";
import { z } from "zod";

// Create a logger instance for this module
const logger = createLogger("AdminKeysActions");

// Schema for creating a new API key
const createApiKeySchema = z.object({
  name: z.string().min(1, "Name is required"),
  permissions: z.array(z.string()).optional().default([]),
  metadata: z.record(z.any()).optional().default({}),
});

/**
 * Lists all API keys (admin only)
 */
export async function listApiKeysAction(): Promise<
  ActionResponse<ApiKeyInfo[]>
> {
  try {
    // Check if the current user has admin role
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: "Not authenticated" };
    }

    // Use the auth service to check if the user has admin role
    const hasAdminRole = await isAdmin();

    if (!hasAdminRole) {
      logger.warn(
        `User ${user.id} attempted to list API keys without admin role`,
      );
      return { success: false, error: "Unauthorized: Admin role required" };
    }

    const keys = await listAllApiKeys();

    if (!keys) {
      return { success: false, error: "Failed to list API keys" };
    }

    return { success: true, data: keys };
  } catch (error) {
    logger.error("Error in listApiKeysAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to list API keys",
    };
  }
}

/**
 * Gets details for a specific API key (admin only)
 */
export async function getApiKeyDetailsAction(
  keyId: string,
): Promise<ActionResponse<ApiKeyInfo>> {
  try {
    // Check if the current user has admin role
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: "Not authenticated" };
    }

    // Use the auth service to check if the user has admin role
    const hasAdminRole = await isAdmin();

    if (!hasAdminRole) {
      logger.warn(
        `User ${user.id} attempted to get API key details without admin role`,
      );
      return { success: false, error: "Unauthorized: Admin role required" };
    }

    const keyInfo = await getApiKeyDetails(keyId);

    if (!keyInfo) {
      return { success: false, error: "Failed to get API key details" };
    }

    return { success: true, data: keyInfo };
  } catch (error) {
    logger.error("Error in getApiKeyDetailsAction:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to get API key details",
    };
  }
}

/**
 * Creates a new API key (admin only)
 */
export async function createApiKeyAction(
  formData:
    | FormData
    | { name: string; permissions?: string[]; metadata?: Record<string, any> },
): Promise<ActionResponse<{ key: string; keyId: string }>> {
  try {
    // Check if the current user has admin role
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: "Not authenticated" };
    }

    // Use the auth service to check if the user has admin role
    const hasAdminRole = await isAdmin();

    if (!hasAdminRole) {
      logger.warn(
        `User ${user.id} attempted to create API key without admin role`,
      );
      return { success: false, error: "Unauthorized: Admin role required" };
    }

    // Parse and validate input
    let data: z.infer<typeof createApiKeySchema>;

    if (formData instanceof FormData) {
      const name = formData.get("name") as string;
      const permissionsStr = formData.get("permissions") as string;
      const metadataStr = formData.get("metadata") as string;

      const permissions = permissionsStr
        ? permissionsStr.split(",").map((p) => p.trim())
        : [];
      const metadata = metadataStr ? JSON.parse(metadataStr) : {};

      data = { name, permissions, metadata };
    } else {
      data = {
        name: formData.name,
        permissions: formData.permissions || [],
        metadata: formData.metadata || {},
      };
    }

    // Validate with schema
    const result = createApiKeySchema.safeParse(data);
    if (!result.success) {
      const fieldErrors = result.error.flatten().fieldErrors;
      return {
        success: false,
        error: "Validation failed",
        fieldErrors,
      };
    }

    // Create the API key
    const keyData = await createUserApiKey(
      user.id,
      result.data.name,
      result.data.metadata,
      result.data.permissions,
    );

    if (!keyData) {
      return { success: false, error: "Failed to create API key" };
    }

    // Revalidate the admin keys page
    revalidatePath("/admin/keys");

    return { success: true, data: keyData };
  } catch (error) {
    logger.error("Error in createApiKeyAction:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to create API key",
    };
  }
}

/**
 * Revokes an API key (admin only)
 */
export async function revokeApiKeyAction(
  keyId: string,
): Promise<ActionResponse<boolean>> {
  try {
    // Check if the current user has admin role
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: "Not authenticated" };
    }

    // Use the auth service to check if the user has admin role
    const hasAdminRole = await isAdmin();

    if (!hasAdminRole) {
      logger.warn(
        `User ${user.id} attempted to revoke API key without admin role`,
      );
      return { success: false, error: "Unauthorized: Admin role required" };
    }

    const success = await revokeUserApiKey(keyId);

    if (!success) {
      return { success: false, error: "Failed to revoke API key" };
    }

    // Revalidate the admin keys page
    revalidatePath("/admin/keys");

    return { success: true, data: true };
  } catch (error) {
    logger.error("Error in revokeApiKeyAction:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to revoke API key",
    };
  }
}
