"use server";

import { createLogger } from "@/lib/utils/logger/logger";
import {
  sendEmail as sendEmailService,
  sendRFQEmail as sendRFQEmailService,
  EmailData,
  RFQEmailData,
} from "@/lib/services/email/email.service";

// Create a logger instance for this module
const logger = createLogger("EmailActions");

// Define response type for actions
type ActionResponse<T> =
  | { success: true; data: T }
  | { success: false; error: string; fieldErrors?: Record<string, string[]> };

/**
 * Send a single email using the service layer
 * @param emailData The email data to send
 * @returns Action response with success status and result or error
 */
export async function sendEmailAction(
  emailData: EmailData,
): Promise<ActionResponse<any>> {
  try {
    logger.info(
      `Sending email to ${emailData.to.email} with subject "${emailData.subject}"`,
    );

    const result = await sendEmailService(emailData);

    return { success: true, data: result };
  } catch (error) {
    logger.error("Error in sendEmailAction:", error);

    // Check if the error is related to email settings
    if (error instanceof Error &&
        (error.message.includes("Email settings are not configured") ||
         error.message.includes("Email settings are incomplete"))) {
      // Return a more descriptive error message for email settings issues
      return {
        success: false,
        error: `${error.message} An administrator must configure email settings in the Admin Dashboard before emails can be sent.`,
      };
    }

    // For other errors, return the standard error message
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to send email",
    };
  }
}

/**
 * Send an RFQ email with proper formatting using the service layer
 * @param emailData The RFQ email data to send
 * @returns Action response with success status and result or error
 */
export async function sendRFQEmailAction(
  emailData: RFQEmailData,
): Promise<ActionResponse<any>> {
  try {
    logger.info(
      `Sending RFQ email to ${emailData.to.email} with subject "${emailData.subject}"`,
    );

    const result = await sendRFQEmailService(emailData);

    return { success: true, data: result };
  } catch (error) {
    logger.error("Error in sendRFQEmailAction:", error);

    // Check if the error is related to email settings
    if (error instanceof Error &&
        (error.message.includes("Email settings are not configured") ||
         error.message.includes("Email settings are incomplete"))) {
      // Return a more descriptive error message for email settings issues
      return {
        success: false,
        error: `${error.message} An administrator must configure email settings in the Admin Dashboard before emails can be sent.`,
      };
    }

    // For other errors, return the standard error message
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to send RFQ email",
    };
  }
}

/**
 * Send multiple emails at once using the service layer
 * @param emailsData Array of email data to send
 * @returns Action response with success status and results or error
 */
export async function sendBulkEmailsAction(
  emailsData: EmailData[],
): Promise<ActionResponse<any[]>> {
  try {
    logger.info(`Sending ${emailsData.length} emails in bulk`);

    // Process each email individually
    const promises = emailsData.map((emailData) => sendEmailAction(emailData));
    const responses = await Promise.all(promises);

    // Check if any email failed to send
    const failedEmails = responses.filter((response) => !response.success);

    if (failedEmails.length > 0) {
      logger.warn(
        `${failedEmails.length} out of ${emailsData.length} emails failed to send`,
      );
      return {
        success: false,
        error: `Failed to send ${failedEmails.length} out of ${emailsData.length} emails`,
      };
    }

    logger.info(`Successfully sent ${responses.length} emails in bulk`);
    return {
      success: true,
      data: responses.map((response) =>
        response.success ? response.data : null,
      ),
    };
  } catch (error) {
    logger.error("Error in sendBulkEmailsAction:", error);

    // Check if the error is related to email settings
    if (error instanceof Error &&
        (error.message.includes("Email settings are not configured") ||
         error.message.includes("Email settings are incomplete"))) {
      // Return a more descriptive error message for email settings issues
      return {
        success: false,
        error: `${error.message} An administrator must configure email settings in the Admin Dashboard before emails can be sent.`,
      };
    }

    // For other errors, return the standard error message
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to send bulk emails",
    };
  }
}
