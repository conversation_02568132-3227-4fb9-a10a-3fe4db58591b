"use server";

import { revalidate<PERSON>ath } from "next/cache";
import {
  fetchEquipment,
  addEquipment,
  modifyEquipment,
  removeEquipment,
  linkEquipmentToProvider,
  unlinkEquipmentFromProvider,
  getProviderEquipment as getProviderEquipmentService,
} from "@/lib/services/equipment.service";
import {
  CreateEquipmentTypeSchema,
  UpdateEquipmentTypeSchema,
  DeleteEquipmentTypeParamsSchema,
  EquipmentProviderLinkSchema,
  EquipmentProviderUnlinkSchema,
  type EquipmentType,
} from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";
import { ActionState } from "@/lib/utils/actions";

const logger = createLogger("EquipmentActions");

// --- GET ALL --- //
export async function getEquipment(): Promise<{
  data: EquipmentType[];
  error: string | null;
}> {
  try {
    const result = await fetchEquipment({ page: 1, pageSize: 100 });
    return { data: result.data as EquipmentType[], error: null };
  } catch (error) {
    logger.error("Error fetching equipment:", error);
    const message =
      error instanceof Error ? error.message : "Failed to fetch equipment.";
    return { data: [], error: message };
  }
}

// --- GET BY ID --- //
export async function getEquipmentById(
  id: string,
): Promise<{ data: EquipmentType | null; error: string | null }> {
  try {
    const validation = DeleteEquipmentTypeParamsSchema.safeParse({ id });
    if (!validation.success) {
      return { data: null, error: "Invalid equipment ID." };
    }

    const result = await fetchEquipment({ id, page: 1, pageSize: 1 });
    return { data: result.data as EquipmentType, error: null };
  } catch (error) {
    logger.error("Error fetching equipment:", error);
    const message =
      error instanceof Error ? error.message : "Failed to fetch equipment.";
    return { data: null, error: message };
  }
}

// --- GET BY CATEGORY --- //
export async function getEquipmentByCategory(
  category: string,
): Promise<{ data: EquipmentType[]; error: string | null }> {
  try {
    if (!category) {
      return { data: [], error: "Category is required." };
    }

    const result = await fetchEquipment({ category, page: 1, pageSize: 100 });
    return { data: result.data as EquipmentType[], error: null };
  } catch (error) {
    logger.error("Error fetching equipment by category:", error);
    const message =
      error instanceof Error ? error.message : "Failed to fetch equipment.";
    return { data: [], error: message };
  }
}

// --- CREATE --- //
export async function createEquipment(
  prevState: ActionState | undefined,
  formData: FormData,
): Promise<ActionState> {
  const validatedFields = CreateEquipmentTypeSchema.safeParse({
    name: formData.get("name"),
    category: formData.get("category"),
    description: formData.get("description"),
  });

  if (!validatedFields.success) {
    return {
      message: "Validation failed.",
      errors: validatedFields.error.flatten(),
      success: false,
    };
  }

  try {
    await addEquipment(validatedFields.data);
    revalidatePath("/dashboard/equipment");
    return { message: "Equipment created successfully.", success: true };
  } catch (error) {
    logger.error("Error creating equipment:", error);
    const message =
      error instanceof Error ? error.message : "Failed to create equipment.";
    return { message, success: false };
  }
}

// --- UPDATE --- //
export async function updateEquipment(
  prevState: ActionState | undefined,
  formData: FormData,
): Promise<ActionState> {
  const id = formData.get("id") as string;

  if (!id) {
    return { message: "Error: Missing ID for update.", success: false };
  }

  const validatedFields = UpdateEquipmentTypeSchema.safeParse({
    id,
    name: formData.get("name"),
    category: formData.get("category"),
    description: formData.get("description"),
  });

  if (!validatedFields.success) {
    return {
      message: "Validation failed.",
      errors: validatedFields.error.flatten(),
      success: false,
    };
  }

  try {
    await modifyEquipment(validatedFields.data);
    revalidatePath("/dashboard/equipment");
    return { message: "Equipment updated successfully.", success: true };
  } catch (error) {
    logger.error("Error updating equipment:", error);
    const message =
      error instanceof Error ? error.message : "Failed to update equipment.";
    return { message, success: false };
  }
}

// --- DELETE --- //
export async function deleteEquipment(id: string): Promise<ActionState> {
  if (!id) {
    return { message: "Error: Missing ID for delete.", success: false };
  }

  const validation = DeleteEquipmentTypeParamsSchema.safeParse({ id });
  if (!validation.success) {
    return {
      message: "Invalid equipment ID.",
      errors: validation.error.flatten(),
      success: false,
    };
  }

  try {
    await removeEquipment(validation.data);
    revalidatePath("/dashboard/equipment");
    return { message: "Equipment deleted successfully.", success: true };
  } catch (error) {
    logger.error("Error deleting equipment:", error);
    const message =
      error instanceof Error ? error.message : "Failed to delete equipment.";
    return { message, success: false };
  }
}

// --- Provider-Equipment Relationships --- //

// Add equipment to provider
export async function addEquipmentToProvider(
  providerId: string,
  equipmentId: string,
): Promise<ActionState> {
  if (!providerId || !equipmentId) {
    return { message: "Error: Missing required IDs.", success: false };
  }

  const validation = EquipmentProviderLinkSchema.safeParse({
    provider_id: providerId,
    equipment_type_id: equipmentId,
  });

  if (!validation.success) {
    return {
      message: "Validation failed.",
      errors: validation.error.flatten(),
      success: false,
    };
  }

  try {
    // Extract only the fields needed by linkEquipmentToProvider
    const { provider_id, equipment_type_id } = validation.data;
    await linkEquipmentToProvider({
      provider_id,
      equipment_type_id
    });
    revalidatePath(`/dashboard/providers/${providerId}`);
    return {
      message: "Equipment added to provider successfully.",
      success: true,
    };
  } catch (error) {
    logger.error("Error adding equipment to provider:", error);
    const message =
      error instanceof Error
        ? error.message
        : "Failed to add equipment to provider.";
    return { message, success: false };
  }
}

// Remove equipment from provider
export async function removeEquipmentFromProvider(
  providerId: string,
  equipmentId: string,
): Promise<ActionState> {
  if (!providerId || !equipmentId) {
    return { message: "Error: Missing required IDs.", success: false };
  }

  const validation = EquipmentProviderUnlinkSchema.safeParse({
    provider_id: providerId,
    equipment_type_id: equipmentId,
  });

  if (!validation.success) {
    return {
      message: "Validation failed.",
      errors: validation.error.flatten(),
      success: false,
    };
  }

  try {
    await unlinkEquipmentFromProvider(validation.data);
    revalidatePath(`/dashboard/providers/${providerId}`);
    return {
      message: "Equipment removed from provider successfully.",
      success: true,
    };
  } catch (error) {
    logger.error("Error removing equipment from provider:", error);
    const message =
      error instanceof Error
        ? error.message
        : "Failed to remove equipment from provider.";
    return { message, success: false };
  }
}

// Get equipment for a provider
export async function getProviderEquipment(providerId: string): Promise<{
  data: Array<EquipmentType>;
  error: string | null;
}> {
  if (!providerId) {
    return { data: [], error: "Missing provider ID." };
  }

  try {
    const result = await getProviderEquipmentService(providerId);
    return {
      data: result.data,
      error: null,
    };
  } catch (error) {
    logger.error("Error fetching provider equipment:", error);
    const message =
      error instanceof Error
        ? error.message
        : "Failed to fetch provider equipment.";
    return { data: [], error: message };
  }
}
