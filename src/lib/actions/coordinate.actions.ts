"use server";

import { revalidatePath } from "next/cache";
import { createLogger } from "@/lib/utils/logger/logger";
import { ActionResponse } from "@/lib/schemas";
import {
  resolveCoordinates,
  batchResolveCoordinates,
  generateAddressHash,
  detectAddress<PERSON>hange,
  validateCoordinates,
} from "@/lib/services/google-maps/coordinate-resolution.service";
import type {
  CoordinateResolutionResult,
  AddressBatchInput,
  BatchCoordinateResolutionResult,
  AddressChangeDetection,
} from "@/lib/services/google-maps/types";

const logger = createLogger("CoordinateActions");

/**
 * Server Action for manual coordinate resolution
 * Resolves coordinates for a single address using Google Maps Geocoding API
 */
export async function resolveCoordinatesAction(
  address: string,
  countryCode?: string,
): Promise<ActionResponse<CoordinateResolutionResult>> {
  try {
    logger.info(`Resolving coordinates for address: ${address}`);

    if (!address || address.trim().length === 0) {
      return {
        success: false,
        error: "Address is required for coordinate resolution",
        fieldErrors: { address: ["Address cannot be empty"] },
      };
    }

    const result = await resolveCoordinates(address.trim(), countryCode);

    if (result.success) {
      logger.info(`Successfully resolved coordinates for address: ${address}`);
    } else {
      logger.warn(`Failed to resolve coordinates for address: ${address} - ${result.error}`);
    }

    return { success: true, data: result };
  } catch (error) {
    logger.error("Error in resolveCoordinatesAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to resolve coordinates",
    };
  }
}

/**
 * Server Action for batch coordinate resolution
 * Resolves coordinates for multiple addresses concurrently
 */
export async function batchResolveCoordinatesAction(
  addresses: AddressBatchInput[],
  options?: {
    concurrency?: number;
    batchDelay?: number;
  },
): Promise<ActionResponse<BatchCoordinateResolutionResult>> {
  try {
    logger.info(`Starting batch coordinate resolution for ${addresses.length} addresses`);

    if (!addresses || addresses.length === 0) {
      return {
        success: false,
        error: "At least one address is required for batch coordinate resolution",
        fieldErrors: { addresses: ["Addresses array cannot be empty"] },
      };
    }

    // Validate input addresses
    for (let i = 0; i < addresses.length; i++) {
      const addressInput = addresses[i];
      if (!addressInput.id || !addressInput.address || addressInput.address.trim().length === 0) {
        return {
          success: false,
          error: `Invalid address at index ${i}: ID and address are required`,
          fieldErrors: { [`addresses.${i}`]: ["ID and address are required"] },
        };
      }
    }

    const result = await batchResolveCoordinates(addresses, options);

    logger.info(
      `Batch coordinate resolution completed: ${result.successfulResolutions}/${result.totalProcessed} successful`,
    );

    return { success: true, data: result };
  } catch (error) {
    logger.error("Error in batchResolveCoordinatesAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to resolve coordinates in batch",
    };
  }
}

/**
 * Server Action for address change detection
 * Generates hash for address components and detects changes
 */
export async function detectAddressChangeAction(
  addressComponents: {
    originCity?: string;
    originAddress?: string;
    originPostalCode?: string;
    destinationCity?: string;
    destinationAddress?: string;
    destinationPostalCode?: string;
  },
  previousHash?: string,
): Promise<ActionResponse<AddressChangeDetection>> {
  try {
    logger.debug("Detecting address changes");

    const result = detectAddressChange(addressComponents, previousHash);

    logger.debug(`Address change detection result: ${result.hasChanged ? "changed" : "unchanged"}`);

    return { success: true, data: result };
  } catch (error) {
    logger.error("Error in detectAddressChangeAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to detect address changes",
    };
  }
}

/**
 * Server Action for generating address hash
 * Generates SHA-256 hash for address components
 */
export async function generateAddressHashAction(
  addressComponents: {
    originCity?: string;
    originAddress?: string;
    originPostalCode?: string;
    destinationCity?: string;
    destinationAddress?: string;
    destinationPostalCode?: string;
  },
): Promise<ActionResponse<string>> {
  try {
    logger.debug("Generating address hash");

    const hash = generateAddressHash(addressComponents);

    logger.debug(`Generated address hash: ${hash.substring(0, 8)}...`);

    return { success: true, data: hash };
  } catch (error) {
    logger.error("Error in generateAddressHashAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to generate address hash",
    };
  }
}

/**
 * Server Action for coordinate validation
 * Validates latitude and longitude values
 */
export async function validateCoordinatesAction(
  lat: number,
  lng: number,
): Promise<ActionResponse<boolean>> {
  try {
    logger.debug(`Validating coordinates: ${lat}, ${lng}`);

    const isValid = validateCoordinates(lat, lng);

    logger.debug(`Coordinate validation result: ${isValid ? "valid" : "invalid"}`);

    return { success: true, data: isValid };
  } catch (error) {
    logger.error("Error in validateCoordinatesAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to validate coordinates",
    };
  }
}

/**
 * Server Action for geocoding with address normalization
 * Resolves coordinates with enhanced address normalization and validation
 */
export async function geocodeAddressAction(
  formData: FormData,
): Promise<ActionResponse<CoordinateResolutionResult>> {
  try {
    const address = formData.get("address") as string;
    const countryCode = formData.get("countryCode") as string;

    logger.info(`Geocoding address from form: ${address}`);

    if (!address || address.trim().length === 0) {
      return {
        success: false,
        error: "Address is required",
        fieldErrors: { address: ["Address cannot be empty"] },
      };
    }

    const result = await resolveCoordinates(address.trim(), countryCode || undefined);

    // Revalidate any pages that might display coordinate data
    revalidatePath("/dashboard/rfqs");

    return { success: true, data: result };
  } catch (error) {
    logger.error("Error in geocodeAddressAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to geocode address",
    };
  }
}
