"use server";

import { revalidatePath } from "next/cache";
import {
  fetchProviderRoutes,
  addProviderRoute,
  updateProviderRoute,
  deleteProviderRoute,
} from "@/lib/services/provider-routes.service";
import { validateWithSchema } from "@/lib/utils/validation";
import {
  CreateProviderRouteInput,
  UpdateProviderRouteInput,
  DeleteProviderRouteParams,
  GetProviderRoutesParams,
  CreateProviderRouteSchema,
  UpdateProviderRouteSchema,
  DeleteProviderRouteParamsSchema,
  GetProviderRoutesParamsSchema,
  ActionResponse,
} from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("ProviderRoutesActions");

/**
 * Get all routes for a provider with pagination support
 */
export async function getProviderRoutesAction(
  params: GetProviderRoutesParams,
): Promise<ActionResponse<any>> {
  try {
    // Ensure page and pageSize are defined with defaults
    const paramsWithDefaults = {
      ...params,
      page: params.page || 1,
      pageSize: params.pageSize || 10,
    };

    // Validate params
    const validationResult = validateWithSchema(
      paramsWithDefaults,
      GetProviderRoutesParamsSchema,
    );

    if (!validationResult.success) {
      logger.error(
        "Provider routes params validation failed:",
        validationResult.error,
      );
      return {
        success: false,
        error: validationResult.error?.message || "Validation failed",
        fieldErrors: validationResult.error?.fieldErrors,
      };
    }

    // Fetch routes with pagination
    // Ensure the validated data has required properties with proper types
    const validatedData = validationResult.data! as Required<GetProviderRoutesParams>;
    const result = await fetchProviderRoutes(validatedData);

    // Return the data with pagination metadata
    return {
      success: true,
      data: result.data,
      pagination: result.pagination,
      count: result.count,
    };
  } catch (error) {
    logger.error("Error in getProviderRoutesAction:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch provider routes",
    };
  }
}

/**
 * Create a new provider route
 */
export async function createProviderRouteAction(
  input: CreateProviderRouteInput,
): Promise<ActionResponse<any>> {
  try {
    // Validate input
    const validationResult = validateWithSchema(
      input,
      CreateProviderRouteSchema,
    );

    if (!validationResult.success) {
      logger.error(
        "Provider route creation validation failed:",
        validationResult.error,
      );
      return {
        success: false,
        error: validationResult.error?.message || "Validation failed",
        fieldErrors: validationResult.error?.fieldErrors,
      };
    }

    // Ensure is_active is always defined
    const validatedData = validationResult.data!;
    const routeData = {
      ...validatedData,
      is_active:
        validatedData.is_active === undefined ? true : validatedData.is_active,
    };

    const newRoute = await addProviderRoute(routeData);

    // Revalidate paths
    revalidatePath("/dashboard/providers");
    revalidatePath(`/dashboard/providers/${routeData.provider_id}`);

    return { success: true, data: newRoute };
  } catch (error) {
    logger.error("Error in createProviderRouteAction:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to create provider route",
    };
  }
}

/**
 * Update an existing provider route
 */
export async function updateProviderRouteAction(
  id: string,
  providerId: string,
  input: Omit<UpdateProviderRouteInput, "id" | "provider_id">,
): Promise<ActionResponse<any>> {
  try {
    // Create complete input with id and provider_id
    const completeInput = {
      id,
      provider_id: providerId,
      ...input,
    };

    // Validate input
    const validationResult = validateWithSchema(
      completeInput,
      UpdateProviderRouteSchema,
    );

    if (!validationResult.success) {
      logger.error(
        "Provider route update validation failed:",
        validationResult.error,
      );
      return {
        success: false,
        error: validationResult.error?.message || "Validation failed",
        fieldErrors: validationResult.error?.fieldErrors,
      };
    }

    const updatedRoute = await updateProviderRoute(validationResult.data!);

    // Revalidate paths
    revalidatePath("/dashboard/providers");
    revalidatePath(`/dashboard/providers/${providerId}`);

    return { success: true, data: updatedRoute };
  } catch (error) {
    logger.error("Error in updateProviderRouteAction:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to update provider route",
    };
  }
}

/**
 * Delete a provider route
 */
export async function deleteProviderRouteAction(
  id: string,
  providerId: string,
): Promise<ActionResponse<any>> {
  try {
    // Create params object
    const params = { id, provider_id: providerId };

    // Validate params
    const validationResult = validateWithSchema(
      params,
      DeleteProviderRouteParamsSchema,
    );

    if (!validationResult.success) {
      logger.error(
        "Provider route deletion validation failed:",
        validationResult.error,
      );
      return {
        success: false,
        error: validationResult.error?.message || "Validation failed",
        fieldErrors: validationResult.error?.fieldErrors,
      };
    }

    const result = await deleteProviderRoute(validationResult.data!);

    // Revalidate paths
    revalidatePath("/dashboard/providers");
    revalidatePath(`/dashboard/providers/${providerId}`);

    return { success: true, data: result };
  } catch (error) {
    logger.error("Error in deleteProviderRouteAction:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to delete provider route",
    };
  }
}
