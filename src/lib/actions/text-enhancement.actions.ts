"use server";

import { enhanceText } from "@/lib/services/text-enhancement.service";
import { EnhancementType } from "@/lib/types/text-enhancement.types";
import { createLogger } from "@/lib/utils/logger/logger";
import { ActionResponse } from "@/lib/schemas";

// Create a logger instance for this module
const logger = createLogger("TextEnhancementActions");

/**
 * Server action to enhance text using Google's Gemini AI
 * @param text The text to enhance
 * @param enhancementType The type of enhancement to apply
 * @returns ActionResponse containing the enhanced text or an error
 */
export async function enhanceTextAction(
  text: string,
  enhancementType: EnhancementType,
): Promise<ActionResponse<string>> {
  try {
    if (!text.trim()) {
      return {
        success: false,
        error: "Text is required",
      };
    }

    logger.debug(`Enhancing text with type: ${enhancementType}`);
    const enhancedText = await enhanceText(text, enhancementType);

    // Check if the enhanced text is significantly different from the original
    if (enhancedText === text) {
      return {
        success: false,
        error:
          "No significant changes were made. Try a different enhancement option.",
      };
    }

    return {
      success: true,
      data: enhancedText,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Error in enhanceTextAction:", errorMessage);

    return {
      success: false,
      error: errorMessage,
    };
  }
}
