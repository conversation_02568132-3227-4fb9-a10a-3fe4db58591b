"use server";

import { revalidatePath } from "next/cache";
import {
  fetchProviderEquipments,
  addProviderEquipments,
  removeProviderEquipment,
} from "@/lib/services/provider-equipments.service";
import { ActionResponse } from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("ProviderEquipmentsActions");

/**
 * Get equipments for a provider
 */
export async function getProviderEquipmentsAction(
  providerId: string,
): Promise<ActionResponse<any>> {
  try {
    const data = await fetchProviderEquipments(providerId);
    return { success: true, data };
  } catch (error) {
    logger.error("Error in getProviderEquipmentsAction:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch provider equipments",
    };
  }
}

/**
 * Add equipments to a provider in bulk
 */
export async function addProviderEquipmentsAction(
  providerId: string,
  equipmentTypeIds: string[],
): Promise<ActionResponse<any>> {
  try {
    // Always call service function even if no equipment type IDs provided
    // This will remove all equipment capabilities if the array is empty
    const result = await addProviderEquipments(providerId, equipmentTypeIds);

    revalidatePath(`/dashboard/providers/${providerId}`);
    revalidatePath(`/dashboard/providers`);

    return { success: true, data: result };
  } catch (error) {
    logger.error("Error in addProviderEquipmentsAction:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to add provider equipments",
    };
  }
}

/**
 * Remove an equipment from a provider
 */
export async function removeProviderEquipmentAction(
  id: string,
  providerId: string,
): Promise<ActionResponse<any>> {
  try {
    const result = await removeProviderEquipment({
      id,
      provider_id: providerId,
    });

    revalidatePath(`/dashboard/providers/${providerId}`);
    revalidatePath(`/dashboard/providers`);

    return { success: true, data: result };
  } catch (error) {
    logger.error("Error in removeProviderEquipmentAction:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to remove provider equipment",
    };
  }
}
