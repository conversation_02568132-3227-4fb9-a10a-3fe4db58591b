"use server";

import { revalidatePath } from "next/cache";
import {
  signInWithPassword,
  signOut,
  getCurrentUserWithProfile,
  getCurrentUser,
  isAdmin,
  checkUserHasRole,
  checkUserHasAllRoles,
  getCurrentUserRoles,
  type LoginResponse,
  type LogoutResponse,
} from "@/lib/services/auth.service";
import { createLogger } from "@/lib/utils/logger/logger";

// Create a logger instance for this module
const logger = createLogger("AuthActions");

// Define response type for actions
type ActionResponse<T> =
  | { success: true; data: T }
  | { success: false; error: string };

/**
 * Server action for user login
 * @param email User's email
 * @param password User's password
 * @returns Action response with success status and user or error
 */
export async function loginAction(
  email: string,
  password: string,
): Promise<ActionResponse<any>> {
  try {
    logger.debug(`Login action called for email: ${email}`);
    const result = await signInWithPassword(email, password);

    if (!result.success) {
      return {
        success: false,
        error: result.error || "Login failed",
      };
    }

    // Revalidate relevant paths
    revalidatePath("/dashboard");

    return {
      success: true,
      data: result.user,
    };
  } catch (error) {
    logger.error("Error in loginAction:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "An unexpected error occurred during login",
    };
  }
}

/**
 * Server action for user logout
 * @returns Action response with success status or error
 */
export async function logoutAction(): Promise<ActionResponse<null>> {
  try {
    logger.debug("Logout action called");
    const result = await signOut();

    if (!result.success) {
      return {
        success: false,
        error: result.error || "Logout failed",
      };
    }

    // Revalidate relevant paths
    revalidatePath("/");

    return {
      success: true,
      data: null,
    };
  } catch (error) {
    logger.error("Error in logoutAction:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "An unexpected error occurred during logout",
    };
  }
}

/**
 * Server action to get the current user with profile data
 * @returns Action response with user data or error
 */
export async function getCurrentUserAction(): Promise<ActionResponse<any>> {
  try {
    logger.debug("Get current user action called");
    const user = await getCurrentUserWithProfile();

    if (!user) {
      return {
        success: false,
        error: "No authenticated user found",
      };
    }

    return {
      success: true,
      data: user,
    };
  } catch (error) {
    logger.error("Error in getCurrentUserAction:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to get current user",
    };
  }
}

/**
 * Server action to check if the current user has admin role
 * @returns Action response with admin status or error
 */
export async function isAdminAction(): Promise<ActionResponse<boolean>> {
  try {
    const hasAdminRole = await isAdmin();

    return {
      success: true,
      data: hasAdminRole,
    };
  } catch (error) {
    logger.error("Error in isAdminAction:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to check admin status",
    };
  }
}

/**
 * Server action to check if the current user has any of the specified roles
 * @param roles Array of role names to check against
 * @returns Action response with role status or error
 */
export async function checkUserHasRoleAction(
  roles: string[],
): Promise<ActionResponse<boolean>> {
  try {
    logger.debug(
      `Check user has role action called for roles: ${roles.join(", ")}`,
    );
    const hasRole = await checkUserHasRole(roles);

    return {
      success: true,
      data: hasRole,
    };
  } catch (error) {
    logger.error("Error in checkUserHasRoleAction:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to check user roles",
    };
  }
}

/**
 * Server action to get all roles for the current user
 * @returns Action response with user roles or error
 */
export async function getUserRolesAction(): Promise<ActionResponse<string[]>> {
  try {
    logger.debug("Get user roles action called");
    const roles = await getCurrentUserRoles();

    return {
      success: true,
      data: roles,
    };
  } catch (error) {
    logger.error("Error in getUserRolesAction:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to get user roles",
    };
  }
}
