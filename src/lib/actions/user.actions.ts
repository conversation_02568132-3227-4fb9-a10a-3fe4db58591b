"use server";

import { revalidatePath } from "next/cache";
import { fetchUsers, getUser, updateUser, getCurrentUserProfile } from "@/lib/services/user.service";
import { GetUsersParams, User, UserProfileFormSchema } from "@/lib/schemas";
import { ActionResponse } from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";
import { validateWithSchema } from "@/lib/utils/validation";

const logger = createLogger("UserActions");

// Using imported ActionResponse type

/**
 * Get all users or filter by ID
 */
export async function getUsersAction(
  params: GetUsersParams = { page: 1, pageSize: 10 },
): Promise<ActionResponse<User[]>> {
  try {
    const users = await fetchUsers(params);
    return { success: true, data: users };
  } catch (error) {
    logger.error("Error in getUsersAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch users",
    };
  }
}

/**
 * Get a single user by ID
 */
export async function getUserAction(id: string): Promise<ActionResponse<User | null>> {
  try {
    const user = await getUser(id);
    return { success: true, data: user };
  } catch (error) {
    logger.error("Error in getUserAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch user",
    };
  }
}

/**
 * Get the current authenticated user's profile
 */
export async function getCurrentUserProfileAction(): Promise<ActionResponse<User | null>> {
  try {
    const user = await getCurrentUserProfile();

    if (!user) {
      return {
        success: false,
        error: "User not authenticated or profile not found",
      };
    }

    return { success: true, data: user };
  } catch (error) {
    logger.error("Error in getCurrentUserProfileAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch user profile",
    };
  }
}

/**
 * Update the current user's profile
 */
export async function updateUserProfileAction(formData: FormData): Promise<ActionResponse<User>> {
  try {
    // Get the current authenticated user
    const currentUser = await getCurrentUserProfile();

    if (!currentUser) {
      return {
        success: false,
        error: "User not authenticated or profile not found",
      };
    }

    // Parse and validate form data
    const rawFormData = Object.fromEntries(formData.entries());

    const validationResult = validateWithSchema(
      {
        name: rawFormData.name,
        phone_number: rawFormData.phone_number || null,
      },
      UserProfileFormSchema,
    );

    if (!validationResult.success) {
      return {
        success: false,
        error: "Invalid form data",
        fieldErrors: validationResult.error?.fieldErrors,
      };
    }

    // Update the user profile
    const updatedUser = await updateUser({
      id: currentUser.id,
      name: validationResult.data!.name,
      phone_number: validationResult.data!.phone_number,
    });

    // Revalidate the profile page
    revalidatePath("/dashboard/settings/profile");

    return {
      success: true,
      data: updatedUser,
    };
  } catch (error) {
    logger.error("Error in updateUserProfileAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update user profile",
    };
  }
}
