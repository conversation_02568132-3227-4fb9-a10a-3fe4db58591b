/**
 * Email Settings Actions
 *
 * Server actions for managing email settings.
 */

"use server";

import { revalidatePath } from "next/cache";
import { createClient } from "@/lib/supabase/server/server";
import { checkRole } from "@/lib/supabase/server/auth";
import {
  EmailSettings,
  EmailSettingsFormSchema,
  EmailSettingsActionState,
} from "@/lib/schemas";
import {
  getEmailSettings,
  updateEmailSettings,
  createEmailSettings,
} from "@/lib/services/email-settings.service";
import { createLogger } from "@/lib/utils/logger/logger";

// Create a logger instance for this module
const logger = createLogger("EmailSettingsActions");

/**
 * Get email settings action
 *
 * Retrieves the current email settings.
 * Only accessible to admin users.
 *
 * @returns ActionResponse with email settings or error
 */
export async function getEmailSettingsAction(): Promise<EmailSettingsActionState> {
  try {
    // Check if user has admin role
    const supabase = await createClient();
    const isAdmin = await checkRole(supabase, ["admin"]);

    if (!isAdmin) {
      return {
        success: false,
        error: "Unauthorized. Admin role required.",
      };
    }

    // Get email settings
    const settings = await getEmailSettings();

    if (!settings) {
      return {
        success: false,
        error: "Email settings not found.",
      };
    }

    return {
      success: true,
      data: settings,
    };
  } catch (error) {
    logger.error("Error in getEmailSettingsAction:", error);
    return {
      success: false,
      error: "Failed to retrieve email settings.",
    };
  }
}

/**
 * Update email settings action
 *
 * Updates the email settings with the provided data.
 * Only accessible to admin users.
 *
 * @param formData Form data containing email settings
 * @returns ActionResponse with updated email settings or error
 */
export async function updateEmailSettingsAction(
  formData: FormData
): Promise<EmailSettingsActionState> {
  try {
    // Check if user has admin role
    const supabase = await createClient();
    const isAdmin = await checkRole(supabase, ["admin"]);

    if (!isAdmin) {
      return {
        success: false,
        error: "Unauthorized. Admin role required.",
      };
    }

    // Get current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return {
        success: false,
        error: "User not authenticated.",
      };
    }

    // Parse form data
    const rawData: Record<string, any> = {};

    // Extract form fields
    for (const [key, value] of formData.entries()) {
      if (key === "sandbox_mode") {
        rawData[key] = value === "true";
      } else {
        rawData[key] = value;
      }
    }

    // Validate form data
    const validationResult = EmailSettingsFormSchema.safeParse(rawData);

    if (!validationResult.success) {
      const fieldErrors: Record<string, string[]> = {};

      validationResult.error.errors.forEach((error) => {
        const field = error.path.join(".");
        if (!fieldErrors[field]) {
          fieldErrors[field] = [];
        }
        fieldErrors[field].push(error.message);
      });

      return {
        success: false,
        error: "Validation failed",
        fieldErrors,
      };
    }

    // Get current settings to check if we need to create or update
    const currentSettings = await getEmailSettings();
    let result: EmailSettings | null;

    if (currentSettings) {
      // Update existing settings
      result = await updateEmailSettings(
        currentSettings.id,
        validationResult.data,
        user.id
      );
    } else {
      // Create new settings
      result = await createEmailSettings(
        validationResult.data,
        user.id
      );
    }

    if (!result) {
      return {
        success: false,
        error: "Failed to save email settings.",
      };
    }

    // Revalidate admin settings page
    revalidatePath("/admin/settings/email");

    return {
      success: true,
      data: result,
    };
  } catch (error) {
    logger.error("Error in updateEmailSettingsAction:", error);

    // Provide more specific error messages for common validation errors
    if (error instanceof Error) {
      // Check if it's a validation error for email addresses
      if (error.message.includes("Invalid email address")) {
        return {
          success: false,
          error: error.message,
          fieldErrors: {
            bcc_recipients: [error.message]
          }
        };
      }
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update email settings.",
    };
  }
}
