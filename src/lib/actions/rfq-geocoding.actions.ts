"use server";

import { revalidatePath } from "next/cache";
import { createLogger } from "@/lib/utils/logger/logger";
import { ActionResponse } from "@/lib/schemas";
import { createClient } from "@/lib/supabase/server";
import {
  resolveCoordinates,
  generateAddressHash,
  detectAddressChange,
} from "@/lib/services/google-maps/coordinate-resolution.service";
import {
  calculateDistance,
  validateRouteCoordinates,
} from "@/lib/services/google-maps/distance-calculation.service";
import type {
  RouteCoordinates,
  CoordinateResolutionResult,
  DistanceCalculationResult,
} from "@/lib/services/google-maps/types";

const logger = createLogger("RFQGeocodingActions");

/**
 * Server Action for updating RFQ coordinates manually
 * Resolves coordinates for an RFQ's addresses and updates the database
 */
export async function updateRFQCoordinatesAction(
  rfqId: string,
): Promise<ActionResponse<{
  originResult: CoordinateResolutionResult;
  destinationResult: CoordinateResolutionResult;
  coordinatesUpdated: boolean;
}>> {
  try {
    logger.info(`Updating coordinates for RFQ ${rfqId}`);

    const supabase = await createClient();

    // Get RFQ address data
    const { data: rfq, error: fetchError } = await supabase
      .from("rfqs")
      .select("origin_city, origin_address, origin_postal_code, destination_city, destination_address, destination_postal_code")
      .eq("id", rfqId)
      .single();

    if (fetchError) {
      logger.error(`Error fetching RFQ ${rfqId}:`, fetchError);
      return {
        success: false,
        error: `Failed to fetch RFQ: ${fetchError.message}`,
      };
    }

    if (!rfq) {
      return {
        success: false,
        error: "RFQ not found",
      };
    }

    // Build address strings
    const originAddressString = [
      rfq.origin_address,
      rfq.origin_city,
      rfq.origin_postal_code,
    ]
      .filter(Boolean)
      .join(", ");

    const destinationAddressString = [
      rfq.destination_address,
      rfq.destination_city,
      rfq.destination_postal_code,
    ]
      .filter(Boolean)
      .join(", ");

    if (!originAddressString || !destinationAddressString) {
      return {
        success: false,
        error: "RFQ must have both origin and destination addresses",
        fieldErrors: {
          addresses: ["Both origin and destination addresses are required"],
        },
      };
    }

    // Resolve coordinates concurrently
    const [originResult, destinationResult] = await Promise.all([
      resolveCoordinates(originAddressString),
      resolveCoordinates(destinationAddressString),
    ]);

    let coordinatesUpdated = false;

    // Update coordinates if both were resolved successfully
    if (originResult.success && destinationResult.success &&
        originResult.lat !== undefined && originResult.lng !== undefined &&
        destinationResult.lat !== undefined && destinationResult.lng !== undefined) {

      const timestamp = new Date().toISOString();

      const { error: updateError } = await supabase
        .from("rfqs")
        .update({
          origin_lat: originResult.lat,
          origin_lng: originResult.lng,
          destination_lat: destinationResult.lat,
          destination_lng: destinationResult.lng,
          coordinates_resolved_at: timestamp,
          updated_at: timestamp,
        })
        .eq("id", rfqId);

      if (updateError) {
        logger.error(`Error updating RFQ ${rfqId} coordinates:`, updateError);
        return {
          success: false,
          error: `Failed to update coordinates: ${updateError.message}`,
        };
      }

      coordinatesUpdated = true;
      logger.info(`Successfully updated coordinates for RFQ ${rfqId}`);
    }

    // Revalidate RFQ pages
    revalidatePath("/dashboard/rfqs");
    revalidatePath(`/dashboard/rfqs/${rfqId}`);

    return {
      success: true,
      data: {
        originResult,
        destinationResult,
        coordinatesUpdated,
      },
    };
  } catch (error) {
    logger.error("Error in updateRFQCoordinatesAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update RFQ coordinates",
    };
  }
}

/**
 * Server Action for updating RFQ distance manually
 * Calculates distance for an RFQ using its stored coordinates and updates the database
 */
export async function updateRFQDistanceAction(
  rfqId: string,
  options?: {
    mode?: "driving" | "walking" | "bicycling" | "transit";
    units?: "metric" | "imperial";
    avoidTolls?: boolean;
    avoidHighways?: boolean;
    avoidFerries?: boolean;
  },
): Promise<ActionResponse<{
  distanceResult: DistanceCalculationResult;
  distanceUpdated: boolean;
}>> {
  try {
    logger.info(`Updating distance for RFQ ${rfqId}`);

    const supabase = await createClient();

    // Get RFQ coordinate data
    const { data: rfq, error: fetchError } = await supabase
      .from("rfqs")
      .select("origin_lat, origin_lng, destination_lat, destination_lng")
      .eq("id", rfqId)
      .single();

    if (fetchError) {
      logger.error(`Error fetching RFQ ${rfqId}:`, fetchError);
      return {
        success: false,
        error: `Failed to fetch RFQ: ${fetchError.message}`,
      };
    }

    if (!rfq) {
      return {
        success: false,
        error: "RFQ not found",
      };
    }

    // Check if coordinates are available
    if (!rfq.origin_lat || !rfq.origin_lng || !rfq.destination_lat || !rfq.destination_lng) {
      return {
        success: false,
        error: "RFQ must have coordinates before distance can be calculated",
        fieldErrors: {
          coordinates: ["Origin and destination coordinates are required for distance calculation"],
        },
      };
    }

    // Create route coordinates
    const route: RouteCoordinates = {
      origin: {
        lat: rfq.origin_lat,
        lng: rfq.origin_lng,
      },
      destination: {
        lat: rfq.destination_lat,
        lng: rfq.destination_lng,
      },
    };

    // Validate coordinates
    if (!validateRouteCoordinates(route)) {
      return {
        success: false,
        error: "Invalid coordinates stored in RFQ",
        fieldErrors: {
          coordinates: ["Stored coordinates are invalid"],
        },
      };
    }

    // Calculate distance
    const distanceResult = await calculateDistance(route, options);

    let distanceUpdated = false;

    // Update distance if calculation was successful
    if (distanceResult.success && distanceResult.distanceKm !== undefined) {
      const timestamp = new Date().toISOString();

      const { error: updateError } = await supabase
        .from("rfqs")
        .update({
          route_distance_km: distanceResult.distanceKm,
          route_distance_calculated_at: timestamp,
          updated_at: timestamp,
        })
        .eq("id", rfqId);

      if (updateError) {
        logger.error(`Error updating RFQ ${rfqId} distance:`, updateError);
        return {
          success: false,
          error: `Failed to update distance: ${updateError.message}`,
        };
      }

      distanceUpdated = true;
      logger.info(`Successfully updated distance for RFQ ${rfqId}: ${distanceResult.distanceKm}km`);
    }

    // Revalidate RFQ pages
    revalidatePath("/dashboard/rfqs");
    revalidatePath(`/dashboard/rfqs/${rfqId}`);

    return {
      success: true,
      data: {
        distanceResult,
        distanceUpdated,
      },
    };
  } catch (error) {
    logger.error("Error in updateRFQDistanceAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update RFQ distance",
    };
  }
}

/**
 * Server Action for full RFQ geocoding update
 * Resolves coordinates and calculates distance for an RFQ in one operation
 */
export async function updateRFQGeocodingAction(
  rfqId: string,
  options?: {
    mode?: "driving" | "walking" | "bicycling" | "transit";
    units?: "metric" | "imperial";
    avoidTolls?: boolean;
    avoidHighways?: boolean;
    avoidFerries?: boolean;
  },
): Promise<ActionResponse<{
  originResult: CoordinateResolutionResult;
  destinationResult: CoordinateResolutionResult;
  distanceResult?: DistanceCalculationResult;
  coordinatesUpdated: boolean;
  distanceUpdated: boolean;
}>> {
  try {
    logger.info(`Updating full geocoding for RFQ ${rfqId}`);

    // First update coordinates
    const coordinatesResponse = await updateRFQCoordinatesAction(rfqId);

    if (!coordinatesResponse.success) {
      return coordinatesResponse as ActionResponse<any>;
    }

    const { originResult, destinationResult, coordinatesUpdated } = coordinatesResponse.data;

    let distanceResult: DistanceCalculationResult | undefined;
    let distanceUpdated = false;

    // If coordinates were successfully resolved, calculate distance
    if (coordinatesUpdated) {
      const distanceResponse = await updateRFQDistanceAction(rfqId, options);

      if (distanceResponse.success) {
        distanceResult = distanceResponse.data.distanceResult;
        distanceUpdated = distanceResponse.data.distanceUpdated;
      } else {
        // Log distance calculation failure but don't fail the entire operation
        logger.warn(`Distance calculation failed for RFQ ${rfqId}: ${distanceResponse.error}`);
      }
    }

    logger.info(`Full geocoding update completed for RFQ ${rfqId}: coordinates=${coordinatesUpdated}, distance=${distanceUpdated}`);

    return {
      success: true,
      data: {
        originResult,
        destinationResult,
        distanceResult,
        coordinatesUpdated,
        distanceUpdated,
      },
    };
  } catch (error) {
    logger.error("Error in updateRFQGeocodingAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update RFQ geocoding",
    };
  }
}

/**
 * Server Action for batch RFQ geocoding update
 * Updates coordinates and distances for multiple RFQs
 */
export async function batchUpdateRFQGeocodingAction(
  rfqIds: string[],
  options?: {
    mode?: "driving" | "walking" | "bicycling" | "transit";
    units?: "metric" | "imperial";
    avoidTolls?: boolean;
    avoidHighways?: boolean;
    avoidFerries?: boolean;
  },
): Promise<ActionResponse<{
  results: Array<{
    rfqId: string;
    success: boolean;
    coordinatesUpdated: boolean;
    distanceUpdated: boolean;
    error?: string;
  }>;
  totalProcessed: number;
  successfulUpdates: number;
}>> {
  try {
    logger.info(`Starting batch geocoding update for ${rfqIds.length} RFQs`);

    if (!rfqIds || rfqIds.length === 0) {
      return {
        success: false,
        error: "At least one RFQ ID is required for batch geocoding update",
        fieldErrors: { rfqIds: ["RFQ IDs array cannot be empty"] },
      };
    }

    const results: Array<{
      rfqId: string;
      success: boolean;
      coordinatesUpdated: boolean;
      distanceUpdated: boolean;
      error?: string;
    }> = [];

    let successfulUpdates = 0;

    // Process RFQs sequentially to avoid overwhelming the Google Maps API
    for (const rfqId of rfqIds) {
      try {
        const response = await updateRFQGeocodingAction(rfqId, options);

        if (response.success) {
          results.push({
            rfqId,
            success: true,
            coordinatesUpdated: response.data.coordinatesUpdated,
            distanceUpdated: response.data.distanceUpdated,
          });
          successfulUpdates++;
        } else {
          results.push({
            rfqId,
            success: false,
            coordinatesUpdated: false,
            distanceUpdated: false,
            error: response.error,
          });
        }
      } catch (error) {
        logger.error(`Error processing RFQ ${rfqId} in batch:`, error);
        results.push({
          rfqId,
          success: false,
          coordinatesUpdated: false,
          distanceUpdated: false,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }

      // Add small delay between requests to respect API rate limits
      if (rfqIds.indexOf(rfqId) < rfqIds.length - 1) {
        await new Promise((resolve) => setTimeout(resolve, 200));
      }
    }

    logger.info(`Batch geocoding update completed: ${successfulUpdates}/${rfqIds.length} successful`);

    // Revalidate RFQ pages
    revalidatePath("/dashboard/rfqs");

    return {
      success: true,
      data: {
        results,
        totalProcessed: rfqIds.length,
        successfulUpdates,
      },
    };
  } catch (error) {
    logger.error("Error in batchUpdateRFQGeocodingAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update RFQ geocoding in batch",
    };
  }
}

/**
 * Server Action for checking RFQ geocoding status
 * Returns the current geocoding status of an RFQ
 */
export async function getRFQGeocodingStatusAction(
  rfqId: string,
): Promise<ActionResponse<{
  hasCoordinates: boolean;
  hasDistance: boolean;
  coordinatesResolvedAt?: string;
  distanceCalculatedAt?: string;
  addressHash?: string;
}>> {
  try {
    logger.debug(`Checking geocoding status for RFQ ${rfqId}`);

    const supabase = await createClient();

    const { data: rfq, error: fetchError } = await supabase
      .from("rfqs")
      .select("origin_lat, origin_lng, destination_lat, destination_lng, route_distance_km, coordinates_resolved_at, route_distance_calculated_at, address_hash")
      .eq("id", rfqId)
      .single();

    if (fetchError) {
      logger.error(`Error fetching RFQ ${rfqId} geocoding status:`, fetchError);
      return {
        success: false,
        error: `Failed to fetch RFQ geocoding status: ${fetchError.message}`,
      };
    }

    if (!rfq) {
      return {
        success: false,
        error: "RFQ not found",
      };
    }

    const hasCoordinates = !!(rfq.origin_lat && rfq.origin_lng && rfq.destination_lat && rfq.destination_lng);
    const hasDistance = !!rfq.route_distance_km;

    return {
      success: true,
      data: {
        hasCoordinates,
        hasDistance,
        coordinatesResolvedAt: rfq.coordinates_resolved_at || undefined,
        distanceCalculatedAt: rfq.route_distance_calculated_at || undefined,
        addressHash: rfq.address_hash || undefined,
      },
    };
  } catch (error) {
    logger.error("Error in getRFQGeocodingStatusAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get RFQ geocoding status",
    };
  }
}
