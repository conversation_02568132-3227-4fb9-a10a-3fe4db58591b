"use server";

import { revalidatePath } from "next/cache";
import {
  fetchCountries,
  addCountry,
  updateCountry,
  deleteCountry,
} from "@/lib/services/country.service";
import {
  GetCountriesParams,
  CreateCountryInput,
  UpdateCountryInput,
} from "@/lib/schemas";
import { PaginatedResponse, ActionResponse } from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("CountryActions");

/**
 * Get all countries or filter by ID/code with pagination
 * If no pagination parameters are provided, fetches all countries
 */
export async function getCountriesAction(
  params: Partial<GetCountriesParams> = {},
): Promise<ActionResponse<any>> {
  try {
    logger.info(
      `getCountriesAction called with params: ${JSON.stringify(params)}`,
    );

    // Determine if we should fetch all countries (for dropdowns, etc.)
    const fetchAllCountries = params.page === undefined && params.pageSize === undefined;

    // If no specific pagination is requested, fetch all countries
    // by passing undefined for page and pageSize
    const effectiveParams: Partial<GetCountriesParams> = {
      id: params.id,
      alpha2_code: params.alpha2_code,
    };

    // Only add pagination if explicitly provided in params and we're not fetching all countries
    if (!fetchAllCountries) {
      if (params.page !== undefined) {
        effectiveParams.page = params.page;
      }

      if (params.pageSize !== undefined) {
        effectiveParams.pageSize = params.pageSize;
      }
    }

    logger.info(
      `getCountriesAction calling fetchCountries with params: ${JSON.stringify(effectiveParams)}, fetchAllCountries: ${fetchAllCountries}`,
    );

    const result = await fetchCountries(effectiveParams);

    // Return just the array of countries in the data property
    return { success: true, data: result.data };
  } catch (error) {
    logger.error("Error in getCountriesAction:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to fetch countries",
    };
  }
}

/**
 * Get all countries with pagination information
 * If pagination parameters are provided, returns paginated results
 * Otherwise, returns all countries with basic pagination metadata
 */
export async function getPaginatedCountriesAction(
  params: Partial<GetCountriesParams> = {},
): Promise<ActionResponse<PaginatedResponse<any>>> {
  try {
    logger.info(
      `getPaginatedCountriesAction called with params: ${JSON.stringify(params)}`,
    );
    const effectiveParams: GetCountriesParams = {
      id: params.id,
      page: params.page ?? 1,
      pageSize: params.pageSize ?? 10,
    };
    const result = await fetchCountries(effectiveParams);
    logger.info(
      `getPaginatedCountriesAction fetched ${result.data.length} countries`,
    );

    return {
      success: true,
      data: {
        data: result.data,
        pagination: result.pagination || {
          page: 1,
          pageSize: result.data.length,
          totalPages: 1,
          totalCount: result.data.length,
        },
      },
    };
  } catch (error) {
    logger.error("Error in getPaginatedCountriesAction:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to fetch countries",
    };
  }
}

/**
 * Create a new country
 */
export async function createCountryAction(
  input: CreateCountryInput,
): Promise<ActionResponse<any>> {
  try {
    const country = await addCountry(input);
    revalidatePath("/dashboard/countries");
    return { success: true, data: country };
  } catch (error) {
    logger.error("Error in createCountryAction:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to create country",
    };
  }
}

/**
 * Update an existing country
 */
export async function updateCountryAction(
  input: UpdateCountryInput,
): Promise<ActionResponse<any>> {
  try {
    const country = await updateCountry(input);
    revalidatePath("/dashboard/countries");
    revalidatePath(`/dashboard/countries/${input.id}`);
    return { success: true, data: country };
  } catch (error) {
    logger.error("Error in updateCountryAction:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to update country",
    };
  }
}

/**
 * Delete a country
 */
export async function deleteCountryAction(
  id: string,
): Promise<ActionResponse<any>> {
  try {
    await deleteCountry(id);
    revalidatePath("/dashboard/countries");
    return { success: true, data: { message: "Country successfully deleted" } };
  } catch (error) {
    logger.error("Error in deleteCountryAction:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to delete country",
    };
  }
}
