"use server";

import { revalidatePath } from "next/cache";
import {
  fetchCargo,
  addCargo,
  modifyCargo,
  removeCargo,
} from "@/lib/services/cargo.service";
import {
  CreateCargoTypeSchema,
  UpdateCargoTypeSchema,
  type CargoType,
} from "@/lib/schemas";
import { type ActionResponse } from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("CargoActions");

// --- GET --- //
export async function getCargo(params?: {
  page?: number;
  pageSize?: number;
}): Promise<{
  data: CargoType[];
  error: string | null;
  totalCount?: number;
}> {
  try {
    // For RFQ form dropdown, we want to fetch all cargo types (there are only ~45)
    // Using a large pageSize to ensure we get all cargo types in one request
    const result = await fetchCargo({
      page: params?.page || 1,
      pageSize: params?.pageSize || 100, // Use a large number to get all cargo types
    });
    return {
      data: result.data as CargoType[],
      error: null,
      totalCount: result.count,
    };
  } catch (error) {
    logger.error("Error fetching cargo:", error);
    return {
      data: [],
      error: error instanceof Error ? error.message : "Failed to fetch cargo.",
    };
  }
}

// --- CREATE --- //
export async function createCargoAction(
  _prevState: any,
  formData: FormData,
): Promise<ActionResponse<CargoType>> {
  const validatedFields = CreateCargoTypeSchema.safeParse({
    name: formData.get("name"),
    description: formData.get("description"),
    image_url: formData.get("image_url"),
  });

  if (!validatedFields.success) {
    return {
      success: false,
      error: "Validation failed.",
      fieldErrors: validatedFields.error.flatten().fieldErrors,
    };
  }

  try {
    const cargo = await addCargo(validatedFields.data);
    revalidatePath("/dashboard/cargo");
    return {
      success: true,
      data: cargo,
    };
  } catch (error) {
    logger.error("Error creating cargo:", error);

    if (error instanceof Error && error.message.includes("already exists")) {
      return { success: false, error: error.message };
    }

    return {
      success: false,
      error:
        error instanceof Error
          ? `Database Error: ${error.message}`
          : "Database Error: Failed to create cargo.",
    };
  }
}

// --- UPDATE --- //
export async function updateCargoAction(
  _prevState: any,
  formData: FormData,
): Promise<ActionResponse<CargoType>> {
  const id = formData.get("id") as string;

  if (!id) {
    return { success: false, error: "Error: Missing ID for update." };
  }

  const validatedFields = UpdateCargoTypeSchema.safeParse({
    id,
    name: formData.get("name"),
    description: formData.get("description"),
    image_url: formData.get("image_url"),
  });

  if (!validatedFields.success) {
    return {
      success: false,
      error: "Validation failed.",
      fieldErrors: validatedFields.error.flatten().fieldErrors,
    };
  }

  try {
    const cargo = await modifyCargo(validatedFields.data);
    revalidatePath("/dashboard/cargo");
    return {
      success: true,
      data: cargo,
    };
  } catch (error) {
    logger.error("Error updating cargo:", error);

    if (error instanceof Error) {
      if (error.message.includes("already exists")) {
        return { success: false, error: error.message };
      }
      if (error.message.includes("not found")) {
        return { success: false, error: error.message };
      }
    }

    return {
      success: false,
      error:
        error instanceof Error
          ? `Database Error: ${error.message}`
          : "Database Error: Failed to update cargo.",
    };
  }
}

// --- DELETE --- //
export async function deleteCargoAction(
  _prevState: any,
  formData: FormData,
): Promise<ActionResponse<void>> {
  const id = formData.get("id") as string;

  if (!id) {
    return { success: false, error: "Error: Missing ID for delete." };
  }

  try {
    await removeCargo({ id });
    revalidatePath("/dashboard/cargo");
    return { success: true, data: undefined };
  } catch (error) {
    logger.error("Error deleting cargo:", error);

    if (error instanceof Error && error.message.includes("not found")) {
      return { success: false, error: error.message };
    }

    return {
      success: false,
      error:
        error instanceof Error
          ? `Database Error: ${error.message}`
          : "Database Error: Failed to delete cargo.",
    };
  }
}
