"use server";

import { revalidatePath } from "next/cache";
import { emailStorageService } from "@/lib/services/email/email-storage.service";
import { emailPollingService } from "@/lib/services/email/email-polling.service";
import { createLogger } from "@/lib/utils/logger/logger";
import { getCurrentUser, isAdmin } from "@/lib/services/auth.service";

// Create a logger instance for this module
const logger = createLogger("EmailAccountsActions");

// Define response type for actions
type ActionResponse<T> =
  | { success: true; data: T; message?: string }
  | { success: false; error: string; fieldErrors?: Record<string, string[]> };

/**
 * Get all email accounts
 */
export async function getEmailAccountsAction(): Promise<ActionResponse<any[]>> {
  try {
    logger.debug("getEmailAccountsAction called");

    const accounts = await emailStorageService.getAccounts();

    return { success: true, data: accounts };
  } catch (error) {
    logger.error("Error in getEmailAccountsAction:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch email accounts",
    };
  }
}

/**
 * Get the OAuth URL for adding a new email account
 * This follows the service layer architecture by returning the URL instead of redirecting
 */
export async function getEmailAuthUrlAction(
  existingAccountId?: string,
): Promise<ActionResponse<string>> {
  try {
    logger.debug(
      `getEmailAuthUrlAction called${existingAccountId ? ` for existing account ${existingAccountId}` : ""}`,
    );

    // Get the current user using the auth service
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: "Unauthorized" };
    }

    // Check if the user has admin role using the auth service
    const hasAdminRole = await isAdmin();

    if (!hasAdminRole) {
      logger.warn(
        `User ${user.id} attempted to get email auth URL without admin role`,
      );
      return { success: false, error: "Forbidden: Admin role required" };
    }

    // Generate the OAuth URL using the polling service
    const authUrl = emailPollingService.generateAuthUrl();

    // If re-authenticating an existing account, add the account ID as a state parameter
    const finalUrl = existingAccountId
      ? `${authUrl}&state=${encodeURIComponent(existingAccountId)}`
      : authUrl;

    logger.info(
      `Generated OAuth URL for Gmail authentication${existingAccountId ? " (re-authentication)" : ""}`,
    );

    return { success: true, data: finalUrl };
  } catch (error) {
    logger.error("Error in getEmailAuthUrlAction:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to generate auth URL",
    };
  }
}

/**
 * Manually trigger polling for an email account
 *
 * This replaces the old refreshWatchAction which used the watch-based approach.
 * Now it uses the polling-based approach with EmailPollingService.
 *
 * @param accountId The ID of the account to poll
 * @param forceFullSync Whether to force a full sync even if history ID is available
 */
export async function refreshWatchAction(
  accountId: string,
  forceFullSync: boolean = false,
): Promise<ActionResponse<boolean>> {
  try {
    logger.debug(
      `refreshWatchAction called for account ${accountId}, forceFullSync=${forceFullSync}`,
    );

    // Get the current user using the auth service
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: "Unauthorized" };
    }

    // Check if the user has admin role using the auth service
    const hasAdminRole = await isAdmin();

    if (!hasAdminRole) {
      logger.warn(
        `User ${user.id} attempted to trigger polling without admin role`,
      );
      return { success: false, error: "Forbidden: Admin role required" };
    }

    // Get the account to check if it exists
    const account = await emailStorageService.getAccountById(accountId);

    if (!account) {
      return {
        success: false,
        error: `Account not found: No account exists with ID ${accountId}`,
      };
    }

    // Trigger polling for this account
    const result = await emailPollingService.pollAccount(
      accountId,
      account.refresh_token,
      forceFullSync,
    );

    if (!result.success) {
      return { success: false, error: result.error };
    }

    // Revalidate the email accounts page
    revalidatePath("/admin/email-accounts");

    return {
      success: true,
      data: true,
      message: `Polling completed successfully. Processed ${result.processed} messages.`,
    };
  } catch (error) {
    logger.error(
      `Error in refreshWatchAction for account ${accountId}:`,
      error,
    );

    // Provide more specific error messages based on the error type
    let errorMessage = "Failed to poll for new emails";

    if (error instanceof Error) {
      if (error.message.includes("Account not found")) {
        errorMessage = `Account not found: The email account with ID ${accountId} could not be found`;
      } else if (error.message.includes("refresh token")) {
        errorMessage =
          "Invalid refresh token: The account may need to be reconnected";
      } else if (error.message.includes("Requested entity was not found")) {
        errorMessage =
          "Gmail account not found or access revoked. Please reconnect the account.";
      } else {
        errorMessage = error.message;
      }
    }

    return { success: false, error: errorMessage };
  }
}

/**
 * Delete an email account
 */
export async function deleteEmailAccountAction(
  accountId: string,
): Promise<ActionResponse<boolean>> {
  try {
    logger.debug(`deleteEmailAccountAction called for account ${accountId}`);

    // Get the current user using the auth service
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: "Unauthorized" };
    }

    // Check if the user has admin role using the auth service
    const hasAdminRole = await isAdmin();

    if (!hasAdminRole) {
      logger.warn(
        `User ${user.id} attempted to delete email account without admin role`,
      );
      return { success: false, error: "Forbidden: Admin role required" };
    }

    // Delete the account
    const success = await emailStorageService.deleteAccount(accountId);

    if (!success) {
      return { success: false, error: "Failed to delete account" };
    }

    // Revalidate the email accounts page
    revalidatePath("/admin/email-accounts");

    return { success: true, data: true };
  } catch (error) {
    logger.error(
      `Error in deleteEmailAccountAction for account ${accountId}:`,
      error,
    );
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to delete account",
    };
  }
}
