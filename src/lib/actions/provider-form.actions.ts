"use server";

import { revalidatePath } from "next/cache";
import { addProvider, modifyProvider } from "@/lib/services/provider.service";
import { validateWithSchema } from "@/lib/utils/validation";
import {
  CreateProviderSchema,
  UpdateProviderSchema,
  CreateProviderInput,
  ProviderActionState,
  ProviderEquipmentActionState,
} from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";
import {
  addProviderEquipments,
} from "@/lib/services/provider-equipments.service";
import { EquipmentType } from "@/lib/schemas";

const logger = createLogger("ProviderFormActions");

/**
 * Create provider action
 *
 * This action handles creating a new provider using the new React 19 pattern.
 * It returns a structured response for useActionState.
 */
export async function createProviderAction(
  initialState: ProviderActionState,
  formData: FormData,
): Promise<ProviderActionState> {
  try {
    // Parse form data
    const rawFormData = Object.fromEntries(formData.entries());

    // Validate form data
    const validationResult = validateWithSchema(
      {
        name: rawFormData.name,
        tax_id: rawFormData.tax_id || undefined,
        full_address: rawFormData.full_address || undefined,
        status: rawFormData.status || "pending",
        verified: rawFormData.verified === "true",
      },
      CreateProviderSchema,
    );

    if (!validationResult.success) {
      logger.error("Provider validation failed:", validationResult.error);
      return {
        success: false,
        message: validationResult.error?.message || "Validation failed",
        values: initialState.values,
        fieldErrors: validationResult.error?.fieldErrors as Record<string, string[]>,
      };
    }

    // Create provider
    const validatedData = validationResult.data! as CreateProviderInput;
    const newProvider = await addProvider(validatedData);

    // Revalidate providers page
    revalidatePath("/dashboard/providers");

    return {
      success: true,
      message: "Provider created successfully",
      values: {
        name: validatedData.name,
        tax_id: validatedData.tax_id || "",
        full_address: validatedData.full_address || "",
        status: validatedData.status,
        verified: validatedData.verified || false,
      },
      data: newProvider,
    };
  } catch (error) {
    logger.error("Error in createProviderAction:", error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to create provider",
      values: initialState.values,
    };
  }
}

/**
 * Update provider action
 *
 * This action handles updating an existing provider using the new React 19 pattern.
 * It returns a structured response for useActionState.
 */
export async function updateProviderAction(
  initialState: ProviderActionState,
  formData: FormData,
): Promise<ProviderActionState> {
  try {
    // Parse form data
    const rawFormData = Object.fromEntries(formData.entries());
    const providerId = rawFormData.id?.toString();

    if (!providerId) {
      return {
        success: false,
        message: "Provider ID is required",
        values: initialState.values,
      };
    }

    // Validate form data
    const validationResult = validateWithSchema(
      {
        id: providerId,
        name: rawFormData.name,
        tax_id: rawFormData.tax_id || undefined,
        full_address: rawFormData.full_address || undefined,
        status: rawFormData.status || "pending",
        verified: rawFormData.verified === "true",
      },
      UpdateProviderSchema,
    );

    if (!validationResult.success) {
      logger.error(
        "Provider update validation failed:",
        validationResult.error,
      );
      return {
        success: false,
        message: validationResult.error?.message || "Validation failed",
        values: initialState.values,
        fieldErrors: validationResult.error?.fieldErrors as Record<string, string[]>,
      };
    }

    // Update provider
    const validatedData = validationResult.data!;
    const updatedProvider = await modifyProvider(validatedData);

    // Revalidate provider pages
    revalidatePath("/dashboard/providers");
    revalidatePath(`/dashboard/providers/${providerId}`);

    return {
      success: true,
      message: "Provider updated successfully",
      values: {
        name: updatedProvider.name || "",
        tax_id: updatedProvider.tax_id || "",
        full_address: updatedProvider.full_address || "",
        status: (updatedProvider.status as "active" | "inactive" | "pending" | "suspended") || "pending",
        verified: updatedProvider.verified || false,
      },
      data: updatedProvider,
    };
  } catch (error) {
    logger.error("Error in updateProviderAction:", error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to update provider",
      values: initialState.values,
    };
  }
}

/**
 * Update provider equipments action
 *
 * This action handles updating a provider's equipments using the new React 19 pattern.
 * It returns a structured response for useActionState.
 */
export async function updateProviderEquipmentsAction(
  initialState: ProviderEquipmentActionState,
  formData: FormData,
): Promise<ProviderEquipmentActionState> {
  try {
    // Parse form data
    const providerId = formData.get("providerId")?.toString();
    const selectedEquipments = formData
      .getAll("selectedEquipments")
      .map((item) => item.toString());

    if (!providerId) {
      return {
        success: false,
        message: "Provider ID is required",
        selectedEquipments: initialState.selectedEquipments,
      };
    }

    // Update provider equipments
    const result = await addProviderEquipments(providerId, selectedEquipments);

    // Revalidate provider pages
    revalidatePath("/dashboard/providers");
    revalidatePath(`/dashboard/providers/${providerId}`);

    return {
      success: true,
      message: "Provider equipments updated successfully",
      selectedEquipments,
      data: result.equipments as unknown as EquipmentType[],
    };
  } catch (error) {
    logger.error("Error in updateProviderEquipmentsAction:", error);
    return {
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to update provider equipments",
      selectedEquipments: initialState.selectedEquipments,
    };
  }
}
