"use server";

import {
  fetchProviders,
  fetchProvidersWithEquipment,
  getProvider,
  addProvider,
  modifyProvider,
  removeProvider,
  getProviderCountsByStatus,
  getProviderRFQInvitationCounts,
  getProviderRFQInvitationCount
} from "@/lib/services/provider.service";
import { validateWithSchema } from "@/lib/utils/validation";
import {
  CreateProviderSchema,
  UpdateProviderSchema,
  DeleteProviderParamsSchema,
  GetProvidersParamsSchema,
  ActionResponse,
  PaginationParams,
} from "@/lib/schemas";

// Import types from central schemas index
import type { ProviderWithEquipment } from "@/lib/schemas";

// Define a type that combines pagination params with optional ID
type GetProvidersParams = PaginationParams & {
  id?: string;
};
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("ProviderActions");

/**
 * Get all providers
 */
export async function getProvidersAction(
  params: GetProvidersParams = { page: 1, pageSize: 100 },
): Promise<ActionResponse<any>> {
  try {
    const result = await fetchProviders(params);
    return { success: true, data: result.data };
  } catch (error) {
    logger.error("Error in getProvidersAction:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to fetch providers",
    };
  }
}

/**
 * Get a specific provider by ID with optional contacts
 */
export async function getProviderAction(
  id: string,
  options?: { includeContacts?: boolean },
): Promise<ActionResponse<any>> {
  try {
    const result = await getProvider(id, options);
    return { success: true, data: result };
  } catch (error) {
    logger.error("Error in getProviderAction:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to fetch provider",
    };
  }
}

/**
 * Create a new provider
 */
export async function createProviderOnlyAction(
  provider: any,
): Promise<ActionResponse<any>> {
  try {
    // Validate provider data
    const validationResult = validateWithSchema(provider, CreateProviderSchema);

    if (!validationResult.success) {
      logger.error("Provider validation failed:", validationResult.error);
      return {
        success: false,
        error: validationResult.error?.message || "Validation failed",
        fieldErrors: validationResult.error?.fieldErrors,
      };
    }

    // Ensure status is set to a valid value
    const validatedData = validationResult.data!;

    // Force the status to be a valid value
    const status = validatedData.status || "pending";

    // Create a new object with the correct type
    const providerData = {
      ...validatedData,
      status: status as "active" | "inactive" | "pending" | "suspended",
    };

    const newProvider = await addProvider(providerData);
    return { success: true, data: newProvider };
  } catch (error) {
    logger.error("Error in createProviderOnlyAction:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to create provider",
    };
  }
}

/**
 * Update an existing provider
 */
export async function updateProviderAction(
  id: string,
  provider: any,
): Promise<ActionResponse<any>> {
  try {
    // Validate provider data
    const validationResult = validateWithSchema(
      { id, ...provider },
      UpdateProviderSchema,
    );

    if (!validationResult.success) {
      logger.error(
        "Provider update validation failed:",
        validationResult.error,
      );
      return {
        success: false,
        error: validationResult.error?.message || "Validation failed",
        fieldErrors: validationResult.error?.fieldErrors,
      };
    }

    const updatedProvider = await modifyProvider(validationResult.data!);

    return { success: true, data: updatedProvider };
  } catch (error) {
    logger.error("Error in updateProviderAction:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to update provider",
    };
  }
}

/**
 * Delete a provider
 */
export async function deleteProviderAction(
  id: string,
): Promise<ActionResponse<any>> {
  try {
    // Validate provider ID
    const validationResult = validateWithSchema(
      { id },
      DeleteProviderParamsSchema,
    );

    if (!validationResult.success) {
      logger.error(
        "Provider deletion validation failed:",
        validationResult.error,
      );
      return {
        success: false,
        error: validationResult.error?.message || "Validation failed",
        fieldErrors: validationResult.error?.fieldErrors,
      };
    }

    const result = await removeProvider(validationResult.data!);

    return { success: true, data: result };
  } catch (error) {
    logger.error("Error in deleteProviderAction:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to delete provider",
    };
  }
}

/**
 * Get provider counts by status
 */
export async function getProviderCountsByStatusAction(): Promise<
  ActionResponse<{
    totalCount: number;
    activeCount: number;
    pendingCount: number;
    inactiveCount: number;
    verifiedCount: number;
  }>
> {
  try {
    const result = await getProviderCountsByStatus();
    return { success: true, data: result };
  } catch (error) {
    logger.error("Error in getProviderCountsByStatusAction:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch provider counts",
    };
  }
}

/**
 * Get provider counts by status
 *
 * This action is specifically designed for dashboard statistics,
 * returning only the counts needed without loading all provider records.
 */
export async function getProviderCountsAction(): Promise<
  ActionResponse<{
    totalCount: number;
    activeCount: number;
    pendingCount: number;
    inactiveCount: number;
    verifiedCount: number;
  }>
> {
  try {
    const result = await getProviderCountsByStatus();

    return {
      success: true,
      data: result,
    };
  } catch (error) {
    logger.error("Error in getProviderCountsAction:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch provider counts",
    };
  }
}

export async function getProvidersWithEquipmentAction(
  params: GetProvidersParams = { page: 1, pageSize: 100 },
): Promise<ActionResponse<ProviderWithEquipment[]> & { totalCount?: number }> {
  try {
    // Ensure params has valid pagination values
    const validParams = {
      page: params.page || 1,
      pageSize: params.pageSize || 10,
      id: params.id,
    };

    // Validate provider params
    const validationResult = validateWithSchema(
      validParams,
      GetProvidersParamsSchema,
    );

    if (!validationResult.success) {
      logger.error(
        "Provider params validation failed:",
        {
          inputParams: validParams,
          validationError: validationResult.error,
          fieldErrors: validationResult.error?.fieldErrors,
        }
      );
      return {
        success: false,
        error: validationResult.error?.message || "Validation failed",
        fieldErrors: validationResult.error?.fieldErrors,
      };
    }

    // Log the parameters received by the action
    logger.info(
      `getProvidersWithEquipmentAction called with params:`,
      validationResult.data,
    );

    const result = await fetchProvidersWithEquipment(validationResult.data!);

    // Ensure we always return an array, even if the service returns null/undefined
    const safeData = Array.isArray(result.data) ? result.data : [];

    return {
      success: true,
      data: safeData,
      totalCount: result.count,
    };
  } catch (error) {
    logger.error("Error in getProvidersWithEquipmentAction:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch providers with equipment",
    };
  }
}

/**
 * Get the count of RFQ invitations for each provider
 * @returns A map of provider IDs to their RFQ invitation counts
 */
export async function getProviderRFQInvitationCountsAction(): Promise<ActionResponse<Record<string, number>>> {
  try {
    const invitationCounts = await getProviderRFQInvitationCounts();

    return {
      success: true,
      data: invitationCounts
    };
  } catch (error) {
    logger.error("Error in getProviderRFQInvitationCountsAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch provider RFQ invitation counts"
    };
  }
}

/**
 * Get the count of RFQ invitations for a specific provider
 * @param providerId The provider ID
 * @returns The count of RFQ invitations for the provider
 */
export async function getProviderRFQInvitationCountAction(providerId: string): Promise<ActionResponse<number>> {
  try {
    if (!providerId) {
      return {
        success: false,
        error: "Provider ID is required"
      };
    }

    const invitationCount = await getProviderRFQInvitationCount(providerId);

    return {
      success: true,
      data: invitationCount
    };
  } catch (error) {
    logger.error(`Error in getProviderRFQInvitationCountAction for provider ${providerId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch provider RFQ invitation count"
    };
  }
}