/**
 * Pagination constants for consistent pagination behavior across the application
 */

// Maximum page size allowed for data fetching
export const MAX_PAGE_SIZE = 100;

// Safety limit to prevent infinite loops in pagination
export const MAX_PAGES_SAFETY_LIMIT = 50;

// Default page size for most data tables
export const DEFAULT_PAGE_SIZE = 10;

// Default page number (1-based)
export const DEFAULT_PAGE = 1;

// Page size options for data tables
export const PAGE_SIZE_OPTIONS = [10, 20, 30, 40, 50] as const;

// Maximum items per page for performance considerations
export const MAX_ITEMS_PER_PAGE = 100;

// Minimum page size
export const MIN_PAGE_SIZE = 1;

// Minimum page number
export const MIN_PAGE = 1;
