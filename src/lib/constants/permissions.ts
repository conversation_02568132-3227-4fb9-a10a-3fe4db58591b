/**
 * API permissions constants
 *
 * This file defines all available permissions that can be assigned to API keys.
 * These permissions control access to different API endpoints and resources.
 */

// User permissions
export const USER_PERMISSIONS = {
  READ: "read:users",
  WRITE: "write:users",
  DELETE: "delete:users",
};

// Provider permissions
export const PROVIDER_PERMISSIONS = {
  READ: "read:providers",
  WRITE: "write:providers",
  DELETE: "delete:providers",
};

// RFQ permissions
export const RFQ_PERMISSIONS = {
  READ: "read:rfqs",
  WRITE: "write:rfqs",
  DELETE: "delete:rfqs",
  MANAGE: "manage:rfqs",
};

// Cargo permissions
export const CARGO_PERMISSIONS = {
  READ: "read:cargo",
  WRITE: "write:cargo",
  DELETE: "delete:cargo",
};

// Equipment permissions
export const EQUIPMENT_PERMISSIONS = {
  READ: "read:equipment",
  WRITE: "write:equipment",
  DELETE: "delete:equipment",
};

// Country permissions
export const COUNTRY_PERMISSIONS = {
  READ: "read:countries",
  WRITE: "write:countries",
  DELETE: "delete:countries",
};

// API permissions
export const API_PERMISSIONS = {
  READ: "read:api",
};

/**
 * All available permissions as a flat array
 */
export const ALL_PERMISSIONS = [
  ...Object.values(USER_PERMISSIONS),
  ...Object.values(PROVIDER_PERMISSIONS),
  ...Object.values(RFQ_PERMISSIONS),
  ...Object.values(CARGO_PERMISSIONS),
  ...Object.values(EQUIPMENT_PERMISSIONS),
  ...Object.values(COUNTRY_PERMISSIONS),
  ...Object.values(API_PERMISSIONS),
];
