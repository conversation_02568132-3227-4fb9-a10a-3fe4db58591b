# Supabase Client Architecture

This directory contains the Supabase client implementations for different environments in the application.

## Client Types

### 1. Browser Client (`client.ts`)

- Used in client components
- Handles authentication in the browser
- Uses a singleton pattern internally, so it's efficient to call multiple times

```typescript
import { createClient } from "@/lib/supabase/client";

// In a client component
const supabase = createClient();
```

### 2. Server Client (`server/server.ts`)

We have multiple server client implementations to handle different contexts:

#### a. Server Client (`createClient()`)

- Used in App Router Server Components, Server Actions, and Route Handlers
- Handles cookies automatically for session management
- IMPORTANT: Must be called in a context where `cookies()` from `next/headers` is available

```typescript
import { createClient } from "@/lib/supabase/server/server";

// In a Server Component, Server Action, or Route Handler
const supabase = await createClient();
```

#### b. Admin Client (`createAdminClient()`)

- Has full database access using the service role key
- Use for server-side operations that need elevated privileges
- Does not rely on cookies or user authentication
- Bypasses Row Level Security (RLS)

```typescript
import { createAdminClient } from "@/lib/supabase/server/server";

// In a service function
const supabase = createAdminClient();
```

#### c. JWT Client (`createClientFromJwt()`)

- Creates a client authenticated with a JWT token
- Useful for API routes that receive a JWT token
- Does not rely on cookies

```typescript
import { createClientFromJwt } from "@/lib/supabase/server/server";

// In a service function or API route
const supabase = await createClientFromJwt(jwt);
```

## Middleware

The middleware (`middleware.ts`) is responsible for:

1. Refreshing the auth token (by calling `supabase.auth.getUser()`)
2. Passing the refreshed token to Server Components
3. Passing the refreshed token to the browser
4. Protecting routes based on authentication and roles

## Security Best Practices

1. **Always use `getUser()` instead of `getSession()`** for security in server code

   - `getUser()` sends a request to the Supabase Auth server every time to revalidate the Auth token
   - `getSession()` isn't guaranteed to revalidate the Auth token

2. **Never trust cookies directly** for authentication decisions
   - Cookies can be spoofed by anyone
   - Always verify the user's identity with `getUser()`

## Best Practices

1. **Client Components**: Always use `createClient()` from `@/lib/supabase/client`
2. **Server Components/Actions/Routes**: Use `createClient()` from `@/lib/supabase/server/server`
3. **Service Layer**: Use `createAdminClient()` from `@/lib/supabase/server/server` when admin privileges are needed
4. **JWT Authentication**: Use `createClientFromJwt()` from `@/lib/supabase/server/server`

## Troubleshooting

If you encounter the error:

```
You're importing a component that needs "next/headers". That only works in a Server Component which is not supported in the pages/ directory.
```

It means you're trying to use a client that relies on `cookies()` from `next/headers` in a context where it's not available. Make sure you're using the client in a Server Component, Server Action, or Route Handler.
