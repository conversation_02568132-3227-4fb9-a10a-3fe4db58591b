import { createBrowserClient } from "@supabase/ssr";
import { Database } from "@/lib/types/supabase";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("SupabaseClient");

/**
 * Creates a Supabase client for browser-side operations.
 * This client is authenticated based on the user's session in the browser.
 *
 * This function uses a singleton pattern internally via createBrowserClient,
 * so it's efficient to call it multiple times.
 *
 * @returns A Supabase client instance for browser usage
 */
export function createClient() {
  try {

    return createBrowserClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    );
  } catch (error) {
    logger.error("Error creating browser Supabase client:", error);
    throw new Error(
      `Failed to create browser Supabase client: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}
