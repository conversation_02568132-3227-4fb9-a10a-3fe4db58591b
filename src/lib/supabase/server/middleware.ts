import { NextResponse, type NextRequest } from "next/server";
import { createServerClient } from "@supabase/ssr";
import { SupabaseClient } from "@supabase/supabase-js";
import { createLogger } from "@/lib/utils/logger/logger";
import { checkAllRoles, checkRole } from "./auth";

const logger = createLogger("SupabaseMiddleware");

/**
 * Protects a path by checking if the user is authenticated and has the required
 * roles. If the user is not authenticated or does not have the required roles,
 * they are redirected to the unauthorized path.
 *
 * @param supabase - The Supabase client.
 * @param roles - The roles required to access the path.
 * @param allRequired - Whether all roles are required.
 * @param unauthorizedPath - The path to redirect to if the user is not
 *                           authorized.
 *
 * @returns The path to redirect to if the user is not authorized, or null if
 *          the user is authorized.
 */
async function protectPath(
  supabase: SupabaseClient,
  roles: string[] | null,
  allRequired: boolean = false,
  unauthorizedPath: string = "/unauthorized",
) {
  let authorized = false;

  // IMPORTANT: Always use getUser() instead of getSession() for security
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    logger.debug("User not authenticated, redirecting to:", unauthorizedPath);
    return unauthorizedPath;
  }

  if (!roles || roles.length === 0) {
    return null;
  }

  if (allRequired) {
    authorized = await checkAllRoles(supabase, roles);
  } else {
    authorized = await checkRole(supabase, roles);
  }

  if (!authorized) {
    logger.debug(
      "User does not have required roles, redirecting to:",
      unauthorizedPath,
    );
    return unauthorizedPath;
  }

  return null;
}

const protectedRoutes = [
  {
    path: "/dashboard",
    roles: ["user"], // This route only requires authentication, no specific roles
    unauthorizedPath: "/unauthorized",
  },
  {
    path: "/admin",
    roles: ["admin"],
    unauthorizedPath: "/unauthorized",
  },
];

/**
 * Updates the user's session by refreshing the auth token.
 * This middleware is responsible for:
 * 1. Refreshing the auth token (by calling supabase.auth.getUser())
 * 2. Passing the refreshed token to Server Components
 * 3. Passing the refreshed token to the browser
 *
 * @param request The incoming request
 * @returns The response with updated cookies
 */
export async function updateSession(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  });

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet) {
          // Set cookies on the request
          cookiesToSet.forEach(({ name, value }) =>
            request.cookies.set(name, value),
          );

          // Create a new response
          supabaseResponse = NextResponse.next({
            request,
          });

          // Set cookies on the response
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options),
          );
        },
      },
    },
  );

  // IMPORTANT: Do not run code between createServerClient and supabase.auth.getUser()
  // A simple mistake could make it very hard to debug issues with users being randomly logged out

  // IMPORTANT: Call getUser() to refresh the session
  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Check if the user is trying to access a protected route
  for (const route of protectedRoutes) {
    if (request.nextUrl.pathname.startsWith(route.path)) {
      // If the user is not authenticated and trying to access a protected route
      if (
        !user &&
        !request.nextUrl.pathname.startsWith("/login") &&
        !request.nextUrl.pathname.startsWith("/auth")
      ) {
        logger.debug(
          `Unauthenticated user trying to access protected route: ${request.nextUrl.pathname}`,
        );

        // Redirect to login
        // Use PUBLIC_APP_URL environment variable if available, otherwise fallback to request URL
        const baseUrl = process.env.PUBLIC_APP_URL || request.url;
        const redirectUrl = new URL("/login", baseUrl);
        const previousPage = request.headers.get("referer") || "/";
        redirectUrl.searchParams.set("from", previousPage);
        logger.debug(`Redirecting to login: ${redirectUrl.toString()}`);
        return NextResponse.redirect(redirectUrl);
      }

      // If the user is authenticated but needs role-based access
      if (user && route.roles && route.roles.length > 0) {
        const unauthorizedPath = await protectPath(
          supabase,
          route.roles,
          false,
          route.unauthorizedPath,
        );

        if (unauthorizedPath) {
          // Use PUBLIC_APP_URL environment variable if available, otherwise fallback to request URL
          const baseUrl = process.env.PUBLIC_APP_URL || request.url;
          const redirectUrl = new URL(unauthorizedPath, baseUrl);
          const previousPage = request.headers.get("referer") || "/";
          redirectUrl.searchParams.set("from", previousPage);
          logger.debug(`Redirecting to unauthorized: ${redirectUrl.toString()}`);
          return NextResponse.redirect(redirectUrl);
        }
      }
    }
  }

  // IMPORTANT: You must return the supabaseResponse object as it is
  return supabaseResponse;
}
