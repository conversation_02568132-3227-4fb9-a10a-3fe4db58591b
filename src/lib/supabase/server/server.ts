import { cookies } from "next/headers";
import { createServerClient } from "@supabase/ssr";
import { SupabaseClient } from "@supabase/supabase-js";
import { createLogger } from "@/lib/utils/logger/logger";
import { Database } from "@/lib/types/supabase";

const logger = createLogger("SupabaseServer");

/**
 * Creates a Supabase client for server-side operations with cookie handling.
 * This client is authenticated based on the user's session cookies.
 *
 * IMPORTANT: This function must be called in a Server Component, Server Action,
 * or Route Handler context where cookies() from next/headers is available.
 * This is only compatible with the App Router in Next.js 15.
 *
 * @returns A Supabase client instance with cookie handling
 * @throws Error if there's an issue creating the client
 */
export async function createClient() {
  try {
    // Ensure we're in a server context
    if (typeof window !== "undefined") {
      throw new Error(
        "createClient() from server.ts cannot be used in client components. Use createClient() from @/lib/supabase/client instead.",
      );
    }

    const cookieStore = await cookies();

    return createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) => {
                logger.debug(`Setting cookie: ${name}`);
                cookieStore.set(name, value, options);
              });
            } catch (error) {
              logger.error("Failed to set cookies:", error);
              // The setAll method was called from a Server Component.
              // This can be ignored if you have middleware refreshing user sessions.
              logger.debug(
                "This error can be ignored if middleware is refreshing sessions",
              );
            }
          },
        },
      },
    );
  } catch (error) {
    logger.error("Error creating Supabase client:", error);
    // Don't fall back to admin client, instead throw a clear error
    throw new Error(
      `Failed to create authenticated Supabase client: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

/**
 * Creates a Supabase admin client that bypasses Row Level Security (RLS).
 * IMPORTANT: Only use this for operations that explicitly require admin privileges.
 *
 * @returns A Supabase admin client instance
 */
export function createAdminClient() {
  logger.debug("Creating Supabase admin client");

  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      global: {
        headers: {
          "x-service-role": "true",
          "x-client-info": "steelflow-admin",
        },
      },
      cookies: {
        getAll() {
          return [];
        },
        setAll() {
          // No-op
        },
      },
    },
  );
}

/**
 * Creates a Supabase client from a JWT token.
 *
 * @param jwt The JWT token to use for authentication
 * @returns A Supabase client instance authenticated with the JWT token
 * @throws Error if no JWT is provided
 */
export async function createClientFromJwt(jwt: string) {
  if (!jwt) {
    logger.error("No JWT provided for Supabase client creation");
    throw new Error("No JWT provided for Supabase client creation");
  }

  logger.debug("Creating Supabase client from JWT");

  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      global: {
        headers: {
          Authorization: `Bearer ${jwt}`,
          "x-client-info": "steelflow-jwt",
        },
      },
      cookies: {
        getAll() {
          return [];
        },
        setAll() {
          // No-op
        },
      },
    },
  );
}

export async function getUser(supabase: SupabaseClient) {
  try {
    // IMPORTANT: Always use getUser() instead of getSession() for security
    // getUser() sends a request to the Supabase Auth server every time to revalidate the Auth token
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error) {
      logger.error("Error getting user:", error.message);
      return null;
    }

    if (!user) {
      logger.warn("No user found");
      return null;
    }

    return user;
  } catch (error) {
    logger.error("Unexpected error in getUser:", error);
    return null;
  }
}

export async function getUserId(supabase: SupabaseClient) {
  const user = await getUser(supabase);

  if (!user) {
    logger.warn("No user found when getting user ID");
    return null;
  }

  return user.id || null;
}
