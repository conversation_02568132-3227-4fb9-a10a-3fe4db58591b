/**
 * Email utilities for generating Message-IDs and handling plus addressing
 */
import crypto from "crypto";
import { createLogger } from "./logger";

const logger = createLogger("EmailUtils");

/**
 * Generate a unique Message-ID for an email
 * Format: <random-string@domain>
 *
 * @param domain The domain to use for the Message-ID
 * @param prefix Optional prefix for the random string
 * @returns A unique Message-ID
 */
export function generateMessageId(domain: string, prefix: string = ""): string {
  // Generate a random string
  const randomString = crypto.randomBytes(16).toString("hex");

  // Create the Message-ID
  return `<${prefix}${randomString}@${domain}>`;
}

/**
 * Generate a plus-addressed email for RFQ replies
 * Format: rfq+{rfq_id}@domain.com
 *
 * @param rfqId The RFQ ID to include in the plus address
 * @param baseEmail The base email address (e.g., "<EMAIL>")
 * @returns The plus-addressed email
 */
export function generateRfqPlusAddress(
  rfqId: string,
  baseEmail: string,
): string {
  // Extract the local part and domain from the base email
  const [localPart, domain] = baseEmail.split("@");

  if (!localPart || !domain) {
    logger.error(`Invalid base email: ${baseEmail}`);
    throw new Error("Invalid base email format");
  }

  // Create the plus-addressed email
  return `${localPart}+rfq-${rfqId}@${domain}`;
}

/**
 * Extract the RFQ ID from a plus-addressed email
 * Format: rfq+{rfq_id}@domain.com
 *
 * @param email The plus-addressed email
 * @returns The RFQ ID or null if not found
 */
export function extractRfqIdFromPlusAddress(email: string): string | null {
  try {
    // Extract the local part from the email
    const localPart = email.split("@")[0];

    if (!localPart) {
      return null;
    }

    // Check if this is a plus-addressed email for an RFQ
    const plusMatch = localPart.match(/^.+\+rfq-([a-f0-9-]+)$/i);

    if (plusMatch && plusMatch[1]) {
      return plusMatch[1];
    }

    return null;
  } catch (error) {
    logger.error("Error extracting RFQ ID from plus address:", error);
    return null;
  }
}

/**
 * Extract the RFQ ID from the Reply-To header of an email
 *
 * @param replyTo The Reply-To header value
 * @returns The RFQ ID or null if not found
 */
export function extractRfqIdFromReplyTo(replyTo: string): string | null {
  try {
    // Handle multiple email addresses in Reply-To
    const emails = replyTo.split(",").map((email) => email.trim());

    // Try to extract RFQ ID from each email
    for (const email of emails) {
      const rfqId = extractRfqIdFromPlusAddress(email);
      if (rfqId) {
        return rfqId;
      }
    }

    return null;
  } catch (error) {
    logger.error("Error extracting RFQ ID from Reply-To:", error);
    return null;
  }
}

/**
 * Extract domain from an email address
 *
 * @param email The email address
 * @returns The domain part of the email
 */
export function extractDomainFromEmail(email: string): string {
  const parts = email.split("@");
  return parts.length > 1 ? parts[1] : "";
}
