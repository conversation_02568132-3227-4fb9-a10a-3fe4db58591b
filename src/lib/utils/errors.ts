import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("Errors");

export class AppError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string,
  ) {
    super(message);
    this.name = "AppError";
  }
}

export class ValidationError extends AppError {
  constructor(message: string, code?: string) {
    super(message, 400, code || "VALIDATION_ERROR");
    this.name = "ValidationError";
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = "Authentication failed", code?: string) {
    super(message, 401, code || "AUTHENTICATION_ERROR");
    this.name = "AuthenticationError";
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = "Not authorized", code?: string) {
    super(message, 403, code || "AUTHORIZATION_ERROR");
    this.name = "AuthorizationError";
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = "Resource not found", code?: string) {
    super(message, 404, code || "NOT_FOUND_ERROR");
    this.name = "NotFoundError";
  }
}

export class ConflictError extends AppError {
  constructor(message: string = "Resource already exists", code?: string) {
    super(message, 409, code || "CONFLICT_ERROR");
    this.name = "ConflictError";
  }
}

export function handleApiError(error: unknown) {
  const errorLog = {
    timestamp: new Date().toISOString(),
    error:
      error instanceof Error
        ? {
            name: error.name,
            message: error.message,
            stack: error.stack,
          }
        : error,
    type: error instanceof AppError ? error.constructor.name : typeof error,
  };

  logger.error("API Error Details:", JSON.stringify(errorLog, null, 2));

  if (error instanceof AppError) {
    return {
      error: error.message,
      code: error.code,
      status: error.statusCode,
    };
  }

  if (error && typeof error === "object" && "message" in error) {
    return {
      error: (error as { message: string }).message,
      code: "INTERNAL_ERROR",
      status: 500,
    };
  }

  return {
    error: "An unexpected error occurred",
    code: "INTERNAL_ERROR",
    status: 500,
  };
}

export function isAppError(error: unknown): error is AppError {
  return error instanceof AppError;
}
