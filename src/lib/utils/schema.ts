/**
 * Schema utility functions for extending and customizing Supazod-generated schemas
 *
 * This file provides utility functions for working with Zod schemas, particularly
 * for extending and customizing Supazod-generated schemas to add additional validation
 * or transform them for specific use cases.
 */

import { z } from "zod";
import * as schemas from "@/lib/schemas/schemas";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("SchemaUtils");

/**
 * Creates a structured address schema with optional fields
 *
 * @returns A Zod schema for structured address data
 */
export const createStructuredAddressSchema = () =>
  z
    .object({
      street_address: z.string().optional(),
      city: z.string().optional(),
      state: z.string().optional(),
      postal_code: z.string().optional(),
      country: z.string().optional(),
    })
    .optional();

/**
 * Creates a pagination parameters schema with customizable defaults
 *
 * @param defaultPage Default page number (default: 1)
 * @param defaultPageSize Default page size (default: 10)
 * @param maxPageSize Maximum allowed page size (default: 100)
 * @returns A Zod schema for pagination parameters
 */
export const createPaginationSchema = (
  defaultPage = 1,
  defaultPageSize = 10,
  maxPageSize = 100,
) =>
  z.object({
    page: z.coerce.number().int().min(1).default(defaultPage),
    pageSize: z.coerce
      .number()
      .int()
      .min(1)
      .max(maxPageSize)
      .default(defaultPageSize),
  });

/**
 * Creates a schema for fetching a resource by ID with optional pagination
 *
 * @param idField Name of the ID field (default: "id")
 * @param errorMessage Custom error message for invalid UUID
 * @returns A Zod schema for fetching a resource by ID with pagination
 */
export const createGetResourceParamsSchema = (
  idField = "id",
  errorMessage = "Invalid ID",
) =>
  createPaginationSchema().extend({
    [idField]: z.string().uuid(errorMessage).optional(),
  });

/**
 * Extends a Supazod-generated schema with additional validation or transformations
 *
 * @param baseSchema The Supazod-generated schema to extend
 * @param extension The extension object with additional fields or overrides
 * @returns A new Zod schema that extends the base schema
 */
export function extendSupazodSchema<
  T extends z.ZodRawShape,
  U extends z.ZodRawShape,
>(baseSchema: z.ZodObject<T>, extension: U): z.ZodObject<T & U> {
  return baseSchema.extend(extension) as z.ZodObject<T & U>;
}

/**
 * Creates a schema for provider-specific operations
 *
 * @returns An object containing provider-related schemas
 */
export function createProviderSchemas() {
  // Schema for fetching providers with pagination or by ID
  const getProvidersParamsSchema = createGetResourceParamsSchema(
    "id",
    "Invalid provider ID",
  );

  // Schema for creating a new provider with enhanced validation
  const createProviderSchema = extendSupazodSchema(
    schemas.publicProvidersInsertSchemaSchema,
    {
      name: z.string().min(1, "Name is required"),
      // Ensure status is always defined with a default value
      status: z
        .enum(["active", "inactive", "pending", "suspended"])
        .default("pending"),
      structured_address: createStructuredAddressSchema(),
    },
  );

  // Schema for updating an existing provider
  const updateProviderSchema = z.object({
    id: z.string().uuid("Invalid provider ID"),
    name: z.string().min(1, "Name is required").optional(),
    full_address: z.string().nullable().optional(),
    structured_address: createStructuredAddressSchema().optional(),
    tax_id: z.string().nullable().optional(),
    status: z.enum(["active", "inactive", "pending", "suspended"]).optional(),
    verified: z.boolean().optional(),
  });

  // Schema for deleting a provider
  const deleteProviderParamsSchema = z.object({
    id: z.string().uuid("Invalid provider ID"),
  });

  return {
    getProvidersParamsSchema,
    createProviderSchema,
    updateProviderSchema,
    deleteProviderParamsSchema,
  };
}

/**
 * Creates a schema for provider contact operations
 *
 * @returns An object containing provider contact related schemas
 */
export function createProviderContactSchemas() {
  // Schema for creating a provider contact with enhanced validation
  const createProviderContactSchema = extendSupazodSchema(
    schemas.publicProviderContactsInsertSchemaSchema,
    {
      email: z.string().email("Invalid email format"),
      provider_id: z.string().uuid("Invalid provider ID"),
    },
  );

  // Schema for updating a provider contact
  const updateProviderContactSchema = createProviderContactSchema
    .partial()
    .extend({
      id: z.string().uuid("Invalid contact ID"),
    });

  // Schema for deleting a provider contact
  const deleteProviderContactParamsSchema = z.object({
    id: z.string().uuid("Invalid contact ID"),
    provider_id: z.string().uuid("Invalid provider ID").optional(),
  });

  // Schema for fetching provider contacts
  const getProviderContactsParamsSchema = z.object({
    provider_id: z.string().uuid("Invalid provider ID"),
  });

  return {
    createProviderContactSchema,
    updateProviderContactSchema,
    deleteProviderContactParamsSchema,
    getProviderContactsParamsSchema,
  };
}

/**
 * Creates a schema for provider equipment operations
 *
 * @returns An object containing provider equipment related schemas
 */
export function createProviderEquipmentSchemas() {
  // Schema for adding equipment to a provider
  const addProviderEquipmentSchema = z.object({
    provider_id: z.string().uuid("Invalid provider ID"),
    equipment_type_id: z.string().uuid("Invalid equipment type ID"),
    quantity: z.number().int().min(1).default(1),
  });

  // Schema for updating provider equipment
  const updateProviderEquipmentSchema = z.object({
    id: z.string().uuid("Invalid equipment ID"),
    quantity: z.number().int().min(1),
  });

  // Schema for deleting provider equipment
  const deleteProviderEquipmentParamsSchema = z.object({
    id: z.string().uuid("Invalid equipment ID"),
    provider_id: z.string().uuid("Invalid provider ID").optional(),
  });

  // Schema for fetching provider equipment
  const getProviderEquipmentParamsSchema = z.object({
    provider_id: z.string().uuid("Invalid provider ID"),
  });

  return {
    addProviderEquipmentSchema,
    updateProviderEquipmentSchema,
    deleteProviderEquipmentParamsSchema,
    getProviderEquipmentParamsSchema,
  };
}
