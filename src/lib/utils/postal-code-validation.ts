/**
 * Postal Code Validation Utilities
 *
 * This module provides utilities for validating postal codes using the postal-code-validator package.
 * It handles country name mapping and provides a clean interface for postal code validation.
 */

import { isValidCountryPostalCode, Country } from 'postal-code-validator';
import { createLogger } from '@/lib/utils/logger/logger';

const logger = createLogger('PostalCodeValidation');

/**
 * Maps database country names to postal-code-validator Country enum values
 *
 * This mapping handles the differences between how countries are stored in our database
 * and how they are represented in the postal-code-validator package.
 */
const COUNTRY_NAME_MAPPING: Record<string, Country> = {
  // Common countries with exact matches
  'Afghanistan': Country.Afghanistan,
  'Albania': Country.Albania,
  'Algeria': Country.Algeria,
  'Andorra': Country.Andorra,
  'Angola': Country.Angola,
  'Argentina': Country.Argentina,
  'Armenia': Country.Armenia,
  'Australia': Country.Australia,
  'Austria': Country.Austria,
  'Azerbaijan': Country.Azerbaijan,
  'Bahrain': Country.Bahrain,
  'Bangladesh': Country.Bangladesh,
  'Belarus': Country.Belarus,
  'Belgium': Country.Belgium,
  'Belize': Country.Belize,
  'Benin': Country.Benin,
  'Bhutan': Country.Bhutan,
  'Bolivia': Country.Bolivia,
  'Botswana': Country.Botswana,
  'Brazil': Country.Brazil,
  'Bulgaria': Country.Bulgaria,
  'Cambodia': Country.Cambodia,
  'Cameroon': Country.Cameroon,
  'Canada': Country.Canada,
  'Chad': Country.Chad,
  'Chile': Country.Chile,
  'China': Country.China,
  'Colombia': Country.Colombia,
  'Croatia': Country.Croatia,
  'Cuba': Country.Cuba,
  'Cyprus': Country.Cyprus,
  'Denmark': Country.Denmark,
  'Ecuador': Country.Ecuador,
  'Egypt': Country.Egypt,
  'Estonia': Country.Estonia,
  'Ethiopia': Country.Ethiopia,
  'Fiji': Country.Fiji,
  'Finland': Country.Finland,
  'France': Country.France,
  'Gabon': Country.Gabon,
  'Georgia': Country.Georgia,
  'Germany': Country.Germany,
  'Ghana': Country.Ghana,
  'Greece': Country.Greece,
  'Guatemala': Country.Guatemala,
  'Guinea': Country.Guinea,
  'Guyana': Country.Guyana,
  'Haiti': Country.Haiti,
  'Honduras': Country.Honduras,
  'Hungary': Country.Hungary,
  'Iceland': Country.Iceland,
  'India': Country.India,
  'Indonesia': Country.Indonesia,
  'Iran': Country.Iran,
  'Iraq': Country.Iraq,
  'Ireland': Country.Ireland,
  'Israel': Country.Israel,
  'Italy': Country.Italy,
  'Jamaica': Country.Jamaica,
  'Japan': Country.Japan,
  'Jordan': Country.Jordan,
  'Kenya': Country.Kenya,
  'Kuwait': Country.Kuwait,
  'Kyrgyzstan': Country.Kyrgyzstan,
  'Laos': Country.Laos,
  'Latvia': Country.Latvia,
  'Lebanon': Country.Lebanon,
  'Lesotho': Country.Lesotho,
  'Liberia': Country.Liberia,
  'Libya': Country.Libya,
  'Liechtenstein': Country.Liechtenstein,
  'Lithuania': Country.Lithuania,
  'Luxembourg': Country.Luxembourg,
  'Madagascar': Country.Madagascar,
  'Malawi': Country.Malawi,
  'Malaysia': Country.Malaysia,
  'Maldives': Country.Maldives,
  'Mali': Country.Mali,
  'Malta': Country.Malta,
  'Mauritania': Country.Mauritania,
  'Mauritius': Country.Mauritius,
  'Mexico': Country.Mexico,
  'Moldova': Country.Moldova,
  'Monaco': Country.Monaco,
  'Mongolia': Country.Mongolia,
  'Montenegro': Country.Montenegro,
  'Morocco': Country.Morocco,
  'Mozambique': Country.Mozambique,
  'Myanmar': Country.Myanmar,
  'Namibia': Country.Namibia,
  'Nauru': Country.Nauru,
  'Nepal': Country.Nepal,
  'Netherlands': Country.Netherlands,
  'Nicaragua': Country.Nicaragua,
  'Niger': Country.Niger,
  'Nigeria': Country.Nigeria,
  'Norway': Country.Norway,
  'Oman': Country.Oman,
  'Pakistan': Country.Pakistan,
  'Palau': Country.Palau,
  'Panama': Country.Panama,
  'Paraguay': Country.Paraguay,
  'Peru': Country.Peru,
  'Philippines': Country.Philippines,
  'Poland': Country.Poland,
  'Portugal': Country.Portugal,
  'Qatar': Country.Qatar,
  'Romania': Country.Romania,
  'Russia': Country.Russia,
  'Rwanda': Country.Rwanda,
  'Senegal': Country.Senegal,
  'Singapore': Country.Singapore,
  'Slovakia': Country.Slovakia,
  'Slovenia': Country.Slovenia,
  'Somalia': Country.Somalia,
  'Spain': Country.Spain,
  'Sudan': Country.Sudan,
  'Suriname': Country.Suriname,
  'Sweden': Country.Sweden,
  'Switzerland': Country.Switzerland,
  'Syria': Country.Syria,
  'Taiwan': Country.Taiwan,
  'Tajikistan': Country.Tajikistan,
  'Tanzania': Country.Tanzania,
  'Thailand': Country.Thailand,
  'Togo': Country.Togo,
  'Tunisia': Country.Tunisia,
  'Turkey': Country.Turkey,
  'Turkmenistan': Country.Turkmenistan,
  'Uganda': Country.Uganda,
  'Ukraine': Country.Ukraine,
  'Uruguay': Country.Uruguay,
  'Uzbekistan': Country.Uzbekistan,
  'Venezuela': Country.Venezuela,
  'Vietnam': Country.Vietnam,
  'Yemen': Country.Yemen,
  'Zambia': Country.Zambia,
  'Zimbabwe': Country.Zimbabwe,

  // Countries with name variations that need mapping
  'United States': Country.UnitedStatesOfAmerica,
  'United States of America': Country.UnitedStatesOfAmerica,
  'USA': Country.UnitedStatesOfAmerica,
  'US': Country.UnitedStatesOfAmerica,
  'United Kingdom': Country.UnitedKingdom,
  'UK': Country.UnitedKingdom,
  'Great Britain': Country.UnitedKingdom,
  'Czech Republic': Country.CzechRepublic,
  'Czechia': Country.CzechRepublic,
  'South Korea': Country.SouthKorea,
  'North Korea': Country.NorthKorea,
  'South Africa': Country.SouthAfrica,
  'New Zealand': Country.NewZealand,
  'Costa Rica': Country.CostaRica,
  'Dominican Republic': Country.DominicanRepublic,
  'El Salvador': Country.ElSalvador,
  'Saudi Arabia': Country.SaudiArabia,
  'Sri Lanka': Country.SriLanka,
  'United Arab Emirates': Country.UnitedArabEmirates,
  'UAE': Country.UnitedArabEmirates,
  'Bosnia and Herzegovina': Country.BosniaHerzegovina,
  'Bosnia-Herzegovina': Country.BosniaHerzegovina,
  'North Macedonia': Country.NorthMacedonia,
  'Macedonia': Country.NorthMacedonia,
};

/**
 * Validates a postal code for a specific country
 *
 * @param postalCode - The postal code to validate
 * @param countryName - The full country name (e.g., "Germany", "United States")
 * @returns boolean indicating if the postal code is valid for the country
 *
 * @example
 * ```typescript
 * validatePostalCodeForCountry("10115", "Germany") // true
 * validatePostalCodeForCountry("12345", "United States") // true
 * validatePostalCodeForCountry("invalid", "Germany") // false
 * ```
 */
export function validatePostalCodeForCountry(
  postalCode: string,
  countryName: string
): boolean {
  try {
    // Handle empty or invalid inputs
    if (!postalCode || !countryName) {
      logger.warn('Empty postal code or country name provided', { postalCode, countryName });
      return false;
    }

    // Trim whitespace
    const trimmedPostalCode = postalCode.trim();
    const trimmedCountryName = countryName.trim();

    // Map country name to postal-code-validator Country enum
    const mappedCountry = COUNTRY_NAME_MAPPING[trimmedCountryName];

    if (!mappedCountry) {
      logger.warn('Country not found in mapping', { countryName: trimmedCountryName });
      // If country is not in our mapping, we'll be permissive and return true
      // This prevents blocking form submissions for countries we haven't mapped yet
      return true;
    }

    // Use the postal-code-validator package to validate
    const isValid = isValidCountryPostalCode(trimmedPostalCode, mappedCountry);

    logger.debug('Postal code validation result', {
      postalCode: trimmedPostalCode,
      countryName: trimmedCountryName,
      mappedCountry,
      isValid
    });

    return isValid;
  } catch (error) {
    logger.error('Error validating postal code', {
      error: error instanceof Error ? error.message : String(error),
      postalCode,
      countryName
    });

    // On error, be permissive and return true to avoid blocking form submissions
    return true;
  }
}

/**
 * Gets a user-friendly error message for invalid postal codes
 *
 * @param countryName - The country name for context
 * @returns A descriptive error message
 */
export function getPostalCodeErrorMessage(countryName: string): string {
  const mappedCountry = COUNTRY_NAME_MAPPING[countryName?.trim()];

  if (!mappedCountry) {
    return 'Please enter a valid postal code';
  }

  // Provide country-specific guidance for common countries
  switch (mappedCountry) {
    case Country.UnitedStatesOfAmerica:
      return 'Please enter a valid US ZIP code (e.g., 12345 or 12345-6789)';
    case Country.Germany:
      return 'Please enter a valid German postal code (e.g., 10115)';
    case Country.UnitedKingdom:
      return 'Please enter a valid UK postcode (e.g., SW1A 1AA)';
    case Country.Canada:
      return 'Please enter a valid Canadian postal code (e.g., K1A 0A6)';
    case Country.France:
      return 'Please enter a valid French postal code (e.g., 75001)';
    case Country.Netherlands:
      return 'Please enter a valid Dutch postal code (e.g., 1012 JS)';
    case Country.Australia:
      return 'Please enter a valid Australian postcode (e.g., 2000)';
    default:
      return `Please enter a valid postal code for ${countryName}`;
  }
}

/**
 * Type guard to check if a country name is supported for postal code validation
 *
 * @param countryName - The country name to check
 * @returns boolean indicating if the country is supported
 */
export function isCountrySupportedForPostalValidation(countryName: string): boolean {
  return countryName?.trim() in COUNTRY_NAME_MAPPING;
}
