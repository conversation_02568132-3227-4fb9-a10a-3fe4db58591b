import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("RetryUtil");

/**
 * Options for the retry function
 */
export interface RetryOptions {
  /** Maximum number of retry attempts */
  maxRetries?: number;
  /** Initial delay in milliseconds before the first retry */
  initialDelay?: number;
  /** Maximum delay in milliseconds between retries */
  maxDelay?: number;
  /** Factor by which to increase the delay after each retry */
  factor?: number;
  /** Function to determine if a retry should be attempted based on the error */
  shouldRetry?: (error: any) => boolean;
  /** Function called before each retry attempt */
  onRetry?: (error: any, attempt: number) => void;
}

/**
 * Utility function to retry an async operation with exponential backoff
 * @param operation The async operation to retry
 * @param options Retry options
 * @returns The result of the operation
 */
export async function retry<T>(
  operation: () => Promise<T>,
  options: RetryOptions = {},
): Promise<T> {
  const {
    maxRetries = 3,
    initialDelay = 1000,
    maxDelay = 30000,
    factor = 2,
    shouldRetry = () => true,
    onRetry = () => {},
  } = options;

  let attempt = 0;
  let delay = initialDelay;

  while (true) {
    try {
      return await operation();
    } catch (error) {
      attempt++;

      if (attempt >= maxRetries || !shouldRetry(error)) {
        logger.error(
          `All retry attempts failed (${attempt}/${maxRetries}):`,
          error,
        );
        throw error;
      }

      onRetry(error, attempt);
      logger.warn(`Retry attempt ${attempt}/${maxRetries} after error:`, error);

      // Calculate next delay with exponential backoff
      delay = Math.min(delay * factor, maxDelay);

      // Add some jitter to avoid thundering herd problem
      const jitter = delay * 0.1 * Math.random();
      const totalDelay = delay + jitter;

      logger.debug(
        `Waiting ${Math.round(totalDelay)}ms before next retry attempt`,
      );
      await new Promise((resolve) => setTimeout(resolve, totalDelay));
    }
  }
}
