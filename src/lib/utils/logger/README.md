# Logger Utility

This utility provides a standardized logging system for the Steelflow application. It helps maintain consistent logging practices across the codebase and enables better control over log output in different environments.

## Features

- Different log levels (DEBUG, INFO, WARN, ERROR)
- Automatic disabling of debug logs in production
- Module-specific loggers for better context
- Structured logging format

## Usage

### Basic Usage

```typescript
import { createLogger } from "@/lib/utils/logger/logger";

// Create a logger instance for your component or module
const logger = createLogger("MyComponent");

// Use the logger methods
logger.debug("This is a debug message");
logger.info("This is an info message");
logger.warn("This is a warning message");
logger.error("This is an error message");
```

### Logging with Additional Data

```typescript
logger.debug("User data:", { id: 123, name: "<PERSON>" });
logger.error("Failed to process request:", error);
```

### Global Logger

For quick access, you can use the default logger:

```typescript
import { debug, info, warn, error } from "@/lib/utils/logger";

debug("This is a debug message");
info("This is an info message");
```

## Configuration

The logger is configured to automatically disable debug logs in production environments. You can customize this behavior:

```typescript
import { configureLogger, LogLevel } from "@/lib/utils/logger";

// Configure the logger
configureLogger({
  minLevel:
    process.env.NODE_ENV === "development" ? LogLevel.DEBUG : LogLevel.INFO,
  enableConsole: true,
});
```

## Log Levels

- **DEBUG**: Detailed information for debugging purposes
- **INFO**: General information about application operation
- **WARN**: Warning messages that don't prevent the application from working
- **ERROR**: Error messages that might prevent the application from working correctly

## Best Practices

1. **Create Module-Specific Loggers**: Use `createLogger('ModuleName')` to create loggers for specific components or modules.

2. **Use Appropriate Log Levels**:

   - `debug`: For detailed debugging information
   - `info`: For general operational information
   - `warn`: For warning conditions
   - `error`: For error conditions

3. **Include Contextual Information**: Add relevant context to log messages to make them more useful.

4. **Avoid Sensitive Information**: Never log sensitive information like passwords, tokens, or personal data.

5. **Be Concise**: Keep log messages clear and concise.

## Example: Replacing Console Logs

### Before:

```typescript
console.log("Sending RFQ to providers:", selectedProviders);
console.error("Error sending RFQ:", error);
```

### After:

```typescript
logger.info("Sending RFQ to providers:", selectedProviders);
logger.error("Error sending RFQ:", error);
```

## Implementation Details

The logger utility is implemented in `src/lib/utils/logger.ts`. It provides:

- Log level configuration
- Module-specific loggers
- Structured log format
- Environment-aware logging

In production, debug logs are automatically disabled to reduce noise and improve performance.
