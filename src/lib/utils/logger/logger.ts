/**
 * Logger utility for consistent logging across the application
 *
 * This utility provides different log levels and can be configured to disable
 * certain log levels in production environments.
 */

// Define log levels
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

// Default configuration
const defaultConfig = {
  minLevel:
    process.env.NODE_ENV === "production" ? LogLevel.INFO : LogLevel.DEBUG,
  enableConsole: process.env.NODE_ENV !== "production",
};

// Logger configuration
let config = { ...defaultConfig };

/**
 * Configure the logger
 * @param options Configuration options
 */
export function configureLogger(options: Partial<typeof defaultConfig>) {
  config = { ...config, ...options };
}

/**
 * Log a message at the specified level
 * @param level Log level
 * @param message Message to log
 * @param args Additional arguments
 */
function log(level: LogLevel, message: string, ...args: any[]) {
  // Skip if log level is below the minimum level
  if (level < config.minLevel) {
    return;
  }

  // Format the message with a prefix indicating the module/component
  const formattedMessage = `[${LogLevel[level]}] ${message}`;

  // Log to console if enabled
  if (config.enableConsole) {
    switch (level) {
      case LogLevel.DEBUG:
        console.debug(formattedMessage, ...args);
        break;
      case LogLevel.INFO:
        console.info(formattedMessage, ...args);
        break;
      case LogLevel.WARN:
        console.warn(formattedMessage, ...args);
        break;
      case LogLevel.ERROR:
        console.error(formattedMessage, ...args);
        break;
    }
  }

  // In a real application, you might want to add additional logging targets here
  // such as sending logs to a server or writing to a file
}

/**
 * Create a logger instance for a specific module
 * @param module Module name
 * @returns Logger instance
 */
export function createLogger(module: string) {
  return {
    debug: (message: string, ...args: any[]) =>
      log(LogLevel.DEBUG, `[${module}] ${message}`, ...args),
    info: (message: string, ...args: any[]) =>
      log(LogLevel.INFO, `[${module}] ${message}`, ...args),
    warn: (message: string, ...args: any[]) =>
      log(LogLevel.WARN, `[${module}] ${message}`, ...args),
    error: (message: string, ...args: any[]) =>
      log(LogLevel.ERROR, `[${module}] ${message}`, ...args),
  };
}

// Default logger instance
export const logger = createLogger("Steelflow");

// Export default logger methods for convenience
export const debug = logger.debug;
export const info = logger.info;
export const warn = logger.warn;
export const error = logger.error;
