/**
 * Utility functions for equipment-related operations
 */

/**
 * Get color classes for an equipment category
 * @param category The equipment category
 * @returns Object with bg, text, and border color classes
 */
export const getEquipmentColor = (
  category: string,
): { bg: string; text: string; border: string } => {
  switch (category.toLowerCase()) {
    case "trailer":
      return {
        bg: "bg-blue-50",
        text: "text-blue-700",
        border: "border-blue-200",
      };
    case "container":
      return {
        bg: "bg-green-50",
        text: "text-green-700",
        border: "border-green-200",
      };
    case "vehicle":
      return {
        bg: "bg-amber-50",
        text: "text-amber-700",
        border: "border-amber-200",
      };
    case "chassis":
      return {
        bg: "bg-purple-50",
        text: "text-purple-700",
        border: "border-purple-200",
      };
    default:
      return {
        bg: "bg-gray-50",
        text: "text-gray-700",
        border: "border-gray-200",
      };
  }
};
