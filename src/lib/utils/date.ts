import { format, formatDistanceToNow, isValid } from "date-fns";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("DateUtils");

/**
 * Format a date string to a human-readable format
 *
 * @param dateString ISO date string to format
 * @param formatStr Optional format string (defaults to "PPP" - "Apr 29, 2023")
 * @returns Formatted date string or "Not specified" if no date provided
 */
export function formatDate(dateString?: string, formatStr: string = "PPP"): string {
  if (!dateString) return "Not specified";

  try {
    const date = new Date(dateString);
    return format(date, formatStr);
  } catch (error) {
    console.error("Error formatting date:", error);
    return "Invalid date";
  }
}

/**
 * Safely formats a date string using the specified format
 * Handles various date formats including ISO strings and Unix timestamps
 *
 * @param dateStr The date string to format
 * @param formatStr The date-fns format string to use (default: "PPpp")
 * @returns Formatted date string or fallback message if invalid
 */
export function formatDateSafely(dateStr?: string, formatStr?: string): string {
  if (!dateStr) return "Unknown date";

  try {
    // Create date - works for both numeric strings and ISO date strings
    const date = new Date(Number(dateStr) || dateStr);

    if (!isValid(date)) {
      logger.warn(`Invalid date encountered: ${dateStr}`);
      return "Invalid date";
    }

    return format(date, formatStr || "PPpp");
  } catch (error) {
    logger.error(`Error formatting date ${dateStr}:`, error);
    return "Date error";
  }
}

/**
 * Safely formats a date string using formatDistanceToNow
 * Handles various date formats including ISO strings and Unix timestamps
 *
 * @param dateStr The date string to format
 * @param options Options for formatDistanceToNow
 * @returns Relative time string or fallback message if invalid
 */
export function formatDistanceToNowSafely(
  dateStr?: string,
  options: { addSuffix?: boolean } = { addSuffix: true }
): string {
  if (!dateStr) return "Unknown date";

  try {
    // Create date - works for both numeric strings and ISO date strings
    const date = new Date(Number(dateStr) || dateStr);

    if (!isValid(date)) {
      logger.warn(`Invalid date encountered: ${dateStr}`);
      return "Invalid date";
    }

    return formatDistanceToNow(date, options);
  } catch (error) {
    logger.error(`Error formatting date ${dateStr}:`, error);
    return "Date error";
  }
}
