import { NextRequest, NextResponse } from "next/server";
import { NextRequestWithUnkeyContext, withUnkey } from "@unkey/nextjs";
import { formatApiResponse, handleApiError } from "./api";
import { createLogger } from "@/lib/utils/logger/logger";

// Create a logger instance for this module
const logger = createLogger("UnkeyUtils");

/**
 * Options for the createUnkeyHandler function
 */
export interface UnkeyHandlerOptions {
  /**
   * Required permissions for this API route
   * If provided, the API key must have all of these permissions
   */
  requiredPermissions?: string[];

  /**
   * Custom error handler for Unkey errors
   */
  onError?: (req: NextRequest, error: any) => Promise<Response> | Response;

  /**
   * Custom handler for invalid API keys
   */
  handleInvalidKey?: (
    req: NextRequest,
    result: any,
  ) => Promise<Response> | Response;
}

/**
 * Creates a handler for API routes that require Unkey authentication
 *
 * @param handler The handler function to execute if authentication is successful
 * @param options Options for the Unkey handler
 * @returns A function that can be used as a Next.js API route handler
 */
export function createUnkeyHandler(
  handler: (req: NextRequest, keyData: any) => Promise<Response> | Response,
  options: UnkeyHandlerOptions = {},
) {
  const { requiredPermissions = [] } = options;

  return withUnkey(
    async (req: NextRequestWithUnkeyContext) => {
      try {
        // Check if the API key is valid
        if (!req.unkey?.valid) {
          return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // Check if the API key has the required permissions
        if (requiredPermissions.length > 0 && req.unkey) {
          const hasAllPermissions = requiredPermissions.every((permission) =>
            req.unkey?.permissions?.includes(permission),
          );

          if (!hasAllPermissions) {
            const keyId = req.unkey?.keyId || "unknown";
            logger.warn(
              `API key ${keyId} missing required permissions: ${requiredPermissions.join(", ")}`,
            );
            return NextResponse.json(
              {
                error: "Insufficient permissions",
                requiredPermissions,
              },
              { status: 403 },
            );
          }
        }

        // Call the handler with the request and key data
        return await handler(req, req.unkey);
      } catch (error) {
        return handleApiError(error, "Failed to process request");
      }
    },
    {
      apiId: process.env.UNKEY_API_ID,
      onError:
        options.onError ||
        ((req, error) => {
          logger.error(`Unkey error: ${error?.message || "Unknown error"}`);
          return NextResponse.json(
            { error: "Error verifying API key" },
            { status: 500 },
          );
        }),
      handleInvalidKey:
        options.handleInvalidKey ||
        ((req, result) => {
          if (result) {
            logger.warn(`Invalid API key: ${result.requestId}`);
          } else {
            logger.warn("Invalid API key: No details available");
          }
          return NextResponse.json(
            { error: "Invalid API key" },
            { status: 401 },
          );
        }),
    },
  );
}

/**
 * Wraps existing API route handlers with Unkey authentication
 *
 * @param handlers Object containing HTTP method handlers (GET, POST, etc.)
 * @param options Options for the Unkey handler
 * @returns Object with wrapped handlers
 */
export function protectApiRoutes(
  handlers: Record<string, Function>,
  options: UnkeyHandlerOptions = {},
) {
  const wrappedHandlers: Record<string, Function> = {};

  // Wrap each handler with Unkey authentication
  for (const [method, handler] of Object.entries(handlers)) {
    wrappedHandlers[method] = createUnkeyHandler(
      async (req: NextRequest, _keyData: any) => {
        try {
          // Call the original handler with just the request
          return await (handler as Function)(req);
        } catch (error) {
          return handleApiError(error, `Failed to process ${method} request`);
        }
      },
      options,
    );
  }

  return wrappedHandlers;
}
