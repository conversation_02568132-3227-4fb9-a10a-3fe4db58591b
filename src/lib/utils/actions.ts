"use server";

import { ZodSchema } from "zod";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("ActionsUtils");

// Define response type for actions
export type ActionResponse<T> =
  | { success: true; data: T }
  | { success: false; error: string; fieldErrors?: Record<string, string[]> };

// Define state type for form actions
export type ActionState = {
  message: string;
  errors?: {
    formErrors: string[];
    fieldErrors: { [k: string]: string[] | undefined };
  };
  success: boolean;
};

/**
 * Validates data against a Zod schema
 * @param schema The Zod schema to validate against
 * @param data The data to validate (FormData or object)
 * @returns A tuple containing [isValid, validatedData, errorState]
 */
export function validateFormData<T>(
  schema: ZodSchema<T>,
  data: FormData | Record<string, any>,
): [boolean, T | null, ActionState | null] {
  // Convert FormData to object if needed
  const dataObj =
    data instanceof FormData ? Object.fromEntries(data.entries()) : data;

  const result = schema.safeParse(dataObj);

  if (!result.success) {
    const errorState: ActionState = {
      message: "Validation failed.",
      errors: result.error.flatten(),
      success: false,
    };

    return [false, null, errorState];
  }

  return [true, result.data, null];
}

/**
 * Handles errors in server actions
 * @param error The error to handle
 * @param defaultMessage Default error message if error is not an instance of Error
 * @returns ActionState with error information
 */
export function handleActionError(
  error: unknown,
  defaultMessage = "An unexpected error occurred",
): ActionState {
  logger.error("Action Error:", error);

  const message = error instanceof Error ? error.message : defaultMessage;

  return {
    message,
    success: false,
  };
}

/**
 * Creates a successful action response
 * @param data The data to include in the response
 * @returns ActionResponse with success flag and data
 */
export function createSuccessResponse<T>(data: T): ActionResponse<T> {
  return { success: true, data };
}

/**
 * Creates an error action response
 * @param error The error message or Error object
 * @param fieldErrors Optional field-specific errors
 * @returns ActionResponse with success flag and error information
 */
export function createErrorResponse<T>(
  error: string | Error,
  fieldErrors?: Record<string, string[]>,
): ActionResponse<T> {
  const errorMessage = error instanceof Error ? error.message : error;

  return {
    success: false,
    error: errorMessage,
    fieldErrors,
  };
}

/**
 * Creates a successful action state
 * @param message Success message
 * @returns ActionState with success flag and message
 */
export function createSuccessState(message: string): ActionState {
  return {
    message,
    success: true,
  };
}

/**
 * Creates an error action state
 * @param message Error message
 * @param errors Optional validation errors
 * @returns ActionState with success flag and error information
 */
export function createErrorState(
  message: string,
  errors?: {
    formErrors: string[];
    fieldErrors: { [k: string]: string[] | undefined };
  },
): ActionState {
  return {
    message,
    errors,
    success: false,
  };
}
