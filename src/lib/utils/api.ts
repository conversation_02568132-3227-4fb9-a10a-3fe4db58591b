import { NextResponse } from "next/server";
import { Zod<PERSON>rror, ZodSchema } from "zod";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("APIUtils");

/**
 * Validates input data against a Zod schema
 * @param schema The Zod schema to validate against
 * @param data The data to validate
 * @returns A tuple containing [isValid, validatedData, errorResponse]
 */
export function validateWithSchema<T>(
  schema: ZodSchema<T>,
  data: unknown,
): [boolean, T | null, NextResponse] {
  const result = schema.safeParse(data);

  if (!result.success) {
    const errorResponse = NextResponse.json(
      {
        error: "Validation failed",
        details: result.error.flatten().fieldErrors,
      },
      { status: 400 },
    );

    return [false, null, errorResponse];
  }

  // Return an empty response that will never be used if validation succeeds
  return [true, result.data, new NextResponse()];
}

/**
 * Handles API errors and returns appropriate responses
 * @param error The error to handle
 * @param defaultMessage Default error message if error is not an instance of Error
 * @returns NextResponse with appropriate status code and error message
 */
export function handleApiError(
  error: unknown,
  defaultMessage = "An unexpected error occurred",
): NextResponse {
  logger.error("API Error:", error);

  if (!(error instanceof Error)) {
    return NextResponse.json({ error: defaultMessage }, { status: 500 });
  }

  // Handle specific error types
  if (error.message.includes("not found")) {
    return NextResponse.json({ error: error.message }, { status: 404 });
  }

  if (error.message.includes("already exists")) {
    return NextResponse.json({ error: error.message }, { status: 409 });
  }

  if (
    error.message.includes("in use") ||
    error.message.includes("referenced by")
  ) {
    return NextResponse.json({ error: error.message }, { status: 400 });
  }

  return NextResponse.json({ error: error.message }, { status: 500 });
}

/**
 * Formats a successful API response
 * @param data The data to include in the response
 * @param status The HTTP status code (default: 200)
 * @returns NextResponse with the data and status code
 */
export function formatApiResponse(data: any, status = 200): NextResponse {
  return NextResponse.json(data, { status });
}

/**
 * Formats a paginated API response
 * @param data The data array to include in the response
 * @param pagination The pagination metadata
 * @param status The HTTP status code (default: 200)
 * @returns NextResponse with the data, pagination metadata, and status code
 */
export function formatPaginatedApiResponse(
  data: any[],
  pagination: {
    page: number;
    pageSize: number;
    totalItems?: number;
    totalPages?: number;
  },
  status = 200,
): NextResponse {
  const {
    page,
    pageSize,
    totalItems = 0,
    totalPages = Math.ceil(totalItems / pageSize),
  } = pagination;

  return NextResponse.json(
    {
      data,
      pagination: {
        page,
        pageSize,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    },
    { status },
  );
}
