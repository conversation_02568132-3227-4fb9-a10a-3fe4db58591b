/**
 * Reusable toast utilities for consistent toast notifications
 */

import { toast } from "@/components/ui/sonner";

interface ToastOptions {
  title: string;
  description?: string;
  duration?: number;
}

/**
 * Show a success toast notification
 */
export function showSuccessToast({ title, description, duration = 5000 }: ToastOptions) {
  toast.success(title, {
    description,
    duration,
  });
}

/**
 * Show an error toast notification
 */
export function showErrorToast({ title, description, duration = 5000 }: ToastOptions) {
  toast.error(title, {
    description,
    duration,
  });
}

/**
 * Show an info toast notification
 */
export function showInfoToast({ title, description, duration = 5000 }: ToastOptions) {
  toast.info(title, {
    description,
    duration,
  });
}

/**
 * Show a warning toast notification
 */
export function showWarningToast({ title, description, duration = 5000 }: ToastOptions) {
  toast.warning(title, {
    description,
    duration,
  });
}
