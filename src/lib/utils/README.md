# Utility Functions

This directory contains utility functions that are used throughout the application to reduce code duplication and ensure consistent behavior.

## API Utilities (`api.ts`)

These utilities are designed for use in API routes to standardize validation, error handling, and response formatting.

### Functions

- **`validateWithSchema<T>(schema, data)`**: Validates input data against a Zod schema
- **`handleApiError(error, defaultMessage)`**: Handles API errors and returns appropriate responses
- **`formatApiResponse(data, status)`**: Formats a successful API response
- **`formatPaginatedApiResponse(data, pagination, status)`**: Formats a paginated API response

### Example Usage

```typescript
// In an API route
export async function GET(request: NextRequest) {
  try {
    const params = {
      id: request.nextUrl.searchParams.get("id") ?? undefined,
    };

    // Validate params
    const [isValid, validatedData, errorResponse] = validateWithSchema(
      ParamsSchema,
      params,
    );

    if (!isValid) {
      return errorResponse;
    }

    // Use service to fetch data
    const result = await fetchData(validatedData);

    return formatApiResponse(result);
  } catch (error) {
    return handleApiError(error, "Failed to fetch data");
  }
}
```

## Action Utilities (`actions.ts`)

These utilities are designed for use in server actions to standardize validation, error handling, and response formatting.

### Types

- **`ActionResponse<T>`**: Type for responses from server actions
- **`ActionState`**: Type for form action states

### Functions

- **`validateFormData<T>(schema, data)`**: Validates form data against a Zod schema
- **`handleActionError(error, defaultMessage)`**: Handles errors in server actions
- **`createSuccessResponse<T>(data)`**: Creates a successful action response
- **`createErrorResponse<T>(error, fieldErrors)`**: Creates an error action response
- **`createSuccessState(message)`**: Creates a successful action state
- **`createErrorState(message, errors)`**: Creates an error action state

### Example Usage

```typescript
// In a server action
export async function createItem(
  _prevState: ActionState | undefined,
  formData: FormData,
): Promise<ActionState> {
  // Validate form data
  const [isValid, validatedData, errorState] = validateFormData(
    CreateItemSchema,
    formData,
  );

  if (!isValid) {
    return errorState;
  }

  try {
    await addItem(validatedData);
    revalidatePath("/dashboard/items");
    return createSuccessState("Item created successfully.");
  } catch (error) {
    return handleActionError(error, "Failed to create item.");
  }
}
```

## Benefits

Using these utility functions provides several benefits:

1. **Reduced Code Duplication**: Common patterns are extracted into reusable functions
2. **Consistent Behavior**: All API routes and server actions handle validation and errors in the same way
3. **Improved Maintainability**: Changes to validation or error handling only need to be made in one place
4. **Better Type Safety**: Proper TypeScript types ensure correct usage

## Best Practices

- Always use these utility functions instead of implementing your own validation and error handling
- Keep the utility functions focused and atomic
- Add new utility functions when you find yourself duplicating code across components
