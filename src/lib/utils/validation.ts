/**
 * Simplified validation utility for Supazod-generated schemas
 *
 * This file provides a generic validation function for validating data against Zod schemas,
 * including Supazod-generated schemas.
 */

import { z } from "zod";
import { createLogger } from "@/lib/utils/logger/logger";
import { ValidationResult } from "@/lib/schemas";

const logger = createLogger("ValidationUtils");

/**
 * Generic validation function that validates data against a Zod schema
 *
 * @param data The data to validate
 * @param schema The Zod schema to validate against
 * @returns Validation result with success status, data, or error
 *
 * @example
 * // Import Supazod-generated schemas
 * import * as schemas from "@/lib/schemas/schemas";
 *
 * // Validate provider creation data
 * const result = validateWithSchema(data, schemas.publicProvidersInsertSchemaSchema);
 *
 * if (result.success) {
 *   // Use validated data
 *   const validatedData = result.data;
 * } else {
 *   // Handle validation error
 *   console.error(result.error);
 * }
 */
export function validateWithSchema<T>(
  data: unknown,
  schema: z.ZodSchema<T>,
): ValidationResult<T> {
  try {
    const validationResult = schema.safeParse(data);

    if (!validationResult.success) {
      // Get field errors from Zod validation result
      const fieldErrors = validationResult.error.flatten().fieldErrors;

      return {
        success: false,
        error: {
          message: "Validation failed",
          fieldErrors: fieldErrors as
            | Record<string, string[]>
            | { [key: string]: string[] | undefined },
        },
      };
    }

    return {
      success: true,
      data: validationResult.data,
    };
  } catch (error) {
    logger.error(
      "Error validating data:",
      error instanceof Error ? error.message : String(error),
    );
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : "Validation error",
      },
    };
  }
}
