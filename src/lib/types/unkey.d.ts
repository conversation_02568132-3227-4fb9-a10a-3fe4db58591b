import { NextRequest, NextResponse } from "next/server";

declare module "@unkey/nextjs" {
  export interface NextRequestWithUnkeyContext extends NextRequest {
    unkey?: {
      keyId?: string;
      valid: boolean;
      name?: string;
      ownerId?: string;
      meta?: { [key: string]: unknown };
      expires?: number;
      ratelimit?: {
        limit: number;
        remaining: number;
        reset: number;
      };
      permissions?: string[];
      createdAt: number;
      environment?: string;
      workspaceId: string;
      requestId: string;
    };
  }

  export function withUnkey(
    handler: (req: NextRequestWithUnkeyContext) => Promise<Response> | Response,
    options: {
      apiId?: string;
      onError?: (req: NextRequest, error: any) => Response | Promise<Response>;
      handleInvalidKey?: (
        req: NextRequest,
        result: NextRequestWithUnkeyContext["unkey"],
      ) => Response | Promise<Response>;
    },
  ): (req: NextRequest) => Promise<Response>;
}
