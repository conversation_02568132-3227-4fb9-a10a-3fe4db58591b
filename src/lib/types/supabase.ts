export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      cargo_types: {
        Row: {
          created_at: string
          description: string | null
          id: string
          image_url: string | null
          name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          image_url?: string | null
          name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          image_url?: string | null
          name?: string
          updated_at?: string
        }
        Relationships: []
      }
      countries: {
        Row: {
          alpha2_code: string | null
          alpha3_code: string | null
          continent: string | null
          created_at: string | null
          currency_code: string | null
          id: string
          name: string
          region: string | null
          updated_at: string | null
        }
        Insert: {
          alpha2_code?: string | null
          alpha3_code?: string | null
          continent?: string | null
          created_at?: string | null
          currency_code?: string | null
          id?: string
          name: string
          region?: string | null
          updated_at?: string | null
        }
        Update: {
          alpha2_code?: string | null
          alpha3_code?: string | null
          continent?: string | null
          created_at?: string | null
          currency_code?: string | null
          id?: string
          name?: string
          region?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      email_attachments: {
        Row: {
          content: string | null
          content_type: string | null
          created_at: string
          filename: string
          gmail_attachment_id: string
          id: string
          message_id: string
          size: number | null
          storage_path: string | null
          updated_at: string
        }
        Insert: {
          content?: string | null
          content_type?: string | null
          created_at?: string
          filename: string
          gmail_attachment_id: string
          id?: string
          message_id: string
          size?: number | null
          storage_path?: string | null
          updated_at?: string
        }
        Update: {
          content?: string | null
          content_type?: string | null
          created_at?: string
          filename?: string
          gmail_attachment_id?: string
          id?: string
          message_id?: string
          size?: number | null
          storage_path?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "email_attachments_message_id_fkey"
            columns: ["message_id"]
            isOneToOne: false
            referencedRelation: "email_messages"
            referencedColumns: ["id"]
          },
        ]
      }
      email_messages: {
        Row: {
          account_id: string
          bcc_recipients: Json | null
          body_html: string | null
          body_text: string | null
          category: string | null
          cc_recipients: Json | null
          created_at: string | null
          date_received: string | null
          from_email: string | null
          from_name: string | null
          gmail_id: string
          gmail_thread_id: string
          id: string
          in_reply_to: string | null
          is_important: boolean | null
          is_read: boolean | null
          is_starred: boolean | null
          is_trash: boolean | null
          labels: Json | null
          message_id: string | null
          processed_for_rfq: boolean
          provider_id: string | null
          reply_to: string | null
          rfq_id: string | null
          snippet: string | null
          subject: string | null
          to_recipients: Json | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          bcc_recipients?: Json | null
          body_html?: string | null
          body_text?: string | null
          category?: string | null
          cc_recipients?: Json | null
          created_at?: string | null
          date_received?: string | null
          from_email?: string | null
          from_name?: string | null
          gmail_id: string
          gmail_thread_id: string
          id?: string
          in_reply_to?: string | null
          is_important?: boolean | null
          is_read?: boolean | null
          is_starred?: boolean | null
          is_trash?: boolean | null
          labels?: Json | null
          message_id?: string | null
          processed_for_rfq?: boolean
          provider_id?: string | null
          reply_to?: string | null
          rfq_id?: string | null
          snippet?: string | null
          subject?: string | null
          to_recipients?: Json | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          bcc_recipients?: Json | null
          body_html?: string | null
          body_text?: string | null
          category?: string | null
          cc_recipients?: Json | null
          created_at?: string | null
          date_received?: string | null
          from_email?: string | null
          from_name?: string | null
          gmail_id?: string
          gmail_thread_id?: string
          id?: string
          in_reply_to?: string | null
          is_important?: boolean | null
          is_read?: boolean | null
          is_starred?: boolean | null
          is_trash?: boolean | null
          labels?: Json | null
          message_id?: string | null
          processed_for_rfq?: boolean
          provider_id?: string | null
          reply_to?: string | null
          rfq_id?: string | null
          snippet?: string | null
          subject?: string | null
          to_recipients?: Json | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "email_messages_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "watched_email_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_messages_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_messages_rfq_id_fkey"
            columns: ["rfq_id"]
            isOneToOne: false
            referencedRelation: "rfqs"
            referencedColumns: ["id"]
          },
        ]
      }
      email_settings: {
        Row: {
          bcc_recipients: string[] | null
          company_logo_url: string | null
          company_name: string
          created_at: string
          created_by: string | null
          default_sender_email: string
          default_sender_name: string
          email_footer_text: string | null
          email_signature: string | null
          id: string
          reply_to_email: string | null
          sandbox_mode: boolean
          sandbox_recipient_email: string | null
          updated_at: string
          updated_by: string | null
        }
        Insert: {
          bcc_recipients?: string[] | null
          company_logo_url?: string | null
          company_name: string
          created_at?: string
          created_by?: string | null
          default_sender_email: string
          default_sender_name: string
          email_footer_text?: string | null
          email_signature?: string | null
          id?: string
          reply_to_email?: string | null
          sandbox_mode?: boolean
          sandbox_recipient_email?: string | null
          updated_at?: string
          updated_by?: string | null
        }
        Update: {
          bcc_recipients?: string[] | null
          company_logo_url?: string | null
          company_name?: string
          created_at?: string
          created_by?: string | null
          default_sender_email?: string
          default_sender_name?: string
          email_footer_text?: string | null
          email_signature?: string | null
          id?: string
          reply_to_email?: string | null
          sandbox_mode?: boolean
          sandbox_recipient_email?: string | null
          updated_at?: string
          updated_by?: string | null
        }
        Relationships: []
      }
      equipment_types: {
        Row: {
          category: string
          created_at: string
          description: string | null
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          category: string
          created_at?: string
          description?: string | null
          id?: string
          name: string
          updated_at?: string
        }
        Update: {
          category?: string
          created_at?: string
          description?: string | null
          id?: string
          name?: string
          updated_at?: string
        }
        Relationships: []
      }
      provider_contacts: {
        Row: {
          created_at: string | null
          created_by: string | null
          email: string
          id: string
          is_primary: boolean
          name: string | null
          phone: string | null
          provider_id: string
          role: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          email: string
          id?: string
          is_primary?: boolean
          name?: string | null
          phone?: string | null
          provider_id: string
          role?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          email?: string
          id?: string
          is_primary?: boolean
          name?: string | null
          phone?: string | null
          provider_id?: string
          role?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "provider_contacts_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "provider_contacts_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "provider_contacts_updated_by_fkey"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      provider_equipments: {
        Row: {
          created_at: string
          equipment_type_id: string
          id: string
          provider_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          equipment_type_id: string
          id?: string
          provider_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          equipment_type_id?: string
          id?: string
          provider_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "provider_equipments_equipment_type_id_fkey"
            columns: ["equipment_type_id"]
            isOneToOne: false
            referencedRelation: "equipment_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "provider_equipments_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "providers"
            referencedColumns: ["id"]
          },
        ]
      }
      provider_routes: {
        Row: {
          created_at: string | null
          created_by: string | null
          destination_country_id: string
          id: string
          is_active: boolean | null
          notes: string | null
          origin_country_id: string
          provider_id: string
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          destination_country_id: string
          id?: string
          is_active?: boolean | null
          notes?: string | null
          origin_country_id: string
          provider_id: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          destination_country_id?: string
          id?: string
          is_active?: boolean | null
          notes?: string | null
          origin_country_id?: string
          provider_id?: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "provider_routes_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "provider_routes_destination_country_id_fkey"
            columns: ["destination_country_id"]
            isOneToOne: false
            referencedRelation: "countries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "provider_routes_origin_country_id_fkey"
            columns: ["origin_country_id"]
            isOneToOne: false
            referencedRelation: "countries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "provider_routes_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "provider_routes_updated_by_fkey"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      providers: {
        Row: {
          created_at: string | null
          created_by: string | null
          full_address: string | null
          id: string
          name: string
          status: string
          structured_address: Json | null
          tax_id: string | null
          updated_at: string | null
          updated_by: string | null
          verified: boolean
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          full_address?: string | null
          id?: string
          name: string
          status?: string
          structured_address?: Json | null
          tax_id?: string | null
          updated_at?: string | null
          updated_by?: string | null
          verified?: boolean
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          full_address?: string | null
          id?: string
          name?: string
          status?: string
          structured_address?: Json | null
          tax_id?: string | null
          updated_at?: string | null
          updated_by?: string | null
          verified?: boolean
        }
        Relationships: [
          {
            foreignKeyName: "providers_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "providers_updated_by_fkey"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      rfq_bids: {
        Row: {
          created_at: string
          currency: string
          id: string
          is_ai_extracted: boolean
          notes: string | null
          original_email_id: string | null
          price: number
          provider_id: string
          rfq_id: string
          status: string
          submitted_at: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          currency?: string
          id?: string
          is_ai_extracted?: boolean
          notes?: string | null
          original_email_id?: string | null
          price: number
          provider_id: string
          rfq_id: string
          status: string
          submitted_at: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          currency?: string
          id?: string
          is_ai_extracted?: boolean
          notes?: string | null
          original_email_id?: string | null
          price?: number
          provider_id?: string
          rfq_id?: string
          status?: string
          submitted_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "rfq_bids_original_email_id_fkey"
            columns: ["original_email_id"]
            isOneToOne: false
            referencedRelation: "rfq_incoming_emails"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rfq_bids_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rfq_bids_rfq_id_fkey"
            columns: ["rfq_id"]
            isOneToOne: false
            referencedRelation: "rfqs"
            referencedColumns: ["id"]
          },
        ]
      }
      rfq_cargo_types: {
        Row: {
          cargo_type_id: string
          created_at: string
          id: string
          rfq_id: string
          updated_at: string
        }
        Insert: {
          cargo_type_id: string
          created_at?: string
          id?: string
          rfq_id: string
          updated_at?: string
        }
        Update: {
          cargo_type_id?: string
          created_at?: string
          id?: string
          rfq_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "rfq_cargo_types_cargo_type_id_fkey"
            columns: ["cargo_type_id"]
            isOneToOne: false
            referencedRelation: "cargo_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rfq_cargo_types_rfq_id_fkey"
            columns: ["rfq_id"]
            isOneToOne: false
            referencedRelation: "rfqs"
            referencedColumns: ["id"]
          },
        ]
      }
      rfq_incoming_emails: {
        Row: {
          attachments: Json | null
          body: string
          created_at: string
          extracted_bid_id: string | null
          from_email: string
          from_name: string | null
          id: string
          in_reply_to: string | null
          is_processed: boolean
          message_id: string | null
          processing_status: string | null
          provider_id: string
          received_at: string
          rfq_id: string
          subject: string
          thread_id: string | null
          updated_at: string
        }
        Insert: {
          attachments?: Json | null
          body: string
          created_at?: string
          extracted_bid_id?: string | null
          from_email: string
          from_name?: string | null
          id?: string
          in_reply_to?: string | null
          is_processed?: boolean
          message_id?: string | null
          processing_status?: string | null
          provider_id: string
          received_at: string
          rfq_id: string
          subject: string
          thread_id?: string | null
          updated_at?: string
        }
        Update: {
          attachments?: Json | null
          body?: string
          created_at?: string
          extracted_bid_id?: string | null
          from_email?: string
          from_name?: string | null
          id?: string
          in_reply_to?: string | null
          is_processed?: boolean
          message_id?: string | null
          processing_status?: string | null
          provider_id?: string
          received_at?: string
          rfq_id?: string
          subject?: string
          thread_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "rfq_incoming_emails_extracted_bid_id_fkey"
            columns: ["extracted_bid_id"]
            isOneToOne: false
            referencedRelation: "rfq_bids"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rfq_incoming_emails_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rfq_incoming_emails_rfq_id_fkey"
            columns: ["rfq_id"]
            isOneToOne: false
            referencedRelation: "rfqs"
            referencedColumns: ["id"]
          },
        ]
      }
      rfq_outgoing_emails: {
        Row: {
          bcc: string[] | null
          body: string
          bounce_reason: string | null
          cc: string[] | null
          clicked_at: string | null
          created_at: string
          delivered_at: string | null
          delivery_status: string
          failure_reason: string | null
          id: string
          is_ai_generated: boolean
          message_id: string | null
          old_status: string
          opened_at: string | null
          provider_id: string
          reply_to: string | null
          rfq_id: string
          sent_at: string | null
          subject: string
          thread_id: string | null
          to_email: string
          to_name: string | null
          tracking_id: string | null
          updated_at: string
        }
        Insert: {
          bcc?: string[] | null
          body: string
          bounce_reason?: string | null
          cc?: string[] | null
          clicked_at?: string | null
          created_at?: string
          delivered_at?: string | null
          delivery_status?: string
          failure_reason?: string | null
          id?: string
          is_ai_generated?: boolean
          message_id?: string | null
          old_status: string
          opened_at?: string | null
          provider_id: string
          reply_to?: string | null
          rfq_id: string
          sent_at?: string | null
          subject: string
          thread_id?: string | null
          to_email: string
          to_name?: string | null
          tracking_id?: string | null
          updated_at?: string
        }
        Update: {
          bcc?: string[] | null
          body?: string
          bounce_reason?: string | null
          cc?: string[] | null
          clicked_at?: string | null
          created_at?: string
          delivered_at?: string | null
          delivery_status?: string
          failure_reason?: string | null
          id?: string
          is_ai_generated?: boolean
          message_id?: string | null
          old_status?: string
          opened_at?: string | null
          provider_id?: string
          reply_to?: string | null
          rfq_id?: string
          sent_at?: string | null
          subject?: string
          thread_id?: string | null
          to_email?: string
          to_name?: string | null
          tracking_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "rfq_outgoing_emails_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rfq_outgoing_emails_rfq_id_fkey"
            columns: ["rfq_id"]
            isOneToOne: false
            referencedRelation: "rfqs"
            referencedColumns: ["id"]
          },
        ]
      }
      rfq_providers: {
        Row: {
          created_at: string
          email_opened_at: string | null
          id: string
          invited_at: string | null
          notes: string | null
          provider_id: string
          response_received_at: string | null
          rfq_id: string
          selected_at: string | null
          status: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          email_opened_at?: string | null
          id?: string
          invited_at?: string | null
          notes?: string | null
          provider_id: string
          response_received_at?: string | null
          rfq_id: string
          selected_at?: string | null
          status: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          email_opened_at?: string | null
          id?: string
          invited_at?: string | null
          notes?: string | null
          provider_id?: string
          response_received_at?: string | null
          rfq_id?: string
          selected_at?: string | null
          status?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "rfq_providers_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rfq_providers_rfq_id_fkey"
            columns: ["rfq_id"]
            isOneToOne: false
            referencedRelation: "rfqs"
            referencedColumns: ["id"]
          },
        ]
      }
      rfqs: {
        Row: {
          address_hash: string | null
          cargo_type_id: string | null
          coordinates_resolved_at: string | null
          created_at: string
          created_by: string
          destination_address: string
          destination_city: string
          destination_country_id: string
          destination_lat: number | null
          destination_lng: number | null
          destination_postal_code: string | null
          equipment_quantity: number
          equipment_type_id: string
          expiration_date: string | null
          height: number | null
          id: string
          length: number | null
          notes: string | null
          origin_address: string
          origin_city: string
          origin_country_id: string
          origin_lat: number | null
          origin_lng: number | null
          origin_postal_code: string | null
          preferred_shipping_date: string | null
          quantity: number
          route_distance_calculated_at: string | null
          route_distance_km: number | null
          sequence_number: string | null
          special_requirements: string | null
          status: string
          title: string | null
          updated_at: string
          weight: number
          width: number | null
        }
        Insert: {
          address_hash?: string | null
          cargo_type_id?: string | null
          coordinates_resolved_at?: string | null
          created_at?: string
          created_by: string
          destination_address: string
          destination_city: string
          destination_country_id: string
          destination_lat?: number | null
          destination_lng?: number | null
          destination_postal_code?: string | null
          equipment_quantity?: number
          equipment_type_id: string
          expiration_date?: string | null
          height?: number | null
          id?: string
          length?: number | null
          notes?: string | null
          origin_address: string
          origin_city: string
          origin_country_id: string
          origin_lat?: number | null
          origin_lng?: number | null
          origin_postal_code?: string | null
          preferred_shipping_date?: string | null
          quantity?: number
          route_distance_calculated_at?: string | null
          route_distance_km?: number | null
          sequence_number?: string | null
          special_requirements?: string | null
          status: string
          title?: string | null
          updated_at?: string
          weight: number
          width?: number | null
        }
        Update: {
          address_hash?: string | null
          cargo_type_id?: string | null
          coordinates_resolved_at?: string | null
          created_at?: string
          created_by?: string
          destination_address?: string
          destination_city?: string
          destination_country_id?: string
          destination_lat?: number | null
          destination_lng?: number | null
          destination_postal_code?: string | null
          equipment_quantity?: number
          equipment_type_id?: string
          expiration_date?: string | null
          height?: number | null
          id?: string
          length?: number | null
          notes?: string | null
          origin_address?: string
          origin_city?: string
          origin_country_id?: string
          origin_lat?: number | null
          origin_lng?: number | null
          origin_postal_code?: string | null
          preferred_shipping_date?: string | null
          quantity?: number
          route_distance_calculated_at?: string | null
          route_distance_km?: number | null
          sequence_number?: string | null
          special_requirements?: string | null
          status?: string
          title?: string | null
          updated_at?: string
          weight?: number
          width?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "rfqs_cargo_type_id_fkey"
            columns: ["cargo_type_id"]
            isOneToOne: false
            referencedRelation: "cargo_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rfqs_destination_country_id_fkey"
            columns: ["destination_country_id"]
            isOneToOne: false
            referencedRelation: "countries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rfqs_equipment_type_id_fkey"
            columns: ["equipment_type_id"]
            isOneToOne: false
            referencedRelation: "equipment_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rfqs_origin_country_id_fkey"
            columns: ["origin_country_id"]
            isOneToOne: false
            referencedRelation: "countries"
            referencedColumns: ["id"]
          },
        ]
      }
      roles: {
        Row: {
          created_at: string | null
          id: string
          name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          name?: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      user_roles: {
        Row: {
          created_at: string | null
          id: string
          role_id: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          role_id: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          role_id?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_role_id"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_user_id"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          id: string
          name: string | null
          phone_number: string | null
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          id: string
          name?: string | null
          phone_number?: string | null
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          id?: string
          name?: string | null
          phone_number?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      watched_email_accounts: {
        Row: {
          access_token: string | null
          created_at: string
          created_by: string | null
          display_name: string | null
          email: string
          id: string
          last_full_sync_at: string | null
          last_sync_at: string | null
          refresh_token: string
          status: string
          sync_error: string | null
          sync_message: string | null
          token_expiry: string | null
          updated_at: string
          watch_expiry: string | null
        }
        Insert: {
          access_token?: string | null
          created_at?: string
          created_by?: string | null
          display_name?: string | null
          email: string
          id?: string
          last_full_sync_at?: string | null
          last_sync_at?: string | null
          refresh_token: string
          status?: string
          sync_error?: string | null
          sync_message?: string | null
          token_expiry?: string | null
          updated_at?: string
          watch_expiry?: string | null
        }
        Update: {
          access_token?: string | null
          created_at?: string
          created_by?: string | null
          display_name?: string | null
          email?: string
          id?: string
          last_full_sync_at?: string | null
          last_sync_at?: string | null
          refresh_token?: string
          status?: string
          sync_error?: string | null
          sync_message?: string | null
          token_expiry?: string | null
          updated_at?: string
          watch_expiry?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      has_role: {
        Args: { user_id: string; role_name: string }
        Returns: boolean
      }
      is_admin: {
        Args: { user_id: string }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
