import { Tables } from "@/lib/types/supabase";

/**
 * Provider route with related country information
 */
export type ProviderRoute = Tables<"provider_routes"> & {
  origin_country: Tables<"countries">;
  destination_country: Tables<"countries">;
};

/**
 * Country information
 */
export type Country = Tables<"countries">;

/**
 * Props for the RoutesPanel component
 */
export interface RoutesPanelProps {
  providerId: string;
  routes: ProviderRoute[];
  countries: Country[];
}

/**
 * Props for the RouteForm component
 */
export interface RouteFormProps {
  providerId: string;
  countries: Country[];
  initialData?: ProviderRoute;
  onSuccess?: () => void;
}

/**
 * Props for the RouteFormModal component
 */
export interface RouteFormModalProps {
  providerId: string;
  countries: Country[];
  onSuccess?: () => void;
}

/**
 * Props for the DeleteRouteDialog component
 */
export interface DeleteRouteDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  routeToDelete: ProviderRoute | null;
  onDelete: () => Promise<void>;
  isDeleting: boolean;
}

/**
 * Props for the EditRouteDialog component
 */
export interface EditRouteDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  selectedRoute: ProviderRoute | undefined;
  providerId: string;
  countries: Country[];
  onSuccess: () => void;
}
