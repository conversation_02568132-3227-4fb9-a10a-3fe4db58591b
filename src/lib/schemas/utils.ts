/**
 * Schema utility functions
 *
 * This file contains utility functions for creating and extending schemas.
 * These functions are used across multiple schema files to maintain consistency
 * and avoid duplication.
 */

import { z } from "zod";

/**
 * Creates a structured address schema with optional fields
 *
 * @returns A Zod schema for structured address data with optional fields
 *
 * @example
 * ```typescript
 * const addressSchema = createStructuredAddressSchema();
 * const data = { street_address: "123 Main St", city: "New York", postal_code: "10001" };
 * const result = addressSchema.parse(data); // Valid
 * ```
 */
export const createStructuredAddressSchema = () =>
  z
    .object({
      street_address: z.string().optional(),
      city: z.string().optional(),
      state: z.string().optional(),
      postal_code: z.string().optional(),
      country: z.string().optional(),
    })
    .optional();

/**
 * Creates a pagination parameters schema with customizable defaults
 *
 * @param defaultPage Default page number (default: 1)
 * @param defaultPageSize Default page size (default: 10)
 * @param maxPageSize Maximum allowed page size (default: 100)
 * @returns A Zod schema for pagination parameters
 *
 * @example
 * ```typescript
 * const paginationSchema = createPaginationSchema(1, 20, 50);
 * const params = { page: 2, pageSize: 30 };
 * const result = paginationSchema.parse(params); // Valid
 * ```
 */
export const createPaginationSchema = (
  defaultPage = 1,
  defaultPageSize = 10,
  maxPageSize = 100,
) =>
  z.object({
    page: z.coerce.number().int().min(1).default(defaultPage),
    pageSize: z.coerce
      .number()
      .int()
      .min(1)
      .max(maxPageSize)
      .default(defaultPageSize),
  });

/**
 * Creates a schema for fetching a resource by ID with optional pagination
 *
 * @param idField Name of the ID field (default: "id")
 * @param errorMessage Custom error message for invalid UUID
 * @returns A Zod schema for fetching a resource by ID with pagination
 *
 * @example
 * ```typescript
 * const getResourceSchema = createGetResourceParamsSchema("providerId", "Invalid provider ID");
 * const params = { providerId: "123e4567-e89b-12d3-a456-************", page: 1, pageSize: 10 };
 * const result = getResourceSchema.parse(params); // Valid if UUID is valid
 * ```
 */
export const createGetResourceParamsSchema = (
  idField = "id",
  errorMessage = "Invalid ID",
) =>
  createPaginationSchema().extend({
    [idField]: z.string().uuid(errorMessage).optional(),
  });
