/*
 * ==========================================
 * |          GENERATED BY SUPAZOD          |
 * ==========================================
 */

import { z } from "zod";
import { type Json } from "./../types/supabase";

export const jsonSchema: z.ZodSchema<Json> = z.lazy(() =>
  z
    .union([
      z.string(),
      z.number(),
      z.boolean(),
      z.record(z.union([jsonSchema, z.undefined()])),
      z.array(jsonSchema),
    ])
    .nullable(),
);

export const publicCargoTypesRowSchemaSchema = z.object({
  created_at: z.string(),
  description: z.string().nullable(),
  id: z.string(),
  image_url: z.string().nullable(),
  name: z.string(),
  updated_at: z.string(),
});

export const publicCargoTypesInsertSchemaSchema = z.object({
  created_at: z.string().optional(),
  description: z.string().optional().nullable(),
  id: z.string().optional(),
  image_url: z.string().optional().nullable(),
  name: z.string(),
  updated_at: z.string().optional(),
});

export const publicCargoTypesUpdateSchemaSchema = z.object({
  created_at: z.string().optional(),
  description: z.string().optional().nullable(),
  id: z.string().optional(),
  image_url: z.string().optional().nullable(),
  name: z.string().optional(),
  updated_at: z.string().optional(),
});

export const publicCountriesRowSchemaSchema = z.object({
  alpha2_code: z.string().nullable(),
  alpha3_code: z.string().nullable(),
  continent: z.string().nullable(),
  created_at: z.string().nullable(),
  currency_code: z.string().nullable(),
  id: z.string(),
  name: z.string(),
  region: z.string().nullable(),
  updated_at: z.string().nullable(),
});

export const publicCountriesInsertSchemaSchema = z.object({
  alpha2_code: z.string().optional().nullable(),
  alpha3_code: z.string().optional().nullable(),
  continent: z.string().optional().nullable(),
  created_at: z.string().optional().nullable(),
  currency_code: z.string().optional().nullable(),
  id: z.string().optional(),
  name: z.string(),
  region: z.string().optional().nullable(),
  updated_at: z.string().optional().nullable(),
});

export const publicCountriesUpdateSchemaSchema = z.object({
  alpha2_code: z.string().optional().nullable(),
  alpha3_code: z.string().optional().nullable(),
  continent: z.string().optional().nullable(),
  created_at: z.string().optional().nullable(),
  currency_code: z.string().optional().nullable(),
  id: z.string().optional(),
  name: z.string().optional(),
  region: z.string().optional().nullable(),
  updated_at: z.string().optional().nullable(),
});

export const publicEmailAttachmentsRowSchemaSchema = z.object({
  content: z.string().nullable(),
  content_type: z.string().nullable(),
  created_at: z.string(),
  filename: z.string(),
  gmail_attachment_id: z.string(),
  id: z.string(),
  message_id: z.string(),
  size: z.number().nullable(),
  storage_path: z.string().nullable(),
  updated_at: z.string(),
});

export const publicEmailAttachmentsInsertSchemaSchema = z.object({
  content: z.string().optional().nullable(),
  content_type: z.string().optional().nullable(),
  created_at: z.string().optional(),
  filename: z.string(),
  gmail_attachment_id: z.string(),
  id: z.string().optional(),
  message_id: z.string(),
  size: z.number().optional().nullable(),
  storage_path: z.string().optional().nullable(),
  updated_at: z.string().optional(),
});

export const publicEmailAttachmentsUpdateSchemaSchema = z.object({
  content: z.string().optional().nullable(),
  content_type: z.string().optional().nullable(),
  created_at: z.string().optional(),
  filename: z.string().optional(),
  gmail_attachment_id: z.string().optional(),
  id: z.string().optional(),
  message_id: z.string().optional(),
  size: z.number().optional().nullable(),
  storage_path: z.string().optional().nullable(),
  updated_at: z.string().optional(),
});

export const publicEmailAttachmentsRelationshipsSchemaSchema = z.tuple([
  z.object({
    foreignKeyName: z.literal("email_attachments_message_id_fkey"),
    columns: z.tuple([z.literal("message_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("email_messages"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
]);

export const publicEmailMessagesRowSchemaSchema = z.object({
  account_id: z.string(),
  bcc_recipients: jsonSchema.nullable(),
  body_html: z.string().nullable(),
  body_text: z.string().nullable(),
  category: z.string().nullable(),
  cc_recipients: jsonSchema.nullable(),
  created_at: z.string().nullable(),
  date_received: z.string().nullable(),
  from_email: z.string().nullable(),
  from_name: z.string().nullable(),
  gmail_id: z.string(),
  gmail_thread_id: z.string(),
  id: z.string(),
  in_reply_to: z.string().nullable(),
  is_important: z.boolean().nullable(),
  is_read: z.boolean().nullable(),
  is_starred: z.boolean().nullable(),
  is_trash: z.boolean().nullable(),
  labels: jsonSchema.nullable(),
  processed_for_rfq: z.boolean(),
  provider_id: z.string().nullable(),
  reply_to: z.string().nullable(),
  rfq_id: z.string().nullable(),
  snippet: z.string().nullable(),
  subject: z.string().nullable(),
  to_recipients: jsonSchema.nullable(),
  updated_at: z.string().nullable(),
});

export const publicEmailMessagesInsertSchemaSchema = z.object({
  account_id: z.string(),
  bcc_recipients: jsonSchema.optional().nullable(),
  body_html: z.string().optional().nullable(),
  body_text: z.string().optional().nullable(),
  category: z.string().optional().nullable(),
  cc_recipients: jsonSchema.optional().nullable(),
  created_at: z.string().optional().nullable(),
  date_received: z.string().optional().nullable(),
  from_email: z.string().optional().nullable(),
  from_name: z.string().optional().nullable(),
  gmail_id: z.string(),
  gmail_thread_id: z.string(),
  id: z.string().optional(),
  in_reply_to: z.string().optional().nullable(),
  is_important: z.boolean().optional().nullable(),
  is_read: z.boolean().optional().nullable(),
  is_starred: z.boolean().optional().nullable(),
  is_trash: z.boolean().optional().nullable(),
  labels: jsonSchema.optional().nullable(),
  processed_for_rfq: z.boolean().optional(),
  provider_id: z.string().optional().nullable(),
  reply_to: z.string().optional().nullable(),
  rfq_id: z.string().optional().nullable(),
  snippet: z.string().optional().nullable(),
  subject: z.string().optional().nullable(),
  to_recipients: jsonSchema.optional().nullable(),
  updated_at: z.string().optional().nullable(),
});

export const publicEmailMessagesUpdateSchemaSchema = z.object({
  account_id: z.string().optional(),
  bcc_recipients: jsonSchema.optional().nullable(),
  body_html: z.string().optional().nullable(),
  body_text: z.string().optional().nullable(),
  category: z.string().optional().nullable(),
  cc_recipients: jsonSchema.optional().nullable(),
  created_at: z.string().optional().nullable(),
  date_received: z.string().optional().nullable(),
  from_email: z.string().optional().nullable(),
  from_name: z.string().optional().nullable(),
  gmail_id: z.string().optional(),
  gmail_thread_id: z.string().optional(),
  id: z.string().optional(),
  in_reply_to: z.string().optional().nullable(),
  is_important: z.boolean().optional().nullable(),
  is_read: z.boolean().optional().nullable(),
  is_starred: z.boolean().optional().nullable(),
  is_trash: z.boolean().optional().nullable(),
  labels: jsonSchema.optional().nullable(),
  processed_for_rfq: z.boolean().optional(),
  provider_id: z.string().optional().nullable(),
  reply_to: z.string().optional().nullable(),
  rfq_id: z.string().optional().nullable(),
  snippet: z.string().optional().nullable(),
  subject: z.string().optional().nullable(),
  to_recipients: jsonSchema.optional().nullable(),
  updated_at: z.string().optional().nullable(),
});

export const publicEmailMessagesRelationshipsSchemaSchema = z.tuple([
  z.object({
    foreignKeyName: z.literal("email_messages_account_id_fkey"),
    columns: z.tuple([z.literal("account_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("watched_email_accounts"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
  z.object({
    foreignKeyName: z.literal("email_messages_provider_id_fkey"),
    columns: z.tuple([z.literal("provider_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("providers"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
  z.object({
    foreignKeyName: z.literal("email_messages_rfq_id_fkey"),
    columns: z.tuple([z.literal("rfq_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("rfqs"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
]);

export const publicEmailSettingsRowSchemaSchema = z.object({
  bcc_recipients: z.array(z.string()).nullable(),
  company_logo_url: z.string().nullable(),
  company_name: z.string(),
  created_at: z.string(),
  created_by: z.string().nullable(),
  default_sender_email: z.string(),
  default_sender_name: z.string(),
  email_footer_text: z.string().nullable(),
  email_signature: z.string().nullable(),
  id: z.string(),
  reply_to_email: z.string().nullable(),
  sandbox_mode: z.boolean(),
  sandbox_recipient_email: z.string().nullable(),
  updated_at: z.string(),
  updated_by: z.string().nullable(),
});

export const publicEmailSettingsInsertSchemaSchema = z.object({
  bcc_recipients: z.array(z.string()).optional().nullable(),
  company_logo_url: z.string().optional().nullable(),
  company_name: z.string(),
  created_at: z.string().optional(),
  created_by: z.string().optional().nullable(),
  default_sender_email: z.string(),
  default_sender_name: z.string(),
  email_footer_text: z.string().optional().nullable(),
  email_signature: z.string().optional().nullable(),
  id: z.string().optional(),
  reply_to_email: z.string().optional().nullable(),
  sandbox_mode: z.boolean().optional(),
  sandbox_recipient_email: z.string().optional().nullable(),
  updated_at: z.string().optional(),
  updated_by: z.string().optional().nullable(),
});

export const publicEmailSettingsUpdateSchemaSchema = z.object({
  bcc_recipients: z.array(z.string()).optional().nullable(),
  company_logo_url: z.string().optional().nullable(),
  company_name: z.string().optional(),
  created_at: z.string().optional(),
  created_by: z.string().optional().nullable(),
  default_sender_email: z.string().optional(),
  default_sender_name: z.string().optional(),
  email_footer_text: z.string().optional().nullable(),
  email_signature: z.string().optional().nullable(),
  id: z.string().optional(),
  reply_to_email: z.string().optional().nullable(),
  sandbox_mode: z.boolean().optional(),
  sandbox_recipient_email: z.string().optional().nullable(),
  updated_at: z.string().optional(),
  updated_by: z.string().optional().nullable(),
});

export const publicEquipmentTypesRowSchemaSchema = z.object({
  category: z.string(),
  created_at: z.string(),
  description: z.string().nullable(),
  id: z.string(),
  name: z.string(),
  updated_at: z.string(),
});

export const publicEquipmentTypesInsertSchemaSchema = z.object({
  category: z.string(),
  created_at: z.string().optional(),
  description: z.string().optional().nullable(),
  id: z.string().optional(),
  name: z.string(),
  updated_at: z.string().optional(),
});

export const publicEquipmentTypesUpdateSchemaSchema = z.object({
  category: z.string().optional(),
  created_at: z.string().optional(),
  description: z.string().optional().nullable(),
  id: z.string().optional(),
  name: z.string().optional(),
  updated_at: z.string().optional(),
});

export const publicProviderContactsRowSchemaSchema = z.object({
  created_at: z.string().nullable(),
  created_by: z.string().nullable(),
  email: z.string(),
  id: z.string(),
  is_primary: z.boolean(),
  name: z.string().nullable(),
  phone: z.string().nullable(),
  provider_id: z.string(),
  role: z.string().nullable(),
  updated_at: z.string().nullable(),
  updated_by: z.string().nullable(),
});

export const publicProviderContactsInsertSchemaSchema = z.object({
  created_at: z.string().optional().nullable(),
  created_by: z.string().optional().nullable(),
  email: z.string(),
  id: z.string().optional(),
  is_primary: z.boolean().optional(),
  name: z.string().optional().nullable(),
  phone: z.string().optional().nullable(),
  provider_id: z.string(),
  role: z.string().optional().nullable(),
  updated_at: z.string().optional().nullable(),
  updated_by: z.string().optional().nullable(),
});

export const publicProviderContactsUpdateSchemaSchema = z.object({
  created_at: z.string().optional().nullable(),
  created_by: z.string().optional().nullable(),
  email: z.string().optional(),
  id: z.string().optional(),
  is_primary: z.boolean().optional(),
  name: z.string().optional().nullable(),
  phone: z.string().optional().nullable(),
  provider_id: z.string().optional(),
  role: z.string().optional().nullable(),
  updated_at: z.string().optional().nullable(),
  updated_by: z.string().optional().nullable(),
});

export const publicProviderContactsRelationshipsSchemaSchema = z.tuple([
  z.object({
    foreignKeyName: z.literal("provider_contacts_created_by_fkey"),
    columns: z.tuple([z.literal("created_by")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("users"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
  z.object({
    foreignKeyName: z.literal("provider_contacts_provider_id_fkey"),
    columns: z.tuple([z.literal("provider_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("providers"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
  z.object({
    foreignKeyName: z.literal("provider_contacts_updated_by_fkey"),
    columns: z.tuple([z.literal("updated_by")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("users"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
]);

export const publicProviderEquipmentsRowSchemaSchema = z.object({
  created_at: z.string(),
  equipment_type_id: z.string(),
  id: z.string(),
  provider_id: z.string(),
  updated_at: z.string(),
});

export const publicProviderEquipmentsInsertSchemaSchema = z.object({
  created_at: z.string().optional(),
  equipment_type_id: z.string(),
  id: z.string().optional(),
  provider_id: z.string(),
  updated_at: z.string().optional(),
});

export const publicProviderEquipmentsUpdateSchemaSchema = z.object({
  created_at: z.string().optional(),
  equipment_type_id: z.string().optional(),
  id: z.string().optional(),
  provider_id: z.string().optional(),
  updated_at: z.string().optional(),
});

export const publicProviderEquipmentsRelationshipsSchemaSchema = z.tuple([
  z.object({
    foreignKeyName: z.literal("provider_equipments_equipment_type_id_fkey"),
    columns: z.tuple([z.literal("equipment_type_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("equipment_types"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
  z.object({
    foreignKeyName: z.literal("provider_equipments_provider_id_fkey"),
    columns: z.tuple([z.literal("provider_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("providers"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
]);

export const publicProviderRoutesRowSchemaSchema = z.object({
  created_at: z.string().nullable(),
  created_by: z.string().nullable(),
  destination_country_id: z.string(),
  id: z.string(),
  is_active: z.boolean().nullable(),
  notes: z.string().nullable(),
  origin_country_id: z.string(),
  provider_id: z.string(),
  updated_at: z.string().nullable(),
  updated_by: z.string().nullable(),
});

export const publicProviderRoutesInsertSchemaSchema = z.object({
  created_at: z.string().optional().nullable(),
  created_by: z.string().optional().nullable(),
  destination_country_id: z.string(),
  id: z.string().optional(),
  is_active: z.boolean().optional().nullable(),
  notes: z.string().optional().nullable(),
  origin_country_id: z.string(),
  provider_id: z.string(),
  updated_at: z.string().optional().nullable(),
  updated_by: z.string().optional().nullable(),
});

export const publicProviderRoutesUpdateSchemaSchema = z.object({
  created_at: z.string().optional().nullable(),
  created_by: z.string().optional().nullable(),
  destination_country_id: z.string().optional(),
  id: z.string().optional(),
  is_active: z.boolean().optional().nullable(),
  notes: z.string().optional().nullable(),
  origin_country_id: z.string().optional(),
  provider_id: z.string().optional(),
  updated_at: z.string().optional().nullable(),
  updated_by: z.string().optional().nullable(),
});

export const publicProviderRoutesRelationshipsSchemaSchema = z.tuple([
  z.object({
    foreignKeyName: z.literal("provider_routes_created_by_fkey"),
    columns: z.tuple([z.literal("created_by")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("users"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
  z.object({
    foreignKeyName: z.literal("provider_routes_destination_country_id_fkey"),
    columns: z.tuple([z.literal("destination_country_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("countries"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
  z.object({
    foreignKeyName: z.literal("provider_routes_origin_country_id_fkey"),
    columns: z.tuple([z.literal("origin_country_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("countries"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
  z.object({
    foreignKeyName: z.literal("provider_routes_provider_id_fkey"),
    columns: z.tuple([z.literal("provider_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("providers"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
  z.object({
    foreignKeyName: z.literal("provider_routes_updated_by_fkey"),
    columns: z.tuple([z.literal("updated_by")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("users"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
]);

export const publicProvidersRowSchemaSchema = z.object({
  created_at: z.string().nullable(),
  created_by: z.string().nullable(),
  full_address: z.string().nullable(),
  id: z.string(),
  name: z.string(),
  status: z.string(),
  structured_address: jsonSchema.nullable(),
  tax_id: z.string().nullable(),
  updated_at: z.string().nullable(),
  updated_by: z.string().nullable(),
  verified: z.boolean(),
});

export const publicProvidersInsertSchemaSchema = z.object({
  created_at: z.string().optional().nullable(),
  created_by: z.string().optional().nullable(),
  full_address: z.string().optional().nullable(),
  id: z.string().optional(),
  name: z.string(),
  status: z.string().optional(),
  structured_address: jsonSchema.optional().nullable(),
  tax_id: z.string().optional().nullable(),
  updated_at: z.string().optional().nullable(),
  updated_by: z.string().optional().nullable(),
  verified: z.boolean().optional(),
});

export const publicProvidersUpdateSchemaSchema = z.object({
  created_at: z.string().optional().nullable(),
  created_by: z.string().optional().nullable(),
  full_address: z.string().optional().nullable(),
  id: z.string().optional(),
  name: z.string().optional(),
  status: z.string().optional(),
  structured_address: jsonSchema.optional().nullable(),
  tax_id: z.string().optional().nullable(),
  updated_at: z.string().optional().nullable(),
  updated_by: z.string().optional().nullable(),
  verified: z.boolean().optional(),
});

export const publicProvidersRelationshipsSchemaSchema = z.tuple([
  z.object({
    foreignKeyName: z.literal("providers_created_by_fkey"),
    columns: z.tuple([z.literal("created_by")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("users"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
  z.object({
    foreignKeyName: z.literal("providers_updated_by_fkey"),
    columns: z.tuple([z.literal("updated_by")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("users"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
]);

export const publicRfqBidsRowSchemaSchema = z.object({
  created_at: z.string(),
  currency: z.string(),
  id: z.string(),
  is_ai_extracted: z.boolean(),
  notes: z.string().nullable(),
  original_email_id: z.string().nullable(),
  price: z.number(),
  provider_id: z.string(),
  rfq_id: z.string(),
  status: z.string(),
  submitted_at: z.string(),
  updated_at: z.string(),
});

export const publicRfqBidsInsertSchemaSchema = z.object({
  created_at: z.string().optional(),
  currency: z.string().optional(),
  id: z.string().optional(),
  is_ai_extracted: z.boolean().optional(),
  notes: z.string().optional().nullable(),
  original_email_id: z.string().optional().nullable(),
  price: z.number(),
  provider_id: z.string(),
  rfq_id: z.string(),
  status: z.string(),
  submitted_at: z.string(),
  updated_at: z.string().optional(),
});

export const publicRfqBidsUpdateSchemaSchema = z.object({
  created_at: z.string().optional(),
  currency: z.string().optional(),
  id: z.string().optional(),
  is_ai_extracted: z.boolean().optional(),
  notes: z.string().optional().nullable(),
  original_email_id: z.string().optional().nullable(),
  price: z.number().optional(),
  provider_id: z.string().optional(),
  rfq_id: z.string().optional(),
  status: z.string().optional(),
  submitted_at: z.string().optional(),
  updated_at: z.string().optional(),
});

export const publicRfqBidsRelationshipsSchemaSchema = z.tuple([
  z.object({
    foreignKeyName: z.literal("rfq_bids_original_email_id_fkey"),
    columns: z.tuple([z.literal("original_email_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("rfq_incoming_emails"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
  z.object({
    foreignKeyName: z.literal("rfq_bids_provider_id_fkey"),
    columns: z.tuple([z.literal("provider_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("providers"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
  z.object({
    foreignKeyName: z.literal("rfq_bids_rfq_id_fkey"),
    columns: z.tuple([z.literal("rfq_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("rfqs"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
]);

export const publicRfqCargoTypesRowSchemaSchema = z.object({
  cargo_type_id: z.string(),
  created_at: z.string(),
  id: z.string(),
  rfq_id: z.string(),
  updated_at: z.string(),
});

export const publicRfqCargoTypesInsertSchemaSchema = z.object({
  cargo_type_id: z.string(),
  created_at: z.string().optional(),
  id: z.string().optional(),
  rfq_id: z.string(),
  updated_at: z.string().optional(),
});

export const publicRfqCargoTypesUpdateSchemaSchema = z.object({
  cargo_type_id: z.string().optional(),
  created_at: z.string().optional(),
  id: z.string().optional(),
  rfq_id: z.string().optional(),
  updated_at: z.string().optional(),
});

export const publicRfqCargoTypesRelationshipsSchemaSchema = z.tuple([
  z.object({
    foreignKeyName: z.literal("rfq_cargo_types_cargo_type_id_fkey"),
    columns: z.tuple([z.literal("cargo_type_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("cargo_types"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
  z.object({
    foreignKeyName: z.literal("rfq_cargo_types_rfq_id_fkey"),
    columns: z.tuple([z.literal("rfq_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("rfqs"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
]);

export const publicRfqIncomingEmailsRowSchemaSchema = z.object({
  attachments: jsonSchema.nullable(),
  body: z.string(),
  created_at: z.string(),
  extracted_bid_id: z.string().nullable(),
  from_email: z.string(),
  from_name: z.string().nullable(),
  id: z.string(),
  in_reply_to: z.string().nullable(),
  is_processed: z.boolean(),
  message_id: z.string().nullable(),
  processing_status: z.string().nullable(),
  provider_id: z.string(),
  received_at: z.string(),
  rfq_id: z.string(),
  subject: z.string(),
  thread_id: z.string().nullable(),
  updated_at: z.string(),
});

export const publicRfqIncomingEmailsInsertSchemaSchema = z.object({
  attachments: jsonSchema.optional().nullable(),
  body: z.string(),
  created_at: z.string().optional(),
  extracted_bid_id: z.string().optional().nullable(),
  from_email: z.string(),
  from_name: z.string().optional().nullable(),
  id: z.string().optional(),
  in_reply_to: z.string().optional().nullable(),
  is_processed: z.boolean().optional(),
  message_id: z.string().optional().nullable(),
  processing_status: z.string().optional().nullable(),
  provider_id: z.string(),
  received_at: z.string(),
  rfq_id: z.string(),
  subject: z.string(),
  thread_id: z.string().optional().nullable(),
  updated_at: z.string().optional(),
});

export const publicRfqIncomingEmailsUpdateSchemaSchema = z.object({
  attachments: jsonSchema.optional().nullable(),
  body: z.string().optional(),
  created_at: z.string().optional(),
  extracted_bid_id: z.string().optional().nullable(),
  from_email: z.string().optional(),
  from_name: z.string().optional().nullable(),
  id: z.string().optional(),
  in_reply_to: z.string().optional().nullable(),
  is_processed: z.boolean().optional(),
  message_id: z.string().optional().nullable(),
  processing_status: z.string().optional().nullable(),
  provider_id: z.string().optional(),
  received_at: z.string().optional(),
  rfq_id: z.string().optional(),
  subject: z.string().optional(),
  thread_id: z.string().optional().nullable(),
  updated_at: z.string().optional(),
});

export const publicRfqIncomingEmailsRelationshipsSchemaSchema = z.tuple([
  z.object({
    foreignKeyName: z.literal("rfq_incoming_emails_extracted_bid_id_fkey"),
    columns: z.tuple([z.literal("extracted_bid_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("rfq_bids"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
  z.object({
    foreignKeyName: z.literal("rfq_incoming_emails_provider_id_fkey"),
    columns: z.tuple([z.literal("provider_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("providers"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
  z.object({
    foreignKeyName: z.literal("rfq_incoming_emails_rfq_id_fkey"),
    columns: z.tuple([z.literal("rfq_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("rfqs"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
]);

export const publicRfqOutgoingEmailsRowSchemaSchema = z.object({
  bcc: z.array(z.string()).nullable(),
  body: z.string(),
  bounce_reason: z.string().nullable(),
  cc: z.array(z.string()).nullable(),
  clicked_at: z.string().nullable(),
  created_at: z.string(),
  delivered_at: z.string().nullable(),
  delivery_status: z.string(),
  failure_reason: z.string().nullable(),
  id: z.string(),
  is_ai_generated: z.boolean(),
  message_id: z.string().nullable(),
  old_status: z.string(),
  opened_at: z.string().nullable(),
  provider_id: z.string(),
  reply_to: z.string().nullable(),
  rfq_id: z.string(),
  sent_at: z.string().nullable(),
  subject: z.string(),
  thread_id: z.string().nullable(),
  to_email: z.string(),
  to_name: z.string().nullable(),
  tracking_id: z.string().nullable(),
  updated_at: z.string(),
});

export const publicRfqOutgoingEmailsInsertSchemaSchema = z.object({
  bcc: z.array(z.string()).optional().nullable(),
  body: z.string(),
  bounce_reason: z.string().optional().nullable(),
  cc: z.array(z.string()).optional().nullable(),
  clicked_at: z.string().optional().nullable(),
  created_at: z.string().optional(),
  delivered_at: z.string().optional().nullable(),
  delivery_status: z.string().optional(),
  failure_reason: z.string().optional().nullable(),
  id: z.string().optional(),
  is_ai_generated: z.boolean().optional(),
  message_id: z.string().optional().nullable(),
  old_status: z.string(),
  opened_at: z.string().optional().nullable(),
  provider_id: z.string(),
  reply_to: z.string().optional().nullable(),
  rfq_id: z.string(),
  sent_at: z.string().optional().nullable(),
  subject: z.string(),
  thread_id: z.string().optional().nullable(),
  to_email: z.string(),
  to_name: z.string().optional().nullable(),
  tracking_id: z.string().optional().nullable(),
  updated_at: z.string().optional(),
});

export const publicRfqOutgoingEmailsUpdateSchemaSchema = z.object({
  bcc: z.array(z.string()).optional().nullable(),
  body: z.string().optional(),
  bounce_reason: z.string().optional().nullable(),
  cc: z.array(z.string()).optional().nullable(),
  clicked_at: z.string().optional().nullable(),
  created_at: z.string().optional(),
  delivered_at: z.string().optional().nullable(),
  delivery_status: z.string().optional(),
  failure_reason: z.string().optional().nullable(),
  id: z.string().optional(),
  is_ai_generated: z.boolean().optional(),
  message_id: z.string().optional().nullable(),
  old_status: z.string().optional(),
  opened_at: z.string().optional().nullable(),
  provider_id: z.string().optional(),
  reply_to: z.string().optional().nullable(),
  rfq_id: z.string().optional(),
  sent_at: z.string().optional().nullable(),
  subject: z.string().optional(),
  thread_id: z.string().optional().nullable(),
  to_email: z.string().optional(),
  to_name: z.string().optional().nullable(),
  tracking_id: z.string().optional().nullable(),
  updated_at: z.string().optional(),
});

export const publicRfqOutgoingEmailsRelationshipsSchemaSchema = z.tuple([
  z.object({
    foreignKeyName: z.literal("rfq_outgoing_emails_provider_id_fkey"),
    columns: z.tuple([z.literal("provider_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("providers"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
  z.object({
    foreignKeyName: z.literal("rfq_outgoing_emails_rfq_id_fkey"),
    columns: z.tuple([z.literal("rfq_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("rfqs"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
]);

export const publicRfqProvidersRowSchemaSchema = z.object({
  created_at: z.string(),
  email_opened_at: z.string().nullable(),
  id: z.string(),
  invited_at: z.string().nullable(),
  notes: z.string().nullable(),
  provider_id: z.string(),
  response_received_at: z.string().nullable(),
  rfq_id: z.string(),
  selected_at: z.string().nullable(),
  status: z.string(),
  updated_at: z.string(),
});

export const publicRfqProvidersInsertSchemaSchema = z.object({
  created_at: z.string().optional(),
  email_opened_at: z.string().optional().nullable(),
  id: z.string().optional(),
  invited_at: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  provider_id: z.string(),
  response_received_at: z.string().optional().nullable(),
  rfq_id: z.string(),
  selected_at: z.string().optional().nullable(),
  status: z.string(),
  updated_at: z.string().optional(),
});

export const publicRfqProvidersUpdateSchemaSchema = z.object({
  created_at: z.string().optional(),
  email_opened_at: z.string().optional().nullable(),
  id: z.string().optional(),
  invited_at: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  provider_id: z.string().optional(),
  response_received_at: z.string().optional().nullable(),
  rfq_id: z.string().optional(),
  selected_at: z.string().optional().nullable(),
  status: z.string().optional(),
  updated_at: z.string().optional(),
});

export const publicRfqProvidersRelationshipsSchemaSchema = z.tuple([
  z.object({
    foreignKeyName: z.literal("rfq_providers_provider_id_fkey"),
    columns: z.tuple([z.literal("provider_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("providers"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
  z.object({
    foreignKeyName: z.literal("rfq_providers_rfq_id_fkey"),
    columns: z.tuple([z.literal("rfq_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("rfqs"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
]);

export const publicRfqsRowSchemaSchema = z.object({
  cargo_type_id: z.string().nullable(),
  created_at: z.string(),
  created_by: z.string(),
  destination_address: z.string(),
  destination_city: z.string(),
  destination_country_id: z.string(),
  destination_postal_code: z.string().nullable(),
  equipment_quantity: z.number(),
  equipment_type_id: z.string(),
  expiration_date: z.string().nullable(),
  height: z.number().nullable(),
  id: z.string(),
  length: z.number().nullable(),
  notes: z.string().nullable(),
  origin_address: z.string(),
  origin_city: z.string(),
  origin_country_id: z.string(),
  origin_postal_code: z.string().nullable(),
  preferred_shipping_date: z.string().nullable(),
  quantity: z.number(),
  sequence_number: z.string().nullable(),
  special_requirements: z.string().nullable(),
  status: z.string(),
  title: z.string().nullable(),
  updated_at: z.string(),
  weight: z.number(),
  width: z.number().nullable(),
});

export const publicRfqsInsertSchemaSchema = z.object({
  cargo_type_id: z.string().optional().nullable(),
  created_at: z.string().optional(),
  created_by: z.string(),
  destination_address: z.string(),
  destination_city: z.string(),
  destination_country_id: z.string(),
  destination_postal_code: z.string().optional().nullable(),
  equipment_quantity: z.number().optional(),
  equipment_type_id: z.string(),
  expiration_date: z.string().optional().nullable(),
  height: z.number().optional().nullable(),
  id: z.string().optional(),
  length: z.number().optional().nullable(),
  notes: z.string().optional().nullable(),
  origin_address: z.string(),
  origin_city: z.string(),
  origin_country_id: z.string(),
  origin_postal_code: z.string().optional().nullable(),
  preferred_shipping_date: z.string().optional().nullable(),
  quantity: z.number().optional(),
  sequence_number: z.string().optional().nullable(),
  special_requirements: z.string().optional().nullable(),
  status: z.string(),
  title: z.string().optional().nullable(),
  updated_at: z.string().optional(),
  weight: z.number(),
  width: z.number().optional().nullable(),
});

export const publicRfqsUpdateSchemaSchema = z.object({
  cargo_type_id: z.string().optional().nullable(),
  created_at: z.string().optional(),
  created_by: z.string().optional(),
  destination_address: z.string().optional(),
  destination_city: z.string().optional(),
  destination_country_id: z.string().optional(),
  destination_postal_code: z.string().optional().nullable(),
  equipment_quantity: z.number().optional(),
  equipment_type_id: z.string().optional(),
  expiration_date: z.string().optional().nullable(),
  height: z.number().optional().nullable(),
  id: z.string().optional(),
  length: z.number().optional().nullable(),
  notes: z.string().optional().nullable(),
  origin_address: z.string().optional(),
  origin_city: z.string().optional(),
  origin_country_id: z.string().optional(),
  origin_postal_code: z.string().optional().nullable(),
  preferred_shipping_date: z.string().optional().nullable(),
  quantity: z.number().optional(),
  sequence_number: z.string().optional().nullable(),
  special_requirements: z.string().optional().nullable(),
  status: z.string().optional(),
  title: z.string().optional().nullable(),
  updated_at: z.string().optional(),
  weight: z.number().optional(),
  width: z.number().optional().nullable(),
});

export const publicRfqsRelationshipsSchemaSchema = z.tuple([
  z.object({
    foreignKeyName: z.literal("rfqs_cargo_type_id_fkey"),
    columns: z.tuple([z.literal("cargo_type_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("cargo_types"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
  z.object({
    foreignKeyName: z.literal("rfqs_destination_country_id_fkey"),
    columns: z.tuple([z.literal("destination_country_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("countries"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
  z.object({
    foreignKeyName: z.literal("rfqs_equipment_type_id_fkey"),
    columns: z.tuple([z.literal("equipment_type_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("equipment_types"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
  z.object({
    foreignKeyName: z.literal("rfqs_origin_country_id_fkey"),
    columns: z.tuple([z.literal("origin_country_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("countries"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
]);

export const publicRolesRowSchemaSchema = z.object({
  created_at: z.string().nullable(),
  id: z.string(),
  name: z.string(),
  updated_at: z.string().nullable(),
});

export const publicRolesInsertSchemaSchema = z.object({
  created_at: z.string().optional().nullable(),
  id: z.string().optional(),
  name: z.string().optional(),
  updated_at: z.string().optional().nullable(),
});

export const publicRolesUpdateSchemaSchema = z.object({
  created_at: z.string().optional().nullable(),
  id: z.string().optional(),
  name: z.string().optional(),
  updated_at: z.string().optional().nullable(),
});

export const publicUserRolesRowSchemaSchema = z.object({
  created_at: z.string().nullable(),
  id: z.string(),
  role_id: z.string(),
  updated_at: z.string().nullable(),
  user_id: z.string(),
});

export const publicUserRolesInsertSchemaSchema = z.object({
  created_at: z.string().optional().nullable(),
  id: z.string().optional(),
  role_id: z.string(),
  updated_at: z.string().optional().nullable(),
  user_id: z.string(),
});

export const publicUserRolesUpdateSchemaSchema = z.object({
  created_at: z.string().optional().nullable(),
  id: z.string().optional(),
  role_id: z.string().optional(),
  updated_at: z.string().optional().nullable(),
  user_id: z.string().optional(),
});

export const publicUserRolesRelationshipsSchemaSchema = z.tuple([
  z.object({
    foreignKeyName: z.literal("fk_role_id"),
    columns: z.tuple([z.literal("role_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("roles"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
  z.object({
    foreignKeyName: z.literal("fk_user_id"),
    columns: z.tuple([z.literal("user_id")]),
    isOneToOne: z.literal(false),
    referencedRelation: z.literal("users"),
    referencedColumns: z.tuple([z.literal("id")]),
  }),
]);

export const publicUsersRowSchemaSchema = z.object({
  avatar_url: z.string().nullable(),
  created_at: z.string().nullable(),
  id: z.string(),
  name: z.string().nullable(),
  phone_number: z.string().nullable(),
  updated_at: z.string().nullable(),
});

export const publicUsersInsertSchemaSchema = z.object({
  avatar_url: z.string().optional().nullable(),
  created_at: z.string().optional().nullable(),
  id: z.string(),
  name: z.string().optional().nullable(),
  phone_number: z.string().optional().nullable(),
  updated_at: z.string().optional().nullable(),
});

export const publicUsersUpdateSchemaSchema = z.object({
  avatar_url: z.string().optional().nullable(),
  created_at: z.string().optional().nullable(),
  id: z.string().optional(),
  name: z.string().optional().nullable(),
  phone_number: z.string().optional().nullable(),
  updated_at: z.string().optional().nullable(),
});

export const publicWatchedEmailAccountsRowSchemaSchema = z.object({
  access_token: z.string().nullable(),
  created_at: z.string(),
  created_by: z.string().nullable(),
  display_name: z.string().nullable(),
  email: z.string(),
  id: z.string(),
  last_full_sync_at: z.string().nullable(),
  last_sync_at: z.string().nullable(),
  refresh_token: z.string(),
  status: z.string(),
  sync_error: z.string().nullable(),
  sync_message: z.string().nullable(),
  token_expiry: z.string().nullable(),
  updated_at: z.string(),
  watch_expiry: z.string().nullable(),
});

export const publicWatchedEmailAccountsInsertSchemaSchema = z.object({
  access_token: z.string().optional().nullable(),
  created_at: z.string().optional(),
  created_by: z.string().optional().nullable(),
  display_name: z.string().optional().nullable(),
  email: z.string(),
  id: z.string().optional(),
  last_full_sync_at: z.string().optional().nullable(),
  last_sync_at: z.string().optional().nullable(),
  refresh_token: z.string(),
  status: z.string().optional(),
  sync_error: z.string().optional().nullable(),
  sync_message: z.string().optional().nullable(),
  token_expiry: z.string().optional().nullable(),
  updated_at: z.string().optional(),
  watch_expiry: z.string().optional().nullable(),
});

export const publicWatchedEmailAccountsUpdateSchemaSchema = z.object({
  access_token: z.string().optional().nullable(),
  created_at: z.string().optional(),
  created_by: z.string().optional().nullable(),
  display_name: z.string().optional().nullable(),
  email: z.string().optional(),
  id: z.string().optional(),
  last_full_sync_at: z.string().optional().nullable(),
  last_sync_at: z.string().optional().nullable(),
  refresh_token: z.string().optional(),
  status: z.string().optional(),
  sync_error: z.string().optional().nullable(),
  sync_message: z.string().optional().nullable(),
  token_expiry: z.string().optional().nullable(),
  updated_at: z.string().optional(),
  watch_expiry: z.string().optional().nullable(),
});

export const publicHasRoleArgsSchemaSchema = z.object({
  user_id: z.string(),
  role_name: z.string(),
});

export const publicHasRoleReturnsSchemaSchema = z.boolean();

export const publicIsAdminArgsSchemaSchema = z.object({
  user_id: z.string(),
});

export const publicIsAdminReturnsSchemaSchema = z.boolean();
