/**
 * Composite Types
 *
 * This file contains composite types that combine multiple entity types.
 * It imports types from all base schema files to avoid circular dependencies.
 */

// Import types from base schema files
import type { Provider } from "./provider.schema";
import type { ProviderContact } from "./provider-contact.schema";
import type { ProviderRoute } from "./provider-route.schema";
import type { EquipmentType } from "./equipment.schema";
import type { Country } from "./country.schema";
import type { RFQProvider, RFQBid, RFQ } from "./rfq.schema";

/**
 * Provider Route with Country Information
 *
 * Extends the base ProviderRoute type with related country information.
 * This is used when fetching provider routes with joined country data.
 */
export type ProviderRouteWithCountries = ProviderRoute & {
  origin_country: Country;
  destination_country: Country;
};

/**
 * Provider with associated equipment types
 *
 * Represents a provider entity with its associated equipment types.
 */
export type ProviderWithEquipment = Provider & {
  equipments?: EquipmentType[];
};

/**
 * Provider with associated contacts
 *
 * Represents a provider entity with its associated contacts.
 */
export type ProviderWithContacts = Provider & {
  contacts?: ProviderContact[];
};

/**
 * Provider with associated routes
 *
 * Represents a provider entity with its associated routes,
 * including origin and destination country information.
 */
export type ProviderWithRoutes = Provider & {
  routes?: ProviderRouteWithCountries[];
};

/**
 * Provider with all relations
 *
 * Represents a provider entity with all its associated relations:
 * contacts, equipment types, and routes.
 */
export type ProviderWithRelations = Provider & {
  contacts?: ProviderContact[];
  equipments?: EquipmentType[];
  routes?: ProviderRouteWithCountries[];
};

/**
 * RFQ Provider with Bid
 *
 * Extends the base RFQProvider type with additional fields for provider and bid information.
 * This is used when fetching RFQ providers with joined provider and bid data.
 */
export type RFQProviderWithBid = RFQProvider & {
  provider?: Provider;
  bid?: RFQBid;
};

/**
 * Manual Provider Type
 *
 * Type definition for manually selected providers in UI state.
 * Used for temporary provider selection before invitation.
 */
export type ManualProviderType = ProviderWithEquipment;

/**
 * RFQ Display Details
 *
 * Type definition for RFQ details used in UI components and email templates.
 * This type represents the formatted/processed RFQ data for display purposes,
 * derived from the base RFQ type with additional computed fields for UI display.
 */
export type RFQDisplayDetails = Pick<RFQ,
  | 'title'
  | 'sequence_number'
  | 'origin_postal_code'
  | 'destination_postal_code'
  | 'weight'
  | 'preferred_shipping_date'
  | 'special_requirements'
  | 'notes'
> & {
  // Computed/formatted fields for display
  origin: string;
  origin_country: string;
  destination: string;
  destination_country: string;
  equipmentType: string;
  equipmentQuantity: number;
  cargoTypes: string[];
  // Optional formatted versions of nullable database fields
  preferredShippingDate?: string;
  specialRequirements?: string;
};

/**
 * Bid with Provider Name
 *
 * Type definition for bid data with additional provider name for display purposes.
 * Used in bid update dialogs and tables where provider context is needed.
 */
export type BidWithProviderName = RFQBid & {
  provider_name?: string;
};

/**
 * Bid Display Data
 *
 * Type definition for bid data used in UI components and dialogs.
 * Contains essential bid fields for display and update operations.
 */
export interface BidDisplayData {
  id: string;
  rfq_id: string;
  provider_id: string;
  price: number;
  currency: string;
  status: "received" | "under_review" | "accepted" | "rejected" | "negotiating";
  notes?: string;
  provider_name?: string;
}

/**
 * Bid Update Data
 *
 * Type definition for bid update operations in dialogs and forms.
 * Represents the essential bid fields needed for updating existing bids.
 */
export interface BidUpdateData {
  price: number;
  status: string;
  notes?: string;
  rfq_id: string;
};

/**
 * Enriched RFQ
 *
 * Type definition for RFQ data with additional business insight properties.
 * Used in data tables and dashboards to display computed metrics and country information.
 */
export interface EnrichedRFQ extends RFQ {
  invitedProvidersCount: number;
  matchedProvidersCount: number;
  bidsReceivedCount: number;
  responseRate: number;
  origin_country_name: string;
  origin_country_code: string;
  destination_country_name: string;
  destination_country_code: string;
};
