/**
 * Common Schemas and Types
 *
 * This file contains common schemas and types used across the application.
 * These are not entity-specific but are used by multiple entities.
 *
 * @module CommonSchema
 */

import { z } from "zod";

// ==========================================
// Common Types
// ==========================================

/**
 * Action Response
 *
 * Generic type for server action responses.
 * Provides a consistent structure for success and error responses.
 * Includes optional pagination and count for paginated responses.
 *
 * @template T The type of data returned on success
 */
export type ActionResponse<T> =
  | {
      success: true;
      data: T;
      pagination?: {
        page: number;
        pageSize: number;
        totalItems: number;
        totalPages: number;
        hasNextPage: boolean;
        hasPrevPage: boolean;
      };
      count?: number;
    }
  | {
      success: false;
      error: string;
      fieldErrors?:
        | Record<string, string[]>
        | { [key: string]: string[] | undefined };
    };

/**
 * Validation Result
 *
 * Generic type for validation results.
 * Used by the validation utility to return validation results.
 *
 * @template T The type of validated data
 */
export type ValidationResult<T> = {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    fieldErrors?:
      | Record<string, string[]>
      | { [key: string]: string[] | undefined };
  };
};

/**
 * JSON Value
 *
 * Type for JSON values.
 * Used for fields that store JSON data.
 */
export type JsonValue =
  | string
  | number
  | boolean
  | null
  | { [key: string]: JsonValue | undefined }
  | JsonValue[];

/**
 * Pagination Params
 *
 * Common type for pagination parameters.
 * Used for consistency across the application.
 */
export type PaginationParams = {
  page?: number;
  pageSize?: number;
};

/**
 * Paginated Response
 *
 * Generic type for paginated responses.
 * Used for consistency across the application.
 *
 * @template T The type of data in the response
 */
export type PaginatedResponse<T> = {
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    totalPages: number;
    totalCount: number;
  };
};
