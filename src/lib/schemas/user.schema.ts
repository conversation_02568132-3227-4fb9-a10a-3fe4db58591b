/**
 * User schemas and types
 *
 * This file contains schemas and types for the User entity.
 * It imports directly from Supazod-generated schemas and extends them where needed.
 */

import { z } from "zod";
import * as generated from "./schemas";

/**
 * Phone number validation regex
 * Accepts formats like:
 * European formats:
 * - +49 170 1234567 (German mobile)
 * - +49 30 12345678 (German landline with Berlin area code)
 * - +49-89-1234-5678 (German landline with Munich area code)
 * - +49.1601234567 (German mobile with dots)
 * - 0170 123 4567 (German mobile without country code)
 * - 030 12345678 (German landline without country code)
 * - +33 1 23 45 67 89 (French)
 * - +39 02 1234 5678 (Italian)
 *
 * North American formats:
 * - +1 (*************
 * - ******-456-7890
 * - +11234567890
 * - ************
 * - (*************
 * - 1234567890
 */
const PHONE_REGEX = /^(?:(?:\+|00)([1-9]\d{0,3})[\s.-]?)?(?:0?[\s.-]?)?(?:\(([0-9]{1,5})\)[\s.-]?|([0-9]{1,5})[\s.-]?)([0-9\s.-]{5,15})$/;

// User Schemas

// Schema for fetching users with pagination or by ID
export const GetUsersParamsSchema = z.object({
  id: z.string().uuid("Invalid user ID").optional(),
  page: z.coerce.number().int().min(1).default(1),
  pageSize: z.coerce.number().int().min(1).max(100).default(10),
});

// Schema for creating a new user
export const CreateUserSchema = generated.publicUsersInsertSchemaSchema.extend({
  name: z.string().min(1, "Name is required"),
});

// Schema for updating an existing user
export const UpdateUserSchema = CreateUserSchema.partial().extend({
  id: z.string().uuid("Invalid user ID"),
});

// User role schema
export const UserRoleSchema = z.object({
  user_id: z.string().uuid("Invalid user ID"),
  role_id: z.string().uuid("Invalid role ID"),
});

// User row schema (direct re-export from generated)
export const UserSchema = generated.publicUsersRowSchemaSchema.extend({
  phone_number: z.string().optional().nullable(),
});

/**
 * User Profile Form Schema
 *
 * Schema for the user profile form in the dashboard.
 * Used for validating user profile updates.
 */
export const UserProfileFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  phone_number: z
    .union([
      z.string().regex(PHONE_REGEX, "Please enter a valid phone number"),
      z.string().max(0), // Empty string
      z.null(),
      z.undefined(),
    ])
    .optional()
    .nullable(),
});

// Export types
export type User = z.infer<typeof UserSchema>;
export type CreateUserInput = z.infer<typeof CreateUserSchema>;
export type UpdateUserInput = z.infer<typeof UpdateUserSchema>;
export type GetUsersParams = z.infer<typeof GetUsersParamsSchema>;
export type UserRole = z.infer<typeof generated.publicUserRolesRowSchemaSchema>;
export type UserRoleInput = z.infer<typeof UserRoleSchema>;
export type UserProfileFormValues = z.infer<typeof UserProfileFormSchema>;
