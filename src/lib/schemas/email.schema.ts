/**
 * Email Schemas and Types
 *
 * This file contains schemas and types for email-related entities.
 * It imports directly from Supazod-generated schemas and extends them where needed.
 *
 * @module EmailSchema
 */

import { z } from "zod";
import * as generated from "./schemas";

// ==========================================
// Schema Definitions
// ==========================================

/**
 * Watched Email Account Schema
 *
 * Base schema for watched email accounts, directly from Supazod-generated schema.
 */
export const WatchedEmailAccountSchema =
  generated.publicWatchedEmailAccountsRowSchemaSchema;

/**
 * Email Message Schema
 *
 * Base schema for email messages, directly from Supazod-generated schema.
 */
export const EmailMessageSchema = generated.publicEmailMessagesRowSchemaSchema;

/**
 * Email Attachment Schema
 *
 * Base schema for email attachments, directly from Supazod-generated schema.
 */
export const EmailAttachmentSchema =
  generated.publicEmailAttachmentsRowSchemaSchema;

/**
 * RFQ Incoming Email Schema
 *
 * Base schema for RFQ incoming emails, directly from Supazod-generated schema.
 */
export const RFQIncomingEmailSchema =
  generated.publicRfqIncomingEmailsRowSchemaSchema;

/**
 * RFQ Outgoing Email Schema
 *
 * Base schema for RFQ outgoing emails, directly from Supazod-generated schema.
 */
export const RFQOutgoingEmailSchema =
  generated.publicRfqOutgoingEmailsRowSchemaSchema;

// ==========================================
// Type Definitions
// ==========================================

/**
 * Watched Email Account
 *
 * Type definition for a watched email account entity, derived from WatchedEmailAccountSchema.
 */
export type WatchedEmailAccount = z.infer<typeof WatchedEmailAccountSchema>;

/**
 * Email Message
 *
 * Type definition for an email message entity, derived from EmailMessageSchema.
 */
export type EmailMessage = z.infer<typeof EmailMessageSchema>;

/**
 * Email Attachment
 *
 * Type definition for an email attachment entity, derived from EmailAttachmentSchema.
 */
export type EmailAttachment = z.infer<typeof EmailAttachmentSchema>;

/**
 * RFQ Incoming Email
 *
 * Type definition for an RFQ incoming email entity, derived from RFQIncomingEmailSchema.
 */
export type RFQIncomingEmail = z.infer<typeof RFQIncomingEmailSchema>;

/**
 * RFQ Outgoing Email
 *
 * Type definition for an RFQ outgoing email entity, derived from RFQOutgoingEmailSchema.
 */
export type RFQOutgoingEmail = z.infer<typeof RFQOutgoingEmailSchema>;

// ==========================================
// Email Service Types
// ==========================================

/**
 * Email Recipient
 *
 * @description
 * Represents an email recipient with email address and optional name.
 * Used in email service operations for sending emails.
 */
export interface EmailRecipient {
  email: string;
  name?: string;
}

/**
 * Email Service Attachment
 *
 * @description
 * Represents an email attachment for email service operations.
 * Different from database EmailAttachment which stores persisted attachments.
 */
export interface EmailServiceAttachment {
  content: string;
  filename: string;
  type: string;
  disposition: "attachment" | "inline";
}

/**
 * Email Service Data
 *
 * @description
 * Represents email data for sending emails through the email service.
 * Used for both client-side and server-side email operations.
 */
export interface EmailServiceData {
  to: EmailRecipient;
  subject: string;
  body: string;
  textBody?: string; // Plain text alternative for HTML emails
  isHtml?: boolean;
  trackingId?: string;
  messageId?: string;
  replyTo?: string;
  attachments?: EmailServiceAttachment[];
}

/**
 * Email Composition Data
 *
 * @description
 * Simplified email data structure used in email composition dialogs.
 * Contains only the essential fields needed for composing emails.
 */
export interface EmailCompositionData {
  subject: string;
  body: string;
  isCustom: boolean;
}

/**
 * Email Validation Result
 *
 * @description
 * Generic type for email validation results with success/error states.
 */
export type EmailValidationResult<T> = {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    fieldErrors?: Record<string, string[]>;
  };
};
