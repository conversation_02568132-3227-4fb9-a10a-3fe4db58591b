/**
 * Country schemas and types
 *
 * This file contains schemas and types for the Country entity.
 * It imports directly from Supazod-generated schemas and extends them where needed.
 */

import { z } from "zod";
import * as generated from "./schemas";

// Country Schemas

// Country row schema (direct re-export from generated)
// This needs to be defined before schemas that might .pick() from it.
export const CountrySchema = generated.publicCountriesRowSchemaSchema;

// Schema for fetching countries with pagination or by ID
export const GetCountriesParamsSchema = CountrySchema.pick({
  id: true,
  alpha2_code: true,
})
  .partial() // Makes picked fields optional
  .extend({
    // Ensure original validations and messages are preserved/applied
    id: z.string().uuid("Invalid country ID").optional(),
    alpha2_code: z.string().length(2).optional(),
    // Add pagination fields - make them optional to allow fetching all countries
    page: z.coerce.number().int().min(1).optional(),
    pageSize: z.coerce.number().int().min(1).max(100).optional(),
  });

// Schema for creating a new country
export const CreateCountrySchema =
  generated.publicCountriesInsertSchemaSchema.extend({
    name: z.string().min(1, "Country name is required"),
    alpha2_code: z
      .string()
      .length(2, "Alpha-2 code must be exactly 2 characters")
      .optional(),
    alpha3_code: z
      .string()
      .length(3, "Alpha-3 code must be exactly 3 characters")
      .optional(),
  });

// Schema for updating an existing country
export const UpdateCountrySchema = CreateCountrySchema.partial().extend({
  id: z.string().uuid("Invalid country ID"),
});

// Schema for deleting a country
export const DeleteCountryParamsSchema = CountrySchema.pick({ id: true }) // id is picked from CountrySchema
  .extend({
    // Ensure original validation and message are preserved/applied
    // pick({id: true}) provides the base type, e.g. z.string().uuid().
    // We then ensure our specific requirements for this param schema.
    id: z.string().uuid("Invalid country ID"), // Ensures it's not optional and has the specific message.
  });

// Export types
export type Country = z.infer<typeof CountrySchema>;
export type CreateCountryInput = z.infer<typeof CreateCountrySchema>;
export type UpdateCountryInput = z.infer<typeof UpdateCountrySchema>;
export type DeleteCountryParams = z.infer<typeof DeleteCountryParamsSchema>;
export type GetCountriesParams = z.infer<typeof GetCountriesParamsSchema>;
