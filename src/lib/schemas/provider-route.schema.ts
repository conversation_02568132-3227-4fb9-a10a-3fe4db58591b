/**
 * Provider Route Schemas and Types
 *
 * This file contains schemas and types for the Provider Route entity.
 * It imports directly from Supazod-generated schemas and extends them where needed.
 *
 * @module ProviderRouteSchema
 */

import { z } from "zod";
import * as generated from "./schemas";

// ==========================================
// Schema Definitions
// ==========================================

/**
 * Provider Route Schema
 *
 * Base schema for provider route entity, directly from Supazod-generated schema.
 * Contains all fields from the provider_routes table.
 */
export const ProviderRouteSchema =
  generated.publicProviderRoutesRowSchemaSchema;

/**
 * Create Provider Route Schema
 *
 * Schema for creating a new provider route:
 * - Requires valid UUIDs for provider, origin country, and destination country
 * - Sets a default value for is_active
 */
export const CreateProviderRouteSchema = z.object({
  provider_id: z.string().uuid("Invalid provider ID"),
  origin_country_id: z.string().uuid("Invalid origin country ID"),
  destination_country_id: z.string().uuid("Invalid destination country ID"),
  is_active: z.boolean().default(true),
  notes: z.string().nullable(),
});

/**
 * Update Provider Route Schema
 *
 * Schema for updating an existing provider route:
 * - Requires a valid UUID for the route ID
 * - All fields are optional except the ID
 * - Maintains the same validation rules as CreateProviderRouteSchema for each field
 */
export const UpdateProviderRouteSchema = z.object({
  id: z.string().uuid("Invalid route ID"),
  provider_id: z.string().uuid("Invalid provider ID").optional(),
  origin_country_id: z.string().uuid("Invalid origin country ID").optional(),
  destination_country_id: z
    .string()
    .uuid("Invalid destination country ID")
    .optional(),
  is_active: z.boolean().optional(),
  notes: z.string().nullable().optional(),
});

/**
 * Delete Provider Route Params Schema
 *
 * Schema for deleting a provider route:
 * - Requires a valid UUID for the route ID
 * - Optionally includes the provider ID for additional validation
 */
export const DeleteProviderRouteParamsSchema = z.object({
  id: z.string().uuid("Invalid route ID"),
  provider_id: z.string().uuid("Invalid provider ID").optional(),
});

/**
 * Get Provider Routes Params Schema
 *
 * Schema for fetching provider routes:
 * - Requires a valid UUID for the provider ID to filter routes by provider
 * - Includes pagination parameters with defaults
 */
export const GetProviderRoutesParamsSchema = z.object({
  provider_id: z.string().uuid("Invalid provider ID"),
  page: z.coerce.number().int().min(1).default(1),
  pageSize: z.coerce.number().int().min(1).max(100).default(10),
  search: z.string().optional(),
  origin_country_id: z.string().uuid("Invalid origin country ID").optional(),
  destination_country_id: z
    .string()
    .uuid("Invalid destination country ID")
    .optional(),
  is_active: z
    .union([z.boolean(), z.enum(["true", "false"])])
    .optional()
    .transform((val) => {
      if (val === "true") return true;
      if (val === "false") return false;
      return val;
    }),
});

/**
 * Provider Route Form Schema
 *
 * Schema for provider route form submission:
 * - Requires valid UUIDs for origin and destination countries
 * - Sets a default value for is_active
 * - Notes are optional and nullable
 */
export const ProviderRouteFormSchema = z.object({
  origin_country_id: z.string().uuid("Origin country is required"),
  destination_country_id: z.string().uuid("Destination country is required"),
  is_active: z.boolean().default(true),
  notes: z.string().nullable().optional(),
});

// ==========================================
// Type Definitions
// ==========================================

/**
 * Provider Route
 *
 * Type definition for a provider route entity, derived from ProviderRouteSchema.
 */
export type ProviderRoute = z.infer<typeof ProviderRouteSchema>;

/**
 * Create Provider Route Input
 *
 * Type definition for provider route creation payload, derived from CreateProviderRouteSchema.
 */
export type CreateProviderRouteInput = z.infer<
  typeof CreateProviderRouteSchema
>;

/**
 * Update Provider Route Input
 *
 * Type definition for provider route update payload, derived from UpdateProviderRouteSchema.
 */
export type UpdateProviderRouteInput = z.infer<
  typeof UpdateProviderRouteSchema
>;

/**
 * Delete Provider Route Params
 *
 * Type definition for provider route deletion parameters, derived from DeleteProviderRouteParamsSchema.
 */
export type DeleteProviderRouteParams = z.infer<
  typeof DeleteProviderRouteParamsSchema
>;

/**
 * Get Provider Routes Params
 *
 * Type definition for provider route query parameters, derived from GetProviderRoutesParamsSchema.
 */
export type GetProviderRoutesParams = z.infer<
  typeof GetProviderRoutesParamsSchema
>;

/**
 * Provider Route Form Values
 *
 * Type definition for provider route form values, derived from ProviderRouteFormSchema.
 */
export type ProviderRouteFormValues = z.infer<typeof ProviderRouteFormSchema>;

/**
 * Provider Route Relationships
 *
 * Type definition for provider route relationships, derived from Supazod-generated schema.
 */
export type ProviderRouteRelationships = z.infer<
  typeof generated.publicProviderRoutesRelationshipsSchemaSchema
>;
