/**
 * Central Schema Exports
 *
 * This file re-exports all schemas and types from entity-specific schema files.
 * Import from here for a clean, centralized import experience.
 *
 * @module schemas
 */

// ==========================================
// Entity-Specific Schemas
// ==========================================

// Provider and related schemas
export * from "./provider.schema";
export * from "./provider-contact.schema";
export * from "./provider-route.schema";
export * from "./provider-equipment.schema";

// Other entity schemas
export * from "./equipment.schema";
export * from "./cargo.schema";
export * from "./rfq.schema";
export * from "./rfq-table.schema"; // UI-specific RFQ table types
export * from "./user.schema";
export * from "./country.schema";
export * from "./email.schema";
export * from "./email-settings.schema";

// ==========================================
// Composite Types
// ==========================================

// Export composite types that combine multiple entity types
export * from "./composite-types";

// ==========================================
// Utility Functions
// ==========================================

// Export utility functions for schema creation
export * from "./utils";

// ==========================================
// Common Schemas and Types
// ==========================================

// Export common schemas and types
export * from "./common.schema";

// No more legacy re-exports
