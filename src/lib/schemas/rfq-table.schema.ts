/**
 * RFQ Table Schemas and Types
 *
 * This file contains schemas and types for RFQ table components.
 * It extends the base schemas with UI-specific properties needed for data tables.
 *
 * @module RFQTableSchema
 */

import { z } from "zod";
import { RFQProvider, RFQBid } from "./rfq.schema";
import { Provider } from "./provider.schema";

// ==========================================
// Type Definitions
// ==========================================

/**
 * RFQ Provider For Table
 *
 * Extends the base RFQProvider type with additional properties needed for UI tables.
 * This type is used in the provider selection table component.
 */
export type RFQProviderForTable = RFQProvider & {
  // Include provider object for UI display
  provider?: {
    id?: string;
    name: string;
    status: string;
    verified: boolean;
  };
  // Add these properties to match the column IDs used in the DataTable
  provider_name?: string;
  provider_status?: string;
  provider_verified?: boolean;
  rfq_status?: string | null;
};

/**
 * Invited Provider For Table
 *
 * Extends the base RFQProvider type with additional properties needed for the invited providers table.
 * This type is used in the invited providers table component.
 */
export type InvitedProviderForTable = RFQProvider & {
  provider?: Provider;
  bid?: RFQBid;
  invited_at?: string | null;
};

/**
 * Schema for RFQ Provider For Table
 *
 * Zod schema for validating RFQProviderForTable objects.
 */
export const RFQProviderForTableSchema = z.object({
  id: z.string().uuid(),
  provider_id: z.string().uuid(),
  status: z.string(),
  provider: z
    .object({
      id: z.string().uuid().optional(),
      name: z.string(),
      status: z.string(),
      verified: z.boolean(),
    })
    .optional(),
  rfq_status: z.string().nullable().optional(),
  provider_name: z.string().optional(),
  provider_status: z.string().optional(),
  provider_verified: z.boolean().optional(),
});

/**
 * Schema for Invited Provider For Table
 *
 * Zod schema for validating InvitedProviderForTable objects.
 */
export const InvitedProviderForTableSchema = z.object({
  id: z.string().uuid(),
  provider_id: z.string().uuid(),
  status: z.string(),
  invited_at: z.string().nullable().optional(),
  provider: z
    .object({
      id: z.string().uuid(),
      name: z.string(),
      status: z.string(),
      verified: z.boolean(),
      full_address: z.string().nullable().optional(),
      tax_id: z.string().nullable().optional(),
    })
    .optional(),
  bid: z
    .object({
      id: z.string().uuid(),
      rfq_id: z.string().uuid(),
      provider_id: z.string().uuid(),
      price: z.number(),
      currency: z.string(),
      status: z.string(),
      notes: z.string().nullable().optional(),
    })
    .optional(),
});
