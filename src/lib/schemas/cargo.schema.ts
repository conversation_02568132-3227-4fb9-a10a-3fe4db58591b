/**
 * Cargo schemas and types
 *
 * This file contains schemas and types for the Cargo entity.
 * It imports directly from Supazod-generated schemas and extends them where needed.
 */

import { z } from "zod";
import * as generated from "./schemas";

// Cargo Schemas

// Schema for fetching cargo with pagination or by ID
export const GetCargoTypesParamsSchema = z.object({
  id: z.string().uuid("Invalid UUID format").optional(),
  page: z.coerce.number().int().min(1).default(1),
  pageSize: z.coerce.number().int().min(1).max(100).default(10),
});

// Schema for creating new cargo
export const CreateCargoTypeSchema =
  generated.publicCargoTypesInsertSchemaSchema.extend({
    name: z
      .string()
      .min(1, { message: "Cargo name cannot be empty." })
      .max(100, { message: "Cargo name too long." }),
    description: z
      .string()
      .max(500, { message: "Description cannot exceed 500 characters." })
      .optional(),
    image_url: z
      .string()
      .url("Invalid image URL")
      .nullable()
      .or(z.literal(""))
      .optional(),
  });

// Schema for updating existing cargo
export const UpdateCargoTypeSchema = CreateCargoTypeSchema.extend({
  id: z.string().uuid("Invalid UUID format"),
});

// Schema for deleting cargo
export const DeleteCargoTypeParamsSchema = z.object({
  id: z.string().uuid("Invalid Cargo ID"),
});

// Cargo type row schema (direct re-export from generated)
export const CargoTypeSchema = generated.publicCargoTypesRowSchemaSchema;

// Export types
export type CargoType = z.infer<typeof CargoTypeSchema>;
export type CreateCargoTypeInput = z.infer<typeof CreateCargoTypeSchema>;
export type UpdateCargoTypeInput = z.infer<typeof UpdateCargoTypeSchema>;
export type DeleteCargoTypeParams = z.infer<typeof DeleteCargoTypeParamsSchema>;
export type GetCargoTypesParams = z.infer<typeof GetCargoTypesParamsSchema>;
