/**
 * Equipment schemas and types
 *
 * This file contains schemas and types for the Equipment entity.
 * It imports directly from Supazod-generated schemas and extends them where needed.
 */

import { z } from "zod";
import * as generated from "./schemas";

// Equipment Schemas

// Schema for fetching equipment with filtering and pagination
export const GetEquipmentTypesParamsSchema = z.object({
  id: z.string().uuid("Invalid UUID format").optional(),
  page: z.coerce.number().int().min(1).default(1),
  pageSize: z.coerce.number().int().min(1).max(100).default(10),
  category: z.string().optional(),
});

// Schema for creating new equipment
export const CreateEquipmentTypeSchema =
  generated.publicEquipmentTypesInsertSchemaSchema.extend({
    name: z
      .string()
      .min(1, { message: "Equipment name cannot be empty." })
      .max(100, { message: "Equipment name too long." }),
    category: z
      .string()
      .min(1, { message: "Category cannot be empty." })
      .max(50, { message: "Category too long." }),
    description: z
      .string()
      .max(500, { message: "Description cannot exceed 500 characters." })
      .optional(),
  });

// Schema for updating existing equipment
export const UpdateEquipmentTypeSchema = CreateEquipmentTypeSchema.extend({
  id: z.string().uuid("Invalid UUID format"),
});

// Schema for deleting equipment
export const DeleteEquipmentTypeParamsSchema = z.object({
  id: z.string().uuid("Invalid Equipment ID"),
});

// Equipment form schema
export const EquipmentSchema = z.object({
  id: z.string().uuid().optional(), // Optional for creation, required for update
  name: z
    .string()
    .min(1, { message: "Equipment name cannot be empty." })
    .max(100, { message: "Equipment name too long." }),
  category: z
    .string()
    .min(1, { message: "Category cannot be empty." })
    .max(50, { message: "Category too long." }),
  description: z
    .string()
    .max(500, { message: "Description cannot exceed 500 characters." })
    .optional(),
});

// Equipment type row schema (direct re-export from generated)
export const EquipmentTypeSchema =
  generated.publicEquipmentTypesRowSchemaSchema;

// Export types
export type EquipmentType = z.infer<typeof EquipmentTypeSchema>;
export type CreateEquipmentTypeInput = z.infer<
  typeof CreateEquipmentTypeSchema
>;
export type UpdateEquipmentTypeInput = z.infer<
  typeof UpdateEquipmentTypeSchema
>;
export type DeleteEquipmentTypeParams = z.infer<
  typeof DeleteEquipmentTypeParamsSchema
>;
export type GetEquipmentTypesParams = z.infer<
  typeof GetEquipmentTypesParamsSchema
>;
export type EquipmentFormValues = z.infer<typeof EquipmentSchema>;

// Provider Equipment Schemas
export const EquipmentProviderLinkSchema = z.object({
  provider_id: z.string().uuid("Invalid provider ID"),
  equipment_type_id: z.string().uuid("Invalid equipment type ID")
});

export const EquipmentProviderUnlinkSchema = z.object({
  provider_id: z.string().uuid("Invalid provider ID"),
  equipment_type_id: z.string().uuid("Invalid equipment type ID"),
});

// Provider Equipment Types
export type ProviderEquipmentInput = z.infer<
  typeof EquipmentProviderLinkSchema
>;
export type ProviderEquipmentDeleteInput = z.infer<
  typeof EquipmentProviderUnlinkSchema
>;

// Common categories - for UI reference
export const COMMON_EQUIPMENT_CATEGORIES = [
  "trailer",
  "container",
  "vehicle",
  "chassis",
  "other",
] as const;

export type EquipmentCategory = (typeof COMMON_EQUIPMENT_CATEGORIES)[number];
