/*
 * ==========================================
 * |          GENERATED BY SUPAZOD          |
 * ==========================================
 */

import { z } from "zod";
import * as generated from "./schemas";
export type Json = z.infer<typeof generated.jsonSchema>;
export type PublicCargoTypesRowSchema = z.infer<
  typeof generated.publicCargoTypesRowSchemaSchema
>;
export type PublicCargoTypesInsertSchema = z.infer<
  typeof generated.publicCargoTypesInsertSchemaSchema
>;
export type PublicCargoTypesUpdateSchema = z.infer<
  typeof generated.publicCargoTypesUpdateSchemaSchema
>;
export type PublicCountriesRowSchema = z.infer<
  typeof generated.publicCountriesRowSchemaSchema
>;
export type PublicCountriesInsertSchema = z.infer<
  typeof generated.publicCountriesInsertSchemaSchema
>;
export type PublicCountriesUpdateSchema = z.infer<
  typeof generated.publicCountriesUpdateSchemaSchema
>;
export type PublicEmailAttachmentsRowSchema = z.infer<
  typeof generated.publicEmailAttachmentsRowSchemaSchema
>;
export type PublicEmailAttachmentsInsertSchema = z.infer<
  typeof generated.publicEmailAttachmentsInsertSchemaSchema
>;
export type PublicEmailAttachmentsUpdateSchema = z.infer<
  typeof generated.publicEmailAttachmentsUpdateSchemaSchema
>;
export type PublicEmailAttachmentsRelationshipsSchema = z.infer<
  typeof generated.publicEmailAttachmentsRelationshipsSchemaSchema
>;
export type PublicEmailMessagesRowSchema = z.infer<
  typeof generated.publicEmailMessagesRowSchemaSchema
>;
export type PublicEmailMessagesInsertSchema = z.infer<
  typeof generated.publicEmailMessagesInsertSchemaSchema
>;
export type PublicEmailMessagesUpdateSchema = z.infer<
  typeof generated.publicEmailMessagesUpdateSchemaSchema
>;
export type PublicEmailMessagesRelationshipsSchema = z.infer<
  typeof generated.publicEmailMessagesRelationshipsSchemaSchema
>;
export type PublicEmailSettingsRowSchema = z.infer<
  typeof generated.publicEmailSettingsRowSchemaSchema
>;
export type PublicEmailSettingsInsertSchema = z.infer<
  typeof generated.publicEmailSettingsInsertSchemaSchema
>;
export type PublicEmailSettingsUpdateSchema = z.infer<
  typeof generated.publicEmailSettingsUpdateSchemaSchema
>;
export type PublicEquipmentTypesRowSchema = z.infer<
  typeof generated.publicEquipmentTypesRowSchemaSchema
>;
export type PublicEquipmentTypesInsertSchema = z.infer<
  typeof generated.publicEquipmentTypesInsertSchemaSchema
>;
export type PublicEquipmentTypesUpdateSchema = z.infer<
  typeof generated.publicEquipmentTypesUpdateSchemaSchema
>;
export type PublicProviderContactsRowSchema = z.infer<
  typeof generated.publicProviderContactsRowSchemaSchema
>;
export type PublicProviderContactsInsertSchema = z.infer<
  typeof generated.publicProviderContactsInsertSchemaSchema
>;
export type PublicProviderContactsUpdateSchema = z.infer<
  typeof generated.publicProviderContactsUpdateSchemaSchema
>;
export type PublicProviderContactsRelationshipsSchema = z.infer<
  typeof generated.publicProviderContactsRelationshipsSchemaSchema
>;
export type PublicProviderEquipmentsRowSchema = z.infer<
  typeof generated.publicProviderEquipmentsRowSchemaSchema
>;
export type PublicProviderEquipmentsInsertSchema = z.infer<
  typeof generated.publicProviderEquipmentsInsertSchemaSchema
>;
export type PublicProviderEquipmentsUpdateSchema = z.infer<
  typeof generated.publicProviderEquipmentsUpdateSchemaSchema
>;
export type PublicProviderEquipmentsRelationshipsSchema = z.infer<
  typeof generated.publicProviderEquipmentsRelationshipsSchemaSchema
>;
export type PublicProviderRoutesRowSchema = z.infer<
  typeof generated.publicProviderRoutesRowSchemaSchema
>;
export type PublicProviderRoutesInsertSchema = z.infer<
  typeof generated.publicProviderRoutesInsertSchemaSchema
>;
export type PublicProviderRoutesUpdateSchema = z.infer<
  typeof generated.publicProviderRoutesUpdateSchemaSchema
>;
export type PublicProviderRoutesRelationshipsSchema = z.infer<
  typeof generated.publicProviderRoutesRelationshipsSchemaSchema
>;
export type PublicProvidersRowSchema = z.infer<
  typeof generated.publicProvidersRowSchemaSchema
>;
export type PublicProvidersInsertSchema = z.infer<
  typeof generated.publicProvidersInsertSchemaSchema
>;
export type PublicProvidersUpdateSchema = z.infer<
  typeof generated.publicProvidersUpdateSchemaSchema
>;
export type PublicProvidersRelationshipsSchema = z.infer<
  typeof generated.publicProvidersRelationshipsSchemaSchema
>;
export type PublicRfqBidsRowSchema = z.infer<
  typeof generated.publicRfqBidsRowSchemaSchema
>;
export type PublicRfqBidsInsertSchema = z.infer<
  typeof generated.publicRfqBidsInsertSchemaSchema
>;
export type PublicRfqBidsUpdateSchema = z.infer<
  typeof generated.publicRfqBidsUpdateSchemaSchema
>;
export type PublicRfqBidsRelationshipsSchema = z.infer<
  typeof generated.publicRfqBidsRelationshipsSchemaSchema
>;
export type PublicRfqCargoTypesRowSchema = z.infer<
  typeof generated.publicRfqCargoTypesRowSchemaSchema
>;
export type PublicRfqCargoTypesInsertSchema = z.infer<
  typeof generated.publicRfqCargoTypesInsertSchemaSchema
>;
export type PublicRfqCargoTypesUpdateSchema = z.infer<
  typeof generated.publicRfqCargoTypesUpdateSchemaSchema
>;
export type PublicRfqCargoTypesRelationshipsSchema = z.infer<
  typeof generated.publicRfqCargoTypesRelationshipsSchemaSchema
>;
export type PublicRfqIncomingEmailsRowSchema = z.infer<
  typeof generated.publicRfqIncomingEmailsRowSchemaSchema
>;
export type PublicRfqIncomingEmailsInsertSchema = z.infer<
  typeof generated.publicRfqIncomingEmailsInsertSchemaSchema
>;
export type PublicRfqIncomingEmailsUpdateSchema = z.infer<
  typeof generated.publicRfqIncomingEmailsUpdateSchemaSchema
>;
export type PublicRfqIncomingEmailsRelationshipsSchema = z.infer<
  typeof generated.publicRfqIncomingEmailsRelationshipsSchemaSchema
>;
export type PublicRfqOutgoingEmailsRowSchema = z.infer<
  typeof generated.publicRfqOutgoingEmailsRowSchemaSchema
>;
export type PublicRfqOutgoingEmailsInsertSchema = z.infer<
  typeof generated.publicRfqOutgoingEmailsInsertSchemaSchema
>;
export type PublicRfqOutgoingEmailsUpdateSchema = z.infer<
  typeof generated.publicRfqOutgoingEmailsUpdateSchemaSchema
>;
export type PublicRfqOutgoingEmailsRelationshipsSchema = z.infer<
  typeof generated.publicRfqOutgoingEmailsRelationshipsSchemaSchema
>;
export type PublicRfqProvidersRowSchema = z.infer<
  typeof generated.publicRfqProvidersRowSchemaSchema
>;
export type PublicRfqProvidersInsertSchema = z.infer<
  typeof generated.publicRfqProvidersInsertSchemaSchema
>;
export type PublicRfqProvidersUpdateSchema = z.infer<
  typeof generated.publicRfqProvidersUpdateSchemaSchema
>;
export type PublicRfqProvidersRelationshipsSchema = z.infer<
  typeof generated.publicRfqProvidersRelationshipsSchemaSchema
>;
export type PublicRfqsRowSchema = z.infer<
  typeof generated.publicRfqsRowSchemaSchema
>;
export type PublicRfqsInsertSchema = z.infer<
  typeof generated.publicRfqsInsertSchemaSchema
>;
export type PublicRfqsUpdateSchema = z.infer<
  typeof generated.publicRfqsUpdateSchemaSchema
>;
export type PublicRfqsRelationshipsSchema = z.infer<
  typeof generated.publicRfqsRelationshipsSchemaSchema
>;
export type PublicRolesRowSchema = z.infer<
  typeof generated.publicRolesRowSchemaSchema
>;
export type PublicRolesInsertSchema = z.infer<
  typeof generated.publicRolesInsertSchemaSchema
>;
export type PublicRolesUpdateSchema = z.infer<
  typeof generated.publicRolesUpdateSchemaSchema
>;
export type PublicUserRolesRowSchema = z.infer<
  typeof generated.publicUserRolesRowSchemaSchema
>;
export type PublicUserRolesInsertSchema = z.infer<
  typeof generated.publicUserRolesInsertSchemaSchema
>;
export type PublicUserRolesUpdateSchema = z.infer<
  typeof generated.publicUserRolesUpdateSchemaSchema
>;
export type PublicUserRolesRelationshipsSchema = z.infer<
  typeof generated.publicUserRolesRelationshipsSchemaSchema
>;
export type PublicUsersRowSchema = z.infer<
  typeof generated.publicUsersRowSchemaSchema
>;
export type PublicUsersInsertSchema = z.infer<
  typeof generated.publicUsersInsertSchemaSchema
>;
export type PublicUsersUpdateSchema = z.infer<
  typeof generated.publicUsersUpdateSchemaSchema
>;
export type PublicWatchedEmailAccountsRowSchema = z.infer<
  typeof generated.publicWatchedEmailAccountsRowSchemaSchema
>;
export type PublicWatchedEmailAccountsInsertSchema = z.infer<
  typeof generated.publicWatchedEmailAccountsInsertSchemaSchema
>;
export type PublicWatchedEmailAccountsUpdateSchema = z.infer<
  typeof generated.publicWatchedEmailAccountsUpdateSchemaSchema
>;
export type PublicHasRoleArgsSchema = z.infer<
  typeof generated.publicHasRoleArgsSchemaSchema
>;
export type PublicHasRoleReturnsSchema = z.infer<
  typeof generated.publicHasRoleReturnsSchemaSchema
>;
export type PublicIsAdminArgsSchema = z.infer<
  typeof generated.publicIsAdminArgsSchemaSchema
>;
export type PublicIsAdminReturnsSchema = z.infer<
  typeof generated.publicIsAdminReturnsSchemaSchema
>;
