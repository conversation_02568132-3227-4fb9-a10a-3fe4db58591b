/**
 * Provider Schemas and Types
 *
 * This file contains schemas and types for the Provider entity.
 * It imports directly from Supazod-generated schemas and extends them where needed.
 * Includes form schemas, selection schemas, and UI-specific types.
 *
 * @module ProviderSchema
 */

import { z } from "zod";
import * as generated from "./schemas";
import {
  createStructuredAddressSchema,
  createGetResourceParamsSchema,
} from "./utils";

// ==========================================
// Schema Definitions
// ==========================================

/**
 * Provider Schema
 *
 * Base schema for provider entity, directly from Supazod-generated schema.
 * Contains all fields from the providers table.
 */
export const ProviderSchema = generated.publicProvidersRowSchemaSchema;

/**
 * Create Provider Schema
 *
 * Schema for creating a new provider with enhanced validation:
 * - Requires a non-empty name
 * - Restricts status to predefined values with a default
 * - Includes structured address validation
 */
export const CreateProviderSchema =
  generated.publicProvidersInsertSchemaSchema.extend({
    name: z.string().min(1, "Name is required"),
    status: z
      .enum(["active", "inactive", "pending", "suspended"])
      .default("active"),
    structured_address: createStructuredAddressSchema(),
  });

/**
 * Update Provider Schema
 *
 * Schema for updating an existing provider:
 * - Requires a valid UUID for the provider ID
 * - All fields are optional except the ID
 * - Maintains the same validation rules as CreateProviderSchema for each field
 */
export const UpdateProviderSchema = z.object({
  id: z.string().uuid("Invalid provider ID"),
  name: z.string().min(1, "Name is required").optional(),
  full_address: z.string().nullable().optional(),
  structured_address: createStructuredAddressSchema().optional(),
  tax_id: z.string().nullable().optional(),
  status: z.enum(["active", "inactive", "pending", "suspended"]).optional(),
  verified: z.boolean().optional(),
});

/**
 * Get Providers Params Schema
 *
 * Schema for fetching providers with pagination or by ID:
 * - Optional UUID for filtering by provider ID
 * - Pagination parameters with defaults
 */
export const GetProvidersParamsSchema = createGetResourceParamsSchema(
  "id",
  "Invalid provider ID",
);

/**
 * Delete Provider Params Schema
 *
 * Schema for deleting a provider:
 * - Requires a valid UUID for the provider ID
 */
export const DeleteProviderParamsSchema = z.object({
  id: z.string().uuid("Invalid provider ID"),
});

/**
 * Provider Base Schema
 *
 * Extended schema that includes all creation fields plus a required ID.
 * Useful for validation in contexts where an ID is required.
 */
export const ProviderBaseSchema = CreateProviderSchema.extend({
  id: z.string().uuid(),
});

/**
 * Provider Form Schema
 *
 * Schema for the provider form in the dashboard.
 * Used for validating provider creation and update inputs.
 */
export const ProviderFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  tax_id: z.string().optional(),
  full_address: z.string().optional(),
  status: z
    .enum(["active", "inactive", "pending", "suspended"])
    .default("pending"),
  verified: z.boolean().default(false),
});

/**
 * Provider Selection Form Schema
 *
 * Schema for the provider selection form in the RFQ module.
 * Used for validating search, filter, and selection inputs.
 */
export const ProviderSelectionFormSchema = z.object({
  searchQuery: z.string().optional(),
  statusFilter: z
    .enum(["all", "active", "verified", "unverified"])
    .default("all"),
  selectedProviders: z.array(z.string().uuid()).default([]),
  page: z.number().int().positive().default(1),
  pageSize: z.number().int().positive().default(10),
});

/**
 * Email Data Schema
 *
 * Schema for email data when sending RFQs to providers.
 */
export const EmailDataSchema = z.object({
  subject: z.string().min(1, "Subject is required"),
  body: z.string().min(1, "Email body is required"),
  isCustom: z.boolean().default(false),
  templateId: z.string().optional(),
});

// ==========================================
// Type Definitions
// ==========================================

/**
 * Provider
 *
 * Type definition for a provider entity, derived from ProviderSchema.
 */
export type Provider = z.infer<typeof ProviderSchema>;

/**
 * Create Provider Input
 *
 * Type definition for provider creation payload, derived from CreateProviderSchema.
 */
export type CreateProviderInput = z.infer<typeof CreateProviderSchema>;

/**
 * Update Provider Input
 *
 * Type definition for provider update payload, derived from UpdateProviderSchema.
 */
export type UpdateProviderInput = z.infer<typeof UpdateProviderSchema>;

/**
 * Delete Provider Params
 *
 * Type definition for provider deletion parameters, derived from DeleteProviderParamsSchema.
 */
export type DeleteProviderParams = z.infer<typeof DeleteProviderParamsSchema>;

/**
 * Get Providers Params
 *
 * Type definition for provider query parameters, derived from GetProvidersParamsSchema.
 */
export type GetProvidersParams = z.infer<typeof GetProvidersParamsSchema>;

/**
 * Provider Relationships
 *
 * Type definition for provider relationships, derived from Supazod-generated schema.
 */
export type ProviderRelationships = z.infer<
  typeof generated.publicProvidersRelationshipsSchemaSchema
>;

/**
 * Provider Form Values
 *
 * Type definition for the provider form values.
 */
export type ProviderFormValues = z.infer<typeof ProviderFormSchema>;

/**
 * Provider Selection Form Values
 *
 * Type definition for the provider selection form values.
 */
export type ProviderSelectionFormValues = z.infer<typeof ProviderSelectionFormSchema>;

/**
 * Email Data
 *
 * Type definition for email data.
 */
export type EmailData = z.infer<typeof EmailDataSchema>;

// ==========================================
// Action State Interfaces
// ==========================================

/**
 * Provider Action State
 *
 * Interface for the provider form action state.
 */
export interface ProviderActionState {
  success: boolean;
  message?: string;
  values: ProviderFormValues;
  data?: Provider;
  error?: string;
  fieldErrors?: Record<string, string[]>;
}

/**
 * Provider Selection Action State
 *
 * Type definition for the action state returned by the provider selection server action.
 */
export interface ProviderSelectionActionState {
  success: boolean;
  message?: string;
  errors?: Record<string, { message: string }>;
  values: ProviderSelectionFormValues;
  providers?: any[];
  rfqProviders?: any[];
  totalCount?: number;
}

/**
 * Provider Equipment Action State
 *
 * Interface for the provider equipment action state.
 */
export interface ProviderEquipmentActionState {
  success: boolean;
  message?: string;
  selectedEquipments: string[];
  data?: any[];
  error?: string;
}
