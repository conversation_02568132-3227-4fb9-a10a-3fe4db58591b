/**
 * RFQ schemas and types
 *
 * This file contains schemas and types for the RFQ (Request for Quote) entity.
 * It imports directly from Supazod-generated schemas and extends them where needed.
 */

import { z } from "zod";
import * as generated from "./schemas";
import { createGetResourceParamsSchema } from "./utils";
import { validatePostalCodeForCountry, getPostalCodeErrorMessage } from "@/lib/utils/postal-code-validation";

// ==========================================
// Helper Functions
// ==========================================

/**
 * Creates a postal code validation function for use with Zod refinements
 *
 * This function creates a validator that can be used in Zod schemas to validate
 * postal codes against their corresponding countries.
 *
 * @param getCountryName - Function to get country name from country ID
 * @returns Validation function for Zod refinement
 */
export function createPostalCodeValidator(
  getCountryName: (countryId: string) => string | undefined
) {
  return (data: any, ctx: z.RefinementCtx) => {
    // Validate origin postal code
    if (data.origin_postal_code && data.origin_country_id) {
      const originCountryName = getCountryName(data.origin_country_id);
      if (originCountryName) {
        const isValid = validatePostalCodeForCountry(data.origin_postal_code, originCountryName);
        if (!isValid) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['origin_postal_code'],
            message: getPostalCodeErrorMessage(originCountryName),
          });
        }
      }
    }

    // Validate destination postal code
    if (data.destination_postal_code && data.destination_country_id) {
      const destinationCountryName = getCountryName(data.destination_country_id);
      if (destinationCountryName) {
        const isValid = validatePostalCodeForCountry(data.destination_postal_code, destinationCountryName);
        if (!isValid) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['destination_postal_code'],
            message: getPostalCodeErrorMessage(destinationCountryName),
          });
        }
      }
    }
  };
}

/**
 * Validates a single postal code against a country name
 *
 * This is a simpler validation function for individual postal code fields
 * when you have the country name available.
 *
 * @param postalCode - The postal code to validate
 * @param countryName - The country name
 * @returns Validation result
 */
export function validateSinglePostalCode(postalCode: string, countryName: string): boolean {
  return validatePostalCodeForCountry(postalCode, countryName);
}

/**
 * Creates an enhanced RFQ schema with postal code validation
 *
 * This function creates a schema that includes postal code validation based on
 * the selected countries. It should be used in forms where country data is available.
 *
 * @param countries - Array of countries with id and name properties
 * @returns Enhanced CreateRFQSchema with postal code validation
 */
export function createRFQSchemaWithPostalValidation(countries: Array<{ id: string; name: string }>) {
  // Create a map for fast country lookup
  const countryMap = new Map(countries.map(country => [country.id, country.name]));

  const getCountryName = (countryId: string) => countryMap.get(countryId);

  return CreateRFQSchema.superRefine(createPostalCodeValidator(getCountryName));
}

/**
 * Creates an enhanced Update RFQ schema with postal code validation
 *
 * This function creates an update schema that includes postal code validation based on
 * the selected countries. It should be used in forms where country data is available.
 *
 * @param countries - Array of countries with id and name properties
 * @returns Enhanced UpdateRFQSchema with postal code validation
 */
export function createUpdateRFQSchemaWithPostalValidation(countries: Array<{ id: string; name: string }>) {
  // Create a map for fast country lookup
  const countryMap = new Map(countries.map(country => [country.id, country.name]));

  const getCountryName = (countryId: string) => countryMap.get(countryId);

  return UpdateRFQSchema.superRefine(createPostalCodeValidator(getCountryName));
}

// ==========================================
// Schema Definitions
// ==========================================

/**
 * RFQ Schema
 *
 * Base schema for RFQ entity, directly from Supazod-generated schema.
 * Contains all fields from the rfqs table.
 *
 * @remarks
 * This schema is a direct re-export of the Supazod-generated schema for the rfqs table.
 * It includes all fields defined in the database table.
 */
export const RFQSchema = generated.publicRfqsRowSchemaSchema;

/**
 * Schema for creating a new RFQ
 *
 * Extends the Supazod-generated insert schema with additional validation.
 *
 * @remarks
 * This schema adds validation rules on top of the base insert schema.
 * It includes required fields, field-specific validation, and default values.
 *
 * @example
 * ```typescript
 * const newRFQ = {
 *   title: "Steel shipment from Berlin to Paris",
 *   status: "draft",
 *   origin_country_id: "123e4567-e89b-12d3-a456-************",
 *   origin_city: "Berlin",
 *   origin_postal_code: "10115",
 *   destination_country_id: "223e4567-e89b-12d3-a456-************",
 *   destination_city: "Paris",
 *   destination_postal_code: "75001",
 *   cargo_type_ids: ["323e4567-e89b-12d3-a456-************"],
 *   weight: 25,
 *   equipment_type_id: "423e4567-e89b-12d3-a456-************",
 * };
 * const result = CreateRFQSchema.parse(newRFQ);
 * ```
 */
export const CreateRFQSchema = generated.publicRfqsInsertSchemaSchema.extend({
  title: z.string().optional(),
  status: z.enum(["draft", "ready", "sent", "closed"]).default("draft"),
  expiration_date: z
    .string()
    .datetime("Please provide a valid date and time")
    .optional(),
  notes: z.string().optional(),

  // Make created_by optional - it will be set from the authenticated user on the server
  created_by: z.string().optional(),

  // Shipping details
  origin_country_id: z.string().uuid("Please select a valid origin country"),
  origin_city: z.string().min(1, "Please enter the origin city"),
  origin_address: z.string().optional(),
  origin_postal_code: z.string().min(1, "Please enter the origin postal code"),
  destination_country_id: z
    .string()
    .uuid("Please select a valid destination country"),
  destination_city: z.string().min(1, "Please enter the destination city"),
  destination_address: z.string().optional(),
  destination_postal_code: z
    .string()
    .min(1, "Please enter the destination postal code"),
  preferred_shipping_date: z
    .string()
    .datetime("Please provide a valid date and time")
    .optional(),

  // Cargo specifications
  cargo_type_id: z.string().uuid("Please select a valid cargo type").optional(),
  // Additional field not in the database schema
  cargo_type_ids: z
    .array(z.string().uuid("Please select valid cargo types"))
    .min(1, "Please select at least one cargo type"),
  weight: z
    .number()
    .positive("Please enter a weight greater than 0")
    .refine((val) => val <= 1000, "Weight must be 1000 tonnes or less"),
  length: z
    .number()
    .positive("Please enter a length greater than 0")
    .optional(),
  width: z.number().positive("Please enter a width greater than 0").optional(),
  height: z
    .number()
    .positive("Please enter a height greater than 0")
    .optional(),
  quantity: z
    .number()
    .int()
    .positive("Please enter a quantity greater than 0")
    .default(1),
  special_requirements: z.string().optional(),

  // Equipment requirements
  equipment_type_id: z.string().uuid("Please select a valid equipment type"),
  equipment_quantity: z
    .number()
    .int()
    .positive("Please enter an equipment quantity greater than 0")
    .default(1),
});

/**
 * Schema for updating an existing RFQ
 *
 * Extends the Supazod-generated update schema with additional validation.
 *
 * @remarks
 * This schema is used for updating an existing RFQ. All fields are optional
 * except for the ID, which is required to identify the RFQ to update.
 *
 * @example
 * ```typescript
 * const updateData = {
 *   id: "123e4567-e89b-12d3-a456-************",
 *   title: "Updated Steel shipment from Berlin to Paris",
 *   status: "ready",
 *   cargo_type_ids: ["323e4567-e89b-12d3-a456-************", "523e4567-e89b-12d3-a456-************"],
 * };
 * const result = UpdateRFQSchema.parse(updateData);
 * ```
 */
export const UpdateRFQSchema = generated.publicRfqsUpdateSchemaSchema.extend({
  id: z.string().uuid("Please provide a valid RFQ ID"),
  updated_at: z
    .string()
    .datetime("Please provide a valid date and time")
    .optional(),
  // Additional field not in the database schema
  cargo_type_ids: z
    .array(z.string().uuid("Please select valid cargo types"))
    .min(1, "Please select at least one cargo type")
    .optional(),
});

/**
 * Schema for fetching RFQs with pagination or by ID
 *
 * @remarks
 * This schema extends the base resource params schema with RFQ-specific filters.
 * It includes pagination parameters (page, pageSize) and optional filters (id, status).
 *
 * @example
 * ```typescript
 * // Get all RFQs with pagination
 * const params = { page: 1, pageSize: 10 };
 *
 * // Get RFQs with specific status
 * const params = { status: "draft", page: 1, pageSize: 10 };
 *
 * // Get a specific RFQ by ID
 * const params = { id: "123e4567-e89b-12d3-a456-************" };
 * ```
 */
export const GetRFQsParamsSchema = z.object({
  id: z.string().uuid("Please provide a valid ID").optional(),
  page: z.union([z.string(), z.number()]).transform(val => Number(val)).optional().default(1),
  pageSize: z.union([z.string(), z.number()]).transform(val => Number(val)).optional().default(10),
  status: z.enum(["draft", "ready", "sent", "closed"]).optional(),
  origin_city: z.string().optional(),
  destination_city: z.string().optional(),
  search: z.string().optional(),
  created_after: z.string().datetime().optional(),
  created_before: z.string().datetime().optional(),
});

/**
 * RFQ Provider Schema
 *
 * Base schema for RFQ provider entity, directly from Supazod-generated schema.
 *
 * @remarks
 * This schema is a direct re-export of the Supazod-generated schema for the rfq_providers table.
 * It includes all fields defined in the database table.
 */
export const RFQProviderRowSchema = generated.publicRfqProvidersRowSchemaSchema;

/**
 * Schema for RFQ provider selection
 *
 * Extends the Supazod-generated insert schema with additional validation.
 *
 * @remarks
 * This schema is used for creating a new RFQ provider relationship.
 * It defines the relationship between an RFQ and a provider, including the status of that relationship.
 *
 * @example
 * ```typescript
 * const rfqProvider = {
 *   rfq_id: "123e4567-e89b-12d3-a456-************",
 *   provider_id: "223e4567-e89b-12d3-a456-************",
 *   status: "selected",
 * };
 * const result = RFQProviderSchema.parse(rfqProvider);
 * ```
 */
export const RFQProviderSchema =
  generated.publicRfqProvidersInsertSchemaSchema.extend({
    rfq_id: z.string().uuid("Please provide a valid RFQ ID"),
    provider_id: z.string().uuid("Please provide a valid provider ID"),
    status: z
      .enum(["matched", "selected", "invited", "declined", "bid_submitted"])
      .default("matched"),
    selected_at: z
      .string()
      .datetime("Please provide a valid date and time")
      .optional(),
    invited_at: z
      .string()
      .datetime("Please provide a valid date and time")
      .optional(),
    email_opened_at: z
      .string()
      .datetime("Please provide a valid date and time")
      .optional(),
    response_received_at: z
      .string()
      .datetime("Please provide a valid date and time")
      .optional(),
    notes: z.string().optional(),
  });

/**
 * RFQ Bid Schema
 *
 * Base schema for RFQ bid entity, directly from Supazod-generated schema.
 *
 * @remarks
 * This schema is a direct re-export of the Supazod-generated schema for the rfq_bids table.
 * It includes all fields defined in the database table.
 */
export const RFQBidRowSchema = generated.publicRfqBidsRowSchemaSchema;

/**
 * Schema for RFQ bid submission
 *
 * Extends the Supazod-generated insert schema with additional validation.
 *
 * @remarks
 * This schema is used for creating a new bid on an RFQ.
 * It includes validation for price, currency, and status.
 *
 * @example
 * ```typescript
 * const bid = {
 *   rfq_id: "123e4567-e89b-12d3-a456-************",
 *   provider_id: "223e4567-e89b-12d3-a456-************",
 *   price: 5000,
 *   notes: "Includes insurance and customs clearance",
 * };
 * const result = RFQBidSchema.parse(bid);
 * ```
 */
export const RFQBidSchema = generated.publicRfqBidsInsertSchemaSchema.extend({
  rfq_id: z.string().uuid("Please provide a valid RFQ ID"),
  provider_id: z.string().uuid("Please provide a valid provider ID"),
  price: z
    .number()
    .nonnegative("Please enter a valid price (must be 0 or greater)"),
  currency: z
    .string()
    .default("EUR")
    .transform(() => "EUR"), // Always set to EUR
  status: z
    .enum(["received", "under_review", "accepted", "rejected", "negotiating"])
    .default("received"),
  submitted_at: z
    .string()
    .datetime("Please provide a valid submission date and time")
    .optional()
    .default(() => new Date().toISOString()), // Default to current timestamp if not provided
  is_ai_extracted: z
    .union([z.boolean(), z.string()])
    .transform((val) => {
      if (typeof val === "string") {
        return val === "true";
      }
      return Boolean(val);
    })
    .default(false),
  notes: z.string().optional(),
});

/**
 * RFQ Cargo Type Row Schema
 *
 * Base schema for RFQ cargo type entity, directly from Supazod-generated schema.
 *
 * @remarks
 * This schema is a direct re-export of the Supazod-generated schema for the rfq_cargo_types table.
 * It includes all fields defined in the database table.
 */
export const RFQCargoTypeRowSchema =
  generated.publicRfqCargoTypesRowSchemaSchema;

/**
 * Schema for RFQ cargo types (join table)
 *
 * Extends the Supazod-generated insert schema with additional validation.
 *
 * @remarks
 * This schema is used for creating a new relationship between an RFQ and a cargo type.
 * It represents the many-to-many relationship between RFQs and cargo types.
 *
 * @example
 * ```typescript
 * const rfqCargoType = {
 *   rfq_id: "123e4567-e89b-12d3-a456-************",
 *   cargo_type_id: "323e4567-e89b-12d3-a456-************",
 * };
 * const result = RFQCargoTypeSchema.parse(rfqCargoType);
 * ```
 */
export const RFQCargoTypeSchema =
  generated.publicRfqCargoTypesInsertSchemaSchema.extend({
    rfq_id: z.string().uuid("Please provide a valid RFQ ID"),
    cargo_type_id: z.string().uuid("Please provide a valid cargo type ID"),
  });

/**
 * Schema for updating an existing RFQ bid
 *
 * Extends the Supazod-generated update schema with additional validation.
 *
 * @remarks
 * This schema is used for updating an existing bid on an RFQ.
 * All fields are optional except for the ID, which is required to identify the bid to update.
 *
 * @example
 * ```typescript
 * const updateBid = {
 *   id: "123e4567-e89b-12d3-a456-************",
 *   price: 4800,
 *   status: "under_review",
 *   notes: "Updated price after negotiation",
 * };
 * const result = UpdateRFQBidSchema.parse(updateBid);
 * ```
 */
export const UpdateRFQBidSchema =
  generated.publicRfqBidsUpdateSchemaSchema.extend({
    id: z.string().uuid("Please provide a valid bid ID"),
    price: z
      .number()
      .nonnegative("Please enter a valid price (must be 0 or greater)")
      .optional(),
    status: z
      .enum(["received", "under_review", "accepted", "rejected", "negotiating"])
      .optional(),
    notes: z.string().optional(),
  });

// ==========================================
// Type Definitions
// ==========================================

/**
 * RFQ type
 *
 * @description
 * Represents a Request for Quote (RFQ) entity as stored in the database.
 * This type is inferred from the RFQSchema.
 */
export type RFQ = z.infer<typeof RFQSchema>;

/**
 * Input type for creating a new RFQ
 *
 * @description
 * Represents the data structure required to create a new RFQ.
 * This type is inferred from the CreateRFQSchema.
 */
export type CreateRFQInput = z.infer<typeof CreateRFQSchema>;

/**
 * Input type for updating an existing RFQ
 *
 * @description
 * Represents the data structure for updating an existing RFQ.
 * All fields are optional except for the ID.
 * This type is inferred from the UpdateRFQSchema.
 */
export type UpdateRFQInput = z.infer<typeof UpdateRFQSchema>;

/**
 * Parameters type for fetching RFQs
 *
 * @description
 * Represents the parameters that can be used to filter and paginate RFQs.
 * This type is inferred from the GetRFQsParamsSchema.
 */
export type GetRFQsParams = z.infer<typeof GetRFQsParamsSchema>;

/**
 * RFQ provider type
 *
 * @description
 * Represents the relationship between an RFQ and a provider.
 * This type is inferred from the RFQProviderSchema.
 */
export type RFQProvider = z.infer<typeof RFQProviderSchema>;

/**
 * Input type for creating a new RFQ provider relationship
 *
 * @description
 * Represents the data structure required to create a new RFQ provider relationship.
 * This type is inferred from the RFQProviderSchema.
 */
export type RFQProviderInput = z.infer<typeof RFQProviderSchema>;

/**
 * RFQ bid type
 *
 * @description
 * Represents a bid submitted by a provider for an RFQ.
 * This type is inferred from the RFQBidSchema.
 */
export type RFQBid = z.infer<typeof RFQBidSchema>;

/**
 * Input type for creating a new RFQ bid
 *
 * @description
 * Represents the data structure required to create a new bid for an RFQ.
 * This type is inferred from the RFQBidSchema.
 */
export type RFQBidInput = z.infer<typeof RFQBidSchema>;

/**
 * RFQ cargo type
 *
 * @description
 * Represents the relationship between an RFQ and a cargo type.
 * This type is inferred from the RFQCargoTypeSchema.
 */
export type RFQCargoType = z.infer<typeof RFQCargoTypeSchema>;

/**
 * Input type for creating a new RFQ cargo type relationship
 *
 * @description
 * Represents the data structure required to create a new relationship between an RFQ and a cargo type.
 * This type is inferred from the RFQCargoTypeSchema.
 */
export type RFQCargoTypeInput = z.infer<typeof RFQCargoTypeSchema>;

/**
 * Input type for updating an RFQ bid
 *
 * @description
 * Represents the data structure for updating an existing bid for an RFQ.
 * All fields are optional except for the ID.
 * This type is inferred from the UpdateRFQBidSchema.
 */
export type UpdateRFQBidInput = z.infer<typeof UpdateRFQBidSchema>;
