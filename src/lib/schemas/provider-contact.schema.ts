/**
 * Provider Contact Schemas and Types
 *
 * This file contains schemas and types for the Provider Contact entity.
 * It imports directly from Supazod-generated schemas and extends them where needed.
 *
 * @module ProviderContactSchema
 */

import { z } from "zod";
import * as generated from "./schemas";
import { createGetResourceParamsSchema } from "./utils";

// ==========================================
// Schema Definitions
// ==========================================

/**
 * Provider Contact Schema
 *
 * Base schema for provider contact entity, directly from Supazod-generated schema.
 * Contains all fields from the provider_contacts table.
 */
export const ProviderContactSchema =
  generated.publicProviderContactsRowSchemaSchema;

/**
 * Create Provider Contact Schema
 *
 * Schema for creating a new provider contact with enhanced validation:
 * - Requires a valid email format
 * - Requires a valid UUID for the provider ID
 */
export const CreateProviderContactSchema =
  generated.publicProviderContactsInsertSchemaSchema.extend({
    email: z.string().email("Invalid email format"),
    provider_id: z.string().uuid("Invalid provider ID"),
  });

/**
 * Update Provider Contact Schema
 *
 * Schema for updating an existing provider contact:
 * - Requires a valid UUID for the contact ID
 * - All fields are optional except the ID
 * - Maintains the same validation rules as CreateProviderContactSchema for each field
 */
export const UpdateProviderContactSchema =
  CreateProviderContactSchema.partial().extend({
    id: z.string().uuid("Invalid contact ID"),
  });

/**
 * Delete Provider Contact Params Schema
 *
 * Schema for deleting a provider contact:
 * - Requires a valid UUID for the contact ID
 * - Optionally includes the provider ID for additional validation
 */
export const DeleteProviderContactParamsSchema = z.object({
  id: z.string().uuid("Invalid contact ID"),
  provider_id: z.string().uuid("Invalid provider ID").optional(),
});

/**
 * Get Provider Contacts Params Schema
 *
 * Schema for fetching provider contacts:
 * - Requires a valid UUID for the provider ID to filter contacts by provider
 */
export const GetProviderContactsParamsSchema = z.object({
  provider_id: z.string().uuid("Invalid provider ID"),
});

/**
 * Contact Form Schema
 *
 * Schema for the contact form in the UI:
 * - Requires a valid email
 * - Name, phone, and role are optional
 * - is_primary is a boolean with default false
 */
export const ContactFormSchema = z.object({
  name: z.string().optional().nullable(),
  email: z.string().email("Invalid email format"),
  phone: z.string().optional().nullable(),
  role: z.string().optional().nullable(),
  is_primary: z.boolean().default(false),
});

// ==========================================
// Type Definitions
// ==========================================

/**
 * Provider Contact
 *
 * Type definition for a provider contact entity, derived from ProviderContactSchema.
 */
export type ProviderContact = z.infer<typeof ProviderContactSchema>;

/**
 * Create Provider Contact Input
 *
 * Type definition for provider contact creation payload, derived from CreateProviderContactSchema.
 */
export type CreateProviderContactInput = z.infer<
  typeof CreateProviderContactSchema
>;

/**
 * Update Provider Contact Input
 *
 * Type definition for provider contact update payload, derived from UpdateProviderContactSchema.
 */
export type UpdateProviderContactInput = z.infer<
  typeof UpdateProviderContactSchema
>;

/**
 * Delete Provider Contact Params
 *
 * Type definition for provider contact deletion parameters, derived from DeleteProviderContactParamsSchema.
 */
export type DeleteProviderContactParams = z.infer<
  typeof DeleteProviderContactParamsSchema
>;

/**
 * Get Provider Contacts Params
 *
 * Type definition for provider contact query parameters, derived from GetProviderContactsParamsSchema.
 */
export type GetProviderContactsParams = z.infer<
  typeof GetProviderContactsParamsSchema
>;

/**
 * Provider Contact Relationships
 *
 * Type definition for provider contact relationships, derived from Supazod-generated schema.
 */
export type ProviderContactRelationships = z.infer<
  typeof generated.publicProviderContactsRelationshipsSchemaSchema
>;

/**
 * Contact Form Values
 *
 * Type definition for contact form values, derived from ContactFormSchema.
 */
export type ContactFormValues = z.infer<typeof ContactFormSchema>;
