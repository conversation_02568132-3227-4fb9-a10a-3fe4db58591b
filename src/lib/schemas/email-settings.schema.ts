/**
 * Email Settings Schemas and Types
 *
 * This file contains schemas and types for email settings.
 * It imports directly from Supazod-generated schemas and extends them where needed.
 *
 * @module EmailSettingsSchema
 */

import { z } from "zod";
import * as generated from "./schemas";
import { ActionResponse } from "./common.schema";

// ==========================================
// Schema Definitions
// ==========================================

/**
 * Email Settings Schema
 *
 * Base schema for email settings, directly from Supazod-generated schema.
 */
export const EmailSettingsSchema = generated.publicEmailSettingsRowSchemaSchema;

/**
 * Email Settings Form Schema
 *
 * Schema for the email settings form in the admin dashboard.
 * Used for validating email settings updates.
 */
export const EmailSettingsFormSchema = z.object({
  company_name: z.string().min(1, "Company name is required"),
  company_logo_url: z.string()
    .refine(
      (val) => val === "" || val === null || val === undefined || /^https?:\/\/.*/.test(val),
      "Please enter a valid URL or leave it empty"
    )
    .optional()
    .nullable()
    .transform(val => val === "" ? null : val),
  sandbox_mode: z.boolean().default(true),
  sandbox_recipient_email: z
    .string()
    .email("Please enter a valid email address")
    .optional()
    .nullable(),
  default_sender_email: z
    .string()
    .email("Please enter a valid email address")
    .min(1, "Sender email is required"),
  default_sender_name: z.string().min(1, "Sender name is required"),
  reply_to_email: z
    .string()
    .email("Please enter a valid email address")
    .optional()
    .nullable(),
  bcc_recipients: z
    .string()
    .transform((val, ctx) => {
      if (!val) return [];
      // Split by newline or comma and trim whitespace
      const emails = val
        .split(/[\n,]/)
        .map((email) => email.trim())
        .filter((email) => email.length > 0);

      // Validate each email
      if (emails.length > 0) {
        const invalidEmails = emails.filter(email => !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email));
        if (invalidEmails.length > 0) {
          // Add a more specific error message with the invalid emails
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: `Invalid email address${invalidEmails.length > 1 ? 'es' : ''}: ${invalidEmails.join(', ')}`,
            path: ['bcc_recipients']
          });

          // Return the original value to allow Zod to continue with validation
          // This will be caught by safeParse in the action
          return emails;
        }
      }

      return emails;
    })
    .optional()
    .nullable(),
  email_signature: z.string().optional().nullable(),
  email_footer_text: z.string().optional().nullable(),
});

/**
 * Get Email Settings Params Schema
 *
 * Schema for retrieving email settings.
 */
export const GetEmailSettingsParamsSchema = z.object({
  id: z.string().uuid("Invalid email settings ID").optional(),
});

/**
 * Update Email Settings Params Schema
 *
 * Schema for updating email settings.
 */
export const UpdateEmailSettingsParamsSchema = z.object({
  id: z.string().uuid("Invalid email settings ID"),
  data: EmailSettingsFormSchema,
});

// ==========================================
// Type Definitions
// ==========================================

/**
 * Email Settings type
 *
 * @description
 * Represents an email settings entity as stored in the database.
 * This type is inferred from the EmailSettingsSchema.
 */
export type EmailSettings = z.infer<typeof EmailSettingsSchema>;

/**
 * Email Settings Form Values type
 *
 * @description
 * Represents the data structure for the email settings form.
 * This type is inferred from the EmailSettingsFormSchema.
 */
export type EmailSettingsFormValues = z.infer<typeof EmailSettingsFormSchema>;

/**
 * Email Settings Action State type
 *
 * @description
 * Represents the state of an email settings action.
 * Used for form submissions and server responses.
 */
export type EmailSettingsActionState = ActionResponse<EmailSettings>;
