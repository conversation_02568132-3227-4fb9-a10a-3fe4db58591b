/**
 * Provider Equipment Schemas and Types
 *
 * This file contains schemas and types for the Provider Equipment entity.
 * It imports directly from Supazod-generated schemas and extends them where needed.
 *
 * @module ProviderEquipmentSchema
 */

import { z } from "zod";
import * as generated from "./schemas";

// ==========================================
// Schema Definitions
// ==========================================

/**
 * Provider Equipment Schema
 *
 * Base schema for provider equipment entity, directly from Supazod-generated schema.
 * Contains all fields from the provider_equipments table.
 */
export const ProviderEquipmentSchema =
  generated.publicProviderEquipmentsRowSchemaSchema;

/**
 * Create Provider Equipment Schema
 *
 * Schema for creating a new provider equipment association:
 * - Requires valid UUIDs for provider and equipment type
 */
export const CreateProviderEquipmentSchema = z.object({
  provider_id: z.string().uuid("Invalid provider ID"),
  equipment_type_id: z.string().uuid("Invalid equipment type ID"),
});

/**
 * Update Provider Equipment Schema
 *
 * Schema for updating an existing provider equipment association:
 * - Requires a valid UUID for the equipment ID
 */
export const UpdateProviderEquipmentSchema = z.object({
  id: z.string().uuid("Invalid equipment ID"),
});

/**
 * Delete Provider Equipment Params Schema
 *
 * Schema for deleting a provider equipment association:
 * - Requires a valid UUID for the equipment ID
 * - Optionally includes the provider ID for additional validation
 */
export const DeleteProviderEquipmentParamsSchema = z.object({
  id: z.string().uuid("Invalid equipment ID"),
  provider_id: z.string().uuid("Invalid provider ID").optional(),
});

/**
 * Get Provider Equipment Params Schema
 *
 * Schema for fetching provider equipment:
 * - Requires a valid UUID for the provider ID to filter equipment by provider
 */
export const GetProviderEquipmentParamsSchema = z.object({
  provider_id: z.string().uuid("Invalid provider ID"),
});

// ==========================================
// Type Definitions
// ==========================================

/**
 * Provider Equipment
 *
 * Type definition for a provider equipment entity, derived from ProviderEquipmentSchema.
 */
export type ProviderEquipment = z.infer<typeof ProviderEquipmentSchema>;

/**
 * Create Provider Equipment Input
 *
 * Type definition for provider equipment creation payload, derived from CreateProviderEquipmentSchema.
 */
export type CreateProviderEquipmentInput = z.infer<
  typeof CreateProviderEquipmentSchema
>;

/**
 * Update Provider Equipment Input
 *
 * Type definition for provider equipment update payload, derived from UpdateProviderEquipmentSchema.
 */
export type UpdateProviderEquipmentInput = z.infer<
  typeof UpdateProviderEquipmentSchema
>;

/**
 * Delete Provider Equipment Params
 *
 * Type definition for provider equipment deletion parameters, derived from DeleteProviderEquipmentParamsSchema.
 */
export type DeleteProviderEquipmentParams = z.infer<
  typeof DeleteProviderEquipmentParamsSchema
>;

/**
 * Get Provider Equipment Params
 *
 * Type definition for provider equipment query parameters, derived from GetProviderEquipmentParamsSchema.
 */
export type GetProviderEquipmentParams = z.infer<
  typeof GetProviderEquipmentParamsSchema
>;

/**
 * Provider Equipment Relationships
 *
 * Type definition for provider equipment relationships, derived from Supazod-generated schema.
 */
export type ProviderEquipmentRelationships = z.infer<
  typeof generated.publicProviderEquipmentsRelationshipsSchemaSchema
>;
