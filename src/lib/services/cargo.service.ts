import { createClient } from "@/lib/supabase/server/server";
import {
  GetCargoTypesParams,
  CreateCargoTypeInput,
  UpdateCargoTypeInput,
  DeleteCargoTypeParams,
  type CargoType,
} from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("CargoService");

/**
 * Fetches cargo with optional pagination or by ID.
 */
export async function fetchCargo(params: GetCargoTypesParams): Promise<{
  data: CargoType[] | CargoType;
  count?: number;
  pagination?: {
    page: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}> {
  const supabase = await createClient();
  const { id, page = 1, pageSize = 10 } = params;

  // If requesting a specific cargo by ID
  if (id) {
    const { data, error } = await supabase
      .from("cargo_types")
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      logger.error("Error fetching cargo by ID:", error);
      throw new Error(`Cargo with ID ${id} not found: ${error.message}`);
    }

    return { data };
  }

  // For paginated list request
  const startIndex = (page - 1) * pageSize;
  const endIndex = page * pageSize - 1;

  // Fetch total count for pagination metadata
  const { count, error: countError } = await supabase
    .from("cargo_types")
    .select("*", { count: "exact", head: true });

  if (countError) {
    logger.error("Error counting cargo:", countError);
    throw new Error(`Failed to count cargo: ${countError.message}`);
  }

  // Fetch paginated data
  const { data, error } = await supabase
    .from("cargo_types")
    .select("*")
    .range(startIndex, endIndex)
    .order("name");

  if (error) {
    logger.error("Error fetching cargo:", error);
    throw new Error(`Failed to fetch cargo: ${error.message}`);
  }

  // Calculate pagination metadata
  const totalItems = count || 0;
  const totalPages = Math.ceil(totalItems / pageSize);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    data: data || [],
    count: totalItems,
    pagination: {
      page,
      pageSize,
      totalItems,
      totalPages,
      hasNextPage,
      hasPrevPage,
    },
  };
}

/**
 * Creates new cargo.
 */
export async function addCargo(
  input: CreateCargoTypeInput,
): Promise<CargoType> {
  const supabase = await createClient();

  const cargoData = {
    name: input.name,
    description: input.description || null,
    image_url: input.image_url || null,
    updated_at: new Date().toISOString(),
  };

  const { data, error } = await supabase
    .from("cargo_types")
    .insert(cargoData)
    .select()
    .single();

  if (error) {
    logger.error("Error creating cargo:", error);

    // Check for unique constraint violations
    if (error.code === "23505") {
      throw new Error(`Cargo "${input.name}" already exists.`);
    }

    throw new Error(`Failed to create cargo: ${error.message}`);
  }

  if (!data) {
    throw new Error("Failed to create cargo: No data returned.");
  }

  return data;
}

/**
 * Updates existing cargo.
 */
export async function modifyCargo(
  input: UpdateCargoTypeInput,
): Promise<CargoType> {
  const supabase = await createClient();
  const { id, ...updateFields } = input;

  // Check if cargo exists
  const { data: existingCargo, error: checkError } = await supabase
    .from("cargo_types")
    .select("id")
    .eq("id", id)
    .single();

  if (checkError || !existingCargo) {
    logger.error("Error checking cargo existence:", checkError);
    throw new Error(`Cargo with ID ${id} not found.`);
  }

  const updateData = {
    name: updateFields.name,
    description: updateFields.description || null,
    image_url: updateFields.image_url || null,
    updated_at: new Date().toISOString(),
  };

  const { data, error } = await supabase
    .from("cargo_types")
    .update(updateData)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    logger.error("Error updating cargo:", error);

    // Check for unique constraint violations
    if (error.code === "23505") {
      throw new Error(`Cargo "${updateFields.name}" already exists.`);
    }

    throw new Error(`Failed to update cargo: ${error.message}`);
  }

  if (!data) {
    throw new Error(`Failed to update cargo with ID ${id}: No data returned.`);
  }

  return data;
}

/**
 * Deletes cargo by ID.
 */
export async function removeCargo(
  params: DeleteCargoTypeParams,
): Promise<{ message: string }> {
  const supabase = await createClient();
  const { id } = params;

  // Check if cargo exists
  const { data: existingCargo, error: checkError } = await supabase
    .from("cargo_types")
    .select("id")
    .eq("id", id)
    .single();

  if (checkError || !existingCargo) {
    logger.error("Error checking cargo existence:", checkError);
    throw new Error(`Cargo with ID ${id} not found.`);
  }

  const { error } = await supabase.from("cargo_types").delete().eq("id", id);

  if (error) {
    logger.error("Error deleting cargo:", error);
    throw new Error(`Failed to delete cargo: ${error.message}`);
  }

  return { message: `Cargo with ID ${id} successfully deleted.` };
}
