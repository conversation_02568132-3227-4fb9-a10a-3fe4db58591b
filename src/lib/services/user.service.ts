import { createClient } from "@/lib/supabase/server/server";
import { createLogger } from "@/lib/utils/logger/logger";
import {
  type User,
  type GetUsersParams,
  type CreateUserInput,
  type UpdateUserInput,
} from "@/lib/schemas";

const logger = createLogger("UserService");

// User type is imported from @/lib/schemas

// GetUsersParams type is imported from @/lib/schemas

// CreateUserInput type is imported from @/lib/schemas

/**
 * Fetches users with optional filtering by ID
 */
export async function fetchUsers(
  params: Partial<GetUsersParams> = {},
): Promise<User[]> {
  const supabase = await createClient();
  const { id, page = 1, pageSize = 10 } = params;

  let query = supabase.from("users").select("*");

  if (id) {
    query = query.eq("id", id);
  }

  // Apply pagination if needed
  const from = (page - 1) * pageSize;
  const to = from + pageSize - 1;
  query = query.range(from, to);

  const { data, error } = await query;

  if (error) {
    logger.error("Error fetching users:", error);
    throw new Error(`Failed to fetch users: ${error.message}`);
  }

  return data || [];
}

/**
 * Fetches a single user by ID
 */
export async function getUser(id: string): Promise<User> {
  const users = await fetchUsers({ id });

  if (!users || users.length === 0) {
    throw new Error(`User with ID ${id} not found`);
  }

  return users[0];
}

/**
 * Creates a new user or updates an existing one
 */
export async function createUser(input: CreateUserInput): Promise<User> {
  const supabase = await createClient();

  // First check if user already exists
  const { data: existingUser, error: checkError } = await supabase
    .from("users")
    .select("*")
    .eq("id", input.id)
    .maybeSingle();

  if (checkError) {
    logger.error("Error checking existing user:", checkError);
    throw new Error(`Failed to check existing user: ${checkError.message}`);
  }

  // If user exists, return it
  if (existingUser) {
    return existingUser;
  }

  // Otherwise create a new user
  const { data, error } = await supabase
    .from("users")
    .insert({
      id: input.id,
      name: input.name,
      avatar_url: input.avatar_url,
    })
    .select()
    .single();

  if (error) {
    logger.error("Error creating user:", error);
    throw new Error(`Failed to create user: ${error.message}`);
  }

  return data;
}

/**
 * Updates an existing user's profile
 *
 * @param input User data to update including id and profile fields
 * @returns Updated user data
 */
export async function updateUser(input: UpdateUserInput): Promise<User> {
  const supabase = await createClient();

  // Ensure the user exists before updating
  const existingUser = await getUser(input.id);

  if (!existingUser) {
    throw new Error(`User with ID ${input.id} not found`);
  }

  // Prepare update data (only include fields that are provided)
  const updateData: Partial<User> = {};

  if (input.name !== undefined) {
    updateData.name = input.name;
  }

  if (input.avatar_url !== undefined) {
    updateData.avatar_url = input.avatar_url;
  }

  if (input.phone_number !== undefined) {
    updateData.phone_number = input.phone_number;
  }

  // Only update if there are fields to update
  if (Object.keys(updateData).length === 0) {
    return existingUser;
  }

  // Update the user
  const { data, error } = await supabase
    .from("users")
    .update(updateData)
    .eq("id", input.id)
    .select()
    .single();

  if (error) {
    logger.error("Error updating user:", error);
    throw new Error(`Failed to update user: ${error.message}`);
  }

  return data;
}

/**
 * Gets the current authenticated user's profile
 *
 * @returns The current user's profile data
 */
export async function getCurrentUserProfile(): Promise<User | null> {
  const supabase = await createClient();

  // Get the current authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();

  if (authError || !user) {
    logger.error("Error getting authenticated user:", authError);
    return null;
  }

  try {
    // Get the user's profile data
    return await getUser(user.id);
  } catch (error) {
    logger.error("Error getting user profile:", error);
    return null;
  }
}
