import { createClient } from "@/lib/supabase/server/server";
import {
  GetProviderRoutesParams,
  CreateProviderRouteInput,
  UpdateProviderRouteInput,
  DeleteProviderRouteParams,
  type ProviderRoute,
} from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("ProviderRoutesService");

/**
 * Fetches routes for a specific provider with pagination support
 */
export async function fetchProviderRoutes(
  params: GetProviderRoutesParams,
): Promise<{
  data: ProviderRoute[];
  count: number;
  pagination?: {
    page: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}> {
  const supabase = await createClient();
  const {
    provider_id,
    page = 1,
    pageSize = 10,
    search,
    origin_country_id,
    destination_country_id,
    is_active,
  } = params;

  // Ensure page and pageSize are valid numbers
  const validPage = Math.max(1, page);
  const validPageSize = Math.min(100, Math.max(1, pageSize));

  // Log pagination and filter parameters for debugging
  logger.info(
    `Fetching provider routes with pagination and filters: page=${validPage}, pageSize=${validPageSize}, provider_id=${provider_id}, search=${search}, origin_country_id=${origin_country_id}, destination_country_id=${destination_country_id}, is_active=${is_active}`,
  );

  // Calculate range for pagination
  const startIndex = (validPage - 1) * validPageSize;
  const endIndex = startIndex + validPageSize - 1;

  // Start building the query for count
  let countQuery = supabase
    .from("provider_routes")
    .select("*", { count: "exact", head: true })
    .eq("provider_id", provider_id);

  // Apply filters to count query
  if (origin_country_id) {
    countQuery = countQuery.eq("origin_country_id", origin_country_id);
  }

  if (destination_country_id) {
    countQuery = countQuery.eq(
      "destination_country_id",
      destination_country_id,
    );
  }

  if (is_active !== undefined) {
    countQuery = countQuery.eq("is_active", is_active);
  }

  if (search) {
    countQuery = countQuery.ilike("notes", `%${search}%`);
  }

  // Get the total count for pagination metadata
  const { count, error: countError } = await countQuery;

  if (countError) {
    logger.error("Error counting provider routes:", countError);
    throw new Error(`Failed to count provider routes: ${countError.message}`);
  }

  // Start building the data query
  let dataQuery = supabase
    .from("provider_routes")
    .select(
      "*, origin_country:countries!provider_routes_origin_country_id_fkey(*), destination_country:countries!provider_routes_destination_country_id_fkey(*)",
    )
    .eq("provider_id", provider_id);

  // Apply filters to data query
  if (origin_country_id) {
    dataQuery = dataQuery.eq("origin_country_id", origin_country_id);
  }

  if (destination_country_id) {
    dataQuery = dataQuery.eq("destination_country_id", destination_country_id);
  }

  if (is_active !== undefined) {
    dataQuery = dataQuery.eq("is_active", is_active);
  }

  if (search) {
    dataQuery = dataQuery.ilike("notes", `%${search}%`);
  }

  // Apply pagination and ordering
  dataQuery = dataQuery
    .order("created_at", { ascending: false })
    .range(startIndex, endIndex);

  // Fetch the paginated data
  const { data, error } = await dataQuery;

  if (error) {
    logger.error("Error fetching provider routes:", error);
    throw new Error(`Failed to fetch provider routes: ${error.message}`);
  }

  // Calculate pagination metadata
  const totalItems = count || 0;
  const totalPages = Math.ceil(totalItems / validPageSize);
  const hasNextPage = validPage < totalPages;
  const hasPrevPage = validPage > 1;

  return {
    data: data || [],
    count: totalItems,
    pagination: {
      page: validPage,
      pageSize: validPageSize,
      totalItems,
      totalPages,
      hasNextPage,
      hasPrevPage,
    },
  };
}

/**
 * Creates a new provider route
 */
export async function addProviderRoute(
  input: CreateProviderRouteInput,
): Promise<ProviderRoute> {
  const supabase = await createClient();

  const timestamp = new Date().toISOString();
  const routeData = {
    provider_id: input.provider_id,
    origin_country_id: input.origin_country_id,
    destination_country_id: input.destination_country_id,
    is_active: input.is_active,
    notes: input.notes,
    created_at: timestamp,
    updated_at: timestamp,
  };

  const { data, error } = await supabase
    .from("provider_routes")
    .insert(routeData)
    .select()
    .single();

  if (error) {
    logger.error("Error creating provider route:", error);
    throw new Error(`Failed to create provider route: ${error.message}`);
  }

  if (!data) {
    throw new Error("Failed to create provider route: No data returned.");
  }

  return data;
}

/**
 * Updates an existing provider route
 */
export async function updateProviderRoute(
  input: UpdateProviderRouteInput,
): Promise<ProviderRoute> {
  const supabase = await createClient();
  const { id, ...updateFields } = input;

  // Check if route exists
  const { data: existingRoute, error: checkError } = await supabase
    .from("provider_routes")
    .select("id")
    .eq("id", id)
    .single();

  if (checkError || !existingRoute) {
    logger.error("Error checking provider route existence:", checkError);
    throw new Error(`Provider route with ID ${id} not found.`);
  }

  const updateData = {
    ...updateFields,
    updated_at: new Date().toISOString(),
  };

  // Remove undefined fields
  Object.keys(updateData).forEach(
    (key) =>
      updateData[key as keyof typeof updateData] === undefined &&
      delete updateData[key as keyof typeof updateData],
  );

  const { data, error } = await supabase
    .from("provider_routes")
    .update(updateData)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    logger.error("Error updating provider route:", error);
    throw new Error(`Failed to update provider route: ${error.message}`);
  }

  if (!data) {
    throw new Error(`Provider route with ID ${id} failed to update.`);
  }

  return data;
}

/**
 * Fetches all routes for a specific provider without pagination
 */
export async function fetchAllProviderRoutes(providerId: string): Promise<{
  data: ProviderRoute[];
  count: number;
}> {
  const supabase = await createClient();

  logger.info(`Fetching all provider routes for provider_id=${providerId}`);

  // Get the total count
  const { count, error: countError } = await supabase
    .from("provider_routes")
    .select("*", { count: "exact", head: true })
    .eq("provider_id", providerId);

  if (countError) {
    logger.error("Error counting provider routes:", countError);
    throw new Error(`Failed to count provider routes: ${countError.message}`);
  }

  // Fetch all data
  const { data, error } = await supabase
    .from("provider_routes")
    .select(
      "*, origin_country:countries!provider_routes_origin_country_id_fkey(*), destination_country:countries!provider_routes_destination_country_id_fkey(*)",
    )
    .eq("provider_id", providerId)
    .order("created_at", { ascending: false });

  if (error) {
    logger.error("Error fetching provider routes:", error);
    throw new Error(`Failed to fetch provider routes: ${error.message}`);
  }

  return {
    data: data || [],
    count: count || 0,
  };
}

/**
 * Deletes a provider route by ID
 */
export async function deleteProviderRoute(
  params: DeleteProviderRouteParams,
): Promise<{ message: string }> {
  const supabase = await createClient();
  const { id } = params;

  // Check if route exists
  const { data: existingRoute, error: checkError } = await supabase
    .from("provider_routes")
    .select("id")
    .eq("id", id)
    .single();

  if (checkError || !existingRoute) {
    logger.error("Error checking provider route existence:", checkError);
    throw new Error(`Provider route with ID ${id} not found.`);
  }

  // Delete the route
  const { error } = await supabase
    .from("provider_routes")
    .delete()
    .eq("id", id);

  if (error) {
    logger.error("Error deleting provider route:", error);
    throw new Error(`Failed to delete provider route: ${error.message}`);
  }

  return { message: `Provider route with ID ${id} successfully deleted.` };
}
