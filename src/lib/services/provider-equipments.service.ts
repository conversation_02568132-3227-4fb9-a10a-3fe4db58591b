import { createClient } from "@/lib/supabase/server/server";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("ProviderEquipmentsService");

interface FormattedEquipment {
  id: string;
  name: string;
  description: string | null;
  equipment_id: string;
}

/**
 * Get provider equipments
 */
export async function fetchProviderEquipments(
  providerId: string,
): Promise<FormattedEquipment[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from("provider_equipments")
    .select(
      `
      id,
      equipment_type:equipment_type_id (
        id,
        name,
        description
      )
    `,
    )
    .eq("provider_id", providerId);

  if (error) throw error;

  // Format data to match previous API response
  const formattedData = data.map((item: any) => ({
    id: item.equipment_type.id,
    name: item.equipment_type.name,
    description: item.equipment_type.description,
    equipment_id: item.id,
  }));

  return formattedData;
}

/**
 * Add provider equipments in bulk
 */
export async function addProviderEquipments(
  providerId: string,
  equipmentTypeIds: string[],
) {
  const supabase = await createClient();

  // First delete existing equipment to avoid duplicates
  const { error: deleteError } = await supabase
    .from("provider_equipments")
    .delete()
    .eq("provider_id", providerId);

  if (deleteError) {
    throw deleteError;
  }

  // If no equipment type IDs provided, we're just removing all equipment and we're done
  if (!equipmentTypeIds || equipmentTypeIds.length === 0) {
    return {
      added: 0,
      equipments: [],
    };
  }

  // Create array of equipment records to insert
  const records = equipmentTypeIds.map((equipmentTypeId) => ({
    provider_id: providerId,
    equipment_type_id: equipmentTypeId,
  }));

  // Then insert new equipment
  const { data, error } = await supabase
    .from("provider_equipments")
    .insert(records)
    .select();

  if (error) {
    throw error;
  }

  return {
    added: data.length,
    equipments: data,
  };
}

/**
 * Remove a provider equipment
 */
export async function removeProviderEquipment(params: {
  id: string;
  provider_id: string;
}) {
  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .from("provider_equipments")
      .delete()
      .eq("id", params.id)
      .eq("provider_id", params.provider_id)
      .select()
      .single();

    if (error) throw error;

    return data;
  } catch (error) {
    logger.error("Error removing provider equipment:", error);
    // If we're removing the last item, we might get a "No rows found" error
    // Return an empty object to indicate success in this case
    if (error instanceof Error && error.message.includes("No rows found")) {
      return {};
    }
    throw error;
  }
}
