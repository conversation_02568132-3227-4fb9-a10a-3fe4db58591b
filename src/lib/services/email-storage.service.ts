import { createAdminClient } from "@/lib/supabase/server/server";
import {
  WatchedEmailAccount,
  EmailMessage,
  EmailAttachment,
} from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";

// Create a logger instance for this module
const logger = createLogger("EmailStorageService");

/**
 * Email Storage Service
 *
 * This service handles database operations for email accounts, messages, and attachments.
 */
export class EmailStorageService {
  private adminSupabase: ReturnType<typeof createAdminClient>;

  constructor() {
    this.adminSupabase = createAdminClient();
  }

  /**
   * Create a new watched email account
   *
   * @param email The email address to watch
   * @param displayName The display name for the account
   * @param accessToken The OAuth access token
   * @param refreshToken The OAuth refresh token
   * @param tokenExpiry The expiry date of the access token
   * @param userId The ID of the user who created the account
   * @returns The created account
   */
  async createAccount(
    email: string,
    displayName: string,
    accessToken: string,
    refreshToken: string,
    tokenExpiry: Date,
    userId: string,
  ): Promise<WatchedEmailAccount> {
    try {
      logger.info(`Creating watched email account for ${email}`);

      // Use adminSupabase to bypass RLS policies
      const { data, error } = await this.adminSupabase
        .from("watched_email_accounts")
        .insert({
          email,
          display_name: displayName,
          access_token: accessToken,
          refresh_token: refreshToken,
          token_expiry: tokenExpiry.toISOString(),
          status: "active",
          created_by: userId,
        })
        .select()
        .single();

      if (error) {
        logger.error(`Error creating account: ${error.message}`);
        throw new Error(`Error creating account: ${error.message}`);
      }

      logger.info(`Account created successfully for ${email}`);
      return data;
    } catch (error: any) {
      logger.error(`Error creating account: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get all watched email accounts
   *
   * @returns All watched email accounts
   */
  async getAccounts(): Promise<WatchedEmailAccount[]> {
    try {
      const { data, error } = await this.adminSupabase
        .from("watched_email_accounts")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) {
        logger.error(`Error fetching accounts: ${error.message}`);
        throw new Error(`Error fetching accounts: ${error.message}`);
      }

      return data || [];
    } catch (error: any) {
      logger.error(`Error fetching accounts: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get a watched email account by ID
   *
   * @param accountId The ID of the account to get
   * @returns The account
   */
  async getAccountById(accountId: string): Promise<WatchedEmailAccount> {
    try {
      const { data, error } = await this.adminSupabase
        .from("watched_email_accounts")
        .select("*")
        .eq("id", accountId)
        .single();

      if (error) {
        logger.error(`Error fetching account: ${error.message}`);
        throw new Error(`Error fetching account: ${error.message}`);
      }

      return data;
    } catch (error: any) {
      logger.error(`Error fetching account: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update a watched email account
   *
   * @param accountId The ID of the account to update
   * @param updates The updates to apply
   * @returns The updated account
   */
  async updateAccount(
    accountId: string,
    updates: Partial<
      Omit<WatchedEmailAccount, "id" | "created_at" | "updated_at">
    >,
  ): Promise<WatchedEmailAccount> {
    try {
      logger.info(`Updating account ${accountId}`);

      const { data, error } = await this.adminSupabase
        .from("watched_email_accounts")
        .update(updates)
        .eq("id", accountId)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating account: ${error.message}`);
        throw new Error(`Error updating account: ${error.message}`);
      }

      logger.info(`Account ${accountId} updated successfully`);
      return data;
    } catch (error: any) {
      logger.error(`Error updating account: ${error.message}`);
      throw error;
    }
  }

  /**
   * Delete a watched email account
   *
   * @param accountId The ID of the account to delete
   * @returns True if the account was deleted
   */
  async deleteAccount(accountId: string): Promise<boolean> {
    try {
      logger.info(`Deleting account ${accountId}`);

      const { error } = await this.adminSupabase
        .from("watched_email_accounts")
        .delete()
        .eq("id", accountId);

      if (error) {
        logger.error(`Error deleting account: ${error.message}`);
        throw new Error(`Error deleting account: ${error.message}`);
      }

      logger.info(`Account ${accountId} deleted successfully`);
      return true;
    } catch (error: any) {
      logger.error(`Error deleting account: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get email messages for an account
   *
   * @param accountId The ID of the account to get messages for
   * @param limit The maximum number of messages to return
   * @param offset The offset for pagination
   * @returns The messages
   */
  async getMessages(
    accountId: string,
    limit: number = 50,
    offset: number = 0,
  ): Promise<EmailMessage[]> {
    try {
      const { data, error } = await this.adminSupabase
        .from("email_messages")
        .select("*")
        .eq("account_id", accountId)
        .order("date_received", { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        logger.error(`Error fetching messages: ${error.message}`);
        throw new Error(`Error fetching messages: ${error.message}`);
      }

      return data || [];
    } catch (error: any) {
      logger.error(`Error fetching messages: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get a single email message by ID
   *
   * @param messageId The ID of the message to get
   * @returns The message
   */
  async getMessageById(messageId: string): Promise<EmailMessage> {
    try {
      const { data, error } = await this.adminSupabase
        .from("email_messages")
        .select("*")
        .eq("id", messageId)
        .single();

      if (error) {
        logger.error(`Error fetching message: ${error.message}`);
        throw new Error(`Error fetching message: ${error.message}`);
      }

      return data;
    } catch (error: any) {
      logger.error(`Error fetching message: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get attachments for a message
   *
   * @param messageId The ID of the message to get attachments for
   * @returns The attachments
   */
  async getAttachments(messageId: string): Promise<EmailAttachment[]> {
    try {
      const { data, error } = await this.adminSupabase
        .from("email_attachments")
        .select("*")
        .eq("message_id", messageId);

      if (error) {
        logger.error(`Error fetching attachments: ${error.message}`);
        throw new Error(`Error fetching attachments: ${error.message}`);
      }

      return data || [];
    } catch (error: any) {
      logger.error(`Error fetching attachments: ${error.message}`);
      throw error;
    }
  }

  // The associateMessageWithRfq and associateMessageWithProvider methods have been removed
  // This functionality is now handled by database triggers

  /**
   * Get email messages with pagination, filtering, and attachment information
   *
   * @param params Parameters for filtering and pagination
   * @param params.page Page number (default: 1)
   * @param params.limit Number of items per page (default: 20)
   * @param params.accountId Filter by account ID (optional)
   * @param params.search Search in subject, from_email, or from_name (optional)
   * @returns Object containing messages, pagination info, and total count
   */
  async getMessagesWithPagination(params: {
    page?: number;
    limit?: number;
    accountId?: string | null;
    search?: string | null;
  }): Promise<{
    messages: (EmailMessage & { has_attachments: boolean })[];
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  }> {
    try {
      const { page = 1, limit = 20, accountId = null, search = null } = params;

      // Calculate offset for pagination
      const offset = (page - 1) * limit;

      // Build query
      let query = this.adminSupabase
        .from("email_messages")
        .select("*, email_attachments!inner(*)", { count: "exact" });

      // Apply filters
      if (accountId && accountId !== "all") {
        query = query.eq("account_id", accountId);
      }

      if (search) {
        query = query.or(
          `subject.ilike.%${search}%,from_email.ilike.%${search}%,from_name.ilike.%${search}%`,
        );
      }

      // Apply pagination
      query = query
        .order("date_received", { ascending: false })
        .range(offset, offset + limit - 1);

      // Execute query
      const { data: messages, error, count } = await query;

      if (error) {
        logger.error(
          `Error fetching messages with pagination: ${error.message}`,
        );
        throw new Error(
          `Error fetching messages with pagination: ${error.message}`,
        );
      }

      // Process messages to add has_attachments flag and remove attachment details
      const processedMessages = messages.map((message) => {
        const attachments = message.email_attachments || [];

        return {
          ...message,
          has_attachments: attachments.length > 0,
          email_attachments: undefined, // Remove the attachments from the response
        };
      });

      // Calculate total pages
      const totalPages = Math.ceil((count || 0) / limit);

      return {
        messages: processedMessages,
        page,
        limit,
        total: count || 0,
        totalPages,
      };
    } catch (error: any) {
      logger.error(`Error fetching messages with pagination: ${error.message}`);
      throw error;
    }
  }
}

// Export a singleton instance
export const emailStorageService = new EmailStorageService();
