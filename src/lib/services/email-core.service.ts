/**
 * Core email service with shared types and utilities
 * This file can be imported by both server-side and client-side code
 */

// Define types for email data
export interface EmailRecipient {
  email: string;
  name?: string;
}

export interface EmailData {
  to: EmailRecipient;
  subject: string;
  body: string;
  isHtml?: boolean;
  trackingId?: string;
  messageId?: string;
  replyTo?: string;
  attachments?: Array<{
    content: string;
    filename: string;
    type: string;
    disposition: "attachment" | "inline";
  }>;
}

export interface RFQEmailData extends EmailData {
  rfqDetails: {
    rfqNumber: string;
    origin: string;
    originCountry: string;
    destination: string;
    destinationCountry: string;
    cargoTypes?: string;
  };
}

// Define validation result type
export type EmailValidationResult<T> = {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    fieldErrors?: Record<string, string[]>;
  };
};

/**
 * Validates email data
 * @param data The email data to validate
 * @returns Validation result
 */
export function validateEmailData(data: any): EmailValidationResult<EmailData> {
  // Check for required fields
  const errors: Record<string, string[]> = {};

  if (!data.to || !data.to.email) {
    errors.to = ["Recipient email is required"];
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.to.email)) {
    errors.to = ["Invalid email format"];
  }

  if (!data.subject) {
    errors.subject = ["Subject is required"];
  }

  if (!data.body) {
    errors.body = ["Email body is required"];
  }

  // Return validation result
  if (Object.keys(errors).length > 0) {
    return {
      success: false,
      error: {
        message: "Email validation failed",
        fieldErrors: errors,
      },
    };
  }

  return {
    success: true,
    data: data as EmailData,
  };
}

/**
 * Validates RFQ email data
 * @param data The RFQ email data to validate
 * @returns Validation result
 */
export function validateRFQEmailData(
  data: any,
): EmailValidationResult<RFQEmailData> {
  // First validate the base email data
  const baseValidation = validateEmailData(data);

  if (!baseValidation.success) {
    return baseValidation as EmailValidationResult<RFQEmailData>;
  }

  // Check for RFQ-specific fields
  const errors: Record<string, string[]> = {};

  if (!data.rfqDetails) {
    errors.rfqDetails = ["RFQ details are required"];
  } else {
    if (!data.rfqDetails.rfqNumber) {
      errors["rfqDetails.rfqNumber"] = ["RFQ number is required"];
    }

    if (!data.rfqDetails.origin) {
      errors["rfqDetails.origin"] = ["Origin is required"];
    }

    if (!data.rfqDetails.originCountry) {
      errors["rfqDetails.originCountry"] = ["Origin country is required"];
    }

    if (!data.rfqDetails.destination) {
      errors["rfqDetails.destination"] = ["Destination is required"];
    }

    if (!data.rfqDetails.destinationCountry) {
      errors["rfqDetails.destinationCountry"] = [
        "Destination country is required",
      ];
    }
  }

  // Return validation result
  if (Object.keys(errors).length > 0) {
    return {
      success: false,
      error: {
        message: "RFQ email validation failed",
        fieldErrors: errors,
      },
    };
  }

  return {
    success: true,
    data: data as RFQEmailData,
  };
}

/**
 * Formats error messages for email operations
 * @param error The error object
 * @param defaultMessage The default message to use if no error message is available
 * @returns Formatted error message
 */
export function formatEmailError(
  error: unknown,
  defaultMessage: string = "An error occurred",
): string {
  if (error instanceof Error) {
    return error.message;
  }

  if (typeof error === "string") {
    return error;
  }

  return defaultMessage;
}
