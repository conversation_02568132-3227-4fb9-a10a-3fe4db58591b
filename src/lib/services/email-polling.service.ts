import { google, gmail_v1 } from "googleapis";
import { OAuth2Client } from "google-auth-library";
import { createAdminClient } from "@/lib/supabase/server/server";
import {
  WatchedEmailAccount,
  EmailMessage,
  EmailAttachment,
} from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";

// Helper function to parse boolean environment variables
const parseBoolEnv = (
  value: string | undefined,
  defaultValue: boolean,
): boolean => {
  if (value === undefined) return defaultValue;
  return value.toLowerCase() === "true" || value === "1";
};

// Helper function to parse comma-separated string into array
const parseArrayEnv = (
  value: string | undefined,
  defaultValue: string[],
): string[] => {
  if (value === undefined || value.trim() === "") return defaultValue;
  return value.split(",").map((item) => item.trim());
};

// Configuration options from environment variables with fallbacks
const EMAIL_POLLING_CONFIG = {
  // Control attachment processing via environment variable
  // Set EMAIL_DISABLE_ATTACHMENTS=true to disable all attachment processing
  // Set EMAIL_DISABLE_ATTACHMENTS=false to enable attachment processing
  disableAttachments: parseBoolEnv(process.env.EMAIL_DISABLE_ATTACHMENTS, true),

  // Skip image attachments to save storage space
  // Set EMAIL_SKIP_IMAGE_ATTACHMENTS=false to process image attachments
  // Only applies when disableAttachments is false
  skipImageAttachments: parseBoolEnv(
    process.env.EMAIL_SKIP_IMAGE_ATTACHMENTS,
    true,
  ),

  // List of MIME types to skip (in addition to images if skipImageAttachments is true)
  // Set EMAIL_SKIP_MIME_TYPES to a comma-separated list of MIME types to skip
  // Example: EMAIL_SKIP_MIME_TYPES=video/,audio/,application/pdf
  // Only applies when disableAttachments is false
  skipMimeTypes: parseArrayEnv(process.env.EMAIL_SKIP_MIME_TYPES, [
    // Default MIME types to skip (can be overridden by environment variable)
    // 'video/',       // Skip all video files
    // 'audio/',       // Skip all audio files
    // 'application/x-shockwave-flash', // Skip Flash files
    // 'application/vnd.ms-powerpoint', // Skip PowerPoint files
    // 'application/vnd.openxmlformats-officedocument.presentationml', // Skip PPTX files
  ]),
};

// Create a logger instance
const logger = createLogger("EmailPollingService");

/**
 * Email Polling Service
 *
 * This service handles polling for new emails using the Gmail API.
 * It replaces the watch-based approach with a polling approach that can be
 * triggered by Cloud Scheduler.
 */
export class EmailPollingService {
  private oauth2Client: OAuth2Client;
  private gmail: gmail_v1.Gmail;
  private supabase: ReturnType<typeof createAdminClient>;

  constructor() {
    // Initialize the OAuth2 client
    this.oauth2Client = new google.auth.OAuth2(
      process.env.GMAIL_CLIENT_ID,
      process.env.GMAIL_CLIENT_SECRET,
      process.env.GMAIL_OAUTH_REDIRECT_URI,
    );

    // Initialize the Gmail API client
    this.gmail = google.gmail({
      version: "v1",
      auth: this.oauth2Client,
    });

    // Initialize the Supabase client with admin privileges
    this.supabase = createAdminClient();

    // Log the current configuration
    logger.info("EmailPollingService initialized with configuration:", {
      disableAttachments: EMAIL_POLLING_CONFIG.disableAttachments,
      skipImageAttachments: EMAIL_POLLING_CONFIG.skipImageAttachments,
      skipMimeTypes: EMAIL_POLLING_CONFIG.skipMimeTypes,
    });
  }

  /**
   * Generate the OAuth URL for Gmail authentication
   *
   * @returns The OAuth URL to redirect the user to
   */
  generateAuthUrl(): string {
    const scopes = ["https://www.googleapis.com/auth/gmail.readonly"];

    return this.oauth2Client.generateAuthUrl({
      access_type: "offline",
      scope: scopes,
      prompt: "consent", // Force re-consent to get refresh token
      // Maximum expiry time (10 years)
      expiry_date: Math.floor(Date.now() / 1000) + 10 * 365 * 24 * 60 * 60,
    });
  }

  /**
   * Exchange the authorization code for access and refresh tokens
   *
   * @param code The authorization code from the OAuth callback
   * @returns The tokens
   */
  async getTokens(code: string) {
    const { tokens } = await this.oauth2Client.getToken(code);

    // Set maximum expiry time (10 years)
    tokens.expiry_date = Date.now() + 10 * 365 * 24 * 60 * 60 * 1000;

    return tokens;
  }

  /**
   * Poll for new emails for all active accounts
   *
   * @param options Options for polling
   * @param options.forceFullSync Force a full sync for all accounts
   * @returns Object with success status and number of processed messages
   */
  async pollAllAccounts(options: { forceFullSync?: boolean } = {}) {
    try {
      const { forceFullSync = false } = options;

      if (forceFullSync) {
        logger.info("Starting full sync for all accounts");
      } else {
        logger.info("Starting polling for all accounts");
      }

      // Get all active accounts
      const { data: accounts, error } = await this.supabase
        .from("watched_email_accounts")
        .select("*")
        .eq("status", "active");

      if (error) {
        throw new Error(`Error fetching accounts: ${error.message}`);
      }

      if (!accounts || accounts.length === 0) {
        logger.info("No active accounts found for polling");
        return { success: true, processed: 0 };
      }

      logger.info(`Found ${accounts.length} active accounts for polling`);

      // Poll each account
      let totalProcessed = 0;
      for (const account of accounts) {
        try {
          const result = await this.pollAccount(
            account.id,
            account.refresh_token,
            forceFullSync,
          );
          totalProcessed += result.processed;
        } catch (accountError) {
          logger.error(`Error polling account ${account.id}:`, accountError);
          // Continue with other accounts even if one fails
        }
      }

      return { success: true, processed: totalProcessed };
    } catch (error: any) {
      logger.error("Error in pollAllAccounts:", error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Poll for new emails for a specific account
   *
   * @param accountId The ID of the watched email account in our database
   * @param refreshToken The refresh token for the Gmail account
   * @param forceFullSync Force a full sync even if we have a last_sync_at timestamp
   * @returns Object with success status and number of processed messages
   */
  async pollAccount(
    accountId: string,
    refreshToken: string,
    forceFullSync: boolean = false,
  ) {
    try {
      logger.info(`Polling for account ${accountId}`);

      // Set the refresh token for this request with maximum expiry time
      this.oauth2Client.setCredentials({
        refresh_token: refreshToken,
        expiry_date: Date.now() + 10 * 365 * 24 * 60 * 60 * 1000, // 10 years expiry
      });

      let processed = 0;

      // Check if we need to do a full sync
      const { data: account } = await this.supabase
        .from("watched_email_accounts")
        .select("last_full_sync_at, last_sync_at")
        .eq("id", accountId)
        .single();

      // Check if we need to do a full sync
      // Only do a full sync if:
      // 1. forceFullSync is true, OR
      // 2. We don't have a last_full_sync_at timestamp
      const needsFullSync = forceFullSync || !account?.last_full_sync_at;

      if (needsFullSync) {
        // Perform a full sync of all messages
        logger.info(`Performing full sync for account ${accountId}`);
        const syncResult = await this.performFullSync(accountId);
        processed = syncResult.processed;

        // Update the account with the last sync times
        await this.supabase
          .from("watched_email_accounts")
          .update({
            last_sync_at: new Date().toISOString(),
            last_full_sync_at: new Date().toISOString(),
            status: "active",
            sync_error: null,
          })
          .eq("id", accountId);

        // Return early since we've done a full sync
        logger.info(
          `Full sync completed for account ${accountId}, processed ${processed} messages`,
        );
        return { success: true, processed };
      }

      // If we don't need a full sync, use date-based incremental polling
      if (account?.last_sync_at) {
        const lastSyncDate = new Date(account.last_sync_at);
        // Format date for Gmail API query: YYYY/MM/DD
        const formattedDate = lastSyncDate
          .toISOString()
          .split("T")[0]
          .replace(/-/g, "/");
        const query = `after:${formattedDate}`;

        logger.info(
          `Using date-based incremental polling for account ${accountId} with query: ${query}`,
        );

        try {
          // Get all message IDs that match the query
          const messageIds: string[] = [];
          let nextPageToken: string | undefined;

          // Fetch all message IDs using pagination
          do {
            const messagesResponse = await this.gmail.users.messages.list({
              userId: "me",
              maxResults: 500, // Maximum allowed by Gmail API
              pageToken: nextPageToken,
              q: query, // Use date-based query
            });

            // Get the next page token for pagination
            nextPageToken = messagesResponse.data.nextPageToken || undefined;

            // Add message IDs to our collection
            if (
              messagesResponse.data.messages &&
              messagesResponse.data.messages.length > 0
            ) {
              const ids = messagesResponse.data.messages.map(
                (m) => m.id as string,
              );
              messageIds.push(...ids);
              logger.info(
                `Retrieved ${messageIds.length} message IDs so far for account ${accountId} using date-based query`,
              );
            }
          } while (nextPageToken);

          logger.info(
            `Total message IDs retrieved with date-based query: ${messageIds.length} for account ${accountId}`,
          );

          if (messageIds.length > 0) {
            // Process messages in batches
            const batchSize = 50; // Process 50 messages at a time

            for (let i = 0; i < messageIds.length; i += batchSize) {
              const batchMessageIds = messageIds.slice(i, i + batchSize);
              logger.info(
                `Processing batch of messages ${i + 1}-${Math.min(i + batchSize, messageIds.length)} of ${messageIds.length} for account ${accountId}`,
              );

              try {
                // Collect message data for batch processing
                const messageDataArray = [];
                const attachmentsToProcess = [];

                // Create a batch of promises for parallel processing
                const batchPromises = batchMessageIds.map((messageId) =>
                  this.gmail.users.messages.get({
                    userId: "me",
                    id: messageId,
                  }),
                );

                // Execute batch in parallel
                const batchResults = await Promise.all(batchPromises);

                // Process the results (same logic as in performFullSync)
                for (const response of batchResults) {
                  try {
                    if (
                      !response.data ||
                      !response.data.id ||
                      !response.data.threadId
                    ) {
                      continue;
                    }

                    const message = response.data;

                    // Extract message data using the same logic as fetchMessageData
                    // Extract headers
                    const headers = message.payload?.headers || [];
                    const subject =
                      headers.find((h) => h.name?.toLowerCase() === "subject")
                        ?.value || "";
                    const from =
                      headers.find((h) => h.name?.toLowerCase() === "from")
                        ?.value || "";
                    const to =
                      headers.find((h) => h.name?.toLowerCase() === "to")
                        ?.value || "";
                    const cc =
                      headers.find((h) => h.name?.toLowerCase() === "cc")
                        ?.value || "";
                    const bcc =
                      headers.find((h) => h.name?.toLowerCase() === "bcc")
                        ?.value || "";
                    const replyTo =
                      headers.find((h) => h.name?.toLowerCase() === "reply-to")
                        ?.value || "";
                    const inReplyTo =
                      headers.find(
                        (h) => h.name?.toLowerCase() === "in-reply-to",
                      )?.value || "";
                    const date =
                      headers.find((h) => h.name?.toLowerCase() === "date")
                        ?.value || "";

                    // Parse the from field
                    const fromMatch = from.match(
                      /^(?:"?([^"]*)"?\s*)?<?([^>]*)>?$/,
                    );
                    const fromName = fromMatch ? fromMatch[1] : "";
                    const fromEmail = fromMatch ? fromMatch[2] : from;

                    // Parse recipients
                    const parseRecipients = (recipientString: string) => {
                      if (!recipientString) return [];

                      return recipientString.split(",").map((recipient) => {
                        const match = recipient
                          .trim()
                          .match(/^(?:"?([^"]*)"?\s*)?<?([^>]*)>?$/);
                        return {
                          name: match ? match[1] : "",
                          email: match ? match[2] : recipient.trim(),
                        };
                      });
                    };

                    const toRecipients = parseRecipients(to);
                    const ccRecipients = parseRecipients(cc);
                    const bccRecipients = parseRecipients(bcc);

                    // Extract body parts
                    let bodyHtml = "";
                    let bodyText = "";

                    const extractBody = (part: gmail_v1.Schema$MessagePart) => {
                      if (part.mimeType === "text/html" && part.body?.data) {
                        bodyHtml = Buffer.from(
                          part.body.data,
                          "base64",
                        ).toString("utf-8");
                      } else if (
                        part.mimeType === "text/plain" &&
                        part.body?.data
                      ) {
                        bodyText = Buffer.from(
                          part.body.data,
                          "base64",
                        ).toString("utf-8");
                      }

                      if (part.parts) {
                        for (const subPart of part.parts) {
                          extractBody(subPart);
                        }
                      }
                    };

                    if (message.payload) {
                      extractBody(message.payload);
                    }

                    // Create the message data object
                    const messageData = {
                      account_id: accountId,
                      gmail_id: message.id as string,
                      gmail_thread_id: message.threadId as string,
                      subject,
                      snippet: message.snippet || "",
                      body_html: bodyHtml,
                      body_text: bodyText,
                      from_email: fromEmail,
                      from_name: fromName,
                      to_recipients: toRecipients,
                      cc_recipients: ccRecipients,
                      bcc_recipients: bccRecipients,
                      reply_to: replyTo,
                      in_reply_to: inReplyTo,
                      date_received: new Date(date).toISOString(),
                      labels: message.labelIds || [],
                      is_read: !message.labelIds?.includes("UNREAD"),
                      is_starred:
                        message.labelIds?.includes("STARRED") || false,
                      is_important:
                        message.labelIds?.includes("IMPORTANT") || false,
                      is_trash: message.labelIds?.includes("TRASH") || false,
                      category: null,
                    };

                    // Add to batch
                    messageDataArray.push(messageData);

                    // Store attachment info for later processing
                    // Skip if attachments are disabled
                    if (
                      !EMAIL_POLLING_CONFIG.disableAttachments &&
                      message.payload?.parts
                    ) {
                      attachmentsToProcess.push({
                        messageId: message.id as string,
                        parts: message.payload.parts,
                      });
                    }
                  } catch (error) {
                    logger.error(
                      `Error processing message in batch for account ${accountId}:`,
                      error,
                    );
                    // Continue with other messages even if one fails
                  }
                }

                // Batch save messages to the database
                if (messageDataArray.length > 0) {
                  try {
                    const { data, error } = await this.supabase
                      .from("email_messages")
                      .upsert(messageDataArray, {
                        onConflict: "account_id,gmail_id",
                      })
                      .select("id, gmail_id");

                    if (error) {
                      throw new Error(
                        `Error batch saving messages: ${error.message}`,
                      );
                    }

                    // Create a map of Gmail IDs to database IDs
                    const messageIdMap = new Map();
                    if (data) {
                      data.forEach((msg: { gmail_id: string; id: string }) => {
                        messageIdMap.set(msg.gmail_id, msg.id);
                      });
                    }

                    // Process attachments for messages that have them
                    // Skip if attachments are disabled
                    if (!EMAIL_POLLING_CONFIG.disableAttachments) {
                      for (const item of attachmentsToProcess) {
                        const dbMessageId = messageIdMap.get(item.messageId);
                        if (dbMessageId) {
                          await this.processAttachmentsBatch(
                            dbMessageId,
                            item.parts,
                          );
                        }
                      }
                    } else {
                      logger.info(
                        "Attachment processing is temporarily disabled",
                      );
                    }

                    processed += messageDataArray.length;
                    logger.info(
                      `Saved ${messageDataArray.length} messages in current batch, total processed: ${processed}`,
                    );
                  } catch (saveError) {
                    logger.error(
                      `Error saving batch of messages for account ${accountId}:`,
                      saveError,
                    );
                  }
                }

                // Add a small delay between batches to avoid rate limiting
                await new Promise((resolve) => setTimeout(resolve, 500));
              } catch (batchError) {
                logger.error(
                  `Error processing batch of messages for account ${accountId}:`,
                  batchError,
                );
                // Continue with other batches even if one fails
              }
            }
          } else {
            logger.info(
              `No new messages found for account ${accountId} using date-based query`,
            );
          }

          // Update the account with the last sync time
          await this.supabase
            .from("watched_email_accounts")
            .update({
              last_sync_at: new Date().toISOString(),
              status: "active",
              sync_error: null,
            })
            .eq("id", accountId);

          logger.info(
            `Date-based incremental poll completed for account ${accountId}, processed ${processed} messages`,
          );
        } catch (error: any) {
          logger.error(
            `Error in date-based incremental polling for account ${accountId}:`,
            error,
          );

          // Fall back to full sync if date-based polling fails
          logger.warn(
            `Falling back to full sync for account ${accountId} after date-based polling failure`,
          );

          // Perform a full sync instead
          const syncResult = await this.performFullSync(accountId);
          processed = syncResult.processed;

          // Update the account with the last full sync time
          await this.supabase
            .from("watched_email_accounts")
            .update({
              last_sync_at: new Date().toISOString(),
              last_full_sync_at: new Date().toISOString(),
              status: "active",
              sync_error: null,
            })
            .eq("id", accountId);

          logger.info(
            `Full sync completed for account ${accountId} after date-based polling failure, processed ${processed} messages`,
          );
        }
      } else {
        // This should never happen since we check for last_full_sync_at above
        logger.warn(
          `No last_sync_at timestamp found for account ${accountId}, falling back to full sync`,
        );

        // Perform a full sync instead
        const syncResult = await this.performFullSync(accountId);
        processed = syncResult.processed;

        // Update the account with the last full sync time
        await this.supabase
          .from("watched_email_accounts")
          .update({
            last_sync_at: new Date().toISOString(),
            last_full_sync_at: new Date().toISOString(),
            status: "active",
            sync_error: null,
          })
          .eq("id", accountId);

        logger.info(
          `Full sync completed for account ${accountId} after missing timestamp, processed ${processed} messages`,
        );
      }

      logger.info(`Processed ${processed} messages for account ${accountId}`);
      return { success: true, processed };
    } catch (error: any) {
      logger.error(`Error polling account ${accountId}:`, error);

      // Determine the appropriate error message and status
      let errorMessage = error.message || "Unknown error during polling";
      let accountStatus = "error";

      // Check for specific Gmail API errors
      if (errorMessage.includes("Requested entity was not found")) {
        errorMessage =
          "Gmail account not found or access revoked. Please reconnect the account.";
        accountStatus = "revoked";
      } else if (errorMessage.includes("invalid_grant")) {
        errorMessage =
          "Invalid or expired refresh token. Please reconnect the account.";
        accountStatus = "revoked";
      }

      // Update account status to error
      await this.supabase
        .from("watched_email_accounts")
        .update({
          status: accountStatus,
          sync_error: errorMessage,
        })
        .eq("id", accountId);

      return { success: false, error: errorMessage, processed: 0 };
    }
  }

  /**
   * Perform a full sync of all messages for an account
   *
   * @param accountId The ID of the watched email account in our database
   * @returns Object with success status and number of processed messages
   */
  private async performFullSync(
    accountId: string,
  ): Promise<{ processed: number }> {
    try {
      logger.info(`Starting full sync for account ${accountId}`);

      let processed = 0;

      // Step 1: Collect all message IDs first
      const allMessageIds: string[] = [];
      let nextPageToken: string | undefined;

      // Fetch all message IDs using pagination
      do {
        const messagesResponse = await this.gmail.users.messages.list({
          userId: "me",
          maxResults: 500, // Maximum allowed by Gmail API
          pageToken: nextPageToken,
        });

        // Get the next page token for pagination
        nextPageToken = messagesResponse.data.nextPageToken || undefined;

        // Add message IDs to our collection
        if (
          messagesResponse.data.messages &&
          messagesResponse.data.messages.length > 0
        ) {
          const messageIds = messagesResponse.data.messages.map(
            (m) => m.id as string,
          );
          allMessageIds.push(...messageIds);
          logger.info(
            `Retrieved ${allMessageIds.length} message IDs so far for account ${accountId}`,
          );
        }
      } while (nextPageToken);

      logger.info(
        `Total message IDs retrieved: ${allMessageIds.length} for account ${accountId}`,
      );

      // Step 2: Process messages in batches
      const batchSize = 50; // Process 50 messages at a time

      for (let i = 0; i < allMessageIds.length; i += batchSize) {
        const batchMessageIds = allMessageIds.slice(i, i + batchSize);
        logger.info(
          `Processing batch of messages ${i + 1}-${Math.min(i + batchSize, allMessageIds.length)} of ${allMessageIds.length} for account ${accountId}`,
        );

        try {
          // Collect message data for batch processing
          const messageDataArray = [];
          const attachmentsToProcess = [];

          // Create a batch of promises for parallel processing
          // Don't specify format: 'full' to work with metadata scope
          const batchPromises = batchMessageIds.map((messageId) =>
            this.gmail.users.messages.get({
              userId: "me",
              id: messageId,
              // Removed format: 'full' to work with metadata scope
            }),
          );

          // Execute batch in parallel with a reasonable concurrency
          const batchResults = await Promise.all(batchPromises);

          // Process the results
          for (const response of batchResults) {
            try {
              if (
                !response.data ||
                !response.data.id ||
                !response.data.threadId
              ) {
                continue;
              }

              const message = response.data;

              // Extract message data using the same logic as fetchMessageData
              // Extract headers
              const headers = message.payload?.headers || [];
              const subject =
                headers.find((h) => h.name?.toLowerCase() === "subject")
                  ?.value || "";
              const from =
                headers.find((h) => h.name?.toLowerCase() === "from")?.value ||
                "";
              const to =
                headers.find((h) => h.name?.toLowerCase() === "to")?.value ||
                "";
              const cc =
                headers.find((h) => h.name?.toLowerCase() === "cc")?.value ||
                "";
              const bcc =
                headers.find((h) => h.name?.toLowerCase() === "bcc")?.value ||
                "";
              const replyTo =
                headers.find((h) => h.name?.toLowerCase() === "reply-to")
                  ?.value || "";
              const inReplyTo =
                headers.find((h) => h.name?.toLowerCase() === "in-reply-to")
                  ?.value || "";
              const date =
                headers.find((h) => h.name?.toLowerCase() === "date")?.value ||
                "";

              // Parse the from field
              const fromMatch = from.match(/^(?:"?([^"]*)"?\s*)?<?([^>]*)>?$/);
              const fromName = fromMatch ? fromMatch[1] : "";
              const fromEmail = fromMatch ? fromMatch[2] : from;

              // Parse recipients
              const parseRecipients = (recipientString: string) => {
                if (!recipientString) return [];

                return recipientString.split(",").map((recipient) => {
                  const match = recipient
                    .trim()
                    .match(/^(?:"?([^"]*)"?\s*)?<?([^>]*)>?$/);
                  return {
                    name: match ? match[1] : "",
                    email: match ? match[2] : recipient.trim(),
                  };
                });
              };

              const toRecipients = parseRecipients(to);
              const ccRecipients = parseRecipients(cc);
              const bccRecipients = parseRecipients(bcc);

              // Extract body parts
              let bodyHtml = "";
              let bodyText = "";

              const extractBody = (part: gmail_v1.Schema$MessagePart) => {
                if (part.mimeType === "text/html" && part.body?.data) {
                  bodyHtml = Buffer.from(part.body.data, "base64").toString(
                    "utf-8",
                  );
                } else if (part.mimeType === "text/plain" && part.body?.data) {
                  bodyText = Buffer.from(part.body.data, "base64").toString(
                    "utf-8",
                  );
                }

                if (part.parts) {
                  for (const subPart of part.parts) {
                    extractBody(subPart);
                  }
                }
              };

              if (message.payload) {
                extractBody(message.payload);
              }

              // Create the message data object
              const messageData = {
                account_id: accountId,
                gmail_id: message.id as string,
                gmail_thread_id: message.threadId as string,
                subject,
                snippet: message.snippet || "",
                body_html: bodyHtml,
                body_text: bodyText,
                from_email: fromEmail,
                from_name: fromName,
                to_recipients: toRecipients,
                cc_recipients: ccRecipients,
                bcc_recipients: bccRecipients,
                reply_to: replyTo,
                in_reply_to: inReplyTo,
                date_received: new Date(date).toISOString(),
                labels: message.labelIds || [],
                is_read: !message.labelIds?.includes("UNREAD"),
                is_starred: message.labelIds?.includes("STARRED") || false,
                is_important: message.labelIds?.includes("IMPORTANT") || false,
                is_trash: message.labelIds?.includes("TRASH") || false,
                category: null,
              };

              // Add to batch
              messageDataArray.push(messageData);

              // Store attachment info for later processing
              // Skip if attachments are disabled
              if (
                !EMAIL_POLLING_CONFIG.disableAttachments &&
                message.payload?.parts
              ) {
                attachmentsToProcess.push({
                  messageId: message.id as string,
                  parts: message.payload.parts,
                });
              }

              // No need to track history ID anymore
            } catch (error) {
              logger.error(
                `Error processing message in batch for account ${accountId}:`,
                error,
              );
              // Continue with other messages even if one fails
            }
          }

          // Batch save messages to the database
          if (messageDataArray.length > 0) {
            try {
              const { data, error } = await this.supabase
                .from("email_messages")
                .upsert(messageDataArray, { onConflict: "account_id,gmail_id" })
                .select("id, gmail_id");

              if (error) {
                throw new Error(
                  `Error batch saving messages: ${error.message}`,
                );
              }

              // Create a map of Gmail IDs to database IDs
              const messageIdMap = new Map();
              if (data) {
                data.forEach((msg: { gmail_id: string; id: string }) => {
                  messageIdMap.set(msg.gmail_id, msg.id);
                });
              }

              // Process attachments for messages that have them
              // Skip if attachments are disabled
              if (!EMAIL_POLLING_CONFIG.disableAttachments) {
                for (const item of attachmentsToProcess) {
                  const dbMessageId = messageIdMap.get(item.messageId);
                  if (dbMessageId) {
                    await this.processAttachmentsBatch(dbMessageId, item.parts);
                  }
                }
              } else {
                logger.info("Attachment processing is temporarily disabled");
              }

              // RFQ association is now handled automatically by database triggers
              // No need to call associateMessageWithRfq anymore

              processed += messageDataArray.length;
              logger.info(
                `Saved ${messageDataArray.length} messages in current batch, total processed: ${processed}`,
              );
            } catch (saveError) {
              logger.error(
                `Error saving batch of messages for account ${accountId}:`,
                saveError,
              );
            }
          }

          // Add a small delay between batches to avoid rate limiting
          await new Promise((resolve) => setTimeout(resolve, 500));
        } catch (batchError) {
          logger.error(
            `Error processing batch of messages for account ${accountId}:`,
            batchError,
          );
          // Continue with other batches even if one fails
        }
      }

      logger.info(
        `Full sync completed for account ${accountId}, processed ${processed} messages`,
      );
      return { processed };
    } catch (error: any) {
      logger.error(
        `Error performing full sync for account ${accountId}:`,
        error,
      );

      // Check for specific Gmail API errors and enhance the error message
      if (
        error.message &&
        error.message.includes("Requested entity was not found")
      ) {
        error.message =
          "Gmail account not found or access revoked. Please reconnect the account.";
      } else if (error.message && error.message.includes("invalid_grant")) {
        error.message =
          "Invalid or expired refresh token. Please reconnect the account.";
      }

      throw error; // Let the calling function handle the error
    }
  }

  // The processNewMessages method has been removed as part of the transition from history ID-based to date-based polling

  /**
   * Process attachments in batch
   *
   * @param messageId The ID of the email message in our database
   * @param parts The message parts from Gmail API
   */
  private async processAttachmentsBatch(
    messageId: string,
    parts: gmail_v1.Schema$MessagePart[],
  ) {
    // Skip processing if attachments are disabled
    if (EMAIL_POLLING_CONFIG.disableAttachments) {
      logger.debug(
        `Skipping attachment processing for message ${messageId} (disabled in config)`,
      );
      return;
    }

    const attachmentsToSave: Array<Omit<EmailAttachment, "id" | "created_at">> =
      [];

    // Collect all attachments first
    const collectAttachments = (
      part: gmail_v1.Schema$MessagePart,
      attachments: Array<Omit<EmailAttachment, "id" | "created_at">>,
    ) => {
      if (part.filename && part.body?.attachmentId) {
        // Check if we should skip this attachment based on MIME type
        const mimeType = part.mimeType || "";
        const shouldSkip =
          // Skip images if configured to do so
          (EMAIL_POLLING_CONFIG.skipImageAttachments &&
            mimeType.startsWith("image/")) ||
          // Skip any MIME types in the skipMimeTypes list
          EMAIL_POLLING_CONFIG.skipMimeTypes.some((type) =>
            mimeType.startsWith(type),
          );

        if (shouldSkip) {
          logger.debug(`Skipping attachment: ${part.filename} (${mimeType})`);
          // Skip this attachment and continue processing others
        } else {
          // Log very long filenames or attachment IDs for debugging
          if (part.filename.length > 200) {
            logger.debug(
              `Long filename (${part.filename.length} chars) for attachment: ${part.filename.substring(0, 50)}...`,
            );
          }
          if (part.body.attachmentId.length > 200) {
            logger.debug(
              `Long attachment ID (${part.body.attachmentId.length} chars): ${part.body.attachmentId.substring(0, 50)}...`,
            );
          }

          attachments.push({
            message_id: messageId,
            gmail_attachment_id: part.body.attachmentId,
            filename: part.filename,
            content_type: mimeType,
            size: part.body.size || 0,
            content: null,
            storage_path: null,
            updated_at: new Date().toISOString(),
          });
        }
      }

      // Recursively process nested parts
      if (part.parts) {
        for (const subPart of part.parts) {
          collectAttachments(subPart, attachments);
        }
      }
    };

    // Collect all attachments
    for (const part of parts) {
      collectAttachments(part, attachmentsToSave);
    }

    // Batch save attachments if there are any
    if (attachmentsToSave.length > 0) {
      try {
        logger.debug(
          `Batch saving ${attachmentsToSave.length} attachments for message ${messageId}`,
        );

        const { error } = await this.supabase
          .from("email_attachments")
          .upsert(attachmentsToSave, {
            onConflict: "message_id,gmail_attachment_id",
          });

        if (error) {
          throw new Error(`Error batch saving attachments: ${error.message}`);
        }

        logger.debug(
          `Successfully saved ${attachmentsToSave.length} attachments for message ${messageId}`,
        );
      } catch (error: any) {
        logger.error(
          `Error batch saving attachments for message ${messageId}: ${error.message}`,
        );
      }
    }
  }

  // The associateMessageWithRfq method has been removed
  // This functionality is now handled by database triggers
}

// Export a singleton instance
export const emailPollingService = new EmailPollingService();
