# Service Layer Architecture

This directory contains the service layer for the Steelflow application. The service layer is responsible for encapsulating all business logic and database access, providing a clean separation of concerns.

## Architecture Overview

```
+---------------------+        +--------------------------+        +-------------------------------+
| External Consumer   | ----> | API Route                | --+--> | Service Layer                 | ----> | Database (Supabase)  |
| (External Systems)  |       | (HTTP, Validation)       |   |    | (Core Business Logic)         |       |                      |
+---------------------+       +--------------------------+   |    +-------------------------------+       +----------------------+
                                                           |                 ^
                                                           |                 |
+---------------------+       +--------------------------+   |    +-------------------------------+
| UI Components       | ----> | Server Actions           | --+--> | Schemas (Zod)                 |
| (React/Next.js)     |       | (Form Handling, Cache)   |        | (Validation Rules)            |
+---------------------+       +--------------------------+        +-------------------------------+
```

## Key Components

1. **Service Layer** (`src/lib/services/`): Contains the core business logic that interacts with the database.
2. **Schemas** (`src/lib/schemas/`): Defines data shapes and validation rules using Zod.
3. **API Routes** (`src/app/api/*/route.ts`): Handles external HTTP requests/responses.
4. **Server Actions** (`src/lib/actions/`): Processes UI interactions and form submissions.
5. **Utility Functions** (`src/lib/utils/`): Provides shared functionality for validation, error handling, and response formatting.

## Best Practices

### DO:

✅ **Always use the service layer for database access** - Never access Supabase directly from API routes or server actions
✅ **Keep service functions focused and atomic** - Each function should do one thing well
✅ **Use proper error handling** - Services should throw meaningful errors
✅ **Always validate inputs** - Use Zod schemas for all external inputs
✅ **Use TypeScript types** - Leverage Supabase-generated types

### DON'T:

❌ **Don't duplicate business logic** - If logic exists in a service, don't reimplement it elsewhere
❌ **Don't bypass the architecture** - Avoid shortcuts that create technical debt
❌ **Don't mix concerns** - Keep HTTP handling in API routes, UI state in actions, and business logic in services

## Service File Structure

Each service file should follow this structure:

```typescript
import { createAdminClient } from "@/lib/supabase/server/server";
import { Database } from "@/types/supabase";
import {} from // Import relevant schemas and types
"@/lib/schemas/entity-name";

// Define entity type using Supabase generated types
type EntityName = Database["public"]["Tables"]["table_name"]["Row"];

// Define params and input types if not imported from schemas

/**
 * Fetches entities with optional filtering and pagination
 */
export async function fetchEntities(params): Promise<EntityName[]> {
  const supabase = createAdminClient();
  // Implementation...
}

/**
 * Creates a new entity
 */
export async function addEntity(input): Promise<EntityName> {
  const supabase = createAdminClient();
  // Implementation...
}

/**
 * Updates an existing entity
 */
export async function updateEntity(input): Promise<EntityName> {
  const supabase = createAdminClient();
  // Implementation...
}

/**
 * Deletes an entity
 */
export async function deleteEntity(id): Promise<void> {
  const supabase = createAdminClient();
  // Implementation...
}
```

## Error Handling

Services should throw errors with meaningful messages that can be caught and handled by the calling code:

```typescript
if (error) {
  console.error("Error fetching entity:", error);
  throw new Error(`Failed to fetch entity: ${error.message}`);
}
```

API routes and server actions should use utility functions to handle validation, errors, and responses:

```typescript
// In API routes
try {
  const [isValid, validatedData, errorResponse] = validateWithSchema(
    EntityParamsSchema,
    params,
  );

  if (!isValid) {
    return errorResponse;
  }

  const result = await fetchEntity(validatedData);
  return formatApiResponse(result);
} catch (error) {
  return handleApiError(error, "Failed to fetch entity");
}

// In server actions
try {
  const result = await fetchEntity(params);
  return createSuccessResponse(result);
} catch (error) {
  return handleActionError(error, "Failed to fetch entity");
}
```

## Adding a New Feature

When adding a new feature:

1. Define schemas in `src/lib/schemas/`
2. Implement service functions in `src/lib/services/`
3. Create API routes in `src/app/api/` that use the service and utility functions
4. Create server actions in `src/lib/actions/` that use the service and utility functions
5. Build UI components that use the server actions

By following this architecture and using the utility functions, we ensure that our codebase remains maintainable, testable, and scalable, with consistent behavior across all components.

## Email Services

The application uses two separate email service modules to handle email functionality:

### 1. `email.service.ts` (Server-Side Only)

This service is designed to be used only on the server side. It directly initializes and uses the SendGrid SDK.

- **Usage**: Import and use this service in:

  - Server Components
  - API Routes
  - Server Actions
  - Other server-side services

- **Features**:

  - Direct SendGrid integration
  - Email template rendering
  - Bulk email sending
  - Sandbox mode for testing

- **Environment Variables**:
  - `SENDGRID_API_KEY`: Your SendGrid API key
  - `SENDGRID_FROM_EMAIL`: Default sender email
  - `SENDGRID_FROM_NAME`: Default sender name
  - `SENDGRID_SANDBOX_MODE`: Enable sandbox mode for testing
  - `SENDGRID_SANDBOX_TO_EMAIL`: Redirect all emails to this address in sandbox mode

### 2. `email-client.service.ts` (Client-Safe)

This service is designed to be used in client components. It does not directly use the SendGrid SDK but instead makes API calls to the `/api/email/send` endpoint.

- **Usage**: Import and use this service in:

  - Client Components
  - Event handlers
  - Client-side logic

- **Features**:
  - API-based email sending
  - User-friendly error messages
  - Type compatibility with the server-side service

### Best Practices for Email Services

1. **Server vs. Client**: Always use the appropriate service based on where your code runs:

   - Server-side code: Use `email.service.ts`
   - Client-side code: Use `email-client.service.ts`

2. **Error Handling**: Both services include error handling, but you should add additional error handling in your application code to provide appropriate feedback to users.

3. **Environment Variables**: Ensure all required environment variables are set in your deployment environment.

4. **Testing**: Use the sandbox mode for testing email functionality without sending actual emails.
