"use server";

import { createClient } from "@/lib/supabase/server/server";
import { User, Session } from "@supabase/supabase-js";
import { createLogger } from "@/lib/utils/logger/logger";
import {
  checkUserRole,
  checkUserAllRoles,
  getUserRoles,
} from "@/lib/services/role.service";

// Create a logger instance for this module
const logger = createLogger("AuthService");

// Type for login response
export type LoginResponse = {
  success: boolean;
  error?: string;
  user?: User;
};

// Type for logout response
export type LogoutResponse = {
  success: boolean;
  error?: string;
};

/**
 * Gets the current authenticated user
 * @returns The authenticated user or null if not authenticated
 */
export async function getCurrentUser(): Promise<User | null> {
  try {
    // Create a Supabase client (handles cookies internally in the new implementation)
    const supabase = await createClient();

    // IMPORTANT: Always use getUser() instead of getSession() for security
    // getUser() sends a request to the Supabase Auth server every time to revalidate the Auth token
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error) {
      logger.error("Error getting current user:", error.message);
      return null;
    }

    return user;
  } catch (error) {
    logger.error("Unexpected error in getCurrentUser:", error);
    return null;
  }
}

/**
 * Gets the current authenticated user ID
 * @returns The authenticated user ID or null if not authenticated
 */
export async function getCurrentUserId(): Promise<string | null> {
  const user = await getCurrentUser();
  return user?.id || null;
}

/**
 * Exchanges an auth code for a session
 * This is used in OAuth flows and email confirmation
 *
 * @param code The authorization code to exchange
 * @returns The session or null if exchange failed
 */
export async function exchangeCodeForSession(
  code: string,
): Promise<Session | null> {
  if (!code) {
    logger.error("No authentication code provided");
    return null;
  }

  try {
    // Create a Supabase client (handles cookies internally in the new implementation)
    const supabase = await createClient();

    // Exchange the code for a session
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.exchangeCodeForSession(code);

    if (sessionError) {
      logger.error("Failed to exchange code for session:", sessionError);
      return null;
    }

    if (!session) {
      logger.error("No session returned from exchangeCodeForSession");
      return null;
    }

    // After exchanging the code for a session, get the user to ensure the session is valid
    // This is important for security and to ensure the session is properly refreshed
    const { error: userError } = await supabase.auth.getUser();

    if (userError) {
      logger.error(
        "Failed to get user after exchanging code for session:",
        userError,
      );
      return null;
    }

    logger.debug("Session exchanged successfully, user ID:", session.user?.id);
    return session;
  } catch (error) {
    logger.error("Error exchanging code for session:", error);
    return null;
  }
}

/**
 * Checks if the current user has any of the specified roles
 * @param roles Array of role names to check against
 * @returns True if the user has any of the specified roles, false otherwise
 */
export async function checkUserHasRole(roles: string[]): Promise<boolean> {
  const userId = await getCurrentUserId();
  if (!userId) {
    logger.warn("No authenticated user found when checking roles");
    return false;
  }

  return checkUserRole(userId, roles);
}

/**
 * Checks if the current user has all of the specified roles
 * @param roles Array of role names to check against
 * @returns True if the user has all of the specified roles, false otherwise
 */
export async function checkUserHasAllRoles(roles: string[]): Promise<boolean> {
  const userId = await getCurrentUserId();
  if (!userId) {
    logger.warn("No authenticated user found when checking roles");
    return false;
  }

  return checkUserAllRoles(userId, roles);
}

/**
 * Gets all roles for the current user
 * @returns Array of role names
 */
export async function getCurrentUserRoles(): Promise<string[]> {
  const userId = await getCurrentUserId();
  if (!userId) {
    logger.warn("No authenticated user found when getting roles");
    return [];
  }

  return getUserRoles(userId);
}

/**
 * Checks if the current user has admin role
 * @returns True if the user has admin role, false otherwise
 */
export async function isAdmin(): Promise<boolean> {
  return checkUserHasRole(["admin"]);
}

/**
 * Server-side function to sign in a user with email and password
 * @param email User's email
 * @param password User's password
 * @returns Login response with success status and user or error
 */
export async function signInWithPassword(
  email: string,
  password: string,
): Promise<LoginResponse> {
  try {
    logger.debug(`Attempting to sign in user with email: ${email}`);
    const supabase = await createClient();

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      logger.error("Error signing in with password:", error.message);
      return {
        success: false,
        error: error.message,
      };
    }

    logger.info(`User signed in successfully: ${data.user.id}`);
    return {
      success: true,
      user: data.user,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Unexpected error in signInWithPassword:", errorMessage);
    return {
      success: false,
      error: "An unexpected error occurred during sign in",
    };
  }
}

/**
 * Server-side function to sign out the current user
 * @returns Logout response with success status or error
 */
export async function signOut(): Promise<LogoutResponse> {
  try {
    logger.debug("Attempting to sign out user");
    const supabase = await createClient();

    const { error } = await supabase.auth.signOut();

    if (error) {
      logger.error("Error signing out:", error.message);
      return {
        success: false,
        error: error.message,
      };
    }

    logger.info("User signed out successfully");
    return {
      success: true,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Unexpected error in signOut:", errorMessage);
    return {
      success: false,
      error: "An unexpected error occurred during sign out",
    };
  }
}

/**
 * Server-side function to get the current user with profile data
 * @returns The user with profile data or null if not authenticated
 */
export async function getCurrentUserWithProfile(): Promise<any> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return null;
    }

    const supabase = await createClient();
    const { data, error } = await supabase
      .from("users")
      .select("*")
      .eq("id", user.id)
      .maybeSingle();

    if (error) {
      logger.error("Error fetching user profile:", error.message);
      return null;
    }

    return data;
  } catch (error) {
    logger.error("Unexpected error in getCurrentUserWithProfile:", error);
    return null;
  }
}
