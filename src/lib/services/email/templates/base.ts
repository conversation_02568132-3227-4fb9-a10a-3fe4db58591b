/**
 * Base email templates with common styling and layout
 * Provides both HTML and plain text versions for better email client compatibility
 */

/**
 * HTML email template
 */
export function baseTemplate(
  content: string,
  options: {
    title?: string;
    companyName?: string;
    companyLogoUrl?: string | null;
    emailFooterText?: string | null;
    userName?: string;
    userPhone?: string;
  } = {},
): string {
  // Extract values from options with empty fallbacks
  // This ensures we don't have hardcoded values that might get out of sync with database defaults
  const {
    title = "",
    companyName = "",
    companyLogoUrl = null,
    emailFooterText = "",
    userName = "",
    userPhone = ""
  } = options;

  // Use empty string checks to determine if we need to use fallbacks
  const displayTitle = title || (companyName || "");
  const displayCompanyName = companyName || "";

  return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${displayTitle}</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #ffffff;
      text-align: left;
    }
    .container {
      max-width: 600px;
      margin: 0; /* Remove auto margin to prevent centering */
      padding: 15px;
    }
    .logo {
      margin-bottom: 15px;
      text-align: left;
    }
    .content {
      padding: 0;
      background-color: #ffffff;
      text-align: left;
    }
    .footer {
      margin-top: 30px;
      padding-top: 15px;
      font-size: 12px;
      color: #333333;
      text-align: left;
    }
    .footer-table {
      border: none;
      border-collapse: collapse;
      width: 100%;
    }
    .footer-logo-cell {
      vertical-align: middle;
      padding: 5pt;
      padding-right: 5pt;
      overflow: hidden;
      width: 70px;
    }
    .footer-spacer-cell {
      width: 10px;
      padding: 0;
    }
    .footer-separator-cell {
      vertical-align: middle;
      width: 2.25pt;
      padding: 0;
      background-color: #0047ff;
    }
    .footer-content-cell {
      vertical-align: middle;
      padding: 5pt;
      padding-left: 10pt;
      overflow: hidden;
    }
    .footer-company-name {
      font-weight: bold;
      margin-bottom: 5px;
    }
    .footer-user-name {
      font-weight: bold;
      margin-bottom: 2px;
      font-size: 12px;
      color: #333333;
    }
    .footer-user-role {
      font-size: 11px;
      color: #666666;
      margin-top: 0;
      margin-bottom: 5px;
    }
    .footer-user-phone {
      font-size: 11px;
      color: #333333;
      margin-top: 2px;
      margin-bottom: 5px;
    }
    .footer-text {
      font-size: 11px;
      color: #363636;
      margin-top: 5px;
    }
    .footer-copyright {
      font-size: 10px;
      color: #363636;
      margin-top: 10px;
    }
    h2 {
      color: #333;
      margin-top: 0;
      font-size: 16px;
      font-weight: normal;
      text-align: left;
    }
    p {
      margin-bottom: 15px;
      text-align: left;
    }
    /* Ensure all text elements are left-aligned */
    div, span, ul, ol, li, a {
      text-align: left;
    }

    /* Responsive styles */
    @media only screen and (max-width: 600px) {
      .container {
        width: 100%;
        padding: 10px;
        margin: 0; /* Ensure no auto margin on mobile */
        text-align: left;
      }
      /* Reinforce left alignment on mobile */
      body, div, p, h1, h2, h3, h4, h5, h6, span, ul, ol, li, a {
        text-align: left !important;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="content">
      ${content}
    </div>
    <div class="footer">
      <table class="footer-table">
        <tr>
          ${companyLogoUrl ? `
          <td class="footer-logo-cell">
            <img src="${companyLogoUrl}" alt="${displayCompanyName}" style="height: 70px; width: 70px;" />
          </td>
          <td class="footer-spacer-cell"></td>
          <td class="footer-separator-cell"></td>
          ` : ''}
          <td class="footer-content-cell">
          ${userName ? `<p class="footer-user-name">${userName}</p>` : ''}
          ${userPhone ? `<p class="footer-user-phone">Phone: ${userPhone}</p>` : ''}
          ${displayCompanyName ? `<p class="footer-company-name">${displayCompanyName}</p>` : ''}
          ${emailFooterText ? `<p class="footer-text">${emailFooterText}</p>` : ''}
          </td>
        </tr>
      </table>
      <p class="footer-copyright">&copy; ${new Date().getFullYear()} ${displayCompanyName}${displayCompanyName ? '. All rights reserved.' : ''}</p>
    </div>
  </div>
</body>
</html>
  `;
}

/**
 * Plain text email template
 * Provides a text-only version of the email for clients that don't support HTML
 */
export function baseTextTemplate(
  content: string,
  options: {
    title?: string;
    companyName?: string;
    emailFooterText?: string | null;
    userName?: string;
    userPhone?: string;
  } = {},
): string {
  // Extract values from options with empty fallbacks
  const {
    title = "",
    companyName = "",
    emailFooterText = "",
    userName = "",
    userPhone = ""
  } = options;

  // Use empty string checks to determine if we need to use fallbacks
  const displayTitle = title || (companyName || "");
  const displayCompanyName = companyName || "";

  // Create a plain text version of the email
  return `${displayTitle ? `${displayTitle}\n\n` : ''}${content}

${'-'.repeat(30)}
${userName ? `${userName}\n` : ''}
${userPhone ? `Phone: ${userPhone}\n` : ''}
${displayCompanyName ? `${displayCompanyName}\n` : ''}
${emailFooterText ? `${emailFooterText}\n` : ''}`;
}
