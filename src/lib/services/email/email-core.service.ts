/**
 * Core Email Service - Shared Types and Utilities
 *
 * This file provides shared types and utilities for email-related functionality.
 * It can be imported by both server-side and client-side code.
 *
 * The main email service implementation is in:
 * - Server-side: '@/lib/services/email/email.service.ts'
 * - Client-side: '@/lib/services/email/email-client.service.ts'
 */

import { EmailServiceData, EmailRecipient, EmailServiceAttachment } from "@/lib/schemas";

// Re-export types for backward compatibility
export type EmailData = EmailServiceData;
export type { EmailRecipient, EmailServiceAttachment };

export interface RFQEmailData extends EmailData {
  rfqDetails: {
    rfqNumber: string;
    origin: string;
    originCountry: string;
    destination: string;
    destinationCountry: string;
    cargoTypes?: string;
  };
  senderInfo?: {
    name?: string;
    phone?: string;
  };
}

// Define validation result type
export type EmailValidationResult<T> = {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    fieldErrors?: Record<string, string[]>;
  };
};

/**
 * Validates email data
 * @param data The email data to validate
 * @returns Validation result
 */
export function validateEmailData(data: any): EmailValidationResult<EmailData> {
  // Check for required fields
  const errors: Record<string, string[]> = {};

  if (!data.to || !data.to.email) {
    errors.to = ["Recipient email is required"];
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.to.email)) {
    errors.to = ["Invalid email format"];
  }

  if (!data.subject) {
    errors.subject = ["Subject is required"];
  }

  if (!data.body) {
    errors.body = ["Email body is required"];
  }

  // Return validation result
  if (Object.keys(errors).length > 0) {
    return {
      success: false,
      error: {
        message: "Email validation failed",
        fieldErrors: errors,
      },
    };
  }

  return {
    success: true,
    data: data as EmailData,
  };
}

/**
 * Validates RFQ email data
 * @param data The RFQ email data to validate
 * @returns Validation result
 */
export function validateRFQEmailData(
  data: any,
): EmailValidationResult<RFQEmailData> {
  // First validate the base email data
  const baseValidation = validateEmailData(data);

  if (!baseValidation.success) {
    return baseValidation as EmailValidationResult<RFQEmailData>;
  }

  // Check for RFQ-specific fields
  const errors: Record<string, string[]> = {};

  if (!data.rfqDetails) {
    errors.rfqDetails = ["RFQ details are required"];
  } else {
    if (!data.rfqDetails.rfqNumber) {
      errors["rfqDetails.rfqNumber"] = ["RFQ number is required"];
    }

    if (!data.rfqDetails.origin) {
      errors["rfqDetails.origin"] = ["Origin is required"];
    }

    if (!data.rfqDetails.originCountry) {
      errors["rfqDetails.originCountry"] = ["Origin country is required"];
    }

    if (!data.rfqDetails.destination) {
      errors["rfqDetails.destination"] = ["Destination is required"];
    }

    if (!data.rfqDetails.destinationCountry) {
      errors["rfqDetails.destinationCountry"] = [
        "Destination country is required",
      ];
    }
  }

  // Return validation result
  if (Object.keys(errors).length > 0) {
    return {
      success: false,
      error: {
        message: "RFQ email validation failed",
        fieldErrors: errors,
      },
    };
  }

  return {
    success: true,
    data: data as RFQEmailData,
  };
}

/**
 * Formats error messages for email operations
 * @param error The error object
 * @param defaultMessage The default message to use if no error message is available
 * @returns Formatted error message
 */
export function formatEmailError(
  error: unknown,
  defaultMessage: string = "An error occurred",
): string {
  // Handle standard Error objects
  if (error instanceof Error) {
    return error.message;
  }

  // Handle string errors
  if (typeof error === "string") {
    return error;
  }

  // Handle SendGrid specific errors
  if (error && typeof error === 'object') {
    // Check if it's a SendGrid error with response
    if ('response' in error) {
      const response = (error as any).response;
      if (response && response.body) {
        // Try to extract detailed error messages from SendGrid response
        try {
          const body = response.body;

          // If body is a string that looks like JSON, parse it
          if (typeof body === 'string' && body.startsWith('{')) {
            const parsedBody = JSON.parse(body);
            if (parsedBody.errors && Array.isArray(parsedBody.errors) && parsedBody.errors.length > 0) {
              // Return the first error message
              return `SendGrid Error: ${parsedBody.errors[0].message}`;
            }
          }

          // If body is an object with errors
          if (typeof body === 'object' && body !== null && 'errors' in body) {
            const errors = body.errors;
            if (Array.isArray(errors) && errors.length > 0) {
              // Return the first error message
              return `SendGrid Error: ${errors[0].message}`;
            }
          }
        } catch (parseError) {
          // If parsing fails, return the status code
          return `SendGrid Error: ${response.statusCode || 'Unknown Status Code'}`;
        }
      }
    }
  }

  // Default fallback
  return defaultMessage;
}
