import { createLogger } from "@/lib/utils/logger/logger";
import {
  EmailData,
  EmailRecipient,
  RFQEmailData,
  validateEmailData,
  validateRFQEmailData,
  formatEmailError,
} from "./email-core.service";
import {
  sendEmailAction,
  sendRFQ<PERSON>mailAction,
  sendBulkEmailsAction,
} from "@/lib/actions/email.actions";

// Create a logger instance for this module
const logger = createLogger("EmailClientService");

// Add a flag to track if we've already logged the client-side warning
let hasLoggedClientWarning = false;

// Re-export types from core service for backward compatibility
export type { EmailRecipient, EmailData, RFQEmailData };

/**
 * Send a single email using the server action
 * @param emailData The email data to send
 * @returns The action response
 */
export async function sendEmail(emailData: EmailData): Promise<any> {
  try {
    // Log a warning if this is being called in a client component during rendering
    if (typeof window !== "undefined" && !hasLoggedClientWarning) {
      logger.warn(
        "Email service is being used in a client component. This should only be used for API calls, not during rendering.",
      );
      hasLoggedClientWarning = true;
    }

    // Validate the email data
    const validationResult = validateEmailData(emailData);

    if (!validationResult.success) {
      logger.error("Email validation failed:", validationResult.error);
      throw new Error(
        validationResult.error?.message || "Email validation failed",
      );
    }

    logger.info(
      `Sending email to ${validationResult.data!.to.email} with subject "${validationResult.data!.subject}"`,
    );

    // Use the server action to send the email
    const response = await sendEmailAction(validationResult.data!);

    if (!response.success) {
      const errorMessage = `Failed to send email: ${response.error}`;
      logger.error(errorMessage);
      throw new Error(errorMessage);
    }

    logger.info(
      `Email sent successfully to ${validationResult.data!.to.email}`,
    );
    return response.data;
  } catch (error) {
    const errorMessage = formatEmailError(
      error,
      "Failed to send email. Please try again or contact support if the problem persists.",
    );
    logger.error("Error sending email:", errorMessage);

    // Rethrow with a more user-friendly message that doesn't expose implementation details
    throw new Error(errorMessage);
  }
}

/**
 * Send multiple emails at once
 * @param emailsData Array of email data to send
 * @returns Array of action responses
 */
export async function sendBulkEmails(emailsData: EmailData[]): Promise<any[]> {
  try {
    logger.info(`Sending ${emailsData.length} emails in bulk`);

    // Use the server action to send bulk emails
    const response = await sendBulkEmailsAction(emailsData);

    if (!response.success) {
      throw new Error(response.error);
    }

    logger.info(`Successfully sent ${response.data.length} emails in bulk`);
    return response.data;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Error sending bulk emails:", errorMessage);

    // Rethrow with a more user-friendly message
    throw new Error(
      `Failed to send bulk emails. Please try again or contact support if the problem persists.`,
    );
  }
}

/**
 * Send an RFQ email with proper formatting
 * @param emailData The RFQ email data to send
 * @returns The action response
 */
export async function sendRFQEmail(emailData: RFQEmailData): Promise<any> {
  try {
    // Log a warning if this is being called in a client component during rendering
    if (typeof window !== "undefined" && !hasLoggedClientWarning) {
      logger.warn(
        "Email service is being used in a client component. This should only be used for API calls, not during rendering.",
      );
      hasLoggedClientWarning = true;
    }

    // Validate the RFQ email data
    const validationResult = validateRFQEmailData(emailData);

    if (!validationResult.success) {
      logger.error("RFQ email validation failed:", validationResult.error);
      throw new Error(
        validationResult.error?.message || "RFQ email validation failed",
      );
    }

    logger.info(
      `Sending RFQ email to ${validationResult.data!.to.email} with subject "${validationResult.data!.subject}"`,
    );

    // Use the server action to send the RFQ email
    const response = await sendRFQEmailAction(validationResult.data!);

    if (!response.success) {
      const errorMessage = `Failed to send RFQ email: ${response.error}`;
      logger.error(errorMessage);
      throw new Error(errorMessage);
    }

    logger.info(
      `RFQ email sent successfully to ${validationResult.data!.to.email}`,
    );
    return response.data;
  } catch (error) {
    const errorMessage = formatEmailError(
      error,
      "Failed to send RFQ email. Please try again or contact support if the problem persists.",
    );
    logger.error("Error sending RFQ email:", errorMessage);

    // Rethrow with a more user-friendly message
    throw new Error(errorMessage);
  }
}
