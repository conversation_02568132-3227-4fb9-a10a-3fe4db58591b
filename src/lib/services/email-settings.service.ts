/**
 * Email Settings Service
 *
 * This service provides functions for managing email settings in the database.
 * It handles retrieving, creating, and updating email settings.
 *
 */

// This file should only be imported on the server side
if (typeof window !== "undefined") {
  throw new Error(
    "email-settings.service.ts should only be imported on the server side."
  );
}

import { createAdminClient } from "@/lib/supabase/server/server";
import { EmailSettings, EmailSettingsFormValues } from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";

// Create a logger instance for this module
const logger = createLogger("EmailSettingsService");

/**
 * Get email settings from the database
 *
 * @returns The email settings or null if not found
 */
export async function getEmailSettings(): Promise<EmailSettings | null> {
  try {
    const supabase = createAdminClient();

    // Get the first email settings record
    // We only expect one record in this table
    // @ts-ignore - TypeScript errors will be resolved after migration is applied
    const { data, error } = await supabase
      .from("email_settings")
      .select("*")
      .limit(1)
      .single();

    if (error) {
      // Improved error logging with proper error details
      logger.error("Error fetching email settings:", {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      });
      return null;
    }

    return data as EmailSettings;
  } catch (error) {
    // Improved error handling for unexpected errors
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorStack = error instanceof Error ? error.stack : undefined;

    logger.error("Unexpected error fetching email settings:", {
      message: errorMessage,
      stack: errorStack
    });
    return null;
  }
}

/**
 * Create new email settings
 *
 * @param settings The email settings to create
 * @param userId The ID of the user creating the settings
 * @returns The created email settings or null if creation failed
 */
export async function createEmailSettings(
  settings: EmailSettingsFormValues,
  userId: string
): Promise<EmailSettings | null> {
  try {
    const supabase = createAdminClient();

    // Add created_by field
    const settingsWithUser = {
      ...settings,
      created_by: userId,
    };

    // Insert the new settings
    // @ts-ignore - TypeScript errors will be resolved after migration is applied
    const { data, error } = await supabase
      .from("email_settings")
      .insert(settingsWithUser)
      .select()
      .single();

    if (error) {
      // Improved error logging with proper error details
      logger.error("Error creating email settings:", {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      });
      return null;
    }

    return data as EmailSettings;
  } catch (error) {
    // Improved error handling for unexpected errors
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorStack = error instanceof Error ? error.stack : undefined;

    logger.error("Unexpected error creating email settings:", {
      message: errorMessage,
      stack: errorStack
    });
    return null;
  }
}

/**
 * Update existing email settings
 *
 * @param id The ID of the email settings to update
 * @param settings The updated email settings
 * @param userId The ID of the user updating the settings
 * @returns The updated email settings or null if update failed
 */
export async function updateEmailSettings(
  id: string,
  settings: EmailSettingsFormValues,
  userId: string
): Promise<EmailSettings | null> {
  try {
    const supabase = createAdminClient();

    // Add updated_by field
    const settingsWithUser = {
      ...settings,
      updated_by: userId,
    };

    // Update the settings
    // @ts-ignore - TypeScript errors will be resolved after migration is applied
    const { data, error } = await supabase
      .from("email_settings")
      .update(settingsWithUser)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      // Improved error logging with proper error details
      logger.error("Error updating email settings:", {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      });
      return null;
    }

    return data as EmailSettings;
  } catch (error) {
    // Improved error handling for unexpected errors
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorStack = error instanceof Error ? error.stack : undefined;

    logger.error("Unexpected error updating email settings:", {
      message: errorMessage,
      stack: errorStack
    });
    return null;
  }
}

/**
 * Get formatted email settings from the database
 *
 * This function retrieves email settings from the database and formats them
 * for use in the email service. If no settings are found, it throws an error.
 *
 * @returns Formatted email settings from the database
 * @throws Error if email settings are not configured
 */
export async function getFormattedEmailSettings(): Promise<{
  companyName: string;
  companyLogoUrl: string | null;
  sandboxMode: boolean;
  sandboxRecipientEmail: string | null;
  defaultSenderEmail: string;
  defaultSenderName: string;
  replyToEmail: string | null;
  bccRecipients: string[] | null;
  emailSignature: string | null;
  emailFooterText: string | null;
}> {
  // Get settings from database
  const dbSettings = await getEmailSettings();

  // Throw error if settings are not found
  if (!dbSettings) {
    throw new Error(
      "Email functionality is disabled: Email settings are not configured in the database. " +
      "An administrator must configure email settings in the Admin Dashboard before emails can be sent."
    );
  }

  // Validate required fields
  if (!dbSettings.default_sender_email) {
    throw new Error("Email settings are incomplete: Default sender email is required");
  }

  if (!dbSettings.default_sender_name) {
    throw new Error("Email settings are incomplete: Default sender name is required");
  }

  if (!dbSettings.company_name) {
    throw new Error("Email settings are incomplete: Company name is required");
  }

  // Validate sandbox mode configuration
  if (dbSettings.sandbox_mode && !dbSettings.sandbox_recipient_email) {
    throw new Error(
      "Email settings are incomplete: Sandbox mode is enabled but no sandbox recipient email is configured. " +
      "Either disable sandbox mode or configure a sandbox recipient email in the Admin Dashboard."
    );
  }

  // Return formatted database settings
  return {
    companyName: dbSettings.company_name,
    companyLogoUrl: dbSettings.company_logo_url,
    sandboxMode: dbSettings.sandbox_mode,
    sandboxRecipientEmail: dbSettings.sandbox_recipient_email,
    defaultSenderEmail: dbSettings.default_sender_email,
    defaultSenderName: dbSettings.default_sender_name,
    replyToEmail: dbSettings.reply_to_email,
    bccRecipients: dbSettings.bcc_recipients,
    emailSignature: dbSettings.email_signature,
    emailFooterText: dbSettings.email_footer_text,
  };
}
