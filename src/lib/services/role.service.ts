"use server";

import { SupabaseClient } from "@supabase/supabase-js";
import { createClient } from "@/lib/supabase/server/server";
import { createLogger } from "@/lib/utils/logger/logger";

// Create a logger instance for this module
const logger = createLogger("RoleService");

/**
 * Gets the user ID from a Supabase client
 */
export async function getUserId(
  supabase: SupabaseClient,
): Promise<string | null> {
  try {
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error) {
      logger.error("Error getting user ID:", error.message);
      return null;
    }

    return user?.id || null;
  } catch (error) {
    logger.error("Unexpected error in getUserId:", error);
    return null;
  }
}

/**
 * Checks if a user has any of the specified roles
 * @param userId The user ID to check
 * @param roles Array of role names to check against
 * @returns True if the user has any of the specified roles, false otherwise
 */
export async function checkUserRole(
  userId: string,
  roles: string[],
): Promise<boolean> {
  if (!userId) {
    logger.warn("No user ID provided to checkUser<PERSON>ole");
    return false;
  }

  try {
    const supabase = await createClient();

    const { data, error } = await supabase
      .from("user_roles")
      .select("roles(name)")
      .eq("user_id", userId);

    if (error) {
      logger.error(`Error checking roles for user ${userId}:`, error.message);
      return false;
    }

    if (!data || data.length === 0) {
      logger.debug(`No roles found for user ${userId}`);
      return false;
    }

    const userRoles = data.flatMap((entry) => {
      if (Array.isArray(entry.roles)) {
        return entry.roles.map((role) => role.name);
      } else if (entry.roles && typeof entry.roles === "object") {
        return [(entry.roles as { name: string }).name];
      }
      return [];
    });

    const hasRole = roles.some((requiredRole) =>
      userRoles.includes(requiredRole),
    );

    return hasRole;
  } catch (error) {
    logger.error(`Unexpected error checking roles for user ${userId}:`, error);
    return false;
  }
}

/**
 * Checks if a user has all of the specified roles
 * @param userId The user ID to check
 * @param roles Array of role names to check against
 * @returns True if the user has all of the specified roles, false otherwise
 */
export async function checkUserAllRoles(
  userId: string,
  roles: string[],
): Promise<boolean> {
  if (!userId) {
    logger.warn("No user ID provided to checkUserAllRoles");
    return false;
  }

  try {
    const supabase = await createClient();

    const { data, error } = await supabase
      .from("user_roles")
      .select("roles(name)")
      .eq("user_id", userId);

    if (error) {
      logger.error(`Error checking roles for user ${userId}:`, error.message);
      return false;
    }

    if (!data || data.length === 0) {
      logger.debug(`No roles found for user ${userId}`);
      return false;
    }

    const userRoles = data.flatMap((entry) => {
      if (Array.isArray(entry.roles)) {
        return entry.roles.map((role) => role.name);
      } else if (entry.roles && typeof entry.roles === "object") {
        return [(entry.roles as { name: string }).name];
      }
      return [];
    });

    // Check if all required roles are present
    const hasAllRoles = roles.every((requiredRole) =>
      userRoles.includes(requiredRole),
    );

    return hasAllRoles;
  } catch (error) {
    logger.error(`Unexpected error checking roles for user ${userId}:`, error);
    return false;
  }
}

/**
 * Gets all roles for a user
 * @param userId The user ID to get roles for
 * @returns Array of role names
 */
export async function getUserRoles(userId: string): Promise<string[]> {
  if (!userId) {
    logger.warn("No user ID provided to getUserRoles");
    return [];
  }

  try {
    const supabase = await createClient();

    const { data, error } = await supabase
      .from("user_roles")
      .select("roles(name)")
      .eq("user_id", userId);

    if (error) {
      logger.error(`Error getting roles for user ${userId}:`, error.message);
      return [];
    }

    if (!data || data.length === 0) {
      logger.debug(`No roles found for user ${userId}`);
      return [];
    }

    const userRoles = data.flatMap((entry) => {
      if (Array.isArray(entry.roles)) {
        return entry.roles.map((role) => role.name);
      } else if (entry.roles && typeof entry.roles === "object") {
        return [(entry.roles as { name: string }).name];
      }
      return [];
    });

    return userRoles;
  } catch (error) {
    logger.error(`Unexpected error getting roles for user ${userId}:`, error);
    return [];
  }
}
