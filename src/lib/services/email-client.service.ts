import { createLogger } from "@/lib/utils/logger/logger";
import {
  EmailData,
  EmailRecipient,
  RFQEmailData,
  validateEmailData,
  validateRFQEmailData,
  formatEmailError,
} from "./email-core.service";

// Create a logger instance for this module
const logger = createLogger("EmailClientService");

// Add a flag to track if we've already logged the client-side warning
let hasLoggedClientWarning = false;

// Re-export types from core service for backward compatibility
export type { EmailRecipient, EmailData, RFQEmailData };

/**
 * Send a single email using the API route
 * @param emailData The email data to send
 * @returns The API response
 */
export async function sendEmail(emailData: EmailData): Promise<any> {
  try {
    // Log a warning if this is being called in a client component during rendering
    if (typeof window !== "undefined" && !hasLoggedClientWarning) {
      logger.warn(
        "Email service is being used in a client component. This should only be used for API calls, not during rendering.",
      );
      hasLoggedClientWarning = true;
    }

    // Validate the email data
    const validationResult = validateEmailData(emailData);

    if (!validationResult.success) {
      logger.error("Email validation failed:", validationResult.error);
      throw new Error(
        validationResult.error?.message || "Email validation failed",
      );
    }

    logger.info(
      `Sending email to ${validationResult.data!.to.email} with subject "${validationResult.data!.subject}"`,
    );

    const response = await fetch("/api/email/send", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(validationResult.data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      const errorMessage = `Failed to send email: ${errorData.error || response.statusText}`;
      logger.error(errorMessage);
      throw new Error(errorMessage);
    }

    const result = await response.json();
    logger.info(
      `Email sent successfully to ${validationResult.data!.to.email}`,
    );
    return result;
  } catch (error) {
    const errorMessage = formatEmailError(
      error,
      "Failed to send email. Please try again or contact support if the problem persists.",
    );
    logger.error("Error sending email:", errorMessage);

    // Rethrow with a more user-friendly message that doesn't expose implementation details
    throw new Error(errorMessage);
  }
}

/**
 * Send multiple emails at once
 * @param emailsData Array of email data to send
 * @returns Array of API responses
 */
export async function sendBulkEmails(emailsData: EmailData[]): Promise<any[]> {
  // Process each email individually
  const promises = emailsData.map((emailData) => sendEmail(emailData));

  try {
    logger.info(`Sending ${emailsData.length} emails in bulk`);
    const responses = await Promise.all(promises);
    logger.info(`Successfully sent ${responses.length} emails in bulk`);
    return responses;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Error sending bulk emails:", errorMessage);

    // Rethrow with a more user-friendly message
    throw new Error(
      `Failed to send bulk emails. Please try again or contact support if the problem persists.`,
    );
  }
}

/**
 * Send an RFQ email with proper formatting
 * @param emailData The RFQ email data to send
 * @returns The API response
 */
export async function sendRFQEmail(emailData: RFQEmailData): Promise<any> {
  try {
    // Log a warning if this is being called in a client component during rendering
    if (typeof window !== "undefined" && !hasLoggedClientWarning) {
      logger.warn(
        "Email service is being used in a client component. This should only be used for API calls, not during rendering.",
      );
      hasLoggedClientWarning = true;
    }

    // Validate the RFQ email data
    const validationResult = validateRFQEmailData(emailData);

    if (!validationResult.success) {
      logger.error("RFQ email validation failed:", validationResult.error);
      throw new Error(
        validationResult.error?.message || "RFQ email validation failed",
      );
    }

    logger.info(
      `Sending RFQ email to ${validationResult.data!.to.email} with subject "${validationResult.data!.subject}"`,
    );

    const response = await fetch("/api/email/send", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(validationResult.data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      const errorMessage = `Failed to send RFQ email: ${errorData.error || response.statusText}`;
      logger.error(errorMessage);
      throw new Error(errorMessage);
    }

    const result = await response.json();
    logger.info(
      `RFQ email sent successfully to ${validationResult.data!.to.email}`,
    );
    return result;
  } catch (error) {
    const errorMessage = formatEmailError(
      error,
      "Failed to send RFQ email. Please try again or contact support if the problem persists.",
    );
    logger.error("Error sending RFQ email:", errorMessage);

    // Rethrow with a more user-friendly message
    throw new Error(errorMessage);
  }
}
