"use server";

import { createClient } from "@/lib/supabase/server/server";
import crypto from "crypto";
import {
  CreateRFQInput,
  GetRFQsParams,
  RFQBidInput,
  RFQProviderInput,
  UpdateRFQBidInput,
  UpdateRFQInput,
  CreateRFQSchema,
  UpdateRFQSchema,
  RFQProviderSchema,
  RFQBidSchema,
  UpdateRFQBidSchema,
  GetRFQsParamsSchema,
  RFQ,
  RFQProvider,
  RFQBid,
  PaginatedResponse,
  ValidationResult,
} from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";
import {
  EmailData,
  RFQEmailData,
} from "@/lib/services/email/email-core.service";
import { retry } from "@/lib/utils/retry";
import {
  resolveCoordinates,
  generateAddressHash,
  detectAddressChange,
} from "@/lib/services/google-maps/coordinate-resolution.service";
import {
  calculateDistance,
  validateRouteCoordinates,
} from "@/lib/services/google-maps/distance-calculation.service";
import type { RouteCoordinates } from "@/lib/services/google-maps/types";

const logger = createLogger("RFQService");

/**
 * Processes coordinate resolution and distance calculation for an RFQ
 * Implements PRD Task 2.1 Steps 1-4
 * @param rfqId The RFQ ID to update
 * @param addressData Address components for coordinate resolution
 */
async function processRFQCoordinatesAndDistance(
  rfqId: string,
  addressData: {
    originCity?: string;
    originAddress?: string;
    originPostalCode?: string;
    destinationCity?: string;
    destinationAddress?: string;
    destinationPostalCode?: string;
  },
): Promise<void> {
  const supabase = await createClient();

  logger.info(`Processing coordinates and distance for RFQ ${rfqId}`);

  try {
    // Step 4: Generate address hash for change detection
    const addressHash = generateAddressHash(addressData);

    // Step 1: Resolve coordinates from origin/destination addresses
    const originAddressString = [
      addressData.originAddress,
      addressData.originCity,
      addressData.originPostalCode,
    ]
      .filter(Boolean)
      .join(", ");

    const destinationAddressString = [
      addressData.destinationAddress,
      addressData.destinationCity,
      addressData.destinationPostalCode,
    ]
      .filter(Boolean)
      .join(", ");

    logger.debug(`Resolving coordinates for origin: ${originAddressString}`);
    logger.debug(`Resolving coordinates for destination: ${destinationAddressString}`);

    // Resolve coordinates for both addresses concurrently
    const [originResult, destinationResult] = await Promise.all([
      resolveCoordinates(originAddressString),
      resolveCoordinates(destinationAddressString),
    ]);

    let coordinatesResolved = false;
    let distanceCalculated = false;
    const timestamp = new Date().toISOString();

    // Prepare update data
    const updateData: any = {
      address_hash: addressHash,
      updated_at: timestamp,
    };

    // Step 2 & 3: Calculate distance if both coordinates were resolved successfully
    if (originResult.success && destinationResult.success &&
        originResult.lat !== undefined && originResult.lng !== undefined &&
        destinationResult.lat !== undefined && destinationResult.lng !== undefined) {

      // Store resolved coordinates
      updateData.origin_lat = originResult.lat;
      updateData.origin_lng = originResult.lng;
      updateData.destination_lat = destinationResult.lat;
      updateData.destination_lng = destinationResult.lng;
      updateData.coordinates_resolved_at = timestamp;
      coordinatesResolved = true;

      logger.info(`Coordinates resolved for RFQ ${rfqId}: Origin(${originResult.lat}, ${originResult.lng}), Destination(${destinationResult.lat}, ${destinationResult.lng})`);

      // Create route coordinates for distance calculation
      const route: RouteCoordinates = {
        origin: {
          lat: originResult.lat,
          lng: originResult.lng,
        },
        destination: {
          lat: destinationResult.lat,
          lng: destinationResult.lng,
        },
      };

      // Validate coordinates before distance calculation
      if (validateRouteCoordinates(route)) {
        logger.debug(`Calculating distance for RFQ ${rfqId}`);

        const distanceResult = await calculateDistance(route);

        if (distanceResult.success && distanceResult.distanceKm !== undefined) {
          updateData.route_distance_km = distanceResult.distanceKm;
          updateData.route_distance_calculated_at = timestamp;
          distanceCalculated = true;

          logger.info(`Distance calculated for RFQ ${rfqId}: ${distanceResult.distanceKm}km`);
        } else {
          logger.warn(`Distance calculation failed for RFQ ${rfqId}: ${distanceResult.error}`);
        }
      } else {
        logger.warn(`Invalid coordinates for distance calculation for RFQ ${rfqId}`);
      }
    } else {
      // Log coordinate resolution failures
      if (!originResult.success) {
        logger.warn(`Origin coordinate resolution failed for RFQ ${rfqId}: ${originResult.error}`);
      }
      if (!destinationResult.success) {
        logger.warn(`Destination coordinate resolution failed for RFQ ${rfqId}: ${destinationResult.error}`);
      }
    }

    // Update the RFQ with resolved coordinates and/or distance
    const { error: updateError } = await supabase
      .from("rfqs")
      .update(updateData)
      .eq("id", rfqId);

    if (updateError) {
      logger.error(`Error updating RFQ ${rfqId} with coordinates/distance:`, updateError);
      // Don't throw error - this is a non-critical enhancement
    } else {
      const successMessage = [
        coordinatesResolved ? "coordinates resolved" : null,
        distanceCalculated ? "distance calculated" : null,
      ]
        .filter(Boolean)
        .join(" and ");

      if (successMessage) {
        logger.info(`Successfully updated RFQ ${rfqId} with ${successMessage}`);
      } else {
        logger.info(`Updated RFQ ${rfqId} with address hash (coordinate/distance resolution failed)`);
      }
    }
  } catch (error) {
    logger.error(`Error processing coordinates and distance for RFQ ${rfqId}:`, error);
    // Don't throw error - this is a non-critical enhancement that shouldn't break RFQ creation
  }
}

// Import email services conditionally based on environment
let emailService: {
  sendBulkEmails: (emailsData: EmailData[]) => Promise<any[]>;
  sendRFQEmail: (emailData: RFQEmailData) => Promise<any>;
};

// Only import server-side email service on the server
if (typeof window === "undefined") {
  // Server-side import
  emailService = require("@/lib/services/email/email.service");
} else {
  // Client-side import
  emailService = require("@/lib/services/email/email-client.service");
}

// Types are now imported from @/lib/schemas

/**
 * Validates RFQ data with the appropriate schema
 * @param data The data to validate
 * @param schema The schema to validate against
 * @returns Validation result with success status, data, or error
 */
async function validateData<T>(
  data: any,
  schema: any,
): Promise<ValidationResult<T>> {
  try {
    const validationResult = schema.safeParse(data);

    if (!validationResult.success) {
      return {
        success: false,
        error: {
          message: "Validation failed",
          fieldErrors: validationResult.error.flatten().fieldErrors,
        },
      };
    }

    return {
      success: true,
      data: validationResult.data,
    };
  } catch (error) {
    logger.error("Error validating data:", error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : "Validation error",
      },
    };
  }
}

/**
 * Validates RFQ creation data
 * @param data The RFQ data to validate
 * @returns Validation result
 */
export async function validateCreateRFQ(
  data: any,
): Promise<ValidationResult<CreateRFQInput>> {
  return await validateData<CreateRFQInput>(data, CreateRFQSchema);
}

/**
 * Validates RFQ update data
 * @param data The RFQ data to validate
 * @returns Validation result
 */
export async function validateUpdateRFQ(
  data: any,
): Promise<ValidationResult<UpdateRFQInput>> {
  return await validateData<UpdateRFQInput>(data, UpdateRFQSchema);
}

/**
 * Validates RFQ provider data
 * @param data The provider data to validate
 * @returns Validation result
 */
export async function validateRFQProvider(
  data: any,
): Promise<ValidationResult<RFQProviderInput>> {
  return await validateData<RFQProviderInput>(data, RFQProviderSchema);
}

/**
 * Validates RFQ bid data
 * @param data The bid data to validate
 * @returns Validation result
 */
export async function validateRFQBid(
  data: any,
): Promise<ValidationResult<RFQBidInput>> {
  return await validateData<RFQBidInput>(data, RFQBidSchema);
}

/**
 * Validates RFQ bid update data
 * @param data The bid update data to validate
 * @returns Validation result
 */
export async function validateUpdateRFQBid(
  data: any,
): Promise<ValidationResult<UpdateRFQBidInput>> {
  return await validateData<UpdateRFQBidInput>(data, UpdateRFQBidSchema);
}

/**
 * Validates RFQ query parameters
 * @param params The query parameters to validate
 * @returns Validation result
 */
export async function validateRFQParams(
  params: any,
): Promise<ValidationResult<GetRFQsParams>> {
  return await validateData<GetRFQsParams>(params, GetRFQsParamsSchema);
}

/**
 * Fetches RFQs with optional filtering and pagination
 *
 * @param params - Parameters for filtering and pagination
 * @returns Paginated response with RFQ data
 */
export async function fetchRFQs(
  params: GetRFQsParams = { page: 1, pageSize: 10 },
): Promise<PaginatedResponse<RFQ>> {
  const supabase = await createClient();

  // Ensure page and pageSize are numbers
  const id = params.id;
  const status = params.status;
  const origin_city = params.origin_city;
  const destination_city = params.destination_city;
  const search = params.search;
  const created_after = params.created_after;
  const created_before = params.created_before;
  const page = Number(params.page || 1);
  const pageSize = Number(params.pageSize || 10);

  // Calculate pagination values
  const from = (page - 1) * pageSize;
  const to = from + pageSize - 1;

  // Start building the query
  let query = supabase.from("rfqs").select("*", { count: "exact" });

  // Apply filters if provided
  if (id) {
    query = query.eq("id", id);
  }

  if (status && typeof status === "string") {
    query = query.eq("status", status);
  }

  if (origin_city && typeof origin_city === "string") {
    query = query.ilike("origin_city", `%${origin_city}%`);
  }

  if (destination_city && typeof destination_city === "string") {
    query = query.ilike("destination_city", `%${destination_city}%`);
  }

  if (search && typeof search === "string") {
    // Search across multiple columns
    query = query.or(
      `sequence_number.ilike.%${search}%,origin_city.ilike.%${search}%,destination_city.ilike.%${search}%`
    );
  }

  if (created_after) {
    query = query.gte("created_at", created_after);
  }

  if (created_before) {
    query = query.lte("created_at", created_before);
  }

  // Apply pagination
  query = query.range(from, to).order("created_at", { ascending: false });

  // Execute the query
  const { data, error, count } = await query;

  if (error) {
    logger.error("Error fetching RFQs:", error);
    throw new Error(`Failed to fetch RFQs: ${error.message}`);
  }

  // Calculate total pages
  const totalPages = Math.ceil((count || 0) / pageSize);

  return {
    data: data as RFQ[],
    pagination: {
      page,
      pageSize,
      totalPages,
      totalCount: count || 0,
    },
  };
}

/**
 * Fetches a single RFQ by ID with cargo types and equipment type
 */
export async function getRFQ(id: string): Promise<
  RFQ & {
    equipment_type_name?: string;
    cargo_types?: any[];
    countries_origin?: { name: string; alpha2_code: string };
    countries_destination?: { name: string; alpha2_code: string };
  }
> {
  const supabase = await createClient();

  // Fetch RFQ with equipment type and country information
  const { data, error } = await supabase
    .from("rfqs")
    .select(
      `
      *,
      equipment_types:equipment_type_id (
        name
      ),
      countries_origin:origin_country_id (
        name,
        alpha2_code
      ),
      countries_destination:destination_country_id (
        name,
        alpha2_code
      )
    `,
    )
    .eq("id", id)
    .single();

  if (error) {
    logger.error("Error fetching RFQ:", error);
    throw new Error(`Failed to fetch RFQ: ${error.message}`);
  }

  // Extract equipment type name and add it to the RFQ object
  const rfqData = data as any;
  const equipmentTypeName = rfqData.equipment_types?.name;

  // Create a new object with the RFQ data and the equipment type name
  const result = {
    ...rfqData,
    equipment_type_name: equipmentTypeName,
  };

  // Remove the nested equipment_types object to maintain compatibility
  delete result.equipment_types;

  // Fetch cargo types for this RFQ
  const { data: cargoTypesData, error: cargoTypesError } = await supabase
    .from("rfq_cargo_types")
    .select(
      `
      cargo_type_id,
      cargo_types:cargo_type_id (
        id,
        name,
        description,
        image_url
      )
    `,
    )
    .eq("rfq_id", id);

  if (cargoTypesError) {
    logger.error("Error fetching RFQ cargo types:", cargoTypesError);
    logger.error(
      `Failed to fetch cargo types for RFQ ${id}: ${cargoTypesError.message}`,
    );
  } else if (cargoTypesData && cargoTypesData.length > 0) {
    // Extract cargo types and add them to the result
    result.cargo_types = cargoTypesData.map((item) => item.cargo_types);
  }

  return result;
}

/**
 * Creates a new RFQ with multiple cargo types
 */
export async function createRFQ(
  input: CreateRFQInput,
  userId: string,
): Promise<RFQ> {
  const supabase = await createClient();

  const timestamp = new Date().toISOString();

  logger.info("Creating RFQ with input:", JSON.stringify(input, null, 2));
  logger.info("User ID for RFQ creation:", userId);

  // Validate required fields
  if (!input.origin_country_id) {
    logger.error("Missing origin_country_id in createRFQ input");
    throw new Error("Origin country is required");
  }

  if (!input.destination_country_id) {
    logger.error("Missing destination_country_id in createRFQ input");
    throw new Error("Destination country is required");
  }

  if (!input.cargo_type_ids || input.cargo_type_ids.length === 0) {
    logger.error("Missing cargo_type_ids in createRFQ input");
    throw new Error("At least one cargo type must be selected");
  }

  if (!input.equipment_type_id) {
    logger.error("Missing equipment_type_id in createRFQ input");
    throw new Error("Equipment type is required");
  }

  // Extract cargo_type_ids from input for the join table
  const { cargo_type_ids, ...rfqData } = input;

  // Use the first cargo type as the primary cargo_type_id for backward compatibility
  const primaryCargoTypeId = cargo_type_ids[0];

  logger.info("Preparing RFQ data for insertion:", {
    ...rfqData,
    cargo_type_id: primaryCargoTypeId,
    created_at: timestamp,
    updated_at: timestamp,
    created_by: userId,
  });

  try {
    // Ensure all required fields are present with proper types
    const rfqInsertData = {
      ...rfqData,
      cargo_type_id: primaryCargoTypeId, // Set the first cargo type as the primary
      created_at: timestamp,
      updated_at: timestamp,
      created_by: userId,
      // Ensure required fields have default values if undefined
      destination_address: rfqData.destination_address || "",
      origin_address: rfqData.origin_address || "",
    };

    logger.info("Final RFQ insert data:", rfqInsertData);

    // Create the RFQ record
    const { data, error } = await supabase
      .from("rfqs")
      .insert(rfqInsertData)
      .select()
      .single();

    if (error) {
      logger.error("Error creating RFQ:", error);
      throw new Error(`Failed to create RFQ: ${error.message}`);
    }

    logger.info("RFQ created successfully with ID:", data.id);

    // Now create entries in the rfq_cargo_types join table
    if (cargo_type_ids.length > 0) {
      const cargoTypeEntries = cargo_type_ids.map((cargoTypeId) => ({
        rfq_id: data.id,
        cargo_type_id: cargoTypeId,
      }));

      logger.info("Creating cargo type entries:", cargoTypeEntries);

      const { error: joinError } = await supabase
        .from("rfq_cargo_types")
        .insert(cargoTypeEntries);

      if (joinError) {
        logger.error("Error creating RFQ cargo types:", joinError);
        // Consider whether to roll back the RFQ creation or just log the error
        logger.error(
          `Failed to create RFQ cargo types for RFQ ${data.id}: ${joinError.message}`,
        );
      } else {
        logger.info("RFQ cargo types created successfully");
      }
    }

    // Step 1-4: Resolve coordinates and calculate distance (PRD Task 2.1)
    await processRFQCoordinatesAndDistance(data.id, {
      originCity: rfqData.origin_city,
      originAddress: rfqData.origin_address,
      originPostalCode: rfqData.origin_postal_code,
      destinationCity: rfqData.destination_city,
      destinationAddress: rfqData.destination_address,
      destinationPostalCode: rfqData.destination_postal_code,
    });

    return data as RFQ;
  } catch (error) {
    logger.error("Unexpected error in createRFQ:", error);
    throw error;
  }
}

/**
 * Updates an existing RFQ with multiple cargo types
 */
export async function updateRFQ(input: UpdateRFQInput): Promise<RFQ> {
  const supabase = await createClient();

  const timestamp = new Date().toISOString();

  // Extract cargo_type_ids from input for the join table
  const { cargo_type_ids, ...rfqData } = input;

  // If cargo_type_ids is provided, use the first one as the primary cargo_type_id
  if (cargo_type_ids && cargo_type_ids.length > 0) {
    rfqData.cargo_type_id = cargo_type_ids[0];
  }

  // Update the RFQ record
  const { data, error } = await supabase
    .from("rfqs")
    .update({
      ...rfqData,
      updated_at: timestamp,
    })
    .eq("id", input.id)
    .select()
    .single();

  if (error) {
    logger.error("Error updating RFQ:", error);
    throw new Error(`Failed to update RFQ: ${error.message}`);
  }

  // If cargo_type_ids is provided, update the join table
  if (cargo_type_ids && cargo_type_ids.length > 0) {
    // First, delete existing entries
    const { error: deleteError } = await supabase
      .from("rfq_cargo_types")
      .delete()
      .eq("rfq_id", input.id);

    if (deleteError) {
      logger.error("Error deleting existing RFQ cargo types:", deleteError);
      logger.error(
        `Failed to delete existing RFQ cargo types for RFQ ${input.id}: ${deleteError.message}`,
      );
    }

    // Then, insert new entries
    const cargoTypeEntries = cargo_type_ids.map((cargoTypeId) => ({
      rfq_id: input.id,
      cargo_type_id: cargoTypeId,
    }));

    const { error: insertError } = await supabase
      .from("rfq_cargo_types")
      .insert(cargoTypeEntries);

    if (insertError) {
      logger.error("Error updating RFQ cargo types:", insertError);
      logger.error(
        `Failed to update RFQ cargo types for RFQ ${input.id}: ${insertError.message}`,
      );
    }
  }

  return data as RFQ;
}

/**
 * Deletes an RFQ
 */
export async function deleteRFQ(id: string): Promise<void> {
  const supabase = await createClient();

  const { error } = await supabase.from("rfqs").delete().eq("id", id);

  if (error) {
    logger.error("Error deleting RFQ:", error);
    throw new Error(`Failed to delete RFQ: ${error.message}`);
  }
}

/**
 * Matches providers for an RFQ based on routes and capabilities
 * Optimized version that combines multiple queries into a single efficient query
 */
export async function matchProvidersForRFQ(rfqId: string): Promise<any[]> {
  const supabase = await createClient();

  // Execute RFQ fetch and existing providers in parallel for better performance
  const [rfqResult, existingProvidersResult] = await Promise.all([
    // Get RFQ details (only the fields we need)
    supabase
      .from("rfqs")
      .select("id, equipment_type_id, origin_country_id, destination_country_id")
      .eq("id", rfqId)
      .single(),

    // Get existing RFQ providers to avoid duplicates
    supabase
      .from("rfq_providers")
      .select("provider_id")
      .eq("rfq_id", rfqId)
  ]);

  const { data: rfq, error: rfqError } = rfqResult;
  const { data: existingProviders, error: existingError } = existingProvidersResult;

  if (rfqError) {
    throw new Error(`Failed to fetch RFQ: ${rfqError.message}`);
  }

  if (existingError) {
    throw new Error(`Failed to fetch existing providers: ${existingError.message}`);
  }

  // Now get equipment providers with the correct equipment_type_id
  const { data: providersWithEquipment, error: equipmentError } = await supabase
    .from("provider_equipments")
    .select("provider_id")
    .eq("equipment_type_id", rfq.equipment_type_id);

  if (equipmentError) {
    throw new Error(`Failed to fetch providers with equipment: ${equipmentError.message}`);
  }

  // Create a set of existing provider IDs for quick lookup
  const existingProviderIds = new Set(
    existingProviders?.map((p) => p.provider_id) || []
  );

  // Extract provider IDs that have the required equipment
  const providerIdsWithEquipment = providersWithEquipment?.map(p => p.provider_id) || [];

  // If no providers have the required equipment, return empty array
  if (providerIdsWithEquipment.length === 0) {
    logger.info(`No providers found with equipment type ${rfq.equipment_type_id} for RFQ ${rfqId}`);
    return [];
  }

  // Now find providers that match the route AND have the required equipment type
  const { data: matchingProviders, error: providersError } = await supabase
    .from("provider_routes")
    .select(`
      provider_id,
      providers!inner (
        id,
        name,
        status,
        verified,
        tax_id,
        full_address
      )
    `)
    .eq("origin_country_id", rfq.origin_country_id)
    .eq("destination_country_id", rfq.destination_country_id)
    .eq("is_active", true)
    .eq("providers.status", "active")
    .in("provider_id", providerIdsWithEquipment)
    .order("providers(verified)", { ascending: false });

  if (providersError) {
    throw new Error(`Failed to match providers: ${providersError.message}`);
  }

  if (!matchingProviders || matchingProviders.length === 0) {
    logger.info(`No providers found matching criteria for RFQ ${rfqId}`);
    return [];
  }

  // Process results and filter out existing providers
  const activeProviders = matchingProviders
    .filter((p: any) => {
      // Ensure we have valid provider data and they're not already in rfq_providers
      return (
        p.providers &&
        typeof p.providers === "object" &&
        !Array.isArray(p.providers) &&
        !existingProviderIds.has(p.providers.id)
      );
    })
    .map((p: any) => ({
      id: p.providers.id,
      name: p.providers.name,
      status: p.providers.status,
      verified: p.providers.verified,
      tax_id: p.providers.tax_id,
      full_address: p.providers.full_address,
    }));

  // Bulk insert new provider matches if any exist
  if (activeProviders.length > 0) {
    const rfqProviders = activeProviders.map((p) => ({
      rfq_id: rfqId,
      provider_id: p.id,
      status: "matched",
      selected_at: null,
      invited_at: null,
      email_opened_at: null,
      response_received_at: null,
      notes: null,
    }));

    const { error: insertError } = await supabase
      .from("rfq_providers")
      .insert(rfqProviders);

    if (insertError) {
      throw new Error(`Failed to save provider matches: ${insertError.message}`);
    }

    logger.info(`Successfully matched ${activeProviders.length} new providers for RFQ ${rfqId}`);
  } else {
    logger.info(`No new providers to match for RFQ ${rfqId} - all matching providers already exist`);
  }

  // Return all matching providers (including existing ones for UI consistency)
  const allMatchingProviders = matchingProviders
    .filter((p: any) => {
      return (
        p.providers &&
        typeof p.providers === "object" &&
        !Array.isArray(p.providers)
      );
    })
    .map((p: any) => ({
      id: p.providers.id,
      name: p.providers.name,
      status: p.providers.status,
      verified: p.providers.verified,
      tax_id: p.providers.tax_id,
      full_address: p.providers.full_address,
    }));

  return allMatchingProviders;
}

/**
 * Selects providers for an RFQ
 */
export async function selectProvidersForRFQ(
  input: RFQProviderInput[],
): Promise<RFQProvider[]> {
  logger.info("Selecting providers for RFQ:", input);
  const supabase = await createClient();

  const timestamp = new Date().toISOString();
  const providersData = input.map((p) => ({
    ...p,
    // If status is not provided, default to "selected"
    status: p.status || "selected",
    // If selected_at is not provided, set it to current timestamp
    selected_at: p.selected_at || timestamp,
    // Keep invited_at if provided
    invited_at: p.invited_at || null,
    // Add created_at and updated_at if not present
    created_at: p.created_at || timestamp,
    updated_at: timestamp,
  }));

  logger.info("Prepared provider data for upsert:", providersData);

  const { data, error } = await supabase
    .from("rfq_providers")
    .upsert(providersData, { onConflict: "rfq_id,provider_id" })
    .select();

  if (error) {
    logger.error("Error selecting providers for RFQ:", error);
    throw new Error(`Failed to select providers: ${error.message}`);
  }

  return data as RFQProvider[];
}

/**
 * Creates outgoing emails for RFQ providers and sends them
 */
export async function createRFQEmails(
  rfqId: string,
  providerIds: string[],
  emailData: {
    subject: string;
    body: string;
    isCustom: boolean;
    senderInfo?: {
      name?: string;
      phone?: string;
      role?: string;
    };
  },
): Promise<any[]> {
  const supabase = await createClient();

  logger.info(
    `Creating and sending emails for RFQ ${rfqId} to ${providerIds.length} providers`,
  );

  // Get RFQ details for email context
  const { data: rfq, error: rfqError } = await supabase
    .from("rfqs")
    .select(
      `
      id,
      sequence_number,
      origin_city,
      origin_postal_code,
      origin_country_id,
      destination_city,
      destination_postal_code,
      destination_country_id,
      preferred_shipping_date,
      weight,
      quantity,
      equipment_type_id,
      equipment_quantity,
      special_requirements,
      notes,
      countries_origin:origin_country_id (name),
      countries_destination:destination_country_id (name),
      equipment_types:equipment_type_id (name),
      rfq_cargo_types(
        cargo_type_id,
        cargo_types(name)
      )
    `,
    )
    .eq("id", rfqId)
    .single();

  if (rfqError) {
    logger.error(`Error fetching RFQ ${rfqId} details:`, rfqError);
    throw new Error(`Failed to fetch RFQ details: ${rfqError.message}`);
  }

  // Get provider details with their primary contacts
  const { data: providersWithContacts, error: providersError } = await supabase
    .from("providers")
    .select(
      `
      id,
      name,
      provider_contacts(
        id,
        email,
        name,
        is_primary
      )
    `,
    )
    .in("id", providerIds);

  if (providersError) {
    logger.error("Error fetching providers for emails:", providersError);
    throw new Error(
      `Failed to fetch providers for emails: ${providersError.message}`,
    );
  }

  if (!providersWithContacts || providersWithContacts.length === 0) {
    throw new Error("No valid providers found for sending emails");
  }

  // Prepare template data
  const cargoTypes =
    rfq.rfq_cargo_types
      ?.map((ct) => ct.cargo_types?.name)
      .filter(Boolean)
      .join(", ") || "N/A";

  // Use the provided email content directly
  const subject = emailData.subject;
  const body = emailData.body;

  const timestamp = new Date().toISOString();
  const emailRecords = [];

  // Process each provider and their contacts
  for (const provider of providersWithContacts) {
    if (
      !provider.provider_contacts ||
      provider.provider_contacts.length === 0
    ) {
      logger.warn(
        `Provider ${provider.id} (${provider.name}) has no contacts. Skipping email.`,
      );
      continue;
    }

    // Find primary contact first, or use the first contact if no primary is set
    const primaryContact =
      provider.provider_contacts.find((contact) => contact.is_primary) ||
      provider.provider_contacts[0];

    emailRecords.push({
      rfq_id: rfqId,
      provider_id: provider.id,
      subject: subject,
      body: body,
      to_email: primaryContact.email,
      to_name: provider.name,
      old_status: "sent",
      delivery_status: "pending",
      sent_at: timestamp,
      is_ai_generated: false,
      created_at: timestamp,
      updated_at: timestamp,
    });
  }

  // Check if we have any valid email records
  if (emailRecords.length === 0) {
    throw new Error(
      "No valid contacts found for any of the selected providers",
    );
  }

  // Insert email records
  const { data: emails, error: emailsError } = await supabase
    .from("rfq_outgoing_emails")
    .insert(emailRecords)
    .select();

  if (emailsError) {
    logger.error("Error creating RFQ emails:", emailsError);
    throw new Error(`Failed to create RFQ emails: ${emailsError.message}`);
  }

  // Now send the emails using the email service
  try {
    logger.info(`Sending ${emails.length} emails for RFQ ${rfqId}`);

    // Prepare emails for sending
    const emailsToSend = emails.map((email) => ({
      to: {
        email: email.to_email,
        name: email.to_name || "",
      },
      subject: email.subject,
      body: email.body,
      rfqDetails: {
        rfqNumber: rfq.sequence_number || `SF-RFQ-${rfqId.substring(0, 8)}`,
        origin: rfq.origin_city,
        originCountry:
          rfq.countries_origin && typeof rfq.countries_origin === "object"
            ? (rfq.countries_origin as any).name || "Unknown"
            : "Unknown",
        destination: rfq.destination_city,
        destinationCountry:
          rfq.countries_destination &&
          typeof rfq.countries_destination === "object"
            ? (rfq.countries_destination as any).name || "Unknown"
            : "Unknown",
        cargoTypes: cargoTypes,
      },
      senderInfo: emailData.senderInfo,
      isHtml: true,
      trackingId: email.id,
    }));

    // Send emails in bulk and collect responses
    const emailResponses = await retry(
      async () => {
        // Send each email individually using the RFQ template
        const sendPromises = emailsToSend.map((emailData) =>
          emailService.sendRFQEmail(emailData),
        );
        return Promise.all(sendPromises);
      },
      {
        maxRetries: 3,
        initialDelay: 1000,
        shouldRetry: (error) => {
          // Don't retry in sandbox mode to speed up testing
          // We no longer use environment variables for sandbox mode
          // The email service will handle sandbox mode based on database settings
          return false;

          // Normal retry logic for production
          if (error.response) {
            const status = error.response.status;
            return status === 429 || status >= 500;
          }
          return true; // Retry on network errors
        },
        onRetry: (error, attempt) => {
          logger.warn(
            `Retrying email send (attempt ${attempt}) after error:`,
            error,
          );
        },
      },
    );

    // Process email responses to update the database
    const emailUpdates = emails.map((email) => {
      // For each email, we need to store the generated Message-ID and Reply-To
      // These were generated when creating the email, not from the response

      // Generate the values for Message-ID and Reply-To
      // We need to do this again since we don't have access to the values used when sending
      const fromEmail =
        process.env.SENDGRID_FROM_EMAIL || "<EMAIL>";
      const domain = fromEmail.split("@")[1];
      const messageId = `<rfq-${crypto.randomBytes(16).toString("hex")}@${domain}>`;

      // Generate a plus-addressed Reply-To email
      const rfqId = email.rfq_id;
      const replyTo = `${fromEmail.split("@")[0]}+rfq-${rfqId}@${domain}`;

      logger.info(
        `Using Message-ID: ${messageId} and Reply-To: ${replyTo} for email ${email.id}`,
      );

      return {
        id: email.id,
        message_id: messageId, // Store the generated Message-ID for tracking
        reply_to: replyTo,
        delivery_status: "delivered",
        delivered_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
    });

    // Update the email records with sent status and message headers
    // Since we don't have webhooks, we'll set the status to "delivered" directly
    // In a production environment with webhooks, this would be updated by the webhook handler
    for (const update of emailUpdates) {
      await supabase
        .from("rfq_outgoing_emails")
        .update({
          message_id: update.message_id,
          reply_to: update.reply_to,
          delivery_status: update.delivery_status,
          delivered_at: update.delivered_at,
          updated_at: update.updated_at,
        })
        .eq("id", update.id);
    }

    logger.info(`Successfully sent ${emails.length} emails for RFQ ${rfqId}`);
  } catch (sendError) {
    logger.error("Error sending RFQ emails:", sendError);

    // Update the email records with failed status
    await supabase
      .from("rfq_outgoing_emails")
      .update({
        delivery_status: "failed",
        failure_reason:
          sendError instanceof Error ? sendError.message : String(sendError),
        updated_at: new Date().toISOString(),
      })
      .in(
        "id",
        emails.map((email) => email.id),
      );

    // Re-throw the error to be handled by the caller
    throw new Error(
      `Failed to send RFQ emails: ${sendError instanceof Error ? sendError.message : String(sendError)}`,
    );
  }

  return emails;
}

/**
 * Fetches providers for an RFQ
 */
export async function fetchRFQProviders(rfqId: string): Promise<RFQProvider[]> {
  const supabase = await createClient();

  // First, check if the RFQ exists
  const { error: rfqError } = await supabase
    .from("rfqs")
    .select("id")
    .eq("id", rfqId)
    .single();

  if (rfqError) {
    logger.error(`Error fetching RFQ ${rfqId}:`, rfqError);
    throw new Error(`RFQ with ID ${rfqId} not found: ${rfqError.message}`);
  }

  // Fetch RFQ providers with provider details
  const { data, error } = await supabase
    .from("rfq_providers")
    .select(
      `
      id,
      rfq_id,
      provider_id,
      status,
      selected_at,
      invited_at,
      email_opened_at,
      response_received_at,
      notes,
      created_at,
      updated_at,
      provider:providers (
        id,
        name,
        status,
        verified,
        tax_id,
        full_address
      )
    `,
    )
    .eq("rfq_id", rfqId)
    .order('provider(verified)', { ascending: false });

  if (error) {
    logger.error("Error fetching RFQ providers:", error);
    throw new Error(`Failed to fetch RFQ providers: ${error.message}`);
  }

  // Transform the data to ensure provider information is properly structured
  const transformedData = data.map((item) => {
    // If provider is null, add a placeholder to avoid "Unknown Provider"
    if (!item.provider) {
      logger.warn(`Provider with ID ${item.provider_id} not found`);
    }

    return {
      ...item,
      provider: item.provider || null,
    };
  });

  return transformedData as unknown as RFQProvider[];
}

/**
 * Fetches providers for an RFQ with pagination
 *
 * @param rfqId The RFQ ID
 * @param params Pagination parameters
 * @param filterParams Optional filter parameters
 * @returns Paginated providers with count
 */
export async function fetchRFQProvidersWithPagination(
  rfqId: string,
  params: { page: number; pageSize: number },
  filterParams?: { searchQuery?: string; statusFilter?: string },
): Promise<{
  data: RFQProvider[];
  count: number;
  pagination: {
    page: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
  };
}> {
  logger.info(`Fetching paginated RFQ providers for RFQ ID: ${rfqId}`, {
    params,
    filterParams,
  });
  const supabase = await createClient();
  const { page = 1, pageSize = 10 } = params;
  const { searchQuery = "", statusFilter = "all" } = filterParams || {};

  // First, check if the RFQ exists
  const { error: rfqError } = await supabase
    .from("rfqs")
    .select("id")
    .eq("id", rfqId)
    .single();

  if (rfqError) {
    logger.error(`Error fetching RFQ ${rfqId}:`, rfqError);
    throw new Error(`RFQ with ID ${rfqId} not found: ${rfqError.message}`);
  }

  // Calculate range for pagination
  const from = (page - 1) * pageSize;
  const to = from + pageSize - 1;

  // Build the query
  let query = supabase
    .from("rfq_providers")
    .select(
      `
      id,
      rfq_id,
      provider_id,
      status,
      selected_at,
      invited_at,
      email_opened_at,
      response_received_at,
      notes,
      created_at,
      updated_at,
      provider:providers (
        id,
        name,
        status,
        verified,
        tax_id,
        full_address
      )
    `,
      { count: "exact" },
    )
    .eq("rfq_id", rfqId);

  // Get the count first
  const { count, error: countError } = await query;

  if (countError) {
    logger.error("Error counting RFQ providers:", countError);
    throw new Error(`Failed to count RFQ providers: ${countError.message}`);
  }

  // Sort by verification status (verified providers first)
  // Use the correct syntax for ordering by a foreign key field
  query = query.order('provider(verified)', { ascending: false });

  // Apply range for pagination
  query = query.range(from, to);

  // Execute the query
  const { data, error } = await query;

  if (error) {
    logger.error("Error fetching paginated RFQ providers:", error);
    throw new Error(
      `Failed to fetch paginated RFQ providers: ${error.message}`,
    );
  }

  logger.info(
    `Found ${data?.length || 0} paginated RFQ providers for RFQ ID: ${rfqId} (total: ${count})`,
  );

  // Transform the data to ensure provider information is properly structured
  const transformedData = data.map((item) => {
    // If provider is null, add a placeholder to avoid "Unknown Provider"
    if (!item.provider) {
      logger.warn(`Provider with ID ${item.provider_id} not found`);
    }

    return {
      ...item,
      provider: item.provider || null,
    };
  });

  // Calculate total pages
  const totalPages = Math.ceil((count || 0) / pageSize);

  return {
    data: transformedData as unknown as RFQProvider[],
    count: count || 0,
    pagination: {
      page,
      pageSize,
      totalItems: count || 0,
      totalPages,
    },
  };
}

/**
 * Submits a bid for an RFQ
 */
export async function submitRFQBid(input: RFQBidInput): Promise<RFQBid> {
  const supabase = await createClient();

  const timestamp = new Date().toISOString();
  const bidData = {
    ...input,
    currency: "EUR", // Always set to EUR regardless of input
    submitted_at: timestamp,
    is_ai_extracted: false,
  };

  logger.debug(
    "Submitting bid data to Supabase:",
    JSON.stringify(bidData, null, 2),
  );

  let submittedBid: any;

  try {
    const { data, error } = await supabase
      .from("rfq_bids")
      .insert(bidData)
      .select()
      .single();

    if (error) {
      logger.error("Error submitting RFQ bid:", error);
      logger.error("Error details:", JSON.stringify(error, null, 2));
      throw new Error(`Failed to submit bid: ${error.message}`);
    }

    submittedBid = data;
    logger.debug(
      "Successfully submitted bid:",
      JSON.stringify(submittedBid, null, 2),
    );
  } catch (insertError) {
    logger.error("Exception during bid submission:", insertError);
    throw insertError;
  }

  try {
    // Update the provider status to bid_submitted
    const { error: updateError } = await supabase
      .from("rfq_providers")
      .update({
        status: "bid_submitted",
        response_received_at: timestamp,
      })
      .eq("rfq_id", input.rfq_id)
      .eq("provider_id", input.provider_id);

    if (updateError) {
      logger.error("Error updating provider status:", updateError);
      // Continue even if this fails, as the bid was already submitted
    }
  } catch (updateError) {
    logger.error("Exception updating provider status:", updateError);
    // Continue even if this fails, as the bid was already submitted
  }

  return submittedBid as RFQBid;
}

/**
 * Fetches bids for an RFQ
 */
export async function fetchRFQBids(
  rfqId: string,
  providerId?: string,
): Promise<RFQBid[]> {
  const supabase = await createClient();

  // Start building the query
  let query = supabase
    .from("rfq_bids")
    .select(
      `
      *,
      providers (
        id,
        name
      )
    `,
    )
    .eq("rfq_id", rfqId);

  // If providerId is specified, filter by provider_id
  if (providerId) {
    logger.info(`Filtering by provider_id: ${providerId}`);
    query = query.eq("provider_id", providerId);
  }

  const { data, error } = await query;

  if (error) {
    logger.error("Error fetching RFQ bids:", error);
    throw new Error(`Failed to fetch RFQ bids: ${error.message}`);
  }

  // Transform the data to ensure consistent structure
  const transformedData =
    data?.map((bid) => {
      // Extract provider info if available
      const providerName = bid.providers?.name || "Unknown Provider";

      // Create a clean bid object without the nested providers object
      const { providers, ...cleanBid } = bid;

      // Add provider_name as a flat property
      return {
        ...cleanBid,
        provider_name: providerName,
      };
    }) || [];

  return transformedData as unknown as RFQBid[];
}

/**
 * Fetches outgoing emails for an RFQ
 */
export async function fetchRFQEmails(rfqId: string): Promise<any[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from("rfq_outgoing_emails")
    .select(
      `
      *,
      provider:providers (
        id,
        name
      )
    `,
    )
    .eq("rfq_id", rfqId)
    .order("sent_at", { ascending: false });

  if (error) {
    logger.error("Error fetching RFQ emails:", error);
    throw new Error(`Failed to fetch RFQ emails: ${error.message}`);
  }

  return data;
}

/**
 * Provider Selection Service
 *
 * Centralized business logic for provider categorization and selection.
 * Simplifies the complex provider state management into clear categories.
 */

/**
 * Provider Categories (Business Logic)
 *
 * 1. **Available Providers**: All providers not yet engaged with the RFQ
 * 2. **Matched Providers**: Auto-matched providers (status="matched", no invited_at)
 * 3. **Invited Providers**: Providers with invited_at timestamp or engagement status
 * 4. **Manual Providers**: UI-only selections (temporary state until invited)
 */

/**
 * Checks if a provider has been invited for an RFQ
 *
 * Business Logic - Provider Engagement:
 * A provider is considered "invited" (engaged) if they have been interacted with beyond auto-matching.
 * This includes having an invitation timestamp, engagement statuses, or having submitted a bid.
 */
export async function isProviderInvited(
  rfqProviders: {
    provider_id: string;
    status: string;
    invited_at?: string | null;
    bid?: any;
  }[],
  providerId: string,
): Promise<boolean> {
  const rfqProvider = rfqProviders.find((p) => p.provider_id === providerId);

  // If no rfqProvider record exists, the provider hasn't been invited
  if (!rfqProvider) return false;

  // Consider a provider invited if:
  // 1. They have an invited_at timestamp (definitive indicator)
  // 2. Their status indicates engagement: invited, declined, bid_submitted
  // 3. They have a bid (regardless of status)
  // Explicitly exclude providers with only "matched" status and no other engagement
  const hasInvitedAt = rfqProvider.invited_at !== null && rfqProvider.invited_at !== undefined;
  const hasInvitedStatus = ["invited", "declined", "bid_submitted"].includes(rfqProvider.status);
  const hasBid = rfqProvider.bid !== null && rfqProvider.bid !== undefined;

  // A provider with only "matched" status and no invited_at timestamp or bid is not considered invited
  return hasInvitedAt || hasInvitedStatus || hasBid;
}

/**
 * Unified Provider Selection Data Service
 *
 * Consolidates all provider selection logic into a single service function.
 * Returns categorized providers ready for UI consumption.
 */
export async function getProviderSelectionData(
  rfqId: string,
  options: {
    searchQuery?: string;
    statusFilter?: string;
    includeManualProviders?: any[];
  } = {}
): Promise<{
  availableProviders: any[];
  invitedProviders: any[];
  rfqProviders: any[];
  totalCount: number;
}> {
  const { searchQuery = "", statusFilter = "all", includeManualProviders = [] } = options;

  try {
    logger.info("Fetching unified provider selection data", { rfqId, searchQuery, statusFilter });

    // 1. Get existing RFQ providers with bids
    const rfqProvidersResult = await fetchRFQProviders(rfqId);
    const bidsResult = await fetchRFQBids(rfqId);

    // Merge providers with bids
    const rfqProviders = await mergeProvidersWithBids(
      rfqProvidersResult,
      bidsResult
    );

    // 2. Get invited providers (those who have been engaged)
    const invitedProviders = await getInvitedProviders(rfqProviders);

    // 3. Get matched providers for selection (providers that match RFQ criteria)
    const matchedProviders = await matchProvidersForRFQ(rfqId);

    // 4. Transform matched providers to the expected format and filter available ones
    const transformedMatchedProviders = matchedProviders.map(provider => ({
      id: `matched-${provider.id}`,
      rfq_id: rfqId,
      provider_id: provider.id,
      status: "matched" as const,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      provider: {
        id: provider.id,
        name: provider.name,
        status: provider.status,
        verified: provider.verified,
        tax_id: provider.tax_id,
        full_address: provider.full_address,
      },
      provider_name: provider.name,
      provider_status: provider.status,
      provider_verified: provider.verified,
      provider_tax_id: provider.tax_id,
      provider_full_address: provider.full_address,
      rfq_status: "matched",
    }));

    // Filter available providers (exclude invited ones)
    const availableProviders = transformedMatchedProviders.filter(provider => {
      // Check if provider is already invited/engaged using the service function
      const rfqProvider = rfqProviders.find(rp => rp.provider_id === provider.provider_id);
      if (!rfqProvider) return true; // Provider not in RFQ yet, available for selection

      // Use the same logic as isProviderInvited
      const hasInvitedAt = rfqProvider.invited_at !== null && rfqProvider.invited_at !== undefined;
      const hasInvitedStatus = ["invited", "declined", "bid_submitted"].includes(rfqProvider.status);
      const hasBid = rfqProvider.bid !== null && rfqProvider.bid !== undefined;

      const isEngaged = hasInvitedAt || hasInvitedStatus || hasBid;
      return !isEngaged;
    });

    // 5. Add manual providers to available list (if provided)
    const combinedAvailableProviders = [...availableProviders];
    if (includeManualProviders.length > 0) {
      // Transform manual providers to match expected format
      const transformedManualProviders = includeManualProviders.map(provider => ({
        id: `manual-${provider.id}`,
        rfq_id: rfqId,
        provider_id: provider.id,
        status: "matched" as const,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        provider: {
          id: provider.id,
          name: provider.name,
          status: provider.status,
          verified: provider.verified,
          tax_id: provider.tax_id || null,
          full_address: provider.full_address || null,
        },
        provider_name: provider.name,
        provider_status: provider.status,
        provider_verified: provider.verified,
        provider_tax_id: provider.tax_id || null,
        provider_full_address: provider.full_address || null,
        rfq_status: "manually_added",
      }));

      // Add manual providers that don't already exist
      transformedManualProviders.forEach(manualProvider => {
        const exists = combinedAvailableProviders.some(
          p => p.provider_id === manualProvider.provider_id
        );
        if (!exists) {
          combinedAvailableProviders.push(manualProvider);
        }
      });
    }

    const result = {
      availableProviders: combinedAvailableProviders,
      invitedProviders,
      rfqProviders,
      totalCount: combinedAvailableProviders.length,
    };

    logger.info("Provider selection data fetched successfully", {
      available: result.availableProviders.length,
      invited: result.invitedProviders.length,
      total: result.totalCount,
    });

    return result;
  } catch (error) {
    logger.error("Error fetching provider selection data:", error);
    throw error;
  }
}

/**
 * Checks if a provider has submitted a bid for an RFQ
 *
 * This is a specialized check for bid submission status.
 */
export async function hasProviderSubmittedBid(
  rfqProviders: {
    provider_id: string;
    status: string;
  }[],
  providerId: string,
): Promise<boolean> {
  const rfqProvider = rfqProviders.find((p) => p.provider_id === providerId);
  return rfqProvider?.status === "bid_submitted";
}

/**
 * Filters providers based on search query and status filter
 * Excludes providers that have already been invited
 */
export async function filterProviders<
  T extends {
    id: string;
    name: string;
    status: string;
    verified: boolean;
    tax_id?: string | null;
  },
>(
  providers: T[],
  rfqProviders: {
    provider_id: string;
    status: string;
    invited_at?: string | null;
  }[],
  searchQuery: string,
  statusFilter: string,
): Promise<T[]> {
  const filteredProviders: T[] = [];

  for (const provider of providers) {
    // Skip providers that have already been invited
    const isInvited = await isProviderInvited(rfqProviders, provider.id);
    if (isInvited) {
      continue;
    }

    const matchesSearch =
      provider.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (provider.tax_id &&
        provider.tax_id.toLowerCase().includes(searchQuery.toLowerCase()));

    let shouldInclude = false;
    if (statusFilter === "all") shouldInclude = !!matchesSearch;
    else if (statusFilter === "verified")
      shouldInclude = !!matchesSearch && !!provider.verified;
    else if (statusFilter === "unverified")
      shouldInclude = !!matchesSearch && !provider.verified;
    else shouldInclude = !!matchesSearch && provider.status === statusFilter;

    if (shouldInclude) {
      filteredProviders.push(provider);
    }
  }

  return filteredProviders;
}

/**
 * Gets invited providers for an RFQ
 *
 * Filters RFQ providers to show only those that have been invited.
 * Uses the same business logic as isProviderInvited function for consistency.
 */
export async function getInvitedProviders<
  T extends {
    provider_id: string;
    status: string;
    invited_at?: string | null;
    bid?: any;
    provider?: any;
  },
>(rfqProviders: T[]): Promise<T[]> {
  // Filter providers to show those that have been invited for this RFQ
  return rfqProviders.filter((rfqProvider) => {
    // Make sure the provider object exists
    if (!rfqProvider.provider) {
      logger.warn(
        `Provider object missing for provider_id: ${rfqProvider.provider_id}`,
      );
      return false;
    }

    // Use the same logic as isProviderInvited function for consistency
    // Consider a provider invited if:
    // 1. They have an invited_at timestamp (definitive indicator)
    // 2. Their status indicates engagement: invited, declined, bid_submitted
    // 3. They have a bid (regardless of status)
    const hasInvitedAt = rfqProvider.invited_at !== null && rfqProvider.invited_at !== undefined;
    const hasInvitedStatus = ["invited", "declined", "bid_submitted"].includes(rfqProvider.status);
    const hasBid = rfqProvider.bid !== null && rfqProvider.bid !== undefined;

    return hasInvitedAt || hasInvitedStatus || hasBid;
  });
}

/**
 * Merges RFQ providers with their bids
 */
export async function mergeProvidersWithBids<
  T extends { provider_id: string },
  B extends { provider_id: string },
>(providers: T[], bids: B[]): Promise<(T & { bid?: B })[]> {
  // Create a map of provider IDs to bids
  const bidsByProviderId = new Map<string, B>();
  bids.forEach((bid) => {
    bidsByProviderId.set(bid.provider_id, bid);
  });

  // Merge bids with providers
  return providers.map((provider) => {
    const bid = bidsByProviderId.get(provider.provider_id);
    return {
      ...provider,
      bid: bid,
    };
  });
}

/**
 * Updates an existing RFQ bid
 */
export async function updateRFQBid(input: UpdateRFQBidInput): Promise<RFQBid> {
  const supabase = await createClient();

  logger.debug(`Updating bid with ID: ${input.id}`);

  const timestamp = new Date().toISOString();

  // First, get the current bid to ensure it exists
  const { data: existingBid, error: fetchError } = await supabase
    .from("rfq_bids")
    .select("*")
    .eq("id", input.id)
    .single();

  if (fetchError) {
    logger.error(`Error fetching bid ${input.id}:`, fetchError);
    throw new Error(`Bid with ID ${input.id} not found: ${fetchError.message}`);
  }

  // Prepare update data
  const updateData = {
    ...input,
    updated_at: timestamp,
  };

  // Remove id from update data as it's used in the where clause
  const { id, ...dataToUpdate } = updateData;

  // Update the bid
  const { data, error } = await supabase
    .from("rfq_bids")
    .update(dataToUpdate)
    .eq("id", input.id)
    .select()
    .single();

  if (error) {
    logger.error(`Error updating bid ${input.id}:`, error);
    throw new Error(`Failed to update bid: ${error.message}`);
  }

  logger.debug(`Successfully updated bid ${input.id}`);

  return data as RFQBid;
}
