import { StructuredAddress } from "./types";
import { createLogger } from "@/lib/utils/logger/logger";
// The Google Maps types will be available globally after installing @types/google.maps

const logger = createLogger("GoogleMaps");

const GOOGLE_MAPS_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY!;

if (!GOOGLE_MAPS_API_KEY) {
  throw new Error("NEXT_PUBLIC_GOOGLE_MAPS_API_KEY is not defined");
}

export class GoogleMapsAPIError extends Error {
  constructor(
    message: string,
    public status?: string,
  ) {
    super(message);
    this.name = "GoogleMapsAPIError";
  }
}

/**
 * Extracts structured address components from a Google Maps Place result
 * @param place The Google Maps Place result
 * @returns Structured address components
 */
export function extractPlaceComponents(
  place: google.maps.places.PlaceResult,
): StructuredAddress {
  const components = place.address_components || [];

  // Initialize the structured address
  const structuredAddress: StructuredAddress = {
    street_address: place.formatted_address || "",
  };

  // Extract street address
  const streetNumber =
    components.find((c: google.maps.GeocoderAddressComponent) => c.types.includes("street_number"))?.long_name || "";
  const route =
    components.find((c: google.maps.GeocoderAddressComponent) => c.types.includes("route"))?.long_name || "";

  if (streetNumber || route) {
    structuredAddress.street_address = `${streetNumber} ${route}`.trim();
  }

  // Extract other components
  structuredAddress.city = components.find(
    (c: google.maps.GeocoderAddressComponent) =>
      c.types.includes("locality") || c.types.includes("sublocality_level_1"),
  )?.long_name;

  structuredAddress.state = components.find((c: google.maps.GeocoderAddressComponent) =>
    c.types.includes("administrative_area_level_1"),
  )?.long_name;

  structuredAddress.postal_code = components.find((c: google.maps.GeocoderAddressComponent) =>
    c.types.includes("postal_code"),
  )?.long_name;

  const country = components.find((c: google.maps.GeocoderAddressComponent) => c.types.includes("country"));
  if (country) {
    structuredAddress.country = country.long_name;
    structuredAddress.country_code = country.short_name;
  }

  // Extract coordinates if available
  if (place.geometry?.location) {
    structuredAddress.lat = place.geometry.location.lat();
    structuredAddress.lng = place.geometry.location.lng();
  }

  return structuredAddress;
}

/**
 * Autocompletes an address using Google Maps Places API
 * This function is meant to be used client-side with the Google Maps JavaScript API
 * @param input The input element to attach the autocomplete to
 * @param callback The callback function to call when an address is selected
 */
export function initAutocomplete(
  input: HTMLInputElement,
  callback: (place: google.maps.places.PlaceResult) => void,
): google.maps.places.Autocomplete | null {
  // This function should only be called client-side
  if (typeof window === "undefined" || !window.google || !window.google.maps) {
    logger.error("Google Maps JavaScript API not loaded");
    return null;
  }

  try {
    const autocomplete = new google.maps.places.Autocomplete(input, {
      types: ["address"],
      fields: [
        "address_components",
        "formatted_address",
        "geometry",
        "name",
        "place_id",
      ],
    });

    autocomplete.addListener("place_changed", () => {
      const place = autocomplete.getPlace();
      if (place && place.formatted_address) {
        callback(place);
      }
    });

    return autocomplete;
  } catch (error) {
    logger.error("Error initializing Google Maps Autocomplete:", error);
    return null;
  }
}
