// Type for structured address components
export interface StructuredAddress {
  street_address: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  country_code?: string;
  lat?: number;
  lng?: number;
}

// ==========================================
// Coordinate Resolution Types (PRD Task 1.4)
// ==========================================

/**
 * Result of coordinate resolution for a single address
 */
export interface CoordinateResolutionResult {
  /** Whether the geocoding was successful */
  success: boolean;
  /** Resolved latitude coordinate */
  lat?: number;
  /** Resolved longitude coordinate */
  lng?: number;
  /** Formatted address returned by Google Maps */
  formattedAddress?: string;
  /** Error message if geocoding failed */
  error?: string;
  /** Google Maps place ID for the resolved location */
  placeId?: string;
  /** Confidence level of the geocoding result */
  confidence?: 'ROOFTOP' | 'RANGE_INTERPOLATED' | 'GEOMETRIC_CENTER' | 'APPROXIMATE';
}

/**
 * Cached coordinates with metadata
 */
export interface CachedCoordinates {
  /** Latitude coordinate */
  lat: number;
  /** Longitude coordinate */
  lng: number;
  /** When the coordinates were resolved */
  resolvedAt: Date;
  /** Hash of the address used to resolve these coordinates */
  addressHash: string;
  /** Formatted address from geocoding */
  formattedAddress?: string;
}

/**
 * Input for batch coordinate resolution
 */
export interface AddressBatchInput {
  /** Unique identifier for this address in the batch */
  id: string;
  /** Full address string to geocode */
  address: string;
  /** Optional country code to improve geocoding accuracy */
  countryCode?: string;
}

/**
 * Result of batch coordinate resolution
 */
export interface BatchCoordinateResolutionResult {
  /** Results for each address in the batch */
  results: Array<{
    /** ID from the input */
    id: string;
    /** Coordinate resolution result */
    result: CoordinateResolutionResult;
  }>;
  /** Overall success rate of the batch */
  successRate: number;
  /** Total number of addresses processed */
  totalProcessed: number;
  /** Number of successful resolutions */
  successfulResolutions: number;
}

/**
 * Address change detection interface
 */
export interface AddressChangeDetection {
  /** Current hash of address components */
  currentHash: string;
  /** Previous hash (if any) */
  previousHash?: string;
  /** Whether the address has changed */
  hasChanged: boolean;
  /** Timestamp of the change detection */
  checkedAt: Date;
}

// ==========================================
// Distance Calculation Types (PRD Task 1.4)
// ==========================================

/**
 * Route coordinates for distance calculation
 */
export interface RouteCoordinates {
  /** Origin coordinates */
  origin: {
    lat: number;
    lng: number;
  };
  /** Destination coordinates */
  destination: {
    lat: number;
    lng: number;
  };
}

/**
 * Distance calculation result for a single route
 */
export interface DistanceCalculationResult {
  /** Whether the calculation was successful */
  success: boolean;
  /** Distance in kilometers */
  distanceKm?: number;
  /** Distance in meters (raw from API) */
  distanceMeters?: number;
  /** Duration in seconds */
  durationSeconds?: number;
  /** Human-readable distance text */
  distanceText?: string;
  /** Human-readable duration text */
  durationText?: string;
  /** Error message if calculation failed */
  error?: string;
  /** Status from Distance Matrix API */
  status?: string;
}

/**
 * Input for batch distance calculation
 */
export interface DistanceBatchInput {
  /** Unique identifier for this route in the batch */
  id: string;
  /** Route coordinates */
  route: RouteCoordinates;
}

/**
 * Result of batch distance calculation
 */
export interface BatchDistanceCalculationResult {
  /** Results for each route in the batch */
  results: Array<{
    /** ID from the input */
    id: string;
    /** Distance calculation result */
    result: DistanceCalculationResult;
  }>;
  /** Overall success rate of the batch */
  successRate: number;
  /** Total number of routes processed */
  totalProcessed: number;
  /** Number of successful calculations */
  successfulCalculations: number;
}

/**
 * Google Maps Distance Matrix API response types
 */
export interface DistanceMatrixResponse {
  status: string;
  origin_addresses: string[];
  destination_addresses: string[];
  rows: Array<{
    elements: Array<{
      status: string;
      distance?: {
        text: string;
        value: number;
      };
      duration?: {
        text: string;
        value: number;
      };
    }>;
  }>;
}
