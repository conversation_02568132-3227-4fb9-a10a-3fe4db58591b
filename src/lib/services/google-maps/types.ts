// Type for structured address components
export interface StructuredAddress {
  street_address: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  country_code?: string;
  lat?: number;
  lng?: number;
}

// ==========================================
// Coordinate Resolution Types (PRD Task 1.4)
// ==========================================

/**
 * Result of coordinate resolution for a single address
 */
export interface CoordinateResolutionResult {
  /** Whether the geocoding was successful */
  success: boolean;
  /** Resolved latitude coordinate */
  lat?: number;
  /** Resolved longitude coordinate */
  lng?: number;
  /** Formatted address returned by Google Maps */
  formattedAddress?: string;
  /** Error message if geocoding failed */
  error?: string;
  /** Google Maps place ID for the resolved location */
  placeId?: string;
  /** Confidence level of the geocoding result */
  confidence?: 'ROOFTOP' | 'RANGE_INTERPOLATED' | 'GEOMETRIC_CENTER' | 'APPROXIMATE';
}

/**
 * Cached coordinates with metadata
 */
export interface CachedCoordinates {
  /** Latitude coordinate */
  lat: number;
  /** Longitude coordinate */
  lng: number;
  /** When the coordinates were resolved */
  resolvedAt: Date;
  /** Hash of the address used to resolve these coordinates */
  addressHash: string;
  /** Formatted address from geocoding */
  formattedAddress?: string;
}

/**
 * Input for batch coordinate resolution
 */
export interface AddressBatchInput {
  /** Unique identifier for this address in the batch */
  id: string;
  /** Full address string to geocode */
  address: string;
  /** Optional country code to improve geocoding accuracy */
  countryCode?: string;
}

/**
 * Result of batch coordinate resolution
 */
export interface BatchCoordinateResolutionResult {
  /** Results for each address in the batch */
  results: Array<{
    /** ID from the input */
    id: string;
    /** Coordinate resolution result */
    result: CoordinateResolutionResult;
  }>;
  /** Overall success rate of the batch */
  successRate: number;
  /** Total number of addresses processed */
  totalProcessed: number;
  /** Number of successful resolutions */
  successfulResolutions: number;
}

/**
 * Address change detection interface
 */
export interface AddressChangeDetection {
  /** Current hash of address components */
  currentHash: string;
  /** Previous hash (if any) */
  previousHash?: string;
  /** Whether the address has changed */
  hasChanged: boolean;
  /** Timestamp of the change detection */
  checkedAt: Date;
}
