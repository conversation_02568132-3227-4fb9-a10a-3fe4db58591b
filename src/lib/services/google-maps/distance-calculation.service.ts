"use server";

import { createLogger } from "@/lib/utils/logger/logger";
import { retry } from "@/lib/utils/retry";
import { validateCoordinates } from "./coordinate-resolution.service";
import {
  DistanceCalculationResult,
  RouteCoordinates,
  DistanceBatchInput,
  BatchDistanceCalculationResult,
  DistanceMatrixResponse,
} from "./types";

const logger = createLogger("DistanceCalculationService");

const GOOGLE_MAPS_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY!;

if (!GOOGLE_MAPS_API_KEY) {
  throw new Error("NEXT_PUBLIC_GOOGLE_MAPS_API_KEY is not defined");
}

/**
 * Error class for distance calculation failures
 */
export class DistanceCalculationError extends Error {
  constructor(
    message: string,
    public status?: string,
    public code?: string,
  ) {
    super(message);
    this.name = "DistanceCalculationError";
  }
}

/**
 * Validates route coordinates before distance calculation
 * @param route Route coordinates to validate
 * @returns True if coordinates are valid
 */
export function validateRouteCoordinates(route: RouteCoordinates): boolean {
  return (
    validateCoordinates(route.origin.lat, route.origin.lng) &&
    validateCoordinates(route.destination.lat, route.destination.lng)
  );
}

/**
 * Calculates distance for a single route using Google Maps Distance Matrix API
 * @param route Route coordinates (origin and destination)
 * @param options Calculation options
 * @returns Promise resolving to distance calculation result
 */
export async function calculateDistance(
  route: RouteCoordinates,
  options: {
    /** Travel mode (default: driving) */
    mode?: "driving" | "walking" | "bicycling" | "transit";
    /** Units (default: metric) */
    units?: "metric" | "imperial";
    /** Avoid tolls */
    avoidTolls?: boolean;
    /** Avoid highways */
    avoidHighways?: boolean;
    /** Avoid ferries */
    avoidFerries?: boolean;
  } = {},
): Promise<DistanceCalculationResult> {
  try {
    // Validate coordinates before making API call
    if (!validateRouteCoordinates(route)) {
      logger.warn("Invalid coordinates provided for distance calculation", route);
      return {
        success: false,
        error: "Invalid coordinates provided",
      };
    }

    const { mode = "driving", units = "metric", avoidTolls = false, avoidHighways = false, avoidFerries = false } = options;

    logger.debug(`Calculating distance for route: ${route.origin.lat},${route.origin.lng} to ${route.destination.lat},${route.destination.lng}`);

    const result = await retry(
      async () => {
        const url = new URL("https://maps.googleapis.com/maps/api/distancematrix/json");

        // Set required parameters
        url.searchParams.set("origins", `${route.origin.lat},${route.origin.lng}`);
        url.searchParams.set("destinations", `${route.destination.lat},${route.destination.lng}`);
        url.searchParams.set("key", GOOGLE_MAPS_API_KEY);

        // Set optional parameters
        url.searchParams.set("mode", mode);
        url.searchParams.set("units", units);

        // Set avoidance parameters
        const avoid: string[] = [];
        if (avoidTolls) avoid.push("tolls");
        if (avoidHighways) avoid.push("highways");
        if (avoidFerries) avoid.push("ferries");
        if (avoid.length > 0) {
          url.searchParams.set("avoid", avoid.join("|"));
        }

        const response = await fetch(url.toString());

        if (!response.ok) {
          throw new DistanceCalculationError(
            `HTTP error: ${response.status} ${response.statusText}`,
            response.status.toString(),
          );
        }

        return response.json() as Promise<DistanceMatrixResponse>;
      },
      {
        maxRetries: 3,
        initialDelay: 1000,
        shouldRetry: (error) => {
          // Retry on network errors and server errors, but not on client errors
          if (error instanceof DistanceCalculationError) {
            const status = parseInt(error.status || "0");
            return status === 429 || status >= 500;
          }
          return true; // Retry on network errors
        },
        onRetry: (error, attempt) => {
          logger.warn(
            `Retrying distance calculation (attempt ${attempt}) for route: ${route.origin.lat},${route.origin.lng} to ${route.destination.lat},${route.destination.lng}`,
            error,
          );
        },
      },
    );

    // Parse Google Maps Distance Matrix API response
    if (result.status !== "OK") {
      logger.warn(`Distance calculation failed, API status: ${result.status}`);
      return {
        success: false,
        error: `Distance Matrix API error: ${result.status}`,
        status: result.status,
      };
    }

    if (!result.rows || result.rows.length === 0) {
      logger.warn("No rows in Distance Matrix API response");
      return {
        success: false,
        error: "No route data returned from Distance Matrix API",
      };
    }

    const element = result.rows[0]?.elements?.[0];
    if (!element) {
      logger.warn("No element in Distance Matrix API response");
      return {
        success: false,
        error: "No route element returned from Distance Matrix API",
      };
    }

    if (element.status !== "OK") {
      logger.warn(`Distance calculation element failed, status: ${element.status}`);
      return {
        success: false,
        error: `Route calculation failed: ${element.status}`,
        status: element.status,
      };
    }

    if (!element.distance || !element.duration) {
      logger.warn("Missing distance or duration in API response");
      return {
        success: false,
        error: "Missing distance or duration data in API response",
      };
    }

    // Convert meters to kilometers
    const distanceKm = element.distance.value / 1000;

    logger.info(`Successfully calculated distance: ${distanceKm}km for route`);

    return {
      success: true,
      distanceKm,
      distanceMeters: element.distance.value,
      durationSeconds: element.duration.value,
      distanceText: element.distance.text,
      durationText: element.duration.text,
      status: element.status,
    };
  } catch (error) {
    logger.error("Error calculating distance for route", error);

    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Calculates distances for multiple routes in batch
 * @param routes Array of routes to calculate distances for
 * @param options Batch processing and calculation options
 * @returns Promise resolving to batch distance calculation result
 */
export async function batchCalculateDistances(
  routes: DistanceBatchInput[],
  options: {
    /** Maximum number of concurrent requests */
    concurrency?: number;
    /** Delay between batches in milliseconds */
    batchDelay?: number;
    /** Travel mode (default: driving) */
    mode?: "driving" | "walking" | "bicycling" | "transit";
    /** Units (default: metric) */
    units?: "metric" | "imperial";
    /** Avoid tolls */
    avoidTolls?: boolean;
    /** Avoid highways */
    avoidHighways?: boolean;
    /** Avoid ferries */
    avoidFerries?: boolean;
  } = {},
): Promise<BatchDistanceCalculationResult> {
  const { concurrency = 5, batchDelay = 100, ...calculationOptions } = options;

  logger.info(`Starting batch distance calculation for ${routes.length} routes`);

  const results: BatchDistanceCalculationResult["results"] = [];
  let successfulCalculations = 0;

  // Process routes in batches to respect API rate limits
  for (let i = 0; i < routes.length; i += concurrency) {
    const batch = routes.slice(i, i + concurrency);

    logger.debug(`Processing batch ${Math.floor(i / concurrency) + 1} with ${batch.length} routes`);

    // Process current batch concurrently
    const batchPromises = batch.map(async (routeInput) => {
      const result = await calculateDistance(routeInput.route, calculationOptions);

      if (result.success) {
        successfulCalculations++;
      }

      return {
        id: routeInput.id,
        result,
      };
    });

    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);

    // Add delay between batches to respect rate limits
    if (i + concurrency < routes.length && batchDelay > 0) {
      await new Promise((resolve) => setTimeout(resolve, batchDelay));
    }
  }

  const successRate = routes.length > 0 ? successfulCalculations / routes.length : 0;

  logger.info(
    `Batch distance calculation completed: ${successfulCalculations}/${routes.length} successful (${Math.round(successRate * 100)}%)`,
  );

  return {
    results,
    successRate,
    totalProcessed: routes.length,
    successfulCalculations,
  };
}

/**
 * Optimized batch distance calculation using Distance Matrix API's native batch capability
 * This function can handle up to 25 origins × 25 destinations per request (API limit)
 * @param routes Array of routes to calculate distances for
 * @param options Calculation options
 * @returns Promise resolving to batch distance calculation result
 */
export async function optimizedBatchCalculateDistances(
  routes: DistanceBatchInput[],
  options: {
    /** Maximum routes per API request (max 25 due to API limits) */
    batchSize?: number;
    /** Delay between API requests in milliseconds */
    requestDelay?: number;
    /** Travel mode (default: driving) */
    mode?: "driving" | "walking" | "bicycling" | "transit";
    /** Units (default: metric) */
    units?: "metric" | "imperial";
    /** Avoid tolls */
    avoidTolls?: boolean;
    /** Avoid highways */
    avoidHighways?: boolean;
    /** Avoid ferries */
    avoidFerries?: boolean;
  } = {},
): Promise<BatchDistanceCalculationResult> {
  const { batchSize = 10, requestDelay = 200, ...calculationOptions } = options;

  logger.info(`Starting optimized batch distance calculation for ${routes.length} routes`);

  const results: BatchDistanceCalculationResult["results"] = [];
  let successfulCalculations = 0;

  // Process routes in batches using Distance Matrix API's native batch capability
  for (let i = 0; i < routes.length; i += batchSize) {
    const batch = routes.slice(i, i + batchSize);

    logger.debug(`Processing optimized batch ${Math.floor(i / batchSize) + 1} with ${batch.length} routes`);

    try {
      const batchResult = await calculateDistanceBatch(batch, calculationOptions);
      results.push(...batchResult.results);
      successfulCalculations += batchResult.successfulCalculations;
    } catch (error) {
      logger.error(`Error processing batch ${Math.floor(i / batchSize) + 1}:`, error);

      // Add failed results for this batch
      batch.forEach((routeInput) => {
        results.push({
          id: routeInput.id,
          result: {
            success: false,
            error: error instanceof Error ? error.message : "Batch processing error",
          },
        });
      });
    }

    // Add delay between API requests to respect rate limits
    if (i + batchSize < routes.length && requestDelay > 0) {
      await new Promise((resolve) => setTimeout(resolve, requestDelay));
    }
  }

  const successRate = routes.length > 0 ? successfulCalculations / routes.length : 0;

  logger.info(
    `Optimized batch distance calculation completed: ${successfulCalculations}/${routes.length} successful (${Math.round(successRate * 100)}%)`,
  );

  return {
    results,
    successRate,
    totalProcessed: routes.length,
    successfulCalculations,
  };
}

/**
 * Helper function for optimized batch calculation using Distance Matrix API's native batch capability
 * @param routes Array of routes for a single API request
 * @param options Calculation options
 * @returns Promise resolving to batch result
 */
async function calculateDistanceBatch(
  routes: DistanceBatchInput[],
  options: {
    mode?: "driving" | "walking" | "bicycling" | "transit";
    units?: "metric" | "imperial";
    avoidTolls?: boolean;
    avoidHighways?: boolean;
    avoidFerries?: boolean;
  } = {},
): Promise<BatchDistanceCalculationResult> {
  const { mode = "driving", units = "metric", avoidTolls = false, avoidHighways = false, avoidFerries = false } = options;

  // Validate all coordinates first
  const validRoutes = routes.filter((route) => validateRouteCoordinates(route.route));
  if (validRoutes.length === 0) {
    return {
      results: routes.map((route) => ({
        id: route.id,
        result: {
          success: false,
          error: "Invalid coordinates provided",
        },
      })),
      successRate: 0,
      totalProcessed: routes.length,
      successfulCalculations: 0,
    };
  }

  // Prepare origins and destinations for Distance Matrix API
  const origins = validRoutes.map((route) => `${route.route.origin.lat},${route.route.origin.lng}`);
  const destinations = validRoutes.map((route) => `${route.route.destination.lat},${route.route.destination.lng}`);

  const result = await retry(
    async () => {
      const url = new URL("https://maps.googleapis.com/maps/api/distancematrix/json");

      // Set parameters
      url.searchParams.set("origins", origins.join("|"));
      url.searchParams.set("destinations", destinations.join("|"));
      url.searchParams.set("key", GOOGLE_MAPS_API_KEY);
      url.searchParams.set("mode", mode);
      url.searchParams.set("units", units);

      // Set avoidance parameters
      const avoid: string[] = [];
      if (avoidTolls) avoid.push("tolls");
      if (avoidHighways) avoid.push("highways");
      if (avoidFerries) avoid.push("ferries");
      if (avoid.length > 0) {
        url.searchParams.set("avoid", avoid.join("|"));
      }

      const response = await fetch(url.toString());

      if (!response.ok) {
        throw new DistanceCalculationError(
          `HTTP error: ${response.status} ${response.statusText}`,
          response.status.toString(),
        );
      }

      return response.json() as Promise<DistanceMatrixResponse>;
    },
    {
      maxRetries: 3,
      initialDelay: 1000,
      shouldRetry: (error) => {
        if (error instanceof DistanceCalculationError) {
          const status = parseInt(error.status || "0");
          return status === 429 || status >= 500;
        }
        return true;
      },
    },
  );

  // Process results
  const results: BatchDistanceCalculationResult["results"] = [];
  let successfulCalculations = 0;

  validRoutes.forEach((route, index) => {
    const element = result.rows?.[index]?.elements?.[index];

    if (element && element.status === "OK" && element.distance && element.duration) {
      const distanceKm = element.distance.value / 1000;

      results.push({
        id: route.id,
        result: {
          success: true,
          distanceKm,
          distanceMeters: element.distance.value,
          durationSeconds: element.duration.value,
          distanceText: element.distance.text,
          durationText: element.duration.text,
          status: element.status,
        },
      });

      successfulCalculations++;
    } else {
      results.push({
        id: route.id,
        result: {
          success: false,
          error: element ? `Route calculation failed: ${element.status}` : "No result data",
          status: element?.status,
        },
      });
    }
  });

  // Add results for invalid routes
  const invalidRoutes = routes.filter((route) => !validateRouteCoordinates(route.route));
  invalidRoutes.forEach((route) => {
    results.push({
      id: route.id,
      result: {
        success: false,
        error: "Invalid coordinates provided",
      },
    });
  });

  const successRate = routes.length > 0 ? successfulCalculations / routes.length : 0;

  return {
    results,
    successRate,
    totalProcessed: routes.length,
    successfulCalculations,
  };
}

/**
 * Estimates the cost of distance calculations based on Google Maps pricing
 * @param numberOfRoutes Number of routes to calculate
 * @returns Estimated cost in USD
 */
export function estimateDistanceCalculationCost(numberOfRoutes: number): number {
  // Google Maps Distance Matrix API pricing (as of 2024)
  // $5.00 per 1000 requests for standard usage
  const costPer1000Requests = 5.0;
  return (numberOfRoutes / 1000) * costPer1000Requests;
}

/**
 * Validates distance calculation result
 * @param result Distance calculation result to validate
 * @returns True if result is valid
 */
export function validateDistanceResult(result: DistanceCalculationResult): boolean {
  return (
    result.success &&
    typeof result.distanceKm === "number" &&
    result.distanceKm > 0 &&
    typeof result.durationSeconds === "number" &&
    result.durationSeconds > 0
  );
}