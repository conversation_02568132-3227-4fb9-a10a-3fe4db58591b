"use server";

import { AddressValidationClient } from "@googlemaps/addressvalidation";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("AddressValidationService");

// Initialize the client
const addressValidationClient = new AddressValidationClient();

// Interface for the Google Maps Address Validation API response
export interface IValidateAddressResponse {
  result: {
    verdict?: {
      valid?: boolean;
      hasOwnProperty: (prop: string) => boolean;
    };
    address?: {
      formattedAddress?: string;
      addressComponents?: Array<{
        componentType?: string;
        componentName?: string;
      }>;
    };
  };
  responseId: string;
}

export interface ValidateAddressResult {
  isValid: boolean;
  formattedAddress?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  error?: string;
}

/**
 * Validates an address using the Google Maps Address Validation API
 * @param address The address to validate
 * @param regionCode The region code (country code) for the address
 * @returns The validation result
 */
export async function validateAddress(
  address: string,
  regionCode: string,
): Promise<ValidateAddressResult> {
  try {
    // Split the address into lines if it contains newlines
    const addressLines = address.split(/\r?\n/);

    // Construct the request
    const request = {
      address: {
        regionCode,
        addressLines,
      },
    };

    // Call the API
    const response = await addressValidationClient.validateAddress(request);

    // Extract the validation result
    const result = response[0] as IValidateAddressResponse;

    // Get the formatted address from the first result
    const formattedAddress = result.result.address?.formattedAddress;

    // Extract city, state, postal code from address components
    let city, state, postalCode, country;

    if (result.result.address?.addressComponents) {
      for (const component of result.result.address.addressComponents) {
        if (!component.componentType || !component.componentName) continue;

        const type = component.componentType.toLowerCase();
        const name = component.componentName;

        if (type.includes("locality")) {
          city = name;
        } else if (type.includes("administrative_area_level_1")) {
          state = name;
        } else if (type.includes("postal_code")) {
          postalCode = name;
        } else if (type.includes("country")) {
          country = name;
        }
      }
    }

    // Determine if the address is valid based on the verdict
    const isValid = result.result.verdict?.hasOwnProperty("valid")
      ? Boolean(result.result.verdict.valid)
      : false;

    return {
      isValid,
      formattedAddress,
      city,
      state,
      postalCode,
      country,
    };
  } catch (error) {
    logger.error("Error validating address:", error);

    return {
      isValid: false,
      error: `Error validating address: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}
