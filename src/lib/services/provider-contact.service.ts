import { createClient } from "@/lib/supabase/server/server";
import {
  GetProviderContactsParams,
  CreateProviderContactInput,
  UpdateProviderContactInput,
  DeleteProviderContactParams,
  type ProviderContact,
} from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("ProviderContactService");

/**
 * Fetches contacts for a specific provider.
 */
export async function fetchProviderContacts(
  params: GetProviderContactsParams,
): Promise<ProviderContact[]> {
  const supabase = await createClient();
  const { provider_id } = params;

  // Check if provider exists
  const { data: providerExists, error: providerError } = await supabase
    .from("providers")
    .select("id")
    .eq("id", provider_id)
    .single();

  if (providerError || !providerExists) {
    logger.error("Error checking provider existence:", providerError);
    throw new Error(`Provider with ID ${provider_id} not found.`);
  }

  // Fetch contacts for this provider
  const { data, error } = await supabase
    .from("provider_contacts")
    .select("*")
    .eq("provider_id", provider_id)
    .order("is_primary", { ascending: false });

  if (error) {
    logger.error("Error fetching provider contacts:", error);
    throw new Error(
      `Failed to fetch contacts for provider ${provider_id}: ${error.message}`,
    );
  }

  return data || [];
}

/**
 * Creates a new contact for a provider.
 */
export async function addProviderContact(
  input: CreateProviderContactInput,
): Promise<ProviderContact> {
  logger.info("Service function addProviderContact called with input:", input);
  const supabase = await createClient();
  const { provider_id } = input;

  // Check if provider exists
  logger.info("Checking if provider exists:", provider_id);
  const { data: providerExists, error: providerError } = await supabase
    .from("providers")
    .select("id")
    .eq("id", provider_id)
    .single();

  logger.info("Provider check result:", { providerExists, providerError });

  if (providerError || !providerExists) {
    logger.error("Error checking provider existence:", providerError);
    throw new Error(`Provider with ID ${provider_id} not found.`);
  }

  const timestamp = new Date().toISOString();
  const contactData = {
    ...input,
    created_at: timestamp,
    updated_at: timestamp,
  };

  logger.info("Contact data to insert:", contactData);

  // If this is marked as primary contact, update any existing primary contacts
  if (input.is_primary) {
    logger.info(
      "Contact is marked as primary, checking for existing primary contacts",
    );
    const { data: existingPrimary } = await supabase
      .from("provider_contacts")
      .select("id")
      .eq("provider_id", provider_id)
      .eq("is_primary", true);

    logger.info("Existing primary contacts:", existingPrimary);

    if (existingPrimary && existingPrimary.length > 0) {
      logger.info("Updating existing primary contacts to non-primary");
      // Update existing primary contacts to non-primary
      const { data: updateResult, error: updateError } = await supabase
        .from("provider_contacts")
        .update({ is_primary: false, updated_at: timestamp })
        .eq("provider_id", provider_id)
        .eq("is_primary", true);

      logger.info("Update result:", { updateResult, updateError });

      if (updateError) {
        logger.error("Error updating existing primary contacts:", updateError);
      }
    }
  }

  // Create the new contact
  logger.info("Inserting new contact into database");
  const { data, error } = await supabase
    .from("provider_contacts")
    .insert(contactData)
    .select()
    .single();

  logger.info("Insert result:", { data, error });

  if (error) {
    logger.error("Error creating provider contact:", error);
    throw new Error(
      `Failed to create contact for provider ${provider_id}: ${error.message}`,
    );
  }

  if (!data) {
    logger.error("No data returned from insert operation");
    throw new Error(
      `Failed to create contact for provider ${provider_id}: No data returned.`,
    );
  }

  logger.info("Successfully created contact:", data);
  return data;
}

/**
 * Updates an existing provider contact.
 */
export async function modifyProviderContact(
  input: UpdateProviderContactInput,
): Promise<ProviderContact> {
  const supabase = await createClient();
  const { id, provider_id, ...updateFields } = input;

  if (!id) {
    throw new Error("Contact ID is required for update operations.");
  }

  // Check if contact exists and belongs to the specified provider
  const { data: existingContact, error: checkError } = await supabase
    .from("provider_contacts")
    .select("id, provider_id, is_primary")
    .eq("id", id)
    .single();

  if (checkError || !existingContact) {
    logger.error("Error checking contact existence:", checkError);
    throw new Error(`Contact with ID ${id} not found.`);
  }

  // If provider_id is supplied, make sure it matches the contact's provider
  if (provider_id && existingContact.provider_id !== provider_id) {
    throw new Error(
      `Contact with ID ${id} does not belong to provider ${provider_id}.`,
    );
  }

  const updateData = {
    ...updateFields,
    updated_at: new Date().toISOString(),
  };

  // Remove undefined fields
  Object.keys(updateData).forEach(
    (key) =>
      updateData[key as keyof typeof updateData] === undefined &&
      delete updateData[key as keyof typeof updateData],
  );

  // Handle primary contact changes if applicable
  if (updateData.is_primary === true && !existingContact.is_primary) {
    // This contact is being promoted to primary
    // Update existing primary contacts to non-primary
    await supabase
      .from("provider_contacts")
      .update({ is_primary: false, updated_at: updateData.updated_at })
      .eq("provider_id", existingContact.provider_id)
      .eq("is_primary", true);
  }

  // Update the contact
  const { data, error } = await supabase
    .from("provider_contacts")
    .update(updateData)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    logger.error("Error updating provider contact:", error);
    throw new Error(`Failed to update contact ${id}: ${error.message}`);
  }

  if (!data) {
    throw new Error(`Failed to update contact ${id}: No data returned.`);
  }

  return data;
}

/**
 * Deletes a provider contact.
 */
export async function removeProviderContact(
  params: DeleteProviderContactParams,
): Promise<{ message: string }> {
  const supabase = await createClient();
  const { id, provider_id } = params;

  // Check if contact exists and belongs to the specified provider
  const { data: existingContact, error: checkError } = await supabase
    .from("provider_contacts")
    .select("id, provider_id, is_primary")
    .eq("id", id)
    .single();

  if (checkError || !existingContact) {
    logger.error("Error checking contact existence:", checkError);
    throw new Error(`Contact with ID ${id} not found.`);
  }

  // Verify provider_id if supplied
  if (provider_id && existingContact.provider_id !== provider_id) {
    throw new Error(
      `Contact with ID ${id} does not belong to provider ${provider_id}.`,
    );
  }

  // Delete the contact
  const { error } = await supabase
    .from("provider_contacts")
    .delete()
    .eq("id", id);

  if (error) {
    logger.error("Error deleting provider contact:", error);
    throw new Error(`Failed to delete contact ${id}: ${error.message}`);
  }

  return { message: `Contact with ID ${id} successfully deleted.` };
}
