import { createClient } from "@/lib/supabase/server/server";
import {
  GetEquipmentTypesParams,
  CreateEquipmentTypeInput,
  UpdateEquipmentTypeInput,
  DeleteEquipmentTypeParams,
  ProviderEquipmentInput,
  ProviderEquipmentDeleteInput,
  type EquipmentType,
} from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("EquipmentService");

/**
 * Fetches equipment with optional pagination, filtering, or by ID
 */
export async function fetchEquipment(params: GetEquipmentTypesParams): Promise<{
  data: EquipmentType[] | EquipmentType;
  count?: number;
}> {
  const supabase = await createClient();
  const { id, page = 1, pageSize = 10, category } = params;

  // If requesting a specific equipment
  if (id) {
    const { data, error } = await supabase
      .from("equipment_types")
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      logger.error("Error fetching equipment by ID:", error);
      throw new Error(`Equipment with ID ${id} not found.`);
    }

    return { data: data as EquipmentType };
  }

  // For paginated list request
  const startIndex = (page - 1) * pageSize;
  const endIndex = page * pageSize - 1;

  // Build the query with any filters
  let query = supabase.from("equipment_types").select("*", { count: "exact" });

  // Apply category filter if present
  if (category) {
    query = query.eq("category", category);
  }

  // Fetch the data with pagination
  const { data, count, error } = await query
    .order("name")
    .range(startIndex, endIndex);

  if (error) {
    logger.error("Error fetching equipment:", error);
    throw new Error(`Database error: ${error.message}`);
  }

  return { data: data as EquipmentType[], count: count ?? 0 };
}

/**
 * Creates new equipment
 */
export async function addEquipment(
  input: CreateEquipmentTypeInput,
): Promise<EquipmentType> {
  const supabase = await createClient();

  const timestamp = new Date().toISOString();
  const equipmentData = {
    name: input.name,
    category: input.category,
    description: input.description === "" ? null : input.description,
    created_at: timestamp,
    updated_at: timestamp,
  };

  const { data, error } = await supabase
    .from("equipment_types")
    .insert(equipmentData)
    .select()
    .single();

  if (error) {
    logger.error("Error creating equipment:", error);
    if (error.code === "23505") {
      throw new Error(`Equipment "${input.name}" already exists.`);
    }
    throw new Error(`Failed to create equipment: ${error.message}`);
  }

  if (!data) {
    throw new Error("Failed to create equipment, no data returned.");
  }

  return data;
}

/**
 * Updates existing equipment
 */
export async function modifyEquipment(
  input: UpdateEquipmentTypeInput,
): Promise<EquipmentType> {
  const supabase = await createClient();
  const { id, ...updateFields } = input;

  const updateData = {
    ...updateFields,
    description:
      updateFields.description === "" ? null : updateFields.description,
    updated_at: new Date().toISOString(),
  };

  // Check if equipment exists
  const { data: existingData, error: checkError } = await supabase
    .from("equipment_types")
    .select("id")
    .eq("id", id)
    .single();

  if (checkError || !existingData) {
    throw new Error(`Equipment with ID ${id} not found.`);
  }

  const { data, error } = await supabase
    .from("equipment_types")
    .update(updateData)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    logger.error("Error updating equipment:", error);
    if (error.code === "23505") {
      throw new Error(`Equipment "${updateFields.name}" already exists.`);
    }
    throw new Error(`Failed to update equipment: ${error.message}`);
  }

  if (!data) {
    throw new Error(`Equipment with ID ${id} failed to update.`);
  }

  return data;
}

/**
 * Deletes equipment by ID
 */
export async function removeEquipment(
  params: DeleteEquipmentTypeParams,
): Promise<{ message: string }> {
  const supabase = await createClient();
  const { id } = params;

  // Check if equipment exists
  const { data: existingData, error: checkError } = await supabase
    .from("equipment_types")
    .select("id")
    .eq("id", id)
    .single();

  if (checkError || !existingData) {
    throw new Error(`Equipment with ID ${id} not found.`);
  }

  // Check for associated provider_equipment records
  const { data: equipmentUsage, error: usageError } = await supabase
    .from("provider_equipments")
    .select("id")
    .eq("equipment_type_id", id);

  if (usageError) {
    logger.error("Error checking equipment usage:", usageError);
    throw new Error(`Failed to check equipment usage: ${usageError.message}`);
  }

  // If this equipment is being used, don't allow deletion
  if (equipmentUsage && equipmentUsage.length > 0) {
    throw new Error("Cannot delete equipment that is in use by providers");
  }

  // Delete the equipment
  const { error } = await supabase
    .from("equipment_types")
    .delete()
    .eq("id", id);

  if (error) {
    logger.error("Error deleting equipment:", error);
    throw new Error(`Failed to delete equipment: ${error.message}`);
  }

  return { message: "Equipment successfully deleted" };
}

/**
 * Adds equipment to a provider
 */
export async function linkEquipmentToProvider(
  input: ProviderEquipmentInput,
): Promise<{ message: string }> {
  const supabase = await createClient();
  const { provider_id, equipment_type_id } = input;

  const timestamp = new Date().toISOString();
  const providerEquipmentData = {
    provider_id,
    equipment_type_id,
    created_at: timestamp,
    updated_at: timestamp,
  };

  const { error } = await supabase
    .from("provider_equipments")
    .insert(providerEquipmentData);

  if (error) {
    logger.error("Error adding equipment to provider:", error);
    throw new Error(`Failed to add equipment to provider: ${error.message}`);
  }

  return { message: "Equipment added to provider successfully" };
}

/**
 * Removes equipment from a provider
 */
export async function unlinkEquipmentFromProvider(
  input: ProviderEquipmentDeleteInput,
): Promise<{ message: string }> {
  const supabase = await createClient();
  const { provider_id, equipment_type_id } = input;

  const { error } = await supabase
    .from("provider_equipments")
    .delete()
    .match({ provider_id, equipment_type_id });

  if (error) {
    logger.error("Error removing equipment from provider:", error);
    throw new Error(
      `Failed to remove equipment from provider: ${error.message}`,
    );
  }

  return { message: "Equipment removed from provider successfully" };
}

/**
 * Gets equipment for a provider
 */
export async function getProviderEquipment(providerId: string): Promise<{
  data: Array<EquipmentType>;
  error?: string;
}> {
  const supabase = await createClient();

  if (!providerId) {
    return { data: [], error: "Missing provider ID." };
  }

  // Define return type as an empty array to begin with
  const result: Array<EquipmentType> = [];

  const { data, error } = await supabase
    .from("provider_equipments")
    .select(
      `
      equipment_types:equipment_type_id (*)
    `,
    )
    .eq("provider_id", providerId);

  if (error) {
    logger.error("Error fetching provider equipment:", error);
    return {
      data: [],
      error: `Failed to fetch provider equipment: ${error.message}`,
    };
  }

  // Process data safely
  if (data && Array.isArray(data)) {
    for (const item of data) {
      if (item && 'equipment_types' in item && item.equipment_types) {
        const equipmentTypes = item.equipment_types;
        if (typeof equipmentTypes === "object" && !Array.isArray(equipmentTypes)) {
          const equipmentType = equipmentTypes as unknown as EquipmentType;
          result.push({
            ...equipmentType,
          });
        }
      }
    }
  }

  return { data: result };
}
