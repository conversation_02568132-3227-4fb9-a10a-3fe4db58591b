"use server";

import { createClient } from "@/lib/supabase/server/server";
import { createLogger } from "@/lib/utils/logger/logger";
import {
  Provider,
  CreateProviderInput,
  UpdateProviderInput,
  DeleteProviderParams,
  PaginationParams,
} from "@/lib/schemas";

// Import composite types from the composite-types file
import { ProviderWithEquipment } from "@/lib/schemas/composite-types";

// Define a type that combines pagination params with optional ID
type GetProvidersParams = PaginationParams & {
  id?: string;
};

// Create a logger instance for this module
const logger = createLogger("ProviderService");

// Validation functions are now imported from @/lib/utils/validation

/**
 * Fetches providers with optional pagination, filtering, or by ID.
 */
export async function fetchProviders(params: GetProvidersParams): Promise<{
  data: Provider[] | Provider;
  count?: number;
  pagination?: {
    page: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}> {
  const { id, page = 1, pageSize = 10 } = params;

  // If requesting a specific provider
  if (id) {
    const supabase = await createClient();
    const { data, error } = await supabase
      .from("providers")
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      logger.error("Error fetching provider by ID:", error);
      throw new Error(`Provider with ID ${id} not found: ${error.message}`);
    }

    if (!data) {
      throw new Error(`Provider with ID ${id} not found.`);
    }

    return { data };
  }

  // For paginated list request
  const startIndex = (page - 1) * pageSize;
  const endIndex = page * pageSize - 1;

  const supabase = await createClient();

  // Get the total count for pagination metadata
  const countQuery = supabase
    .from("providers")
    .select("*", { count: "exact", head: true });

  const { count, error: countError } = await countQuery;

  if (countError) {
    logger.error("Error counting providers:", countError);
    throw new Error(`Failed to count providers: ${countError.message}`);
  }

  // Fetch the paginated data
  const { data, error } = await supabase
    .from("providers")
    .select("*")
    .range(startIndex, endIndex)
    .order("updated_at", { ascending: false })
    .order("name");

  if (error) {
    logger.error("Error fetching providers:", error);
    throw new Error(`Failed to fetch providers: ${error.message}`);
  }

  // Calculate pagination metadata
  const totalItems = count || 0;
  const totalPages = Math.ceil(totalItems / pageSize);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    data: data || [],
    count: totalItems,
    pagination: {
      page,
      pageSize,
      totalItems,
      totalPages,
      hasNextPage,
      hasPrevPage,
    },
  };
}

/**
 * Gets a single provider by ID with optional related data.
 */
export async function getProvider(
  id: string,
  options?: { includeContacts?: boolean },
): Promise<Provider> {
  try {
    // Prepare the select statement based on options
    const selectStatement = options?.includeContacts
      ? "*, provider_contacts(*)"
      : "*";

    // Use the supabase client
    const supabase = await createClient();
    const { data, error } = await supabase
      .from("providers")
      .select(selectStatement)
      .eq("id", id)
      .single();

    if (error) {
      logger.error("Error fetching provider:", error);
      throw new Error(`Provider with ID ${id} not found: ${error.message}`);
    }

    if (!data) {
      throw new Error(`Provider with ID ${id} not found.`);
    }

    // Ensure we have valid data before returning
    if (typeof data === "object" && data !== null && "id" in data) {
      return data as Provider;
    }

    throw new Error(`Invalid provider data returned for ID ${id}`);
  } catch (error) {
    logger.error("Unexpected error in getProvider:", error);
    throw new Error(
      `Failed to get provider: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

/**
 * Creates a new provider.
 */
export async function addProvider(
  input: CreateProviderInput,
): Promise<Provider> {
  const timestamp = new Date().toISOString();

  // Ensure status is always defined with a default value
  const status = input.status || "pending";

  const providerData = {
    name: input.name,
    full_address: input.full_address,
    structured_address: input.structured_address,
    tax_id: input.tax_id,
    status: status as "active" | "inactive" | "pending" | "suspended", // Ensure correct type
    verified: input.verified !== undefined ? input.verified : false,
    created_at: timestamp,
    updated_at: timestamp,
  };

  // Use the supabase client
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("providers")
    .insert(providerData)
    .select()
    .single();

  if (error) {
    logger.error("Error creating provider:", error);
    throw new Error(`Failed to create provider: ${error.message}`);
  }

  if (!data) {
    throw new Error("Failed to create provider: No data returned.");
  }

  return data;
}

/**
 * Updates an existing provider.
 */
export async function modifyProvider(
  input: UpdateProviderInput,
): Promise<Provider> {
  const { id, ...updateFields } = input;

  // Ensure id is defined
  if (!id) {
    throw new Error("Provider ID is required for updates");
  }

  const supabase = await createClient();

  // Check if provider exists
  const { data: existingProvider, error: checkError } = await supabase
    .from("providers")
    .select("id")
    .eq("id", id)
    .single();

  if (checkError || !existingProvider) {
    logger.error("Error checking provider existence:", checkError);
    throw new Error(`Provider with ID ${id} not found.`);
  }

  const updateData = {
    ...updateFields,
    updated_at: new Date().toISOString(),
  };

  // Remove undefined fields
  Object.keys(updateData).forEach(
    (key) =>
      updateData[key as keyof typeof updateData] === undefined &&
      delete updateData[key as keyof typeof updateData],
  );

  // Use the supabase client
  const { data, error } = await supabase
    .from("providers")
    .update(updateData)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    logger.error("Error updating provider:", error);
    throw new Error(`Failed to update provider: ${error.message}`);
  }

  if (!data) {
    throw new Error(`Provider with ID ${id} failed to update.`);
  }

  return data;
}

/**
 * Deletes a provider by ID after verifying there are no dependencies.
 */
export async function removeProvider(
  params: DeleteProviderParams,
): Promise<{ message: string }> {
  try {
    const { id } = params;

    const supabase = await createClient();

    // Check if provider exists
    const { data: existingProvider, error: checkError } = await supabase
      .from("providers")
      .select("id")
      .eq("id", id)
      .single();

    if (checkError || !existingProvider) {
      logger.error("Error checking provider existence:", checkError);
      throw new Error(`Provider with ID ${id} not found.`);
    }

    // Check for associated provider_contacts
    const { data: contacts, error: contactsError } = await supabase
      .from("provider_contacts")
      .select("id")
      .eq("provider_id", id);

    if (contactsError) {
      logger.error("Error checking provider contacts:", contactsError);
      throw new Error(
        `Failed to check provider contacts: ${contactsError.message}`,
      );
    }

    // Check for associated provider_equipment
    const { data: equipmentUsage, error: usageError } = await supabase
      .from("provider_equipments")
      .select("id")
      .eq("provider_id", id);

    if (usageError) {
      logger.error("Error checking provider equipment:", usageError);
      throw new Error(
        `Failed to check provider equipment: ${usageError.message}`,
      );
    }

    // If this provider has associated data, don't allow deletion
    if (
      (contacts && contacts.length > 0) ||
      (equipmentUsage && equipmentUsage.length > 0)
    ) {
      throw new Error(
        "Cannot delete provider with associated contacts or equipment. Remove dependencies first.",
      );
    }

    // Delete the provider
    const { error } = await supabase.from("providers").delete().eq("id", id);

    if (error) {
      logger.error("Error deleting provider:", error);
      throw new Error(`Failed to delete provider: ${error.message}`);
    }

    return { message: `Provider with ID ${id} successfully deleted.` };
  } catch (error) {
    logger.error("Unexpected error in removeProvider:", error);
    throw new Error(
      `Failed to delete provider: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

/**
 * Gets provider counts by status
 */
export async function getProviderCountsByStatus(): Promise<{
  totalCount: number;
  activeCount: number;
  pendingCount: number;
  inactiveCount: number;
  verifiedCount: number;
}> {
  try {
    const supabase = await createClient();

    // Get active providers count
    const { count: activeCount, error: activeError } = await supabase
      .from("providers")
      .select("*", { count: "exact", head: true })
      .eq("status", "active");

    if (activeError) {
      logger.error("Error counting active providers:", activeError);
      throw new Error(
        `Failed to count active providers: ${activeError.message}`,
      );
    }

    // Get pending providers count
    const { count: pendingCount, error: pendingError } = await supabase
      .from("providers")
      .select("*", { count: "exact", head: true })
      .eq("status", "pending");

    if (pendingError) {
      logger.error("Error counting pending providers:", pendingError);
      throw new Error(
        `Failed to count pending providers: ${pendingError.message}`,
      );
    }

    // Get inactive providers count (includes both inactive and suspended)
    const { count: inactiveCount, error: inactiveError } = await supabase
      .from("providers")
      .select("*", { count: "exact", head: true })
      .in("status", ["inactive", "suspended"]);

    if (inactiveError) {
      logger.error("Error counting inactive providers:", inactiveError);
      throw new Error(
        `Failed to count inactive providers: ${inactiveError.message}`,
      );
    }

    // Get verified providers count
    const { count: verifiedCount, error: verifiedError } = await supabase
      .from("providers")
      .select("*", { count: "exact", head: true })
      .eq("verified", true);

    if (verifiedError) {
      logger.error("Error counting verified providers:", verifiedError);
      throw new Error(
        `Failed to count verified providers: ${verifiedError.message}`,
      );
    }

    // Calculate the total count from the individual status counts
    const totalCount =
      (activeCount || 0) + (pendingCount || 0) + (inactiveCount || 0);

    return {
      totalCount,
      activeCount: activeCount || 0,
      pendingCount: pendingCount || 0,
      inactiveCount: inactiveCount || 0,
      verifiedCount: verifiedCount || 0,
    };
  } catch (error) {
    logger.error("Unexpected error in getProviderCountsByStatus:", error);
    throw new Error(
      `Failed to get provider counts: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

/**
 * Fetches providers with their equipment data
 */
export async function fetchProvidersWithEquipment(
  params: GetProvidersParams = { page: 1, pageSize: 10 },
): Promise<{
  data: ProviderWithEquipment[];
  count?: number;
  pagination?: {
    page: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}> {
  const { page = 1, pageSize = 100 } = params;

  // Log pagination parameters for debugging
  logger.info(
    `Fetching providers with pagination: page=${page}, pageSize=${pageSize}`,
  );

  // Ensure page and pageSize are valid numbers
  const validPage = Math.max(1, page);
  const validPageSize = Math.min(100, Math.max(1, pageSize));

  if (validPage !== page || validPageSize !== pageSize) {
    logger.warn(
      `Invalid pagination parameters corrected: page=${page}->${validPage}, pageSize=${pageSize}->${validPageSize}`,
    );
  }

  // For paginated list request
  const startIndex = (validPage - 1) * validPageSize;
  const endIndex = startIndex + validPageSize - 1;

  const supabase = await createClient();

  // Get the total count for pagination metadata
  const { count, error: countError } = await supabase
    .from("providers")
    .select("*", { count: "exact", head: true });

  if (countError) {
    logger.error("Error counting providers with admin client:", countError);
    throw new Error(`Failed to count providers: ${countError.message}`);
  }

  // Fetch the paginated providers data with detailed error handling
  let providers;
  let error;

  try {
    // Build the query
    const response = await supabase
      .from("providers")
      .select("*")
      .range(startIndex, endIndex)
      .order("verified", { ascending: false })
      .order("name")
      .order("updated_at", { ascending: false });

    providers = response.data;
    error = response.error;

    if (error) {
      logger.error("Error fetching providers with admin client:", error);
      logger.error("Error details:", {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint,
      });
      throw new Error(`Failed to fetch providers: ${error.message}`);
    }
  } catch (queryError) {
    logger.error("Exception in admin client provider query:", queryError);
    throw new Error(
      `Exception fetching providers: ${queryError instanceof Error ? queryError.message : String(queryError)}`,
    );
  }

  // If no providers found, return empty array
  if (!providers || providers.length === 0) {
    return {
      data: [],
      count: 0,
      pagination: {
        page: validPage,
        pageSize: validPageSize,
        totalItems: 0,
        totalPages: 0,
        hasNextPage: false,
        hasPrevPage: false,
      },
    };
  }

  // Fetch equipment for all providers in one query
  const { data: equipmentData, error: equipmentError } = await supabase
    .from("provider_equipments")
    .select(
      `
      provider_id,
      equipment_type:equipment_type_id (
        id,
        name,
        category,
        description
      )
    `,
    )
    .in(
      "provider_id",
      providers.map((p: any) => p.id),
    );

  if (equipmentError) {
    logger.error(
      "Error fetching provider equipment with admin client:",
      equipmentError,
    );
    // Continue without equipment data rather than failing completely
  }

  // Group equipment by provider
  const equipmentByProvider: Record<string, any[]> = {};

  if (equipmentData) {
    logger.info(
      `Equipment data found for providers: ${equipmentData.length} items`,
    );
    equipmentData.forEach((item: any) => {
      if (!equipmentByProvider[item.provider_id]) {
        equipmentByProvider[item.provider_id] = [];
      }
      equipmentByProvider[item.provider_id].push(item.equipment_type);
    });
  } else {
    logger.warn("No equipment data found for providers");
  }

  // Merge providers with their equipment
  const providersWithEquipment = providers.map((provider: any) => ({
    ...provider,
    equipments: equipmentByProvider[provider.id] || [],
  }));

  // Calculate pagination metadata
  const totalItems = count || 0;
  const totalPages = Math.ceil(totalItems / validPageSize);
  const hasNextPage = validPage < totalPages;
  const hasPrevPage = validPage > 1;

  logger.info(
    `Pagination metadata: totalItems=${totalItems}, totalPages=${totalPages}, currentPage=${validPage}, hasNextPage=${hasNextPage}, hasPrevPage=${hasPrevPage}`,
  );

  return {
    data: providersWithEquipment,
    count: totalItems,
    pagination: {
      page: validPage,
      pageSize: validPageSize,
      totalItems,
      totalPages,
      hasNextPage,
      hasPrevPage,
    },
  };
}

/**
 * Get the count of RFQ invitations for each provider
 * @returns A map of provider IDs to their RFQ invitation counts
 */
export async function getProviderRFQInvitationCounts(): Promise<Record<string, number>> {
  try {
    const supabase = await createClient();

    // Query the rfq_providers table to get all invited providers
    const { data, error } = await supabase
      .from("rfq_providers")
      .select("provider_id, status, invited_at")
      .or("status.eq.invited,status.eq.declined,status.eq.bid_submitted")
      .not("invited_at", "is", null);

    if (error) {
      logger.error("Error fetching provider RFQ invitation counts:", error);
      throw new Error(`Failed to fetch provider RFQ invitation counts: ${error.message}`);
    }

    // Count invitations for each provider manually
    const invitationCounts: Record<string, number> = {};

    data.forEach((item: any) => {
      const providerId = item.provider_id;
      if (!invitationCounts[providerId]) {
        invitationCounts[providerId] = 0;
      }
      invitationCounts[providerId]++;
    });

    return invitationCounts;
  } catch (error) {
    logger.error("Unexpected error in getProviderRFQInvitationCounts:", error);
    return {};
  }
}

/**
 * Get the count of RFQ invitations for a specific provider
 * @param providerId The provider ID
 * @returns The count of RFQ invitations for the provider
 */
export async function getProviderRFQInvitationCount(providerId: string): Promise<number> {
  try {
    const supabase = await createClient();

    // Query the rfq_providers table to get invitations for the provider
    const { data, error } = await supabase
      .from("rfq_providers")
      .select("id")
      .eq("provider_id", providerId)
      .or("status.eq.invited,status.eq.declined,status.eq.bid_submitted")
      .not("invited_at", "is", null);

    if (error) {
      logger.error(`Error fetching RFQ invitation count for provider ${providerId}:`, error);
      throw new Error(`Failed to fetch RFQ invitation count: ${error.message}`);
    }

    // Return the count of invitations
    return data.length;
  } catch (error) {
    logger.error(`Unexpected error in getProviderRFQInvitationCount for provider ${providerId}:`, error);
    return 0;
  }
}