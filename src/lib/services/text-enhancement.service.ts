import { Google<PERSON>enAI } from "@google/genai";
import { createLogger } from "@/lib/utils/logger/logger";
import { EnhancementType } from "@/lib/types/text-enhancement.types";

// Create a logger instance for this module
const logger = createLogger("TextEnhancementService");

/**
 * Service for enhancing text using Google's Gemini AI
 */
export async function enhanceText(
  text: string,
  enhancementType: EnhancementType,
): Promise<string> {
  if (!text.trim()) {
    return "";
  }

  try {
    // Get the API key from environment variables
    const apiKey = process.env.GEMINI_API_KEY;

    if (!apiKey) {
      logger.error("GEMINI_API_KEY is not defined in environment variables");
      throw new Error("Gemini API key is not configured");
    }

    // Initialize the Gemini client
    const genAI = new GoogleGenAI({ apiKey });

    // Create the prompt based on enhancement type
    let prompt = "";

    // Base system prompt to guide the model's behavior
    const systemPrompt = `You are an email enhancement assistant that directly improves email text.

YOUR OUTPUT MUST:
- Contain ONLY the enhanced email text
- Be a single, complete email ready to send
- Maintain the original structure (greeting, body, signature)
- Preserve the core message and intent

YOUR OUTPUT MUST NOT:
- Include multiple versions or options (no "Option 1", "Option 2")
- Contain explanations of changes
- Include meta-text like "Changes Made:" or "Here are options"
- Contain bullet points explaining your process
- Include any commentary about what you changed
- Add any annotations or notes

CRITICAL: Users need the exact enhanced email text with no additional content.`;

    switch (enhancementType) {
      case EnhancementType.SHORTEN:
        prompt = `${systemPrompt}

TASK: Make this email more concise by removing unnecessary words and details.
GOAL: A shorter email that preserves all key information and maintains professionalism.
REMEMBER: Return ONLY the improved email text with no explanations.

EMAIL TO ENHANCE:
${text}

FINAL INSTRUCTION: Provide ONLY the enhanced email with no explanations, options, or annotations.`;
        break;
      case EnhancementType.SPELLING_GRAMMAR:
        prompt = `${systemPrompt}

TASK: Fix all spelling and grammar issues in this email.
GOAL: A grammatically perfect email with no spelling errors.
REMEMBER: Return ONLY the corrected email text with no explanations.

EMAIL TO ENHANCE:
${text}

FINAL INSTRUCTION: Provide ONLY the enhanced email with no explanations, options, or annotations.`;
        break;
      case EnhancementType.SIMPLIFY:
        prompt = `${systemPrompt}

TASK: Simplify the language in this email to make it clearer and easier to understand.
GOAL: An email with simpler vocabulary and sentence structure that maintains the same meaning.
REMEMBER: Return ONLY the simplified email text with no explanations.

EMAIL TO ENHANCE:
${text}

FINAL INSTRUCTION: Provide ONLY the enhanced email with no explanations, options, or annotations.`;
        break;
      case EnhancementType.IMPROVE_FLOW:
        prompt = `${systemPrompt}

TASK: Improve the flow and readability of this email.
GOAL: A more coherent email with better transitions between ideas.
REMEMBER: Return ONLY the improved email text with no explanations.

EMAIL TO ENHANCE:
${text}

FINAL INSTRUCTION: Provide ONLY the enhanced email with no explanations, options, or annotations.`;
        break;
      case EnhancementType.REWRITE:
        prompt = `${systemPrompt}

TASK: Rewrite this email while preserving its core meaning and intent.
GOAL: A fresh version of the email that communicates the same message in a new way.
REMEMBER: Return ONLY the rewritten email text with no explanations.

EMAIL TO ENHANCE:
${text}

FINAL INSTRUCTION: Provide ONLY the enhanced email with no explanations, options, or annotations.`;
        break;
      case EnhancementType.PROFESSIONAL:
        prompt = `${systemPrompt}

TASK: Make this email more professional and formal in tone.
GOAL: A business-appropriate email with formal language and structure.
REMEMBER: Return ONLY the professional email text with no explanations.

EMAIL TO ENHANCE:
${text}

FINAL INSTRUCTION: Provide ONLY the enhanced email with no explanations, options, or annotations.`;
        break;
      case EnhancementType.GENERIC:
        prompt = `${systemPrompt}

TASK: Make this email more general so it could apply to a wider audience.
GOAL: A versatile email that could be used in multiple similar situations.
REMEMBER: Return ONLY the generalized email text with no explanations.

EMAIL TO ENHANCE:
${text}

FINAL INSTRUCTION: Provide ONLY the enhanced email with no explanations, options, or annotations.`;
        break;
      default:
        prompt = `${systemPrompt}

TASK: Improve this email's overall quality and effectiveness.
GOAL: A better version of the email that maintains its core purpose.
REMEMBER: Return ONLY the improved email text with no explanations.

EMAIL TO ENHANCE:
${text}

FINAL INSTRUCTION: Provide ONLY the enhanced email with no explanations, options, or annotations.`;
    }

    // Use the Gemini model to generate content
    const model = genAI.models.generateContent({
      model: "gemini-2.0-flash-001",
      contents: prompt,
      config: {
        temperature: 0.01, // Extremely low temperature for highly deterministic responses
        maxOutputTokens: 1024,
      },
    });

    const result = await model;
    const enhancedText = result.text?.trim() || "";

    return enhancedText;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Error enhancing text:", errorMessage);

    // Rethrow the error to be handled by the caller
    throw error;
  }
}
