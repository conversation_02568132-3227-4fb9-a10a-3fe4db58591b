import { Unkey } from "@unkey/api";
import { createLogger } from "@/lib/utils/logger/logger";
import { getCurrentUser } from "./auth.service";
import { checkRole } from "@/lib/supabase/server/auth";
import { createClient } from "@/lib/supabase/server/server";

// Create a logger instance for this module
const logger = createLogger("ApiKeyService");

// Initialize the Unkey client
const unkey = new Unkey({
  token: process.env.UNKEY_ROOT_KEY || "",
  retry: {
    attempts: 3,
    backoff: (retryCount) => retryCount * 1000,
  },
});

/**
 * Type for API key information
 */
export type ApiKeyInfo = {
  keyId: string;
  name: string;
  ownerId: string;
  createdAt: string;
  expires?: string;
  revokedAt?: string;
  permissions: string[];
  meta: Record<string, any>;
};

/**
 * Creates a new API key for a user
 * @param userId The ID of the user
 * @param name The name of the API key
 * @param metadata Additional metadata to store with the key
 * @param permissions Optional permissions to assign to the key
 * @returns The created API key data or null if creation failed
 */
export async function createUserApiKey(
  userId: string,
  name: string,
  metadata: Record<string, any> = {},
  permissions: string[] = [],
): Promise<{ key: string; keyId: string } | null> {
  try {
    logger.debug(`Creating API key for user ${userId} with name ${name}`);

    const { result, error } = await unkey.keys.create({
      apiId: process.env.UNKEY_API_ID!,
      ownerId: userId,
      meta: {
        ...metadata,
        name,
        userId,
      },
      prefix: "sf",
      ratelimit: {
        type: "fast",
        limit: 100,
        refillRate: 1,
        refillInterval: 60 * 1000, // 1 minute
      },
      permissions,
    });

    if (error) {
      logger.error(
        `Failed to create API key for user ${userId}:`,
        error.message,
      );
      return null;
    }

    logger.info(`API key created successfully for user ${userId}`);
    return {
      key: result.key,
      keyId: result.keyId,
    };
  } catch (error) {
    logger.error(`Error creating API key for user ${userId}:`, error);
    return null;
  }
}

/**
 * Revokes an API key
 * @param keyId The ID of the key to revoke
 * @returns True if the key was revoked successfully, false otherwise
 */
export async function revokeUserApiKey(keyId: string): Promise<boolean> {
  try {
    logger.debug(`Revoking API key ${keyId}`);

    const { error } = await unkey.keys.delete({ keyId });

    if (error) {
      logger.error(`Failed to revoke API key ${keyId}:`, error.message);
      return false;
    }

    logger.info(`API key ${keyId} revoked successfully`);
    return true;
  } catch (error) {
    logger.error(`Error revoking API key ${keyId}:`, error);
    return false;
  }
}

/**
 * Lists all API keys (admin only)
 * @returns Array of API key information or null if the operation failed
 */
export async function listAllApiKeys(): Promise<ApiKeyInfo[] | null> {
  try {
    // Check if the current user has admin role
    const user = await getCurrentUser();
    if (!user) {
      logger.error("No authenticated user found");
      return null;
    }

    const supabase = await createClient();
    const isAdmin = await checkRole(supabase, ["admin"]);

    logger.debug(`User ${user.id} has admin role: ${isAdmin}`);

    if (!isAdmin) {
      logger.error(
        `User ${user.id} attempted to list all API keys without admin role`,
      );
      return null;
    }

    // Check if UNKEY_API_ID is set
    if (!process.env.UNKEY_API_ID) {
      logger.error("UNKEY_API_ID environment variable is not set");
      return null;
    }

    try {
      const { result, error } = await unkey.apis.listKeys({
        apiId: process.env.UNKEY_API_ID,
      });

      if (error) {
        logger.error(`Failed to list API keys: ${error.message}`, error);
        return null;
      }

      if (!result || !result.keys) {
        logger.error("Unexpected response from Unkey API: missing keys array");
        return [];
      }

      // Map the result to our ApiKeyInfo type
      const keys = result.keys.map((key) => ({
        keyId: key.id,
        name: String(key.meta?.name || "Unnamed Key"),
        ownerId: key.ownerId || "",
        createdAt: String(key.createdAt),
        expires: key.expires ? String(key.expires) : undefined,
        revokedAt: undefined, // The revokedAt property doesn't exist in the Unkey API response
        permissions: key.permissions || [],
        meta: key.meta || {},
      }));

      return keys;
    } catch (unkeyError) {
      logger.error("Error calling Unkey API:", unkeyError);
      return null;
    }
  } catch (error) {
    logger.error("Error in listAllApiKeys:", error);
    return null;
  }
}

/**
 * Gets details for a specific API key (admin only)
 * @param keyId The ID of the key to get details for
 * @returns The API key information or null if the operation failed
 */
export async function getApiKeyDetails(
  keyId: string,
): Promise<ApiKeyInfo | null> {
  try {
    // Check if the current user has admin role
    const user = await getCurrentUser();
    if (!user) {
      logger.error("No authenticated user found");
      return null;
    }

    const supabase = await createClient();
    const isAdmin = await checkRole(supabase, ["admin"]);

    if (!isAdmin) {
      logger.error(
        `User ${user.id} attempted to get API key details without admin role`,
      );
      return null;
    }

    logger.debug(`Getting details for API key ${keyId}`);

    const { result, error } = await unkey.keys.get({ keyId });

    if (error) {
      logger.error(
        `Failed to get API key details for ${keyId}:`,
        error.message,
      );
      return null;
    }

    if (!result) {
      logger.error(`API key ${keyId} not found`);
      return null;
    }

    // Map the result to our ApiKeyInfo type
    const keyInfo: ApiKeyInfo = {
      keyId: result.id,
      name: String(result.meta?.name || "Unnamed Key"),
      ownerId: result.ownerId || "",
      createdAt: String(result.createdAt),
      expires: result.expires ? String(result.expires) : undefined,
      revokedAt: undefined, // The revokedAt property doesn't exist in the Unkey API response
      permissions: result.permissions || [],
      meta: result.meta || {},
    };

    return keyInfo;
  } catch (error) {
    logger.error(`Error getting API key details for ${keyId}:`, error);
    return null;
  }
}
