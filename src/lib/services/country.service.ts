import { createClient } from "@/lib/supabase/server/server";
import {
  type Country,
  type GetCountriesParams,
  type CreateCountryInput,
  type UpdateCountryInput,
} from "@/lib/schemas";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("CountryService");

/**
 * Fetches countries with optional filtering and pagination
 */
export async function fetchCountries(params: Partial<GetCountriesParams> = {}): Promise<{
  data: Country[];
  count?: number;
  pagination?: {
    page: number;
    pageSize: number;
    totalPages: number;
    totalCount: number;
  };
}> {
  const supabase = await createClient();

  // Check if we should fetch all countries (no pagination)
  const fetchAllCountries = params.page === undefined && params.pageSize === undefined;

  const effectiveParams: Partial<GetCountriesParams> = {
    id: params.id,
    alpha2_code: params.alpha2_code,
  };

  // Only add pagination params if we're not fetching all countries
  if (!fetchAllCountries) {
    effectiveParams.page = params.page ?? 1;
    effectiveParams.pageSize = params.pageSize ?? 10;
  }

  const { id, alpha2_code, page, pageSize } = effectiveParams;

  if (id || alpha2_code) {
    let query = supabase.from("countries").select("*");

    if (id) {
      query = query.eq("id", id);
    } else if (alpha2_code) {
      query = query.eq("alpha2_code", alpha2_code);
    }

    const { data, error } = await query;

    if (error) {
      logger.error("Error fetching country:", error);
      throw new Error(`Failed to fetch country: ${error.message}`);
    }

    if (!data || data.length === 0) {
      throw new Error(`Country not found`);
    }

    return { data: [data[0]] };
  }

  if (page === undefined || pageSize === undefined) {
    const { data, error } = await supabase
      .from("countries")
      .select("*")
      .order("name");

    if (error) {
      logger.error("Error fetching all countries:", error);
      throw new Error(`Failed to fetch all countries: ${error.message}`);
    }

    return {
      data: data || [],
      count: data?.length || 0,
    };
  }

  const startIndex = (page - 1) * pageSize;
  const endIndex = page * pageSize - 1;

  const { count, error: countError } = await supabase
    .from("countries")
    .select("*", { count: "exact", head: true });

  if (countError) {
    logger.error("Error counting countries:", countError);
    throw new Error(`Failed to count countries: ${countError.message}`);
  }

  const { data, error } = await supabase
    .from("countries")
    .select("*")
    .range(startIndex, endIndex)
    .order("name");

  if (error) {
    logger.error("Error fetching countries:", error);
    throw new Error(`Failed to fetch countries: ${error.message}`);
  }

  const totalCount = count || 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  return {
    data: data || [],
    count: totalCount,
    pagination: {
      page,
      pageSize,
      totalPages,
      totalCount,
    },
  };
}

/**
 * Creates a new country
 */
export async function addCountry(input: CreateCountryInput): Promise<Country> {
  const supabase = await createClient();
  const { name, alpha2_code, alpha3_code } = input;

  if (alpha2_code) {
    const { data: existingCountry, error: checkError } = await supabase
      .from("countries")
      .select("alpha2_code")
      .eq("alpha2_code", alpha2_code);

    if (checkError) {
      logger.error("Error checking existing country by alpha2_code:", checkError);
      throw new Error(`Failed to check existing country: ${checkError.message}`);
    }

    if (existingCountry && existingCountry.length > 0) {
      throw new Error(`Country with alpha2_code ${alpha2_code} already exists`);
    }
  }

  const timestamp = new Date().toISOString();

  const countryData = {
    name,
    alpha2_code: alpha2_code || null,
    alpha3_code: alpha3_code || null,
    created_at: timestamp,
    updated_at: timestamp,
  };

  const { data, error } = await supabase
    .from("countries")
    .insert(countryData)
    .select()
    .single();

  if (error) {
    logger.error("Error creating country:", error);
    throw new Error(`Failed to create country: ${error.message}`);
  }

  return data;
}

/**
 * Updates an existing country
 */
export async function updateCountry(
  input: UpdateCountryInput,
): Promise<Country> {
  const supabase = await createClient();
  const { id, name, alpha2_code, alpha3_code } = input;

  const { data: existingCountryResult, error: checkError } = await supabase
    .from("countries")
    .select("*")
    .eq("id", id)
    .single();

  if (checkError && checkError.code !== 'PGRST116') {
    logger.error("Error checking existing country:", checkError);
    throw new Error(`Failed to check existing country: ${checkError.message}`);
  }

  if (!existingCountryResult) {
    throw new Error(`Country with ID ${id} not found`);
  }
  const existingCountry = existingCountryResult;

  if (alpha2_code && alpha2_code !== existingCountry.alpha2_code) {
    const { data: codeCheck, error: codeCheckError } = await supabase
      .from("countries")
      .select("alpha2_code")
      .eq("alpha2_code", alpha2_code)
      .neq("id", id);

    if (codeCheckError) {
      logger.error("Error checking alpha2_code uniqueness:", codeCheckError);
      throw new Error(
        `Failed to check alpha2_code uniqueness: ${codeCheckError.message}`,
      );
    }

    if (codeCheck && codeCheck.length > 0) {
      throw new Error(`Country with alpha2_code ${alpha2_code} already exists`);
    }
  }

  const updateData: any = {
    updated_at: new Date().toISOString(),
  };

  if (name !== undefined) updateData.name = name;
  if (alpha2_code !== undefined) updateData.alpha2_code = alpha2_code;
  if (alpha3_code !== undefined) updateData.alpha3_code = alpha3_code;

  const { updated_at, ...restOfUpdateData } = updateData;

  if (Object.keys(restOfUpdateData).length === 0) {
    logger.info(`No actual data fields to update for country ID: ${id}. Returning existing data.`);
    return existingCountry;
  }

  const { data, error } = await supabase
    .from("countries")
    .update(updateData)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    logger.error("Error updating country:", error);
    throw new Error(`Failed to update country: ${error.message}`);
  }

  return data;
}

/**
 * Deletes a country by ID
 */
export async function deleteCountry(id: string): Promise<void> {
  const supabase = await createClient();

  const { data: providers, error: providersError } = await supabase
    .from("providers")
    .select("id")
    .eq("country_id", id);

  if (providersError) {
    logger.error("Error checking country references:", providersError);
    throw new Error(
      `Failed to check country references: ${providersError.message}`,
    );
  }

  if (providers && providers.length > 0) {
    throw new Error("Cannot delete country that is referenced by providers");
  }

  const { error } = await supabase.from("countries").delete().eq("id", id);

  if (error) {
    logger.error("Error deleting country:", error);
    throw new Error(`Failed to delete country: ${error.message}`);
  }
}
