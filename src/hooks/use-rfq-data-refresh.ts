/**
 * Custom hook for RFQ data fetching and refreshing
 *
 * This hook consolidates the data fetching logic that was previously
 * scattered across multiple components with nested try-catch blocks.
 * It provides a clean, reusable interface for refreshing RFQ provider data.
 */

import { useCallback, useState } from "react";
import {
  getRFQProvidersAction,
  getRFQB<PERSON>Action,
  mergeProvidersWithBidsAction,
} from "@/lib/actions/rfq.actions";
import { createLogger } from "@/lib/utils/logger/logger";
import type { RFQProviderWithBid } from "@/lib/schemas";

const logger = createLogger("useRFQDataRefresh");

interface UseRFQDataRefreshOptions {
  rfqId: string;
  onSuccess?: (data: RFQProviderWithBid[]) => void;
  onError?: (error: string) => void;
}

interface UseRFQDataRefreshReturn {
  refreshRFQData: () => Promise<RFQProviderWithBid[] | null>;
  isRefreshing: boolean;
}

export function useRFQDataRefresh({
  rfqId,
  onSuccess,
  onError,
}: UseRFQDataRefreshOptions): UseRFQDataRefreshReturn {
  const [isRefreshing, setIsRefreshing] = useState(false);

  const refreshRFQData = useCallback(async (): Promise<RFQProviderWithBid[] | null> => {
    if (isRefreshing) {
      logger.warn("Refresh already in progress, skipping duplicate request");
      return null;
    }

    setIsRefreshing(true);

    try {
      logger.info(`Refreshing RFQ data for RFQ ${rfqId}`);

      // Fetch updated providers and bids in parallel
      const [rfqProvidersResult, bidsResult] = await Promise.all([
        getRFQProvidersAction(rfqId),
        getRFQBidsAction(rfqId),
      ]);

      // Validate results
      if (!rfqProvidersResult) {
        const error = "RFQ providers result is undefined";
        logger.error(error);
        onError?.(error);
        return null;
      }

      if (!bidsResult) {
        const error = "RFQ bids result is undefined";
        logger.error(error);
        onError?.(error);
        return null;
      }

      // Check if both results are successful
      if (!rfqProvidersResult.success) {
        const error = `Failed to fetch RFQ providers: ${rfqProvidersResult.error}`;
        logger.error(error);
        onError?.(error);
        return null;
      }

      if (!bidsResult.success) {
        const error = `Failed to fetch RFQ bids: ${bidsResult.error}`;
        logger.error(error);
        onError?.(error);
        return null;
      }

      // Merge providers with bids using the action function
      const mergeResult = await mergeProvidersWithBidsAction(
        rfqProvidersResult.data,
        bidsResult.data,
      );

      const providersWithBids = mergeResult.success
        ? mergeResult.data
        : rfqProvidersResult.data;

      logger.info(`Successfully refreshed RFQ data: ${providersWithBids.length} providers`);

      // Call success callback
      onSuccess?.(providersWithBids);

      return providersWithBids;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      logger.error("Error refreshing RFQ data:", error);
      onError?.(errorMessage);
      return null;
    } finally {
      setIsRefreshing(false);
    }
  }, [rfqId, onSuccess, onError, isRefreshing]);

  return {
    refreshRFQData,
    isRefreshing,
  };
}
