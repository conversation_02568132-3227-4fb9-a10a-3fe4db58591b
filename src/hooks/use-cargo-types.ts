"use client";

import { useQuery } from "@tanstack/react-query";
import { getCargo } from "@/lib/actions/cargo";
import { type CargoType } from "@/lib/schemas";
import { Option } from "@/components/ui/multi-select";

/**
 * Custom hook to fetch cargo types using TanStack Query
 *
 * This hook follows the architectural guidelines by using the action layer
 * instead of direct Supabase queries.
 *
 * @returns Cargo types data, loading state, and error state
 */
export function useCargoTypes() {
  const {
    data: cargoTypes = [],
    isLoading: loading,
    error,
  } = useQuery({
    queryKey: ["cargoTypes"],
    queryFn: async () => {
      const result = await getCargo();
      if (result.error) {
        throw new Error(result.error);
      }
      return result.data as CargoType[];
    },
  });

  return { cargoTypes, loading, error };
}

/**
 * Custom hook to fetch cargo types as options for select components
 *
 * This hook is useful for populating select components with cargo type options.
 *
 * @returns Cargo types as options, loading state, and error state
 */
export function useCargoTypeOptions() {
  const {
    data: options = [],
    isLoading: loading,
    error,
  } = useQuery({
    queryKey: ["cargoTypeOptions"],
    queryFn: async () => {
      const result = await getCargo();
      if (result.error) {
        throw new Error(result.error);
      }
      return result.data.map((type: CargoType) => ({
        value: type.id,
        label: type.name,
      })) as Option[];
    },
  });

  return { options, loading, error };
}
