"use client";

import { useQuery, useSuspenseQuery } from "@tanstack/react-query";
import { getProvidersWithEquipmentAction } from "@/lib/actions/provider.actions";
import { getRFQProvidersAction, getInvitedProvidersAction, getRFQBidsAction, mergeProvidersWithBidsAction } from "@/lib/actions/rfq.actions";
import { createLogger } from "@/lib/utils/logger/logger";
import { MAX_PAGE_SIZE, MAX_PAGES_SAFETY_LIMIT } from "@/lib/constants/pagination";
import type { ProviderWithEquipment } from "@/lib/schemas";

const logger = createLogger("ManualProviderDialogHooks");

/**
 * Custom hook to fetch all providers with equipment for the manual provider dialog
 * This hook fetches all providers by making multiple requests if needed (due to pagination limits)
 */
export function useAllProviders() {
  return useQuery({
    queryKey: ["providers", "all"],
    queryFn: async () => {
      logger.info("Fetching all providers with equipment...");

      try {
        let allProviders: ProviderWithEquipment[] = [];
        let currentPage = 1;
        const pageSize = MAX_PAGE_SIZE;
        let hasMoreData = true;

        while (hasMoreData) {
          logger.info(`Fetching providers page ${currentPage} with pageSize ${pageSize}`);

          const response = await getProvidersWithEquipmentAction({
            page: currentPage,
            pageSize: pageSize,
          });

          logger.info(`Provider action response for page ${currentPage}:`, {
            success: response.success,
            dataLength: response.success ? response.data?.length : 0,
            totalCount: response.totalCount,
            error: response.success ? null : response.error,
          });

          if (!response.success) {
            const errorMessage = response.error || "Failed to fetch providers";
            logger.error("Provider action failed:", errorMessage);
            throw new Error(errorMessage);
          }

          if (!response.data) {
            logger.warn("Provider action returned no data");
            break;
          }

          if (!Array.isArray(response.data)) {
            logger.error("Provider action returned non-array data:", typeof response.data);
            throw new Error("Invalid data format received from provider action");
          }

          allProviders = [...allProviders, ...response.data];

          // Check if we have more data to fetch
          const totalCount = response.totalCount || 0;
          const fetchedCount = currentPage * pageSize;
          hasMoreData = fetchedCount < totalCount && response.data.length === pageSize;

          logger.info(`Page ${currentPage} complete. Total fetched: ${allProviders.length}, Total available: ${totalCount}, Has more: ${hasMoreData}`);

          currentPage++;

          // Safety check to prevent infinite loops
          if (currentPage > MAX_PAGES_SAFETY_LIMIT) {
            logger.warn(`Reached maximum page limit (${MAX_PAGES_SAFETY_LIMIT}), stopping fetch`);
            break;
          }
        }

        logger.info(`Successfully fetched ${allProviders.length} providers across ${currentPage - 1} pages`);
        return allProviders;
      } catch (error) {
        logger.error("Error in provider fetch:", error);
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      logger.warn(`Provider fetch attempt ${failureCount + 1} failed:`, error);
      return failureCount < 2; // Retry up to 2 times
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

/**
 * Custom hook to fetch RFQ providers for a specific RFQ
 */
export function useRFQProviders(rfqId: string, enabled = true) {
  return useQuery({
    queryKey: ["rfq-providers", rfqId],
    queryFn: async () => {
      logger.info(`Fetching RFQ providers for RFQ: ${rfqId}`);

      try {
        const response = await getRFQProvidersAction(rfqId);

        logger.info("RFQ providers action response:", {
          success: response.success,
          dataLength: response.success ? response.data?.length : 0,
          error: response.success ? null : response.error,
        });

        if (!response.success) {
          const errorMessage = response.error || "Failed to fetch RFQ providers";
          logger.error("RFQ providers action failed:", errorMessage);
          throw new Error(errorMessage);
        }

        if (!response.data) {
          logger.warn("RFQ providers action returned no data");
          return [];
        }

        if (!Array.isArray(response.data)) {
          logger.error("RFQ providers action returned non-array data:", typeof response.data);
          throw new Error("Invalid data format received from RFQ providers action");
        }

        logger.info(`Successfully fetched ${response.data.length} RFQ providers`);
        return response.data;
      } catch (error) {
        logger.error("Error in RFQ providers fetch:", error);
        throw error;
      }
    },
    enabled: enabled && !!rfqId,
    staleTime: 2 * 60 * 1000, // 2 minutes (shorter for more real-time data)
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      logger.warn(`RFQ providers fetch attempt ${failureCount + 1} failed:`, error);
      return failureCount < 2; // Retry up to 2 times
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

/**
 * Custom hook to fetch invited providers for a specific RFQ
 *
 * This hook filters RFQ providers to show only those that have been invited,
 * following the same business logic as the service layer.
 *
 * @param rfqId - The RFQ ID to fetch invited providers for
 * @param options - Configuration options for the query
 * @param options.enabled - Whether the query should be enabled (default: true)
 * @param options.suspense - Whether to use Suspense mode for better UX (default: false)
 */
export function useInvitedProviders(
  rfqId: string,
  options: { enabled?: boolean; suspense?: boolean } = {}
) {
  const { enabled = true, suspense = false } = options;

  // Shared query function
  const queryFn = async () => {
    logger.info(`Fetching invited providers with bids for RFQ: ${rfqId}`);

    try {
      // Fetch RFQ providers and bids in parallel
      const [providersResponse, bidsResponse] = await Promise.all([
        getRFQProvidersAction(rfqId),
        getRFQBidsAction(rfqId)
      ]);

      if (!providersResponse.success) {
        logger.error("Failed to fetch RFQ providers:", providersResponse.error);
        throw new Error(providersResponse.error || "Failed to fetch RFQ providers");
      }

      if (!bidsResponse.success) {
        logger.error("Failed to fetch RFQ bids:", bidsResponse.error);
        throw new Error(bidsResponse.error || "Failed to fetch RFQ bids");
      }

      if (!providersResponse.data || !Array.isArray(providersResponse.data)) {
        logger.warn("No RFQ providers data received");
        return [];
      }

      // Merge providers with bids
      const mergeResult = await mergeProvidersWithBidsAction(
        providersResponse.data,
        bidsResponse.data || []
      );

      if (!mergeResult.success) {
        logger.error("Failed to merge providers with bids:", mergeResult.error);
        // Fallback to providers without bids
        const providersWithoutBids = providersResponse.data.map(provider => ({
          ...provider,
          bid: undefined
        }));

        // Filter to get only invited providers
        const invitedResult = await getInvitedProvidersAction(providersWithoutBids);
        return invitedResult.success ? invitedResult.data : [];
      }

      // Filter to get only invited providers using the action
      const invitedResult = await getInvitedProvidersAction(mergeResult.data);

      if (!invitedResult.success) {
        logger.error("Failed to filter invited providers:", invitedResult.error);
        // Fallback to client-side filtering
        const invited = mergeResult.data.filter((p) =>
          p.invited_at ||
          p.status === "invited" ||
          p.status === "declined" ||
          p.status === "bid_submitted"
        );
        return invited;
      }

      return invitedResult.data;
    } catch (error) {
      logger.error("Error in invited providers fetch:", error);
      throw error;
    }
  };

  // Shared query options
  const queryOptions = {
    queryKey: ["invited-providers", rfqId],
    queryFn,
    staleTime: 2 * 60 * 1000, // 2 minutes (shorter for more real-time data)
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount: number, error: Error) => {
      logger.warn(`Invited providers fetch attempt ${failureCount + 1} failed:`, error);
      return failureCount < 2; // Retry up to 2 times
    },
    retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
  };

  // Always call both hooks to comply with Rules of Hooks
  const suspenseQuery = useSuspenseQuery(queryOptions);
  const regularQuery = useQuery({
    ...queryOptions,
    enabled: enabled && !!rfqId,
  });

  // Return appropriate result based on suspense option
  return suspense ? suspenseQuery : regularQuery;
}

/**
 * Combined hook for the manual provider dialog that fetches both providers and RFQ providers
 */
export function useManualProviderDialogData(rfqId: string, enabled = true) {
  const providersQuery = useAllProviders();
  const rfqProvidersQuery = useRFQProviders(rfqId, enabled);

  // Determine if we have a critical error (providers failed to load)
  const hasCriticalError = providersQuery.isError;

  // RFQ providers failure is not critical - we can still show providers
  const hasNonCriticalError = rfqProvidersQuery.isError && !providersQuery.isError;

  if (hasNonCriticalError) {
    logger.warn("RFQ providers failed to load, but providers loaded successfully. Continuing with empty RFQ providers list.");
  }

  return {
    providers: providersQuery.data || [],
    rfqProviders: rfqProvidersQuery.data || [],
    isLoading: providersQuery.isLoading || (enabled && rfqProvidersQuery.isLoading),
    isError: hasCriticalError, // Only consider it an error if providers failed
    error: providersQuery.error, // Only show provider errors as critical
    hasNonCriticalError,
    nonCriticalError: rfqProvidersQuery.error,
    refetch: () => {
      logger.info("Refetching manual provider dialog data...");
      providersQuery.refetch();
      rfqProvidersQuery.refetch();
    },
    // Individual query states for debugging
    providersQuery: {
      isLoading: providersQuery.isLoading,
      isError: providersQuery.isError,
      error: providersQuery.error,
      data: providersQuery.data,
    },
    rfqProvidersQuery: {
      isLoading: rfqProvidersQuery.isLoading,
      isError: rfqProvidersQuery.isError,
      error: rfqProvidersQuery.error,
      data: rfqProvidersQuery.data,
    },
  };
}
