"use client";

import { useQuery } from "@tanstack/react-query";
import { getEquipment } from "@/lib/actions/equipment";
import { type EquipmentType } from "@/lib/schemas";
import { Option } from "@/components/ui/multi-select";

/**
 * Custom hook to fetch equipment types using TanStack Query
 *
 * This hook follows the architectural guidelines by using the action layer
 * instead of direct Supabase queries.
 *
 * @returns Equipment types data, loading state, and error state
 */
export function useEquipmentTypes() {
  const {
    data: equipmentTypes = [],
    isLoading: loading,
    error,
  } = useQuery({
    queryKey: ["equipmentTypes"],
    queryFn: async () => {
      const result = await getEquipment();
      if (result.error) {
        throw new Error(result.error);
      }
      return result.data as EquipmentType[];
    },
  });

  return { equipmentTypes, loading, error };
}

/**
 * Custom hook to fetch equipment types as options for select components
 *
 * This hook is useful for populating select components with equipment type options.
 *
 * @returns Equipment types as options, loading state, and error state
 */
export function useEquipmentTypeOptions() {
  const {
    data: options = [],
    isLoading: loading,
    error,
  } = useQuery({
    queryKey: ["equipmentTypeOptions"],
    queryFn: async () => {
      const result = await getEquipment();
      if (result.error) {
        throw new Error(result.error);
      }
      return result.data.map((type: EquipmentType) => ({
        value: type.id,
        label: type.name,
      })) as Option[];
    },
  });

  return { options, loading, error };
}
