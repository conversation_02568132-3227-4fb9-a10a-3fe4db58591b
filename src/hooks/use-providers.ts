"use client";

import { useQuery } from "@tanstack/react-query";
import {
  getProvidersWithEquipmentAction,
  getProviderCountsByStatusAction,
  getProviderRFQInvitationCountsAction
} from "@/lib/actions/provider.actions";

/**
 * Custom hook to fetch all providers with equipment and RFQ invitation counts
 * This hook fetches all providers, handling pagination if needed
 * @param options Optional query options
 */
export function useProviders(options: { suspense?: boolean } = {}) {
  return useQuery({
    queryKey: ["providers"],
    ...options,
    queryFn: async () => {
      // First, fetch the first page to get the total count
      const firstPageResponse = await getProvidersWithEquipmentAction({
        page: 1,
        pageSize: 100
      });

      if (!firstPageResponse.success) {
        throw new Error(firstPageResponse.error || "Failed to fetch providers");
      }

      const totalCount = firstPageResponse.totalCount || 0;
      let allProviders = [...(firstPageResponse.data || [])];

      // If there are more providers than the page size, fetch additional pages
      if (totalCount > 100) {
        const totalPages = Math.ceil(totalCount / 100);

        // Fetch remaining pages (starting from page 2)
        const additionalPagesPromises = [];
        for (let page = 2; page <= totalPages; page++) {
          additionalPagesPromises.push(
            getProvidersWithEquipmentAction({ page, pageSize: 100 })
          );
        }

        const additionalPagesResponses = await Promise.all(additionalPagesPromises);

        // Combine all providers from additional pages
        additionalPagesResponses.forEach(response => {
          if (response.success && response.data) {
            allProviders = [...allProviders, ...response.data];
          }
        });
      }

      // Fetch RFQ invitation counts for all providers
      const rfqInvitationCountsResponse = await getProviderRFQInvitationCountsAction();

      // Merge RFQ invitation counts with providers
      const providersWithRFQCounts = allProviders.map(provider => ({
        ...provider,
        rfqInvitationCount: rfqInvitationCountsResponse.success
          ? rfqInvitationCountsResponse.data?.[provider.id] || 0
          : 0
      }));

      return {
        providers: providersWithRFQCounts,
        totalCount
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Custom hook to fetch provider counts by status
 */
export function useProviderCounts() {
  return useQuery({
    queryKey: ["providerCounts"],
    queryFn: async () => {
      const response = await getProviderCountsByStatusAction();
      if (!response.success) {
        throw new Error(response.error || "Failed to fetch provider counts");
      }
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
