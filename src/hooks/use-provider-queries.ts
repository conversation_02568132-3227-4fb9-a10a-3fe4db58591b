"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getProviderEquipmentsAction } from "@/lib/actions/provider-equipments.actions";
import { updateProviderAction, createProviderAction } from "@/lib/actions/provider-form.actions";
import { updateProviderEquipmentsAction } from "@/lib/actions/provider-form.actions";
import { ProviderFormValues } from "@/lib/schemas";

/**
 * Custom hook to fetch provider equipments
 *
 * @param providerId - The ID of the provider to fetch equipments for
 * @returns Provider equipments data, loading state, and error state
 */
export function useProviderEquipments(providerId: string) {
  return useQuery({
    queryKey: ["providerEquipments", providerId],
    queryFn: async () => {
      if (!providerId) {
        return [];
      }
      const result = await getProviderEquipmentsAction(providerId);
      if (!result.success) {
        throw new Error(result.error || "Failed to fetch provider equipments");
      }
      return result.data.map((item: any) => item.id) as string[];
    },
    enabled: !!providerId,
  });
}

/**
 * Custom hook to update a provider
 *
 * @returns Mutation function for updating a provider
 */
export function useUpdateProvider() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      providerId,
      values,
    }: {
      providerId: string;
      values: ProviderFormValues;
    }) => {
      const formData = new FormData();
      formData.append("id", providerId);
      formData.append("name", values.name);
      formData.append("tax_id", values.tax_id || "");
      formData.append("full_address", values.full_address || "");
      formData.append("status", values.status);
      formData.append("verified", values.verified.toString());

      const result = await updateProviderAction(
        {
          success: false,
          values,
        },
        formData
      );

      if (!result.success) {
        throw new Error(result.error || "Failed to update provider");
      }

      return result;
    },
    onSuccess: () => {
      // Invalidate the main providers cache to refresh the table
      queryClient.invalidateQueries({ queryKey: ["providers"] });
      // Invalidate provider counts cache as provider updates might affect statistics
      queryClient.invalidateQueries({ queryKey: ["providerCounts"] });
    },
  });
}

/**
 * Custom hook to create a provider
 *
 * @returns Mutation function for creating a provider
 */
export function useCreateProvider() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (values: ProviderFormValues) => {
      const formData = new FormData();
      formData.append("name", values.name);
      formData.append("tax_id", values.tax_id || "");
      formData.append("full_address", values.full_address || "");
      formData.append("status", values.status);
      formData.append("verified", values.verified.toString());

      const result = await createProviderAction(
        {
          success: false,
          values,
        },
        formData
      );

      if (!result.success) {
        throw new Error(result.error || "Failed to create provider");
      }

      return result;
    },
    onSuccess: () => {
      // Invalidate the main providers cache to refresh the table
      queryClient.invalidateQueries({ queryKey: ["providers"] });
      // Invalidate provider counts cache as new provider affects statistics
      queryClient.invalidateQueries({ queryKey: ["providerCounts"] });
    },
  });
}

/**
 * Custom hook to update provider equipments
 *
 * @returns Mutation function for updating provider equipments
 */
export function useUpdateProviderEquipments() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      providerId,
      equipmentIds,
    }: {
      providerId: string;
      equipmentIds: string[];
    }) => {
      const formData = new FormData();
      formData.append("providerId", providerId);

      equipmentIds.forEach((id) => {
        formData.append("selectedEquipments", id);
      });

      const result = await updateProviderEquipmentsAction(
        {
          success: false,
          selectedEquipments: equipmentIds,
        },
        formData
      );

      if (!result.success) {
        throw new Error(result.error || "Failed to update provider equipments");
      }

      return result;
    },
    onSuccess: (_, variables) => {
      // Invalidate provider equipments cache for this specific provider
      queryClient.invalidateQueries({
        queryKey: ["providerEquipments", variables.providerId],
      });
      // Invalidate the main providers cache to refresh the table with updated equipment data
      queryClient.invalidateQueries({
        queryKey: ["providers"],
      });
      // Invalidate provider counts cache as equipment changes might affect statistics
      queryClient.invalidateQueries({
        queryKey: ["providerCounts"],
      });
    },
  });
}
