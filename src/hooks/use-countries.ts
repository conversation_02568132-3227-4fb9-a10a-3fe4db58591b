"use client";

import { useQuery } from "@tanstack/react-query";
import { useState, useMemo } from "react";
import { getCountriesAction } from "@/lib/actions/country.actions";
import { type Country } from "@/lib/schemas";
import { Option } from "@/components/ui/multi-select";

/**
 * Custom hook to fetch countries using TanStack Query
 *
 * This hook follows the architectural guidelines by using the action layer
 * instead of direct Supabase queries.
 *
 * @returns Countries data, loading state, and error state
 */
export function useCountries() {
  const {
    data: countries = [],
    isLoading: loading,
    error,
  } = useQuery({
    queryKey: ["countries"],
    queryFn: async () => {
      const result = await getCountriesAction();
      if (!result.success) {
        throw new Error(result.error || "Failed to fetch countries");
      }
      return result.data as Country[];
    },
  });

  return { countries, loading, error };
}

/**
 * Custom hook to fetch countries as options for select components
 *
 * This hook is useful for populating select components with country options.
 *
 * @returns Countries as options, loading state, and error state
 */
export function useCountryOptions() {
  const {
    data: options = [],
    isLoading: loading,
    error,
  } = useQuery({
    queryKey: ["countryOptions"],
    queryFn: async () => {
      const result = await getCountriesAction();
      if (!result.success) {
        throw new Error(result.error || "Failed to fetch countries");
      }
      return result.data.map((country: Country) => ({
        value: country.id,
        label: country.name,
      })) as Option[];
    },
  });

  return { options, loading, error };
}

/**
 * Custom hook for filtering countries based on search text
 *
 * @param countries - List of countries to filter
 * @param initialSearch - Initial search text
 * @returns Filtered countries and search state handlers
 */
export function useCountrySearch(
  countries: Country[],
  initialSearch: string = "",
) {
  const [searchText, setSearchText] = useState(initialSearch);

  // Filter countries based on search text
  const filteredCountries = useMemo(() => {
    if (!searchText.trim()) {
      return countries;
    }

    const searchLower = searchText.toLowerCase();
    return countries.filter(
      (country) =>
        country.name.toLowerCase().includes(searchLower) ||
        (country.alpha2_code && country.alpha2_code.toLowerCase().includes(searchLower)) ||
        (country.alpha3_code && country.alpha3_code.toLowerCase().includes(searchLower))
    );
  }, [countries, searchText]);

  return {
    searchText,
    setSearchText,
    filteredCountries,
  };
}
