/**
 * Provider Selection Data Hook
 *
 * Consolidated hook that uses regular useQuery to avoid setState-during-render errors.
 * Provides proper loading states and eliminates the flash of "No results found" during initial loading.
 * Uses the unified service to fetch provider selection data.
 */

import { useQuery } from "@tanstack/react-query";
import { createLogger } from "@/lib/utils/logger/logger";
import { ManualProviderType } from "@/lib/schemas";
import { getProviderSelectionDataAction } from "@/lib/actions/rfq.actions";

const logger = createLogger("useProviderSelectionData");

interface UseProviderSelectionDataProps {
  rfqId: string;
  searchQuery?: string;
  statusFilter?: string;
  manuallyAddedProviders: ManualProviderType[];
}

interface UseProviderSelectionDataReturn {
  transformedProviders: any[];
  isLoading: boolean;
  error: any;
  refetch: () => void;
  totalCount: number;
}

/**
 * Provider selection data hook
 *
 * This hook:
 * 1. Uses regular useQuery to avoid setState-during-render errors
 * 2. Eliminates the flash of "No results found" during initial loading
 * 3. Calls the unified service action
 * 4. Returns data ready for UI consumption with proper loading states
 * 5. Handles manual providers through the service layer
 * 6. Provides consistent error handling
 */
export function useProviderSelectionData({
  rfqId,
  searchQuery = "",
  statusFilter = "all",
  manuallyAddedProviders,
}: UseProviderSelectionDataProps): UseProviderSelectionDataReturn {
  // Fetch data using regular useQuery to avoid setState-during-render errors
  const { data: actionResult, isLoading, error, refetch } = useQuery({
    queryKey: ["provider-selection-unified", rfqId, searchQuery, statusFilter, manuallyAddedProviders],
    queryFn: async () => {
      logger.info("Fetching provider selection data", { rfqId, searchQuery, statusFilter });

      const result = await getProviderSelectionDataAction(rfqId, {
        searchQuery,
        statusFilter,
        includeManualProviders: manuallyAddedProviders,
      });

      if (!result.success) {
        throw new Error(result.error || "Failed to fetch provider selection data");
      }

      return result.data;
    },
    staleTime: 30000, // 30 seconds
    gcTime: 300000, // 5 minutes
    retry: 2,
    retryDelay: 1000,
    enabled: !!rfqId, // Only run query when rfqId is available
  });

  return {
    transformedProviders: actionResult?.availableProviders || [],
    isLoading,
    error,
    refetch,
    totalCount: actionResult?.totalCount || 0,
  };
}
