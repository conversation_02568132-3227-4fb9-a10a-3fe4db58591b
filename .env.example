# Application
PUBLIC_APP_URL=http://localhost:3000

# Database
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Google OAuth credentials
GMAIL_CLIENT_ID=your-gmail-client-id
GMAIL_CLIENT_SECRET=your-gmail-client-secret
GMAIL_OAUTH_REDIRECT_URI=http://localhost:3000/admin/email-accounts/callback

# Google Maps API
GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Email Polling
EMAIL_POLLING_SECRET_KEY=your-secret-key-here
EMAIL_POLLING_INTERVAL=5
EMAIL_DISABLE_ATTACHMENTS=true
EMAIL_SKIP_IMAGE_ATTACHMENTS=true
EMAIL_SKIP_MIME_TYPES=video/,audio/,application/pdf

# SendGrid and Email Configuration
SENDGRID_API_KEY=your-sendgrid-api-key
EMAIL_FORCE_SANDBOX=true  # Force sandbox mode regardless of database settings
NODE_ENV=development    # When set to 'development', forces sandbox mode unless EMAIL_FORCE_SANDBOX is 'false'

# Unkey
UNKEY_API_KEY=your-unkey-api-key
UNKEY_API_ID=your-unkey-api-id

# Gemini AI (server-side only, not exposed to client)
GEMINI_API_KEY=your-gemini-api-key
