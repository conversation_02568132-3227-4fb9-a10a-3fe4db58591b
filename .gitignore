# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env
.env.local
.env.test
.env.development.local
.env.test.local
.env.production
!.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

.cursor/*

# supabase
supabase/.temp/*
supabase/migrations/supabase/.temp/
.aider*

# augment
.augment-guidelines

# scripts
scripts/setup-email-polling.js

# Added by <PERSON> Task Master
# Logs
logs
*.log
dev-debug.log

# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/

# Added by <PERSON> Task Master
# Dependency directories
node_modules/
scripts/

test_results/
repomix-output.xml