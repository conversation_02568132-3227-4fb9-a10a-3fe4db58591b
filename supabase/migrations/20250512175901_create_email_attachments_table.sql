-- Create email_attachments table with improved structure, comments, constraints, and indexes
CREATE TABLE public.email_attachments (
    -- Primary Key
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,

    -- Foreign Keys
    message_id uuid NOT NULL,

    -- Identifiers
    gmail_attachment_id text NOT NULL,
    filename text NOT NULL,

    -- Metadata
    content_type text,
    size integer,
    storage_path text,

    -- Content
    content bytea,

    -- Timestamps
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);

COMMENT ON TABLE public.email_attachments IS 'Attachments from email messages';
COMMENT ON COLUMN public.email_attachments.id IS 'Primary key: Unique identifier for the email attachment (UUID).';
COMMENT ON COLUMN public.email_attachments.message_id IS 'Foreign key: References the related email message (public.email_messages.id).';
COMMENT ON COLUMN public.email_attachments.gmail_attachment_id IS 'Gmail attachment ID (unique per message).';
COMMENT ON COLUMN public.email_attachments.filename IS 'Original filename of the attachment.';
COMMENT ON COLUMN public.email_attachments.content_type IS 'MIME type of the attachment (e.g., application/pdf, image/png).';
COMMENT ON COLUMN public.email_attachments.size IS 'Size of the attachment in bytes.';
COMMENT ON COLUMN public.email_attachments.storage_path IS 'Path to the attachment in external storage, if applicable.';
COMMENT ON COLUMN public.email_attachments.content IS 'Raw binary content of the attachment (may be NULL if stored externally).';
COMMENT ON COLUMN public.email_attachments.created_at IS 'Timestamp when the attachment record was created.';
COMMENT ON COLUMN public.email_attachments.updated_at IS 'Timestamp when the attachment record was last updated.';

-- Constraints
ALTER TABLE ONLY public.email_attachments ADD CONSTRAINT email_attachments_message_id_gmail_attachment_id_key UNIQUE (message_id, gmail_attachment_id);
ALTER TABLE ONLY public.email_attachments ADD CONSTRAINT email_attachments_message_id_fkey FOREIGN KEY (message_id) REFERENCES public.email_messages(id) ON DELETE CASCADE;

-- Indexes for frequently queried columns
CREATE INDEX IF NOT EXISTS idx_email_attachments_message_id ON public.email_attachments USING btree (message_id);
CREATE INDEX IF NOT EXISTS idx_email_attachments_gmail_attachment_id ON public.email_attachments USING btree (gmail_attachment_id);
CREATE INDEX IF NOT EXISTS idx_email_attachments_filename ON public.email_attachments USING btree (filename);
CREATE INDEX IF NOT EXISTS idx_email_attachments_created_at ON public.email_attachments USING btree (created_at);

-- Enable Row Level Security (RLS)
ALTER TABLE public.email_attachments ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to modify email attachments
CREATE POLICY "Authenticated users can modify email attachments"
ON public.email_attachments FOR ALL USING (auth.role() = 'authenticated');

CREATE TRIGGER update_email_attachments_updated_at BEFORE UPDATE ON public.email_attachments FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
