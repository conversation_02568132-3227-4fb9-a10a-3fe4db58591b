-- <PERSON><PERSON> watched_email_accounts table for Gmail API monitoring
CREATE TABLE public.watched_email_accounts (
    -- Primary Key
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,

    -- Required fields
    email character varying(255) NOT NULL UNIQUE, -- Unique email address being monitored
    refresh_token text NOT NULL, -- OAuth2 refresh token for Gmail API (required)
    status character varying(50) DEFAULT 'active'::character varying NOT NULL, -- Account status

    -- Identity/metadata
    display_name character varying(255), -- Display name for the email account
    created_by uuid, -- User who added this account (FK to auth.users)

    -- OAuth & Gmail sync
    access_token text, -- OAuth2 access token for Gmail API (optional, short-lived)
    token_expiry timestamp with time zone, -- When the access token expires
    watch_expiry timestamp with time zone, -- When the Gmail watch expires (if using push notifications)

    -- Sync tracking
    last_sync_at timestamp with time zone, -- Last successful sync time
    last_full_sync_at timestamp with time zone, -- Last full mailbox sync time
    sync_error text, -- Last sync error message, if any
    sync_message text, -- Last sync status/info message

    -- Timestamps
    created_at timestamp with time zone DEFAULT now() NOT NULL, -- When the record was created
    updated_at timestamp with time zone DEFAULT now() NOT NULL -- When the record was last updated
);

COMMENT ON TABLE public.watched_email_accounts IS 'Email accounts being monitored via Gmail API';
COMMENT ON COLUMN public.watched_email_accounts.id IS 'Primary key: Unique identifier for the watched email account (UUID).';
COMMENT ON COLUMN public.watched_email_accounts.email IS 'Unique email address being monitored.';
COMMENT ON COLUMN public.watched_email_accounts.refresh_token IS 'OAuth2 refresh token for Gmail API (required, long-lived).';
COMMENT ON COLUMN public.watched_email_accounts.status IS 'Current status of the account: active, paused, error, revoked, or syncing.';
COMMENT ON COLUMN public.watched_email_accounts.display_name IS 'Display name for the email account (optional, for UI).';
COMMENT ON COLUMN public.watched_email_accounts.created_by IS 'User who added this account (foreign key to auth.users.id).';
COMMENT ON COLUMN public.watched_email_accounts.access_token IS 'OAuth2 access token for Gmail API (optional, short-lived).';
COMMENT ON COLUMN public.watched_email_accounts.token_expiry IS 'Timestamp when the access token expires.';
COMMENT ON COLUMN public.watched_email_accounts.watch_expiry IS 'Timestamp when the Gmail watch expires (if using push notifications).';
COMMENT ON COLUMN public.watched_email_accounts.last_sync_at IS 'Timestamp of the last successful sync.';
COMMENT ON COLUMN public.watched_email_accounts.last_full_sync_at IS 'Timestamp of the last full mailbox sync.';
COMMENT ON COLUMN public.watched_email_accounts.sync_error IS 'Last sync error message, if any.';
COMMENT ON COLUMN public.watched_email_accounts.sync_message IS 'Last sync status or informational message.';
COMMENT ON COLUMN public.watched_email_accounts.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN public.watched_email_accounts.updated_at IS 'Timestamp when the record was last updated.';

-- Constraints
ALTER TABLE ONLY public.watched_email_accounts ADD CONSTRAINT valid_status CHECK (
  status IN ('active', 'paused', 'error', 'revoked', 'syncing')
);

-- Indexes for frequently queried columns
CREATE INDEX IF NOT EXISTS idx_watched_email_accounts_email ON public.watched_email_accounts USING btree (email);
CREATE INDEX IF NOT EXISTS idx_watched_email_accounts_status ON public.watched_email_accounts USING btree (status);
CREATE INDEX IF NOT EXISTS idx_watched_email_accounts_created_by ON public.watched_email_accounts USING btree (created_by);
CREATE INDEX IF NOT EXISTS idx_watched_email_accounts_last_sync_at ON public.watched_email_accounts USING btree (last_sync_at);

-- Foreign key constraint
ALTER TABLE ONLY public.watched_email_accounts
  ADD CONSTRAINT watched_email_accounts_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id);

-- Enable Row Level Security (RLS)
ALTER TABLE public.watched_email_accounts ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to modify watched email accounts
CREATE POLICY "Authenticated users can modify watched email accounts"
ON public.watched_email_accounts FOR ALL USING (auth.role() = 'authenticated');

CREATE TRIGGER update_watched_email_accounts_updated_at BEFORE UPDATE ON public.watched_email_accounts FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
