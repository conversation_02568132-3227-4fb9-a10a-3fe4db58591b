-- Migration: Create email_settings table
-- ----------------------------------------------------------------------
-- This migration creates a table to store email configuration settings
-- that were previously managed through environment variables.

-- Create email_settings table
CREATE TABLE public.email_settings (
    -- Primary Key
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,

    -- Company Information
    company_name text NOT NULL,
    company_logo_url text,

    -- Email Configuration
    sandbox_mode boolean NOT NULL DEFAULT true,
    default_sender_email text NOT NULL,
    default_sender_name text NOT NULL,
    reply_to_email text,
    sandbox_recipient_email text,
    bcc_recipients text[],

    -- Email Content
    email_signature text,
    email_footer_text text,

    -- Metada<PERSON>
    created_by uuid REFERENCES auth.users(id),
    updated_by uuid REFERENCES auth.users(id),

    -- Timestamps
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Add comments to the table and columns
COMMENT ON TABLE public.email_settings IS 'Stores email configuration settings for the application';
COMMENT ON COLUMN public.email_settings.company_name IS 'Company name to display in email headers and footers';
COMMENT ON COLUMN public.email_settings.company_logo_url IS 'URL to the company logo for email templates';
COMMENT ON COLUMN public.email_settings.sandbox_mode IS 'When true, emails are sent to a test address instead of actual recipients';
COMMENT ON COLUMN public.email_settings.default_sender_email IS 'Default email address used as the sender';
COMMENT ON COLUMN public.email_settings.default_sender_name IS 'Default name displayed as the sender';
COMMENT ON COLUMN public.email_settings.reply_to_email IS 'Email address for recipients to reply to';
COMMENT ON COLUMN public.email_settings.email_signature IS 'Signature text to include in emails';
COMMENT ON COLUMN public.email_settings.email_footer_text IS 'Custom text to display in email footers';
COMMENT ON COLUMN public.email_settings.sandbox_recipient_email IS 'Email address to redirect all emails to when sandbox mode is enabled';
COMMENT ON COLUMN public.email_settings.bcc_recipients IS 'Array of email addresses to BCC on all outgoing emails';


-- Add RLS policies
ALTER TABLE public.email_settings ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to modify email attachments
CREATE POLICY "Authenticated users can modify email settings"
ON public.email_settings FOR ALL USING (auth.role() = 'authenticated');