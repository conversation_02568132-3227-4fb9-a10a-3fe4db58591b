-- Create RFQ Outgoing Emails table with improved structure, comments, constraints, and indexes
CREATE TABLE public.rfq_outgoing_emails (
    -- Primary Key
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,

    -- Foreign Keys
    rfq_id uuid NOT NULL,
    provider_id uuid NOT NULL,

    -- Email Identifiers
    message_id text UNIQUE,
    thread_id text,
    reply_to text,
    tracking_id text,

    -- Email Content
    subject text NOT NULL,
    body text NOT NULL,
    to_email text NOT NULL,
    to_name text,
    cc text[],
    bcc text[],

    -- Status & Timestamps
    old_status text NOT NULL,
    delivery_status text DEFAULT 'pending'::text NOT NULL,
    sent_at timestamp with time zone,
    delivered_at timestamp with time zone,
    opened_at timestamp with time zone,
    clicked_at timestamp with time zone,
    bounce_reason text,
    failure_reason text,
    is_ai_generated boolean DEFAULT false NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);

COMMENT ON TABLE public.rfq_outgoing_emails IS 'Stores outgoing emails sent to providers for RFQs, including delivery status tracking.';

-- Column Comments
COMMENT ON COLUMN public.rfq_outgoing_emails.id IS 'Primary key: Unique identifier for the outgoing email (UUID).';
COMMENT ON COLUMN public.rfq_outgoing_emails.rfq_id IS 'Foreign key: References the related RFQ (public.rfqs.id).';
COMMENT ON COLUMN public.rfq_outgoing_emails.provider_id IS 'Foreign key: References the provider receiving the email (public.providers.id).';
COMMENT ON COLUMN public.rfq_outgoing_emails.message_id IS 'Unique message identifier from the email provider.';
COMMENT ON COLUMN public.rfq_outgoing_emails.thread_id IS 'Thread identifier for grouping related emails.';
COMMENT ON COLUMN public.rfq_outgoing_emails.reply_to IS 'Message ID this email is replying to, if any.';
COMMENT ON COLUMN public.rfq_outgoing_emails.tracking_id IS 'Tracking ID for email delivery and open/click tracking.';
COMMENT ON COLUMN public.rfq_outgoing_emails.subject IS 'Subject line of the outgoing email.';
COMMENT ON COLUMN public.rfq_outgoing_emails.body IS 'Full body content of the outgoing email.';
COMMENT ON COLUMN public.rfq_outgoing_emails.to_email IS 'Recipient email address.';
COMMENT ON COLUMN public.rfq_outgoing_emails.to_name IS 'Recipient display name, if available.';
COMMENT ON COLUMN public.rfq_outgoing_emails.cc IS 'Array of CC (carbon copy) email addresses.';
COMMENT ON COLUMN public.rfq_outgoing_emails.bcc IS 'Array of BCC (blind carbon copy) email addresses.';
COMMENT ON COLUMN public.rfq_outgoing_emails.old_status IS 'Previous status of the email (draft, sent, delivered, opened, failed).';
COMMENT ON COLUMN public.rfq_outgoing_emails.delivery_status IS 'Current delivery status (pending, sent, delivered, opened, clicked, bounced, failed).';
COMMENT ON COLUMN public.rfq_outgoing_emails.sent_at IS 'Timestamp when the email was sent.';
COMMENT ON COLUMN public.rfq_outgoing_emails.delivered_at IS 'Timestamp when the email was delivered.';
COMMENT ON COLUMN public.rfq_outgoing_emails.opened_at IS 'Timestamp when the email was opened by the recipient.';
COMMENT ON COLUMN public.rfq_outgoing_emails.clicked_at IS 'Timestamp when a link in the email was clicked.';
COMMENT ON COLUMN public.rfq_outgoing_emails.bounce_reason IS 'Reason for email bounce, if any.';
COMMENT ON COLUMN public.rfq_outgoing_emails.failure_reason IS 'Reason for email delivery failure, if any.';
COMMENT ON COLUMN public.rfq_outgoing_emails.is_ai_generated IS 'Indicates if the email content was generated by AI.';
COMMENT ON COLUMN public.rfq_outgoing_emails.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN public.rfq_outgoing_emails.updated_at IS 'Timestamp when the record was last updated.';

-- Constraints and Indexes
ALTER TABLE ONLY public.rfq_outgoing_emails ADD CONSTRAINT rfq_outgoing_emails_delivery_status_check CHECK ((delivery_status = ANY (ARRAY['pending'::text, 'sent'::text, 'delivered'::text, 'opened'::text, 'clicked'::text, 'bounced'::text, 'failed'::text])));
ALTER TABLE ONLY public.rfq_outgoing_emails ADD CONSTRAINT rfq_outgoing_emails_old_status_check CHECK ((old_status = ANY (ARRAY['draft'::text, 'sent'::text, 'delivered'::text, 'opened'::text, 'failed'::text])));

CREATE INDEX idx_rfq_outgoing_emails_delivery_status ON public.rfq_outgoing_emails USING btree (delivery_status);
CREATE INDEX idx_rfq_outgoing_emails_message_id ON public.rfq_outgoing_emails USING btree (message_id);
CREATE INDEX idx_rfq_outgoing_emails_provider_id ON public.rfq_outgoing_emails USING btree (provider_id);
CREATE INDEX idx_rfq_outgoing_emails_reply_to ON public.rfq_outgoing_emails USING btree (reply_to);
CREATE INDEX idx_rfq_outgoing_emails_rfq_id ON public.rfq_outgoing_emails USING btree (rfq_id);
CREATE INDEX idx_rfq_outgoing_emails_thread_id ON public.rfq_outgoing_emails USING btree (thread_id);
CREATE INDEX idx_rfq_outgoing_emails_tracking_id ON public.rfq_outgoing_emails USING btree (tracking_id);

-- Foreign Keys
ALTER TABLE ONLY public.rfq_outgoing_emails ADD CONSTRAINT rfq_outgoing_emails_rfq_id_fkey FOREIGN KEY (rfq_id) REFERENCES public.rfqs(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.rfq_outgoing_emails ADD CONSTRAINT rfq_outgoing_emails_provider_id_fkey FOREIGN KEY (provider_id) REFERENCES public.providers(id);

-- Row Level Security (RLS)
ALTER TABLE public.rfq_outgoing_emails ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to modify outgoing RFQ emails
CREATE POLICY "Authenticated users can modify outgoing RFQ emails"
ON public.rfq_outgoing_emails FOR ALL USING (auth.role() = 'authenticated');

CREATE TRIGGER update_rfq_outgoing_emails_updated_at BEFORE UPDATE ON public.rfq_outgoing_emails FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
