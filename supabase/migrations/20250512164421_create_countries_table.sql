set search_path = public, extensions;

CREATE TABLE public.countries (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
    name text NOT NULL,
    alpha2_code character(2),
    alpha3_code character varying(3),
    currency_code character varying(3),
    continent character varying(50),
    region character varying(100),
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);
COMMENT ON TABLE public.countries IS 'Reference table for countries with ISO codes, currency, and geographical information';
COMMENT ON COLUMN public.countries.id IS 'Primary key: UUID for the country record';
COMMENT ON COLUMN public.countries.name IS 'Official country name';
COMMENT ON COLUMN public.countries.created_at IS 'Timestamp when the record was created (UTC)';
COMMENT ON COLUMN public.countries.updated_at IS 'Timestamp when the record was last updated (UTC)';
COMMENT ON COLUMN public.countries.alpha2_code IS 'ISO 3166-1 alpha-2 country code (2 characters)';
COMMENT ON COLUMN public.countries.alpha3_code IS 'ISO 3166-1 alpha-3 country code (3 characters)';
COMMENT ON COLUMN public.countries.currency_code IS 'ISO 4217 currency code (3 characters)';
COMMENT ON COLUMN public.countries.continent IS 'Continent where the country is located';
COMMENT ON COLUMN public.countries.region IS 'Geographical region within the continent';

-- Indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_countries_continent ON public.countries USING btree (continent);
CREATE INDEX IF NOT EXISTS idx_countries_region ON public.countries USING btree (region);

-- Constraints for unique codes
ALTER TABLE ONLY public.countries ADD CONSTRAINT countries_alpha2_code_key UNIQUE (alpha2_code);
ALTER TABLE ONLY public.countries ADD CONSTRAINT countries_alpha3_code_key UNIQUE (alpha3_code);

-- RLS for Countries
ALTER TABLE public.countries ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to modify countries
CREATE POLICY "Authenticated users can modify countries"
ON public.countries FOR ALL USING (auth.role() = 'authenticated');

CREATE TRIGGER update_countries_updated_at BEFORE UPDATE ON public.countries FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
