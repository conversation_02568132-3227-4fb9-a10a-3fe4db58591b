CREATE TABLE public.providers (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
    name text NOT NULL,
    tax_id text,
    full_address text,
    status text DEFAULT 'pending'::text NOT NULL,
    verified boolean DEFAULT false NOT NULL,
    structured_address jsonb,
    created_by uuid,
    updated_by uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);
COMMENT ON TABLE public.providers IS 'Stores information about logistics providers';
COMMENT ON COLUMN public.providers.id IS 'Primary key: unique provider identifier (UUID)';
COMMENT ON COLUMN public.providers.name IS 'Provider name (required, unique per provider)';
COMMENT ON COLUMN public.providers.tax_id IS 'Tax identification number for the provider';
COMMENT ON COLUMN public.providers.full_address IS 'Full address as a single text field';
COMMENT ON COLUMN public.providers.status IS 'Provider status: active, inactive, pending, or suspended';
COMMENT ON COLUMN public.providers.verified IS 'Whether the provider has been verified (boolean)';
COMMENT ON COLUMN public.providers.created_at IS 'Timestamp when the provider was created';
COMMENT ON COLUMN public.providers.updated_at IS 'Timestamp when the provider was last updated';
COMMENT ON COLUMN public.providers.created_by IS 'User ID of the creator (references users.id)';
COMMENT ON COLUMN public.providers.updated_by IS 'User ID of the last updater (references users.id)';
COMMENT ON COLUMN public.providers.structured_address IS 'Structured address in JSONB format (should include lat/lon)';
ALTER TABLE ONLY public.providers ADD CONSTRAINT providers_status_check CHECK ((status = ANY (ARRAY['active'::text, 'inactive'::text, 'pending'::text, 'suspended'::text])));

-- FKs for providers (assuming users table already exists due to file order)
ALTER TABLE ONLY public.providers ADD CONSTRAINT providers_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);
ALTER TABLE ONLY public.providers ADD CONSTRAINT providers_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.users(id);

-- RLS for providers
ALTER TABLE public.providers ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to modify providers
CREATE POLICY "Authenticated users can modify providers"
ON public.providers FOR ALL USING (auth.role() = 'authenticated');

CREATE TRIGGER update_providers_updated_at BEFORE UPDATE ON public.providers FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
