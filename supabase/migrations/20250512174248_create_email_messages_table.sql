CREATE TABLE public.email_messages (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
    account_id uuid NOT NULL,
    gmail_id character varying(255) NOT NULL,
    gmail_thread_id character varying(255) NOT NULL,
    subject text,
    snippet text,
    body_html text,
    body_text text,
    from_email character varying(255),
    from_name character varying(255),
    to_recipients jsonb,
    cc_recipients jsonb,
    bcc_recipients jsonb,
    reply_to text,
    date_received timestamp with time zone,
    labels jsonb,
    category character varying(50),
    in_reply_to text,
    is_read boolean DEFAULT false,
    is_starred boolean DEFAULT false,
    is_important boolean DEFAULT false,
    is_trash boolean DEFAULT false,
    processed_for_rfq boolean DEFAULT false NOT NULL,
    rfq_id uuid,
    provider_id uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

COMMENT ON TABLE public.email_messages IS 'Email messages retrieved from watched Gmail accounts.';
COMMENT ON COLUMN public.email_messages.id IS 'Primary key: Unique identifier for the email message (UUID).';
COMMENT ON COLUMN public.email_messages.account_id IS 'Foreign key: References the watched email account (public.watched_email_accounts.id).';
COMMENT ON COLUMN public.email_messages.gmail_id IS 'Gmail message ID (unique per account).';
COMMENT ON COLUMN public.email_messages.gmail_thread_id IS 'Gmail thread ID for grouping related messages.';
COMMENT ON COLUMN public.email_messages.subject IS 'Subject line of the email.';
COMMENT ON COLUMN public.email_messages.snippet IS 'Short snippet/preview of the email body.';
COMMENT ON COLUMN public.email_messages.body_html IS 'Full HTML body of the email.';
COMMENT ON COLUMN public.email_messages.body_text IS 'Full plain text body of the email.';
COMMENT ON COLUMN public.email_messages.from_email IS 'Sender email address.';
COMMENT ON COLUMN public.email_messages.from_name IS 'Sender display name, if available.';
COMMENT ON COLUMN public.email_messages.to_recipients IS 'JSONB array of To recipients (name/email objects).';
COMMENT ON COLUMN public.email_messages.cc_recipients IS 'JSONB array of CC recipients (name/email objects).';
COMMENT ON COLUMN public.email_messages.bcc_recipients IS 'JSONB array of BCC recipients (name/email objects).';
COMMENT ON COLUMN public.email_messages.reply_to IS 'Reply-To email address or addresses.';
COMMENT ON COLUMN public.email_messages.date_received IS 'Timestamp when the email was received.';
COMMENT ON COLUMN public.email_messages.labels IS 'JSONB array of labels/tags from Gmail.';
COMMENT ON COLUMN public.email_messages.category IS 'Category of the email (e.g., primary, social, promotions).';
COMMENT ON COLUMN public.email_messages.in_reply_to IS 'Message ID this email is replying to, if any.';
COMMENT ON COLUMN public.email_messages.is_read IS 'Whether the email has been read.';
COMMENT ON COLUMN public.email_messages.is_starred IS 'Whether the email is starred.';
COMMENT ON COLUMN public.email_messages.is_important IS 'Whether the email is marked as important.';
COMMENT ON COLUMN public.email_messages.is_trash IS 'Whether the email is in the trash folder.';
COMMENT ON COLUMN public.email_messages.processed_for_rfq IS 'Whether this message has been processed for RFQ association.';
COMMENT ON COLUMN public.email_messages.rfq_id IS 'Foreign key: References the related RFQ (public.rfqs.id).';
COMMENT ON COLUMN public.email_messages.provider_id IS 'Foreign key: References the provider (public.providers.id).';
COMMENT ON COLUMN public.email_messages.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN public.email_messages.updated_at IS 'Timestamp when the record was last updated.';

ALTER TABLE ONLY public.email_messages
    ADD CONSTRAINT email_messages_account_id_gmail_id_key UNIQUE (account_id, gmail_id);

CREATE INDEX IF NOT EXISTS idx_email_messages_account_id ON public.email_messages USING btree (account_id);
CREATE INDEX IF NOT EXISTS idx_email_messages_gmail_id ON public.email_messages USING btree (gmail_id);
CREATE INDEX IF NOT EXISTS idx_email_messages_gmail_thread_id ON public.email_messages USING btree (gmail_thread_id);
CREATE INDEX IF NOT EXISTS idx_email_messages_date_received ON public.email_messages USING btree (date_received);
CREATE INDEX IF NOT EXISTS idx_email_messages_category ON public.email_messages USING btree (category);
CREATE INDEX IF NOT EXISTS idx_email_messages_in_reply_to ON public.email_messages USING btree (in_reply_to);
CREATE INDEX IF NOT EXISTS idx_email_messages_processed_for_rfq ON public.email_messages USING btree (processed_for_rfq);
CREATE INDEX IF NOT EXISTS idx_email_messages_rfq_id ON public.email_messages USING btree (rfq_id);
CREATE INDEX IF NOT EXISTS idx_email_messages_provider_id ON public.email_messages USING btree (provider_id);

ALTER TABLE ONLY public.email_messages
    ADD CONSTRAINT email_messages_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.watched_email_accounts(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.email_messages
    ADD CONSTRAINT email_messages_rfq_id_fkey FOREIGN KEY (rfq_id) REFERENCES public.rfqs(id) ON DELETE SET NULL;

ALTER TABLE ONLY public.email_messages
    ADD CONSTRAINT email_messages_provider_id_fkey FOREIGN KEY (provider_id) REFERENCES public.providers(id) ON DELETE SET NULL;

ALTER TABLE public.email_messages ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Authenticated users can modify email messages"
    ON public.email_messages FOR ALL USING (auth.role() = 'authenticated');
