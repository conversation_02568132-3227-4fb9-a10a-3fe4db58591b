CREATE TABLE public.equipment_types (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
    name text NOT NULL,
    category text NOT NULL,
    description text,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);
COMMENT ON TABLE public.equipment_types IS 'Stores standardized equipment types used by logistics providers';
COMMENT ON COLUMN public.equipment_types.id IS 'Primary key: Unique identifier for the equipment type (UUID).';
COMMENT ON COLUMN public.equipment_types.name IS 'Unique name of the equipment type.';
COMMENT ON COLUMN public.equipment_types.category IS 'Category of the equipment type (e.g., container, truck, trailer).';
COMMENT ON COLUMN public.equipment_types.description IS 'Optional description of the equipment type.';
COMMENT ON COLUMN public.equipment_types.created_at IS 'Timestamp when the equipment type record was created (UTC).';
COMMENT ON COLUMN public.equipment_types.updated_at IS 'Timestamp when the equipment type record was last updated (UTC).';
ALTER TABLE ONLY public.equipment_types ADD CONSTRAINT equipment_types_name_unique UNIQUE (name);
CREATE INDEX idx_equipment_types_category ON public.equipment_types USING btree (category);

-- RLS for Equipment Types
ALTER TABLE public.equipment_types ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to modify equipment types
CREATE POLICY "Authenticated users can modify equipment types"
ON public.equipment_types FOR ALL USING (auth.role() = 'authenticated');

CREATE TRIGGER update_equipment_types_updated_at BEFORE UPDATE ON public.equipment_types FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
