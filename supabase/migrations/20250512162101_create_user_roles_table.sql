-- Migration: Create user_roles table to associate users with roles
-- This table implements a many-to-many relationship between users and roles.
-- Each record links a user to a role, supporting flexible permission management.
--
-- RLS (Row Level Security) is enabled to restrict access based on authentication status.

CREATE TABLE public.user_roles (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL, -- Unique identifier for the user-role association
    user_id uuid NOT NULL, -- References the user in the users table
    role_id uuid NOT NULL, -- References the role in the roles table
    created_at timestamp with time zone DEFAULT now(), -- Timestamp when the association was created
    updated_at timestamp with time zone DEFAULT now(), -- Timestamp when the association was last updated
    CONSTRAINT user_role_unique UNIQUE (user_id, role_id) -- Ensure each user-role pair is unique

);

COMMENT ON TABLE public.user_roles IS 'Associates users with roles. Implements a many-to-many relationship between users and roles for permission management.';
COMMENT ON COLUMN public.user_roles.id IS 'Unique identifier for the user-role association.';
COMMENT ON COLUMN public.user_roles.created_at IS 'Timestamp when the association was created.';
COMMENT ON COLUMN public.user_roles.user_id IS 'References the user in the users table.';
COMMENT ON COLUMN public.user_roles.role_id IS 'References the role in the roles table.';
COMMENT ON COLUMN public.user_roles.updated_at IS 'Timestamp when the association was last updated.';

-- Foreign Keys for user_roles (users and roles tables must exist before this migration)
ALTER TABLE ONLY public.user_roles ADD CONSTRAINT fk_user_id FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE; -- Cascade delete if user is removed
ALTER TABLE ONLY public.user_roles ADD CONSTRAINT fk_role_id FOREIGN KEY (role_id) REFERENCES public.roles(id) ON DELETE CASCADE; -- Cascade delete if role is removed

CREATE TRIGGER update_user_roles_updated_at BEFORE UPDATE ON public.user_roles FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Function to check if a user is an admin
CREATE FUNCTION public.is_admin(user_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SET search_path = ''
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_roles ur
    JOIN public.roles r ON ur.role_id = r.id
    WHERE ur.user_id = user_id AND r.name = 'admin'
  );
END;
$$ STABLE;

-- Function to check if a user has admin role (replacing the previous function)
CREATE OR REPLACE FUNCTION public.is_admin(user_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM public.user_roles ur
    JOIN public.roles r ON ur.role_id = r.id
    WHERE ur.user_id = user_id AND r.name = 'admin'
  );
$$;

-- Drop the previous policy and create a simplified one
DROP POLICY IF EXISTS "Authenticated users can modify roles" ON public.roles;
CREATE POLICY "Authenticated users can modify roles"
ON public.roles FOR ALL USING (auth.role() = 'authenticated');

COMMENT ON FUNCTION public.is_admin IS 'Checks if a user has the admin role';

-- Function to check if a user has a specific role
CREATE OR REPLACE FUNCTION public.has_role(user_id uuid, role_name text)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM public.user_roles ur
    JOIN public.roles r ON ur.role_id = r.id
    WHERE ur.user_id = user_id AND r.name = role_name
  );
$$;

COMMENT ON FUNCTION public.has_role IS 'Checks if a user has a specific role';

-- Enable Row Level Security (RLS) for user_roles
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to modify user roles
CREATE POLICY "Authenticated users can modify user roles"
ON public.user_roles FOR ALL USING (auth.role() = 'authenticated');

-- End of migration: user_roles table