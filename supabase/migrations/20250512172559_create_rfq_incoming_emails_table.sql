CREATE TABLE public.rfq_incoming_emails (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
    rfq_id uuid NOT NULL,
    provider_id uuid NOT NULL,
    extracted_bid_id uuid,
    message_id text UNIQUE,
    thread_id text,
    in_reply_to text,
    subject text NOT NULL,
    body text NOT NULL,
    from_email text NOT NULL,
    from_name text,
    attachments jsonb,
    is_processed boolean DEFAULT false NOT NULL,
    processing_status text,
    received_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);

COMMENT ON TABLE public.rfq_incoming_emails IS 'Stores emails received from providers regarding RFQs';
COMMENT ON COLUMN public.rfq_incoming_emails.id IS 'Primary key: Unique identifier for the incoming email (UUID).';
COMMENT ON COLUMN public.rfq_incoming_emails.rfq_id IS 'Foreign key: References the related RFQ (public.rfqs.id).';
COMMENT ON COLUMN public.rfq_incoming_emails.provider_id IS 'Foreign key: References the provider who sent the email (public.providers.id).';
COMMENT ON COLUMN public.rfq_incoming_emails.extracted_bid_id IS 'Optional: ID of the bid extracted from this email, if any.';
COMMENT ON COLUMN public.rfq_incoming_emails.message_id IS 'Unique message identifier from the email provider.';
COMMENT ON COLUMN public.rfq_incoming_emails.thread_id IS 'Thread identifier for grouping related emails.';
COMMENT ON COLUMN public.rfq_incoming_emails.in_reply_to IS 'Message ID this email is replying to, if any.';
COMMENT ON COLUMN public.rfq_incoming_emails.subject IS 'Subject line of the email.';
COMMENT ON COLUMN public.rfq_incoming_emails.body IS 'Full body content of the email.';
COMMENT ON COLUMN public.rfq_incoming_emails.from_email IS 'Sender email address.';
COMMENT ON COLUMN public.rfq_incoming_emails.from_name IS 'Sender display name, if available.';
COMMENT ON COLUMN public.rfq_incoming_emails.attachments IS 'JSONB array of attachment metadata (filename, type, etc).';
COMMENT ON COLUMN public.rfq_incoming_emails.is_processed IS 'Whether this email has been processed for business logic.';
COMMENT ON COLUMN public.rfq_incoming_emails.processing_status IS 'Status or error message from processing, if any.';
COMMENT ON COLUMN public.rfq_incoming_emails.received_at IS 'Timestamp when the email was received.';
COMMENT ON COLUMN public.rfq_incoming_emails.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN public.rfq_incoming_emails.updated_at IS 'Timestamp when the record was last updated.';

-- Indexes for frequently queried columns
CREATE INDEX idx_rfq_incoming_emails_in_reply_to ON public.rfq_incoming_emails USING btree (in_reply_to);
CREATE INDEX idx_rfq_incoming_emails_is_processed ON public.rfq_incoming_emails USING btree (is_processed);
CREATE INDEX idx_rfq_incoming_emails_provider_id ON public.rfq_incoming_emails USING btree (provider_id);
CREATE INDEX idx_rfq_incoming_emails_rfq_id ON public.rfq_incoming_emails USING btree (rfq_id);
CREATE INDEX idx_rfq_incoming_emails_thread_id ON public.rfq_incoming_emails USING btree (thread_id);

-- Foreign Keys
ALTER TABLE ONLY public.rfq_incoming_emails ADD CONSTRAINT rfq_incoming_emails_rfq_id_fkey FOREIGN KEY (rfq_id) REFERENCES public.rfqs(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.rfq_incoming_emails ADD CONSTRAINT rfq_incoming_emails_provider_id_fkey FOREIGN KEY (provider_id) REFERENCES public.providers(id);

-- Row Level Security (RLS)
ALTER TABLE public.rfq_incoming_emails ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to modify incoming RFQ emails
CREATE POLICY "Authenticated users can modify incoming RFQ emails"
ON public.rfq_incoming_emails FOR ALL USING (auth.role() = 'authenticated');

CREATE TRIGGER update_rfq_incoming_emails_updated_at BEFORE UPDATE ON public.rfq_incoming_emails FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
