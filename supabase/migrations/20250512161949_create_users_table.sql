CREATE TABLE public.users (
    id uuid PRIMARY KEY NOT NULL,
    name text,
    avatar_url text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    constraint fk_user_id foreign key (id) references auth.users(id) on delete cascade
);
COMMENT ON TABLE public.users IS 'Stores public user profile information, synced from auth.users';
COMMENT ON COLUMN public.users.id IS 'Unique identifier for the user, matches auth.users.id';
COMMENT ON COLUMN public.users.name IS 'Display name of the user, synced from auth.users.raw_user_meta_data';
COMMENT ON COLUMN public.users.avatar_url IS 'URL to the users avatar image, synced from auth.users.raw_user_meta_data';
COMMENT ON COLUMN public.users.created_at IS 'Timestamp when the user profile was created';
COMMENT ON COLUMN public.users.updated_at IS 'Timestamp when the user profile was last updated';

CREATE OR REPLACE FUNCTION public.sync_auth_user_to_public()
returns trigger
language plpgsql
security definer
set search_path = ''
as $$
DECLARE
  meta jsonb;
  name text;
  avatar_url text;
BEGIN
  meta := NEW.raw_user_meta_data;
  name := NULL;
  avatar_url := NULL;

  IF meta IS NOT NULL THEN
    name := meta->>'name';
    avatar_url := meta->>'avatar_url';
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM public.users WHERE id = NEW.id
  ) THEN
    INSERT INTO public.users (id, created_at, name, avatar_url)
    VALUES (NEW.id, NEW.created_at, name, avatar_url);
  END IF;

  RETURN NEW;
END;
$$;

CREATE TRIGGER trigger_sync_auth_user_to_public
AFTER INSERT ON auth.users
FOR EACH ROW EXECUTE FUNCTION public.sync_auth_user_to_public();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Row Level Security (RLS) for Users Table
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to modify users
CREATE POLICY "Authenticated users can modify users"
ON public.users FOR ALL USING (auth.role() = 'authenticated');