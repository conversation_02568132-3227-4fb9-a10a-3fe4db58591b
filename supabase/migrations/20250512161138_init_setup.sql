-- Migration: Global Helper Function for Automatic updated_at Timestamps
-- ----------------------------------------------------------------------
-- This migration defines a reusable trigger function to automatically update
-- the `updated_at` column of any table whenever a row is modified.
--
-- This pattern helps maintain audit trails and is a best practice for most transactional tables.

-- Enable the UUID extension in the 'extensions' schema
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_extension WHERE extname = 'uuid-ossp'
  ) THEN
    CREATE EXTENSION "uuid-ossp" WITH SCHEMA extensions;
  END IF;
END$$;

-- Function to update the updated_at timestamp automatically (Global Helper)
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
SET search_path = ''
AS $$
BEGIN
   -- Set the updated_at column to the current timestamp on every update
   NEW.updated_at = now();
   RETURN NEW;
END;
$$;

COMMENT ON FUNCTION public.update_updated_at_column() IS 'Trigger function to update the updated_at timestamp automatically. Attach as a BEFORE UPDATE trigger to tables with an updated_at column to maintain modification audit trails.';