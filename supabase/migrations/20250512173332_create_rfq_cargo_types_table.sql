CREATE TABLE public.rfq_cargo_types (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL, -- Unique identifier for the RFQ cargo type link
    rfq_id uuid NOT NULL, -- References the RFQ this cargo type is linked to
    cargo_type_id uuid NOT NULL, -- References the cargo type
    created_at timestamp with time zone DEFAULT now() NOT NULL, -- Timestamp when the record was created
    updated_at timestamp with time zone DEFAULT now() NOT NULL -- Timestamp when the record was last updated
);

COMMENT ON TABLE public.rfq_cargo_types IS 'Links RFQs with multiple cargo types';
COMMENT ON COLUMN public.rfq_cargo_types.id IS 'Unique identifier for the RFQ cargo type link';
COMMENT ON COLUMN public.rfq_cargo_types.rfq_id IS 'References the RFQ this cargo type is linked to';
COMMENT ON COLUMN public.rfq_cargo_types.cargo_type_id IS 'References the cargo type';
COMMENT ON COLUMN public.rfq_cargo_types.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN public.rfq_cargo_types.updated_at IS 'Timestamp when the record was last updated';

-- Add constraints and indexes for rfq_cargo_types
ALTER TABLE ONLY public.rfq_cargo_types ADD CONSTRAINT unique_rfq_cargo_type UNIQUE (rfq_id, cargo_type_id);
CREATE INDEX idx_rfq_cargo_types_rfq_id ON public.rfq_cargo_types USING btree (rfq_id);
CREATE INDEX idx_rfq_cargo_types_cargo_type_id ON public.rfq_cargo_types USING btree (cargo_type_id);

-- FKs for RFQ Cargo Types
ALTER TABLE ONLY public.rfq_cargo_types ADD CONSTRAINT rfq_cargo_types_rfq_id_fkey FOREIGN KEY (rfq_id) REFERENCES public.rfqs(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.rfq_cargo_types ADD CONSTRAINT rfq_cargo_types_cargo_type_id_fkey FOREIGN KEY (cargo_type_id) REFERENCES public.cargo_types(id) ON DELETE RESTRICT;

-- RLS for RFQ Cargo Types
ALTER TABLE public.rfq_cargo_types ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to modify RFQ cargo type associations
CREATE POLICY "Authenticated users can modify RFQ cargo types"
ON public.rfq_cargo_types FOR ALL USING (auth.role() = 'authenticated');

CREATE TRIGGER update_rfq_cargo_types_updated_at BEFORE UPDATE ON public.rfq_cargo_types FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
