CREATE TABLE public.roles (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
    name text UNIQUE NOT NULL DEFAULT 'user',
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Add comments for documentation
COMMENT ON TABLE public.roles IS 'Stores user roles for access control';
COMMENT ON COLUMN public.roles.id IS 'Role identifier (e.g., admin, user)';
COMMENT ON COLUMN public.roles.created_at IS 'Timestamp when the role was created';
COMMENT ON COLUMN public.roles.updated_at IS 'Timestamp when the role was last updated';
COMMENT ON COLUMN public.roles.name IS 'Role name (e.g., admin, user)';

-- RLS for Roles
ALTER TABLE public.roles ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to modify roles
CREATE POLICY "Authenticated users can modify roles"
ON public.roles FOR ALL USING (auth.role() = 'authenticated');

CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON public.roles FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
