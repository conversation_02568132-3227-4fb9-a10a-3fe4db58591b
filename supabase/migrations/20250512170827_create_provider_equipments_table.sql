CREATE TABLE public.provider_equipments (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
    provider_id uuid NOT NULL, -- FK to providers
    equipment_type_id uuid NOT NULL, -- FK to equipment_types
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);
COMMENT ON TABLE public.provider_equipments IS 'Equipment types that providers can handle or have available';
COMMENT ON COLUMN public.provider_equipments.id IS 'Primary key: Unique identifier for the provider-equipment association (UUID).';
COMMENT ON COLUMN public.provider_equipments.provider_id IS 'Foreign key: References the provider (public.providers.id).';
COMMENT ON COLUMN public.provider_equipments.equipment_type_id IS 'Foreign key: References the equipment type (public.equipment_types.id).';
COMMENT ON COLUMN public.provider_equipments.created_at IS 'Timestamp when the provider-equipment record was created (UTC).';
COMMENT ON COLUMN public.provider_equipments.updated_at IS 'Timestamp when the provider-equipment record was last updated (UTC).';

ALTER TABLE ONLY public.provider_equipments ADD CONSTRAINT provider_equipments_provider_id_equipment_type_id_key UNIQUE (provider_id, equipment_type_id);

-- FKs for provider_equipments (providers and equipment_types tables exist)
ALTER TABLE ONLY public.provider_equipments ADD CONSTRAINT provider_equipments_provider_id_fkey FOREIGN KEY (provider_id) REFERENCES public.providers(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.provider_equipments ADD CONSTRAINT provider_equipments_equipment_type_id_fkey FOREIGN KEY (equipment_type_id) REFERENCES public.equipment_types(id) ON DELETE CASCADE;

-- RLS for provider_equipments
ALTER TABLE public.provider_equipments ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to modify provider equipment associations
CREATE POLICY "Authenticated users can modify provider equipments"
ON public.provider_equipments FOR ALL USING (auth.role() = 'authenticated');

CREATE TRIGGER update_provider_equipments_updated_at BEFORE UPDATE ON public.provider_equipments FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
