CREATE TABLE public.provider_routes (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
    provider_id uuid NOT NULL, -- FK to providers
    origin_country_id uuid NOT NULL, -- FK to countries
    destination_country_id uuid NOT NULL, -- FK to countries
    is_active boolean DEFAULT true,
    notes text,
    created_by uuid, -- FK to users
    updated_by uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

COMMENT ON TABLE public.provider_routes IS 'Tracks service routes offered by providers. This table is clustered on the unique_provider_route index for optimal query performance.';
COMMENT ON COLUMN public.provider_routes.id IS 'Primary key: Unique identifier for each provider route.';
COMMENT ON COLUMN public.provider_routes.provider_id IS 'Foreign key: References providers(id). The provider offering this route.';
COMMENT ON COLUMN public.provider_routes.origin_country_id IS 'Foreign key: References countries(id). The origin country for the route.';
COMMENT ON COLUMN public.provider_routes.destination_country_id IS 'Foreign key: References countries(id). The destination country for the route.';
COMMENT ON COLUMN public.provider_routes.is_active IS 'Indicates if the route is currently active.';
COMMENT ON COLUMN public.provider_routes.notes IS 'Optional notes or remarks about the route.';
COMMENT ON COLUMN public.provider_routes.created_at IS 'Timestamp when the route was created.';
COMMENT ON COLUMN public.provider_routes.updated_at IS 'Timestamp when the route was last updated.';
COMMENT ON COLUMN public.provider_routes.created_by IS 'Foreign key: References users(id). The user who created the route.';
COMMENT ON COLUMN public.provider_routes.updated_by IS 'Foreign key: References users(id). The user who last updated the route.';

CREATE INDEX idx_provider_routes_destination ON public.provider_routes USING btree (destination_country_id);
CREATE INDEX idx_provider_routes_origin ON public.provider_routes USING btree (origin_country_id);
CREATE INDEX idx_provider_routes_provider_id ON public.provider_routes USING btree (provider_id);

ALTER TABLE public.provider_routes ADD CONSTRAINT unique_provider_route UNIQUE (provider_id, origin_country_id, destination_country_id);

-- FKs for provider_routes
ALTER TABLE ONLY public.provider_routes ADD CONSTRAINT provider_routes_provider_id_fkey FOREIGN KEY (provider_id) REFERENCES public.providers(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.provider_routes ADD CONSTRAINT provider_routes_origin_country_id_fkey FOREIGN KEY (origin_country_id) REFERENCES public.countries(id);
ALTER TABLE ONLY public.provider_routes ADD CONSTRAINT provider_routes_destination_country_id_fkey FOREIGN KEY (destination_country_id) REFERENCES public.countries(id);
ALTER TABLE ONLY public.provider_routes ADD CONSTRAINT provider_routes_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);
ALTER TABLE ONLY public.provider_routes ADD CONSTRAINT provider_routes_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.users(id);

-- RLS for provider_routes
ALTER TABLE public.provider_routes ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to modify provider routes
CREATE POLICY "Authenticated users can modify provider routes"
ON public.provider_routes FOR ALL USING (auth.role() = 'authenticated');

CREATE TRIGGER update_provider_routes_updated_at BEFORE UPDATE ON public.provider_routes FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
