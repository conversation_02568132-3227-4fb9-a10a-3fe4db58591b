-- Create RFQ Providers table
CREATE TABLE public.rfq_providers (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL, -- Unique identifier for this RFQ-provider link
    rfq_id uuid NOT NULL, -- Reference to the RFQ
    provider_id uuid NOT NULL, -- Reference to the provider
    status text NOT NULL, -- Current status of the provider for this RFQ
    invited_at timestamp with time zone, -- When the provider was invited to the RFQ
    selected_at timestamp with time zone, -- When the provider was selected for the RFQ
    email_opened_at timestamp with time zone, -- When the invitation email was opened
    response_received_at timestamp with time zone, -- When a response was received from the provider
    notes text, -- Additional notes about this provider's participation
    created_at timestamp with time zone DEFAULT now() NOT NULL, -- When this record was created
    updated_at timestamp with time zone DEFAULT now() NOT NULL -- When this record was last updated
);

COMMENT ON TABLE public.rfq_providers IS 'Links RFQs with providers and tracks their status';
COMMENT ON COLUMN public.rfq_providers.id IS 'Unique identifier for this RFQ-provider link';
COMMENT ON COLUMN public.rfq_providers.rfq_id IS 'Reference to the RFQ (public.rfqs.id)';
COMMENT ON COLUMN public.rfq_providers.provider_id IS 'Reference to the provider (public.providers.id)';
COMMENT ON COLUMN public.rfq_providers.status IS 'Current status of the provider for this RFQ (matched, selected, invited, declined, bid_submitted)';
COMMENT ON COLUMN public.rfq_providers.invited_at IS 'Timestamp when the provider was invited to the RFQ';
COMMENT ON COLUMN public.rfq_providers.selected_at IS 'Timestamp when the provider was selected for the RFQ';
COMMENT ON COLUMN public.rfq_providers.email_opened_at IS 'Timestamp when the invitation email was opened by the provider';
COMMENT ON COLUMN public.rfq_providers.response_received_at IS 'Timestamp when a response was received from the provider';
COMMENT ON COLUMN public.rfq_providers.notes IS 'Additional notes about this provider''s participation in the RFQ';
COMMENT ON COLUMN public.rfq_providers.created_at IS 'Timestamp when this record was created';
COMMENT ON COLUMN public.rfq_providers.updated_at IS 'Timestamp when this record was last updated';

-- Constraints and indexes
ALTER TABLE ONLY public.rfq_providers ADD CONSTRAINT rfq_providers_status_check CHECK ((status = ANY (ARRAY['matched'::text, 'selected'::text, 'invited'::text, 'declined'::text, 'bid_submitted'::text])));
ALTER TABLE ONLY public.rfq_providers ADD CONSTRAINT unique_rfq_provider UNIQUE (rfq_id, provider_id);
CREATE INDEX idx_rfq_providers_rfq_id ON public.rfq_providers USING btree (rfq_id);
CREATE INDEX idx_rfq_providers_provider_id ON public.rfq_providers USING btree (provider_id);
CREATE INDEX idx_rfq_providers_status ON public.rfq_providers USING btree (status);

-- Foreign keys
ALTER TABLE ONLY public.rfq_providers ADD CONSTRAINT rfq_providers_rfq_id_fkey FOREIGN KEY (rfq_id) REFERENCES public.rfqs(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.rfq_providers ADD CONSTRAINT rfq_providers_provider_id_fkey FOREIGN KEY (provider_id) REFERENCES public.providers(id);

-- Enable Row Level Security and policies
ALTER TABLE public.rfq_providers ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to modify RFQ providers
CREATE POLICY "Authenticated users can modify RFQ providers"
ON public.rfq_providers FOR ALL USING (auth.role() = 'authenticated');

CREATE TRIGGER update_rfq_providers_updated_at BEFORE UPDATE ON public.rfq_providers FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
