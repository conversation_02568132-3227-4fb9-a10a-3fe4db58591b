-- Create RFQs table with improved structure, comments, constraints, and indexes
CREATE TABLE public.rfqs (
    -- Primary Key
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,

    -- Status and Sequence
    status text NOT NULL,
    sequence_number text UNIQUE,

    -- Origin & Destination (grouped FKs and related fields)
    origin_country_id uuid NOT NULL,
    origin_city text NOT NULL,
    origin_address text NOT NULL,
    origin_postal_code text,
    destination_country_id uuid NOT NULL,
    destination_city text NOT NULL,
    destination_address text NOT NULL,
    destination_postal_code text,

    -- Cargo Details
    cargo_type_id uuid,
    weight numeric NOT NULL,
    length numeric,
    width numeric,
    height numeric,
    quantity integer DEFAULT 1 NOT NULL,
    special_requirements text,

    -- Equipment
    equipment_type_id uuid NOT NULL,
    equipment_quantity integer DEFAULT 1 NOT NULL,

    -- Other
    title text,
    notes text,
    preferred_shipping_date timestamp with time zone,

    -- Creator
    created_by uuid NOT NULL,

    -- Timestamps
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    expiration_date timestamp with time zone
);

-- Column Comments
COMMENT ON TABLE public.rfqs IS 'Stores Request for Quote (RFQ) information for transportation services';
COMMENT ON COLUMN public.rfqs.id IS 'Primary key: Unique identifier for the RFQ (UUID).';
COMMENT ON COLUMN public.rfqs.status IS 'Current status of the RFQ (draft, ready, sent, closed).';
COMMENT ON COLUMN public.rfqs.sequence_number IS 'Human-readable, unique sequence number for the RFQ (e.g., SF-RFQ-00001).';
COMMENT ON COLUMN public.rfqs.created_at IS 'Timestamp when the RFQ was created (UTC).';
COMMENT ON COLUMN public.rfqs.updated_at IS 'Timestamp when the RFQ was last updated (UTC).';
COMMENT ON COLUMN public.rfqs.expiration_date IS 'Date and time when the RFQ expires.';
COMMENT ON COLUMN public.rfqs.created_by IS 'Foreign key: User who created the RFQ (auth.users.id).';
COMMENT ON COLUMN public.rfqs.origin_country_id IS 'Foreign key: Origin country for the shipment (public.countries.id).';
COMMENT ON COLUMN public.rfqs.origin_city IS 'City of origin for the shipment.';
COMMENT ON COLUMN public.rfqs.origin_address IS 'Detailed address of the origin location.';
COMMENT ON COLUMN public.rfqs.origin_postal_code IS 'Postal code of the origin location.';
COMMENT ON COLUMN public.rfqs.destination_country_id IS 'Foreign key: Destination country for the shipment (public.countries.id).';
COMMENT ON COLUMN public.rfqs.destination_city IS 'City of destination for the shipment.';
COMMENT ON COLUMN public.rfqs.destination_address IS 'Detailed address of the destination location.';
COMMENT ON COLUMN public.rfqs.destination_postal_code IS 'Postal code of the destination location.';
COMMENT ON COLUMN public.rfqs.cargo_type_id IS 'Foreign key: Primary cargo type for the shipment (public.cargo_types.id).';
COMMENT ON COLUMN public.rfqs.weight IS 'Total weight of the cargo (in tonnes).';
COMMENT ON COLUMN public.rfqs.length IS 'Length of the cargo (in meters).';
COMMENT ON COLUMN public.rfqs.width IS 'Width of the cargo (in meters).';
COMMENT ON COLUMN public.rfqs.height IS 'Height of the cargo (in meters).';
COMMENT ON COLUMN public.rfqs.quantity IS 'Number of cargo units or packages.';
COMMENT ON COLUMN public.rfqs.special_requirements IS 'Special requirements or handling instructions for the cargo.';
COMMENT ON COLUMN public.rfqs.equipment_type_id IS 'Foreign key: Required equipment type for the shipment (public.equipment_types.id).';
COMMENT ON COLUMN public.rfqs.equipment_quantity IS 'Number of equipment units required.';
COMMENT ON COLUMN public.rfqs.title IS 'Title or short description of the RFQ.';
COMMENT ON COLUMN public.rfqs.notes IS 'Additional notes or instructions for the RFQ.';
COMMENT ON COLUMN public.rfqs.preferred_shipping_date IS 'Preferred date for shipping the cargo.';

-- Constraints
ALTER TABLE ONLY public.rfqs ADD CONSTRAINT rfqs_status_check CHECK ((status = ANY (ARRAY['draft'::text, 'ready'::text, 'sent'::text, 'closed'::text])));
ALTER TABLE ONLY public.rfqs ADD CONSTRAINT rfqs_quantity_check CHECK ((quantity > 0));
ALTER TABLE ONLY public.rfqs ADD CONSTRAINT rfqs_equipment_quantity_check CHECK ((equipment_quantity > 0));
ALTER TABLE ONLY public.rfqs ADD CONSTRAINT rfqs_weight_check CHECK ((weight > (0)::numeric));
ALTER TABLE ONLY public.rfqs ADD CONSTRAINT rfqs_length_check CHECK ((length > (0)::numeric));
ALTER TABLE ONLY public.rfqs ADD CONSTRAINT rfqs_width_check CHECK ((width > (0)::numeric));
ALTER TABLE ONLY public.rfqs ADD CONSTRAINT rfqs_height_check CHECK ((height > (0)::numeric));

-- Indexes for frequently queried columns
CREATE INDEX IF NOT EXISTS idx_rfqs_created_by ON public.rfqs USING btree (created_by);
CREATE INDEX IF NOT EXISTS idx_rfqs_status ON public.rfqs USING btree (status);
CREATE INDEX IF NOT EXISTS idx_rfqs_sequence_number ON public.rfqs USING btree (sequence_number);
CREATE INDEX IF NOT EXISTS idx_rfqs_origin_country_id ON public.rfqs USING btree (origin_country_id);
CREATE INDEX IF NOT EXISTS idx_rfqs_destination_country_id ON public.rfqs USING btree (destination_country_id);
CREATE INDEX IF NOT EXISTS idx_rfqs_origin_postal_code ON public.rfqs USING btree (origin_postal_code);
CREATE INDEX IF NOT EXISTS idx_rfqs_destination_postal_code ON public.rfqs USING btree (destination_postal_code);

-- Create sequence for RFQ numbers with proper naming convention
CREATE SEQUENCE IF NOT EXISTS public.rfq_sequence
  START 1
  INCREMENT 1
  NO MINVALUE
  NO MAXVALUE
  CACHE 1;

-- Function to generate RFQ sequence number with SF-RFQ-00000 format
CREATE OR REPLACE FUNCTION public.generate_rfq_sequence_number()
RETURNS TRIGGER
LANGUAGE plpgsql
SET search_path = ''
AS $$
BEGIN
  -- Format: SF-RFQ-00000 (zero-padded to 5 digits)
  NEW.sequence_number := 'SF-RFQ-' || LPAD(nextval('public.rfq_sequence')::TEXT, 5, '0');
  RETURN NEW;
END;
$$;

-- Trigger to assign sequence number before insert
-- Only executes when sequence_number is NULL (allowing manual override if needed)
CREATE TRIGGER trg_set_rfq_sequence
BEFORE INSERT ON public.rfqs
FOR EACH ROW
WHEN (NEW.sequence_number IS NULL)
EXECUTE FUNCTION public.generate_rfq_sequence_number();

-- Foreign Key Constraints
ALTER TABLE ONLY public.rfqs ADD CONSTRAINT rfqs_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id);
ALTER TABLE ONLY public.rfqs ADD CONSTRAINT rfqs_origin_country_id_fkey FOREIGN KEY (origin_country_id) REFERENCES public.countries(id);
ALTER TABLE ONLY public.rfqs ADD CONSTRAINT rfqs_destination_country_id_fkey FOREIGN KEY (destination_country_id) REFERENCES public.countries(id);
ALTER TABLE ONLY public.rfqs ADD CONSTRAINT rfqs_cargo_type_id_fkey FOREIGN KEY (cargo_type_id) REFERENCES public.cargo_types(id);
ALTER TABLE ONLY public.rfqs ADD CONSTRAINT rfqs_equipment_type_id_fkey FOREIGN KEY (equipment_type_id) REFERENCES public.equipment_types(id);

-- Row Level Security (RLS) Policies
ALTER TABLE public.rfqs ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to modify RFQs
CREATE POLICY "Authenticated users can modify RFQs"
ON public.rfqs FOR ALL USING (auth.role() = 'authenticated');

CREATE TRIGGER update_rfqs_updated_at BEFORE UPDATE ON public.rfqs FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Grant permissions for authenticated users to use the sequence
GRANT USAGE, SELECT ON SEQUENCE public.rfq_sequence TO authenticated;
