CREATE TABLE public.cargo_types (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
    name text UNIQUE NOT NULL,
    description text,
    image_url text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);

COMMENT ON TABLE public.cargo_types IS 'Stores different types of cargo handled.';
COMMENT ON COLUMN public.cargo_types.id IS 'Primary key: Unique identifier for the cargo type (UUID).';
COMMENT ON COLUMN public.cargo_types.name IS 'Unique name of the cargo type.';
COMMENT ON COLUMN public.cargo_types.created_at IS 'Timestamp when the cargo type record was created (UTC).';
COMMENT ON COLUMN public.cargo_types.updated_at IS 'Timestamp when the cargo type record was last updated (UTC).';
COMMENT ON COLUMN public.cargo_types.description IS 'Optional description of the cargo type.';
COMMENT ON COLUMN public.cargo_types.image_url IS 'Optional URL to an image representing the cargo type.';

-- RLS for Cargo Types
ALTER TABLE public.cargo_types ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to modify cargo types
CREATE POLICY "Authenticated users can modify cargo types"
ON public.cargo_types FOR ALL USING (auth.role() = 'authenticated');

CREATE TRIGGER update_cargo_types_updated_at BEFORE UPDATE ON public.cargo_types FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
