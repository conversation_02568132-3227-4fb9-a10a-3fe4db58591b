CREATE TABLE public.provider_contacts (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
    provider_id uuid NOT NULL, -- FK to providers
    name text,
    email text NOT NULL,
    phone text,
    role text,
    is_primary boolean DEFAULT false NOT NULL,
    created_by uuid, -- FK to users
    updated_by uuid, -- FK to users
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- <PERSON>umn comments for clarity
COMMENT ON COLUMN public.provider_contacts.id IS 'Primary key: Unique identifier for the provider contact.';
COMMENT ON COLUMN public.provider_contacts.provider_id IS 'Foreign key: References the provider this contact belongs to.';
COMMENT ON COLUMN public.provider_contacts.email IS 'Email address of the provider contact.';
COMMENT ON COLUMN public.provider_contacts.phone IS 'Phone number of the provider contact.';
COMMENT ON COLUMN public.provider_contacts.role IS 'Role or position of the contact within the provider organization.';
COMMENT ON COLUMN public.provider_contacts.is_primary IS 'Indicates if this contact is the primary contact for the provider.';
COMMENT ON COLUMN public.provider_contacts.created_at IS 'Timestamp when the contact was created.';
COMMENT ON COLUMN public.provider_contacts.updated_at IS 'Timestamp when the contact was last updated.';
COMMENT ON COLUMN public.provider_contacts.created_by IS 'Foreign key: User who created the contact.';
COMMENT ON COLUMN public.provider_contacts.updated_by IS 'Foreign key: User who last updated the contact.';
COMMENT ON COLUMN public.provider_contacts.name IS 'Full name of the provider contact.';

COMMENT ON TABLE public.provider_contacts IS 'Stores contact information for providers with a single optional name field and mandatory email';

-- Indexes for frequently queried columns
CREATE INDEX IF NOT EXISTS idx_provider_contacts_email ON public.provider_contacts USING btree (email);
CREATE INDEX IF NOT EXISTS idx_provider_contacts_provider_id ON public.provider_contacts USING btree (provider_id);
CREATE UNIQUE INDEX idx_provider_primary_contact ON public.provider_contacts USING btree (provider_id) WHERE (is_primary = true);

-- Provider Contact Logic Functions & Triggers
-- Ensures the first contact for a provider is always set as primary on insert.
CREATE OR REPLACE FUNCTION public.ensure_first_contact_is_primary() RETURNS TRIGGER
LANGUAGE plpgsql
SET search_path = ''
AS $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM public.provider_contacts WHERE provider_id = NEW.provider_id AND id != NEW.id
  ) THEN
    NEW.is_primary := TRUE;
  END IF;
  RETURN NEW;
END;
$$;

-- Guarantees only one primary contact per provider by demoting others on insert or update.
CREATE OR REPLACE FUNCTION public.ensure_single_primary_contact() RETURNS TRIGGER
LANGUAGE plpgsql
SET search_path = ''
AS $$
BEGIN
  IF NEW.is_primary THEN
    UPDATE public.provider_contacts
    SET is_primary = FALSE
    WHERE provider_id = NEW.provider_id AND id != NEW.id AND is_primary;
  END IF;
  RETURN NEW;
END;
$$;

-- If a primary contact is deleted, promotes the oldest remaining contact to primary.
CREATE OR REPLACE FUNCTION public.handle_primary_contact_deletion() RETURNS TRIGGER
LANGUAGE plpgsql
SET search_path = ''
AS $$
BEGIN
  IF OLD.is_primary AND EXISTS (
    SELECT 1 FROM public.provider_contacts WHERE provider_id = OLD.provider_id
  ) THEN
    UPDATE public.provider_contacts
    SET is_primary = TRUE
    WHERE id = (
      SELECT id FROM public.provider_contacts
      WHERE provider_id = OLD.provider_id
      ORDER BY created_at ASC LIMIT 1
    );
  END IF;
  RETURN OLD;
END;
$$;

-- Ensures every provider always has at least one primary contact after any change.
CREATE OR REPLACE FUNCTION public.verify_provider_has_primary_contact() RETURNS TRIGGER
LANGUAGE plpgsql
SET search_path = ''
AS $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM public.provider_contacts
    WHERE provider_id = COALESCE(NEW.provider_id, OLD.provider_id) AND is_primary
  ) AND EXISTS (
    SELECT 1 FROM public.provider_contacts
    WHERE provider_id = COALESCE(NEW.provider_id, OLD.provider_id)
  ) THEN
    UPDATE public.provider_contacts
    SET is_primary = TRUE
    WHERE id = (
      SELECT id FROM public.provider_contacts
      WHERE provider_id = COALESCE(NEW.provider_id, OLD.provider_id)
      ORDER BY created_at ASC LIMIT 1
    );
  END IF;
  RETURN NULL;
END;
$$;

CREATE TRIGGER ensure_first_contact_is_primary BEFORE INSERT ON public.provider_contacts FOR EACH ROW EXECUTE FUNCTION public.ensure_first_contact_is_primary();
CREATE TRIGGER ensure_single_primary_contact_on_insert BEFORE INSERT ON public.provider_contacts FOR EACH ROW EXECUTE FUNCTION public.ensure_single_primary_contact();
CREATE TRIGGER ensure_single_primary_contact_on_update BEFORE UPDATE ON public.provider_contacts FOR EACH ROW EXECUTE FUNCTION public.ensure_single_primary_contact();
CREATE TRIGGER handle_primary_contact_deletion AFTER DELETE ON public.provider_contacts FOR EACH ROW EXECUTE FUNCTION public.handle_primary_contact_deletion();
CREATE TRIGGER verify_provider_has_primary_contact_after_insert AFTER INSERT ON public.provider_contacts FOR EACH STATEMENT EXECUTE FUNCTION public.verify_provider_has_primary_contact();
CREATE TRIGGER verify_provider_has_primary_contact_after_update AFTER UPDATE ON public.provider_contacts FOR EACH STATEMENT EXECUTE FUNCTION public.verify_provider_has_primary_contact();
CREATE TRIGGER verify_provider_has_primary_contact_after_delete AFTER DELETE ON public.provider_contacts FOR EACH STATEMENT EXECUTE FUNCTION public.verify_provider_has_primary_contact();

-- FKs for provider_contacts (providers and users tables exist)
ALTER TABLE ONLY public.provider_contacts ADD CONSTRAINT provider_contacts_provider_id_fkey FOREIGN KEY (provider_id) REFERENCES public.providers(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.provider_contacts ADD CONSTRAINT provider_contacts_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);
ALTER TABLE ONLY public.provider_contacts ADD CONSTRAINT provider_contacts_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES public.users(id);

-- RLS for provider_contacts
ALTER TABLE public.provider_contacts ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to modify provider contacts
CREATE POLICY "Authenticated users can modify provider contacts"
ON public.provider_contacts FOR ALL USING (auth.role() = 'authenticated');

CREATE TRIGGER update_provider_contacts_updated_at BEFORE UPDATE ON public.provider_contacts FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
