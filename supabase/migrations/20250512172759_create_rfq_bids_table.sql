CREATE TABLE public.rfq_bids (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
    rfq_id uuid NOT NULL,
    provider_id uuid NOT NULL,
    original_email_id uuid,
    price numeric NOT NULL CONSTRAINT rfq_bids_price_check CHECK ((price >= (0)::numeric)),
    currency text DEFAULT 'EUR'::text NOT NULL,
    status text NOT NULL CONSTRAINT rfq_bids_status_check CHECK ((status = ANY (ARRAY['received'::text, 'under_review'::text, 'accepted'::text, 'rejected'::text, 'negotiating'::text]))),
    submitted_at timestamp with time zone NOT NULL,
    notes text,
    is_ai_extracted boolean DEFAULT false NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);

COMMENT ON TABLE public.rfq_bids IS 'Stores bids submitted by providers in response to RFQs (simplified to only include EUR bid amount)';
COMMENT ON COLUMN public.rfq_bids.id IS 'Unique identifier for the bid.';
COMMENT ON COLUMN public.rfq_bids.rfq_id IS 'Foreign key to the RFQs table.';
COMMENT ON COLUMN public.rfq_bids.provider_id IS 'Foreign key to the Providers table.';
COMMENT ON COLUMN public.rfq_bids.original_email_id IS 'Foreign key to the rfq_incoming_emails table, linking to the original email if the bid was extracted from one.';
COMMENT ON COLUMN public.rfq_bids.price IS 'The bid amount.';
COMMENT ON COLUMN public.rfq_bids.currency IS 'The currency of the bid.';
COMMENT ON COLUMN public.rfq_bids.status IS 'The current status of the bid (e.g., received, under_review, accepted).';
COMMENT ON COLUMN public.rfq_bids.submitted_at IS 'Timestamp when the bid was submitted.';
COMMENT ON COLUMN public.rfq_bids.notes IS 'Additional notes or comments about the bid.';
COMMENT ON COLUMN public.rfq_bids.is_ai_extracted IS 'Boolean indicating if the bid was extracted by AI from an email.';
COMMENT ON COLUMN public.rfq_bids.created_at IS 'Timestamp when the bid record was created.';
COMMENT ON COLUMN public.rfq_bids.updated_at IS 'Timestamp when the bid record was last updated.';

CREATE INDEX idx_rfq_bids_provider_id ON public.rfq_bids USING btree (provider_id);
CREATE INDEX idx_rfq_bids_rfq_id ON public.rfq_bids USING btree (rfq_id);
CREATE INDEX idx_rfq_bids_status ON public.rfq_bids USING btree (status);

-- FKs for RFQ Bids (rfqs, providers, rfq_incoming_emails tables exist)
ALTER TABLE ONLY public.rfq_bids ADD CONSTRAINT rfq_bids_rfq_id_fkey FOREIGN KEY (rfq_id) REFERENCES public.rfqs(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.rfq_bids ADD CONSTRAINT rfq_bids_provider_id_fkey FOREIGN KEY (provider_id) REFERENCES public.providers(id);
ALTER TABLE ONLY public.rfq_bids ADD CONSTRAINT rfq_bids_original_email_id_fkey FOREIGN KEY (original_email_id) REFERENCES public.rfq_incoming_emails(id);

-- FK from rfq_incoming_emails to rfq_bids (added here as both tables now exist)
ALTER TABLE ONLY public.rfq_incoming_emails ADD CONSTRAINT rfq_incoming_emails_extracted_bid_id_fkey FOREIGN KEY (extracted_bid_id) REFERENCES public.rfq_bids(id);

-- RLS for RFQ Bids
ALTER TABLE public.rfq_bids ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to modify RFQ bids
CREATE POLICY "Authenticated users can modify RFQ bids"
ON public.rfq_bids FOR ALL USING (auth.role() = 'authenticated');

CREATE TRIGGER update_rfq_bids_updated_at BEFORE UPDATE ON public.rfq_bids FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
