-- Add coordinate and distance caching columns to RFQs table
-- This migration implements Phase 1, Task 1.1 from the Route Map Tab PRD
-- Enables caching of geocoded coordinates and calculated distances for performance optimization

-- Add coordinate columns to rfqs table
ALTER TABLE public.rfqs 
ADD COLUMN IF NOT EXISTS origin_lat DECIMAL(10,8),
ADD COLUMN IF NOT EXISTS origin_lng DECIMAL(11,8),
ADD COLUMN IF NOT EXISTS destination_lat DECIMAL(10,8),
ADD COLUMN IF NOT EXISTS destination_lng DECIMAL(11,8);

-- Add distance and metadata columns
ALTER TABLE public.rfqs
ADD COLUMN IF NOT EXISTS route_distance_km DECIMAL(8,2),
ADD COLUMN IF NOT EXISTS coordinates_resolved_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS route_distance_calculated_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS address_hash VARCHAR(64);

-- Add column comments for documentation
COMMENT ON COLUMN public.rfqs.origin_lat IS 'Latitude coordinate for origin address (derived from geocoding)';
COMMENT ON COLUMN public.rfqs.origin_lng IS 'Longitude coordinate for origin address (derived from geocoding)';
COMMENT ON COLUMN public.rfqs.destination_lat IS 'Latitude coordinate for destination address (derived from geocoding)';
COMMENT ON COLUMN public.rfqs.destination_lng IS 'Longitude coordinate for destination address (derived from geocoding)';
COMMENT ON COLUMN public.rfqs.route_distance_km IS 'Route distance in kilometers (calculated using Distance Matrix API)';
COMMENT ON COLUMN public.rfqs.coordinates_resolved_at IS 'Timestamp when coordinates were last resolved via geocoding';
COMMENT ON COLUMN public.rfqs.route_distance_calculated_at IS 'Timestamp when route distance was last calculated';
COMMENT ON COLUMN public.rfqs.address_hash IS 'Hash of address fields for change detection and cache invalidation';

-- Add indexes for performance optimization
-- Spatial queries on coordinate columns
CREATE INDEX IF NOT EXISTS idx_rfqs_origin_coordinates ON public.rfqs (origin_lat, origin_lng);
CREATE INDEX IF NOT EXISTS idx_rfqs_destination_coordinates ON public.rfqs (destination_lat, destination_lng);

-- Cache validation queries on timestamp columns
CREATE INDEX IF NOT EXISTS idx_rfqs_coordinates_resolved_at ON public.rfqs (coordinates_resolved_at);
CREATE INDEX IF NOT EXISTS idx_rfqs_route_distance_calculated_at ON public.rfqs (route_distance_calculated_at);

-- Address change detection queries
CREATE INDEX IF NOT EXISTS idx_rfqs_address_hash ON public.rfqs (address_hash);

-- Composite index for route distance queries with coordinates
CREATE INDEX IF NOT EXISTS idx_rfqs_route_with_coordinates ON public.rfqs (route_distance_km, origin_lat, origin_lng, destination_lat, destination_lng);

-- Add constraints for data validation
-- Latitude must be between -90 and 90 degrees
ALTER TABLE public.rfqs ADD CONSTRAINT rfqs_origin_lat_check CHECK (origin_lat IS NULL OR (origin_lat >= -90 AND origin_lat <= 90));
ALTER TABLE public.rfqs ADD CONSTRAINT rfqs_destination_lat_check CHECK (destination_lat IS NULL OR (destination_lat >= -90 AND destination_lat <= 90));

-- Longitude must be between -180 and 180 degrees  
ALTER TABLE public.rfqs ADD CONSTRAINT rfqs_origin_lng_check CHECK (origin_lng IS NULL OR (origin_lng >= -180 AND origin_lng <= 180));
ALTER TABLE public.rfqs ADD CONSTRAINT rfqs_destination_lng_check CHECK (destination_lng IS NULL OR (destination_lng >= -180 AND destination_lng <= 180));

-- Route distance must be positive
ALTER TABLE public.rfqs ADD CONSTRAINT rfqs_route_distance_check CHECK (route_distance_km IS NULL OR route_distance_km > 0);

-- Coordinate pairs must be complete (both lat and lng present or both null)
ALTER TABLE public.rfqs ADD CONSTRAINT rfqs_origin_coordinates_complete CHECK (
  (origin_lat IS NULL AND origin_lng IS NULL) OR 
  (origin_lat IS NOT NULL AND origin_lng IS NOT NULL)
);

ALTER TABLE public.rfqs ADD CONSTRAINT rfqs_destination_coordinates_complete CHECK (
  (destination_lat IS NULL AND destination_lng IS NULL) OR 
  (destination_lat IS NOT NULL AND destination_lng IS NOT NULL)
);
