-- Seed Countries
INSERT INTO public.countries (name, alpha2_code, alpha3_code, currency_code, continent, region) VALUES
('Afghanistan', 'AF', 'AFG', 'AFN', 'Asia', 'Southern Asia'),
('Åland Islands', 'AX', 'ALA', 'EUR', 'Europe', 'Northern Europe'),
('Albania', 'AL', 'ALB', 'ALL', 'Europe', 'Southern Europe'),
('Algeria', 'DZ', 'DZA', 'DZD', 'Africa', 'Northern Africa'),
('American Samoa', 'AS', 'ASM', 'USD', 'Oceania', 'Polynesia'),
('Andorra', 'AD', 'AND', 'EUR', 'Europe', 'Southern Europe'),
('Angola', 'AO', 'AGO', 'AOA', 'Africa', 'Middle Africa'),
('Anguilla', 'AI', 'AIA', 'XCD', 'North America', 'Caribbean'),
('Antarctica', 'AQ', 'ATA', NULL, 'Antarctica', 'Antarctica'), -- XXX represents no currency
('Antigua and Barbuda', 'AG', 'ATG', 'XCD', 'North America', 'Caribbean'),
('Argentina', 'AR', 'ARG', 'ARS', 'South America', 'South America'),
('Armenia', 'AM', 'ARM', 'AMD', 'Asia', 'Western Asia'),
('Aruba', 'AW', 'ABW', 'AWG', 'North America', 'Caribbean'),
('Australia', 'AU', 'AUS', 'AUD', 'Oceania', 'Australia and New Zealand'),
('Austria', 'AT', 'AUT', 'EUR', 'Europe', 'Western Europe'),
('Azerbaijan', 'AZ', 'AZE', 'AZN', 'Asia', 'Western Asia'),
('Bahamas', 'BS', 'BHS', 'BSD', 'North America', 'Caribbean'),
('Bahrain', 'BH', 'BHR', 'BHD', 'Asia', 'Western Asia'),
('Bangladesh', 'BD', 'BGD', 'BDT', 'Asia', 'Southern Asia'),
('Barbados', 'BB', 'BRB', 'BBD', 'North America', 'Caribbean'),
('Belarus', 'BY', 'BLR', 'BYN', 'Europe', 'Eastern Europe'),
('Belgium', 'BE', 'BEL', 'EUR', 'Europe', 'Western Europe'),
('Belize', 'BZ', 'BLZ', 'BZD', 'North America', 'Central America'),
('Benin', 'BJ', 'BEN', 'XOF', 'Africa', 'Western Africa'),
('Bermuda', 'BM', 'BMU', 'BMD', 'North America', 'Northern America'),
('Bhutan', 'BT', 'BTN', 'BTN', 'Asia', 'Southern Asia'),
('Bolivia', 'BO', 'BOL', 'BOB', 'South America', 'South America'),
('Bonaire, Sint Eustatius and Saba', 'BQ', 'BES', 'USD', 'North America', 'Caribbean'),
('Bosnia and Herzegovina', 'BA', 'BIH', 'BAM', 'Europe', 'Southern Europe'),
('Botswana', 'BW', 'BWA', 'BWP', 'Africa', 'Southern Africa'),
('Bouvet Island', 'BV', 'BVT', 'NOK', 'Antarctica', 'Antarctica'), -- Norwegian Krone used
('Brazil', 'BR', 'BRA', 'BRL', 'South America', 'South America'),
('British Indian Ocean Territory', 'IO', 'IOT', 'USD', 'Asia', 'Eastern Africa'), -- Geographically Africa, politically UK, uses USD
('Brunei Darussalam', 'BN', 'BRN', 'BND', 'Asia', 'South-eastern Asia'),
('Bulgaria', 'BG', 'BGR', 'BGN', 'Europe', 'Eastern Europe'),
('Burkina Faso', 'BF', 'BFA', 'XOF', 'Africa', 'Western Africa'),
('Burundi', 'BI', 'BDI', 'BIF', 'Africa', 'Eastern Africa'),
('Cabo Verde', 'CV', 'CPV', 'CVE', 'Africa', 'Western Africa'),
('Cambodia', 'KH', 'KHM', 'KHR', 'Asia', 'South-eastern Asia'),
('Cameroon', 'CM', 'CMR', 'XAF', 'Africa', 'Middle Africa'),
('Canada', 'CA', 'CAN', 'CAD', 'North America', 'Northern America'),
('Cayman Islands', 'KY', 'CYM', 'KYD', 'North America', 'Caribbean'),
('Central African Republic', 'CF', 'CAF', 'XAF', 'Africa', 'Middle Africa'),
('Chad', 'TD', 'TCD', 'XAF', 'Africa', 'Middle Africa'),
('Chile', 'CL', 'CHL', 'CLP', 'South America', 'South America'),
('China', 'CN', 'CHN', 'CNY', 'Asia', 'Eastern Asia'),
('Christmas Island', 'CX', 'CXR', 'AUD', 'Oceania', 'Australia and New Zealand'), -- Politically Australia
('Cocos (Keeling) Islands', 'CC', 'CCK', 'AUD', 'Oceania', 'Australia and New Zealand'), -- Politically Australia
('Colombia', 'CO', 'COL', 'COP', 'South America', 'South America'),
('Comoros', 'KM', 'COM', 'KMF', 'Africa', 'Eastern Africa'),
('Congo, Democratic Republic of the', 'CD', 'COD', 'CDF', 'Africa', 'Middle Africa'),
('Congo', 'CG', 'COG', 'XAF', 'Africa', 'Middle Africa'),
('Cook Islands', 'CK', 'COK', 'NZD', 'Oceania', 'Polynesia'),
('Costa Rica', 'CR', 'CRI', 'CRC', 'North America', 'Central America'),
('Côte d''Ivoire', 'CI', 'CIV', 'XOF', 'Africa', 'Western Africa'),
('Croatia', 'HR', 'HRV', 'EUR', 'Europe', 'Southern Europe'), -- Adopted EUR in 2023
('Cuba', 'CU', 'CUB', 'CUP', 'North America', 'Caribbean'),
('Curaçao', 'CW', 'CUW', 'ANG', 'North America', 'Caribbean'),
('Cyprus', 'CY', 'CYP', 'EUR', 'Asia', 'Western Asia'), -- Geographically Asia, culturally/politically often Europe
('Czechia', 'CZ', 'CZE', 'CZK', 'Europe', 'Eastern Europe'),
('Denmark', 'DK', 'DNK', 'DKK', 'Europe', 'Northern Europe'),
('Djibouti', 'DJ', 'DJI', 'DJF', 'Africa', 'Eastern Africa'),
('Dominica', 'DM', 'DMA', 'XCD', 'North America', 'Caribbean'),
('Dominican Republic', 'DO', 'DOM', 'DOP', 'North America', 'Caribbean'),
('Ecuador', 'EC', 'ECU', 'USD', 'South America', 'South America'), -- Uses USD
('Egypt', 'EG', 'EGY', 'EGP', 'Africa', 'Northern Africa'), -- Also partly in Asia (Sinai)
('El Salvador', 'SV', 'SLV', 'USD', 'North America', 'Central America'), -- Uses USD
('Equatorial Guinea', 'GQ', 'GNQ', 'XAF', 'Africa', 'Middle Africa'),
('Eritrea', 'ER', 'ERI', 'ERN', 'Africa', 'Eastern Africa'),
('Estonia', 'EE', 'EST', 'EUR', 'Europe', 'Northern Europe'),
('Eswatini', 'SZ', 'SWZ', 'SZL', 'Africa', 'Southern Africa'),
('Ethiopia', 'ET', 'ETH', 'ETB', 'Africa', 'Eastern Africa'),
('Falkland Islands', 'FK', 'FLK', 'FKP', 'South America', 'South America'),
('Faroe Islands', 'FO', 'FRO', 'DKK', 'Europe', 'Northern Europe'), -- Danish Krone used (also has Faroese króna FOK pegged 1:1)
('Fiji', 'FJ', 'FJI', 'FJD', 'Oceania', 'Melanesia'),
('Finland', 'FI', 'FIN', 'EUR', 'Europe', 'Northern Europe'),
('France', 'FR', 'FRA', 'EUR', 'Europe', 'Western Europe'),
('French Guiana', 'GF', 'GUF', 'EUR', 'South America', 'South America'),
('French Polynesia', 'PF', 'PYF', 'XPF', 'Oceania', 'Polynesia'),
('French Southern Territories', 'TF', 'ATF', 'EUR', 'Antarctica', 'Antarctica'), -- Uses EUR
('Gabon', 'GA', 'GAB', 'XAF', 'Africa', 'Middle Africa'),
('Gambia', 'GM', 'GMB', 'GMD', 'Africa', 'Western Africa'),
('Georgia', 'GE', 'GEO', 'GEL', 'Asia', 'Western Asia'), -- Transcontinental
('Germany', 'DE', 'DEU', 'EUR', 'Europe', 'Western Europe'),
('Ghana', 'GH', 'GHA', 'GHS', 'Africa', 'Western Africa'),
('Gibraltar', 'GI', 'GIB', 'GIP', 'Europe', 'Southern Europe'),
('Greece', 'GR', 'GRC', 'EUR', 'Europe', 'Southern Europe'),
('Greenland', 'GL', 'GRL', 'DKK', 'North America', 'Northern America'),
('Grenada', 'GD', 'GRD', 'XCD', 'North America', 'Caribbean'),
('Guadeloupe', 'GP', 'GLP', 'EUR', 'North America', 'Caribbean'),
('Guam', 'GU', 'GUM', 'USD', 'Oceania', 'Micronesia'),
('Guatemala', 'GT', 'GTM', 'GTQ', 'North America', 'Central America'),
('Guernsey', 'GG', 'GGY', 'GBP', 'Europe', 'Northern Europe'), -- Uses GBP (also Guernsey pound GGP pegged 1:1)
('Guinea', 'GN', 'GIN', 'GNF', 'Africa', 'Western Africa'),
('Guinea-Bissau', 'GW', 'GNB', 'XOF', 'Africa', 'Western Africa'),
('Guyana', 'GY', 'GUY', 'GYD', 'South America', 'South America'),
('Haiti', 'HT', 'HTI', 'HTG', 'North America', 'Caribbean'),
('Heard Island and McDonald Islands', 'HM', 'HMD', 'AUD', 'Antarctica', 'Antarctica'), -- Uses AUD
('Holy See', 'VA', 'VAT', 'EUR', 'Europe', 'Southern Europe'),
('Honduras', 'HN', 'HND', 'HNL', 'North America', 'Central America'),
('Hong Kong', 'HK', 'HKG', 'HKD', 'Asia', 'Eastern Asia'),
('Hungary', 'HU', 'HUN', 'HUF', 'Europe', 'Eastern Europe'),
('Iceland', 'IS', 'ISL', 'ISK', 'Europe', 'Northern Europe'),
('India', 'IN', 'IND', 'INR', 'Asia', 'Southern Asia'),
('Indonesia', 'ID', 'IDN', 'IDR', 'Asia', 'South-eastern Asia'), -- Also partly in Oceania
('Iran', 'IR', 'IRN', 'IRR', 'Asia', 'Southern Asia'),
('Iraq', 'IQ', 'IRQ', 'IQD', 'Asia', 'Western Asia'),
('Ireland', 'IE', 'IRL', 'EUR', 'Europe', 'Northern Europe'),
('Isle of Man', 'IM', 'IMN', 'GBP', 'Europe', 'Northern Europe'), -- Uses GBP (also Manx pound IMP pegged 1:1)
('Israel', 'IL', 'ISR', 'ILS', 'Asia', 'Western Asia'),
('Italy', 'IT', 'ITA', 'EUR', 'Europe', 'Southern Europe'),
('Jamaica', 'JM', 'JAM', 'JMD', 'North America', 'Caribbean'),
('Japan', 'JP', 'JPN', 'JPY', 'Asia', 'Eastern Asia'),
('Jersey', 'JE', 'JEY', 'GBP', 'Europe', 'Northern Europe'), -- Uses GBP (also Jersey pound JEP pegged 1:1)
('Jordan', 'JO', 'JOR', 'JOD', 'Asia', 'Western Asia'),
('Kazakhstan', 'KZ', 'KAZ', 'KZT', 'Asia', 'Central Asia'), -- Transcontinental (partly in Europe)
('Kenya', 'KE', 'KEN', 'KES', 'Africa', 'Eastern Africa'),
('Kiribati', 'KI', 'KIR', 'AUD', 'Oceania', 'Micronesia'), -- Uses AUD
('Korea, Democratic People''s Republic of', 'KP', 'PRK', 'KPW', 'Asia', 'Eastern Asia'),
('Korea, Republic of', 'KR', 'KOR', 'KRW', 'Asia', 'Eastern Asia'),
('Kuwait', 'KW', 'KWT', 'KWD', 'Asia', 'Western Asia'),
('Kyrgyzstan', 'KG', 'KGZ', 'KGS', 'Asia', 'Central Asia'),
('Lao People''s Democratic Republic', 'LA', 'LAO', 'LAK', 'Asia', 'South-eastern Asia'),
('Latvia', 'LV', 'LVA', 'EUR', 'Europe', 'Northern Europe'),
('Lebanon', 'LB', 'LBN', 'LBP', 'Asia', 'Western Asia'),
('Lesotho', 'LS', 'LSO', 'LSL', 'Africa', 'Southern Africa'),
('Liberia', 'LR', 'LBR', 'LRD', 'Africa', 'Western Africa'),
('Libya', 'LY', 'LBY', 'LYD', 'Africa', 'Northern Africa'),
('Liechtenstein', 'LI', 'LIE', 'CHF', 'Europe', 'Western Europe'),
('Lithuania', 'LT', 'LTU', 'EUR', 'Europe', 'Northern Europe'),
('Luxembourg', 'LU', 'LUX', 'EUR', 'Europe', 'Western Europe'),
('Macao', 'MO', 'MAC', 'MOP', 'Asia', 'Eastern Asia'),
('Madagascar', 'MG', 'MDG', 'MGA', 'Africa', 'Eastern Africa'),
('Malawi', 'MW', 'MWI', 'MWK', 'Africa', 'Eastern Africa'),
('Malaysia', 'MY', 'MYS', 'MYR', 'Asia', 'South-eastern Asia'),
('Maldives', 'MV', 'MDV', 'MVR', 'Asia', 'Southern Asia'),
('Mali', 'ML', 'MLI', 'XOF', 'Africa', 'Western Africa'),
('Malta', 'MT', 'MLT', 'EUR', 'Europe', 'Southern Europe'),
('Marshall Islands', 'MH', 'MHL', 'USD', 'Oceania', 'Micronesia'), -- Uses USD
('Martinique', 'MQ', 'MTQ', 'EUR', 'North America', 'Caribbean'),
('Mauritania', 'MR', 'MRT', 'MRU', 'Africa', 'Western Africa'),
('Mauritius', 'MU', 'MUS', 'MUR', 'Africa', 'Eastern Africa'),
('Mayotte', 'YT', 'MYT', 'EUR', 'Africa', 'Eastern Africa'),
('Mexico', 'MX', 'MEX', 'MXN', 'North America', 'Central America'), -- Geographically North America, often grouped with Central America culturally/regionally
('Micronesia', 'FM', 'FSM', 'USD', 'Oceania', 'Micronesia'), -- Uses USD
('Moldova', 'MD', 'MDA', 'MDL', 'Europe', 'Eastern Europe'),
('Monaco', 'MC', 'MCO', 'EUR', 'Europe', 'Western Europe'),
('Mongolia', 'MN', 'MNG', 'MNT', 'Asia', 'Eastern Asia'),
('Montenegro', 'ME', 'MNE', 'EUR', 'Europe', 'Southern Europe'), -- Uses EUR unilaterally
('Montserrat', 'MS', 'MSR', 'XCD', 'North America', 'Caribbean'),
('Morocco', 'MA', 'MAR', 'MAD', 'Africa', 'Northern Africa'),
('Mozambique', 'MZ', 'MOZ', 'MZN', 'Africa', 'Eastern Africa'),
('Myanmar', 'MM', 'MMR', 'MMK', 'Asia', 'South-eastern Asia'),
('Namibia', 'NA', 'NAM', 'NAD', 'Africa', 'Southern Africa'),
('Nauru', 'NR', 'NRU', 'AUD', 'Oceania', 'Micronesia'), -- Uses AUD
('Nepal', 'NP', 'NPL', 'NPR', 'Asia', 'Southern Asia'),
('Netherlands', 'NL', 'NLD', 'EUR', 'Europe', 'Western Europe'),
('New Caledonia', 'NC', 'NCL', 'XPF', 'Oceania', 'Melanesia'),
('New Zealand', 'NZ', 'NZL', 'NZD', 'Oceania', 'Australia and New Zealand'),
('Nicaragua', 'NI', 'NIC', 'NIO', 'North America', 'Central America'),
('Niger', 'NE', 'NER', 'XOF', 'Africa', 'Western Africa'),
('Nigeria', 'NG', 'NGA', 'NGN', 'Africa', 'Western Africa'),
('Niue', 'NU', 'NIU', 'NZD', 'Oceania', 'Polynesia'), -- Uses NZD
('Norfolk Island', 'NF', 'NFK', 'AUD', 'Oceania', 'Australia and New Zealand'), -- Uses AUD
('North Macedonia', 'MK', 'MKD', 'MKD', 'Europe', 'Southern Europe'),
('Northern Mariana Islands', 'MP', 'MNP', 'USD', 'Oceania', 'Micronesia'), -- Uses USD
('Norway', 'NO', 'NOR', 'NOK', 'Europe', 'Northern Europe'),
('Oman', 'OM', 'OMN', 'OMR', 'Asia', 'Western Asia'),
('Pakistan', 'PK', 'PAK', 'PKR', 'Asia', 'Southern Asia'),
('Palau', 'PW', 'PLW', 'USD', 'Oceania', 'Micronesia'), -- Uses USD
('Palestine, State of', 'PS', 'PSE', 'ILS', 'Asia', 'Western Asia'), -- Primarily ILS, also JOD, EGP used
('Panama', 'PA', 'PAN', 'PAB', 'North America', 'Central America'), -- PAB pegged 1:1 with USD, USD widely used
('Papua New Guinea', 'PG', 'PNG', 'PGK', 'Oceania', 'Melanesia'),
('Paraguay', 'PY', 'PRY', 'PYG', 'South America', 'South America'),
('Peru', 'PE', 'PER', 'PEN', 'South America', 'South America'),
('Philippines', 'PH', 'PHL', 'PHP', 'Asia', 'South-eastern Asia'),
('Pitcairn', 'PN', 'PCN', 'NZD', 'Oceania', 'Polynesia'), -- Uses NZD
('Poland', 'PL', 'POL', 'PLN', 'Europe', 'Eastern Europe'),
('Portugal', 'PT', 'PRT', 'EUR', 'Europe', 'Southern Europe'),
('Puerto Rico', 'PR', 'PRI', 'USD', 'North America', 'Caribbean'), -- Uses USD
('Qatar', 'QA', 'QAT', 'QAR', 'Asia', 'Western Asia'),
('Réunion', 'RE', 'REU', 'EUR', 'Africa', 'Eastern Africa'),
('Romania', 'RO', 'ROU', 'RON', 'Europe', 'Eastern Europe'),
('Russian Federation', 'RU', 'RUS', 'RUB', 'Europe', 'Eastern Europe'), -- Transcontinental (mostly Asia)
('Rwanda', 'RW', 'RWA', 'RWF', 'Africa', 'Eastern Africa'),
('Saint Barthélemy', 'BL', 'BLM', 'EUR', 'North America', 'Caribbean'),
('Saint Helena, Ascension and Tristan da Cunha', 'SH', 'SHN', 'SHP', 'Africa', 'Western Africa'), -- SHP pegged 1:1 with GBP
('Saint Kitts and Nevis', 'KN', 'KNA', 'XCD', 'North America', 'Caribbean'),
('Saint Lucia', 'LC', 'LCA', 'XCD', 'North America', 'Caribbean'),
('Saint Martin (French part)', 'MF', 'MAF', 'EUR', 'North America', 'Caribbean'),
('Saint Pierre and Miquelon', 'PM', 'SPM', 'EUR', 'North America', 'Northern America'),
('Saint Vincent and the Grenadines', 'VC', 'VCT', 'XCD', 'North America', 'Caribbean'),
('Samoa', 'WS', 'WSM', 'WST', 'Oceania', 'Polynesia'),
('San Marino', 'SM', 'SMR', 'EUR', 'Europe', 'Southern Europe'),
('Sao Tome and Principe', 'ST', 'STP', 'STN', 'Africa', 'Middle Africa'),
('Saudi Arabia', 'SA', 'SAU', 'SAR', 'Asia', 'Western Asia'),
('Senegal', 'SN', 'SEN', 'XOF', 'Africa', 'Western Africa'),
('Serbia', 'RS', 'SRB', 'RSD', 'Europe', 'Southern Europe'),
('Seychelles', 'SC', 'SYC', 'SCR', 'Africa', 'Eastern Africa'),
('Sierra Leone', 'SL', 'SLE', 'SLL', 'Africa', 'Western Africa'),
('Singapore', 'SG', 'SGP', 'SGD', 'Asia', 'South-eastern Asia'),
('Sint Maarten (Dutch part)', 'SX', 'SXM', 'ANG', 'North America', 'Caribbean'),
('Slovakia', 'SK', 'SVK', 'EUR', 'Europe', 'Eastern Europe'),
('Slovenia', 'SI', 'SVN', 'EUR', 'Europe', 'Southern Europe'),
('Solomon Islands', 'SB', 'SLB', 'SBD', 'Oceania', 'Melanesia'),
('Somalia', 'SO', 'SOM', 'SOS', 'Africa', 'Eastern Africa'),
('South Africa', 'ZA', 'ZAF', 'ZAR', 'Africa', 'Southern Africa'),
('South Georgia and the South Sandwich Islands', 'GS', 'SGS', 'GBP', 'Antarctica', 'Antarctica'), -- Uses GBP
('South Sudan', 'SS', 'SSD', 'SSP', 'Africa', 'Eastern Africa'),
('Spain', 'ES', 'ESP', 'EUR', 'Europe', 'Southern Europe'),
('Sri Lanka', 'LK', 'LKA', 'LKR', 'Asia', 'Southern Asia'),
('Sudan', 'SD', 'SDN', 'SDG', 'Africa', 'Northern Africa'),
('Suriname', 'SR', 'SUR', 'SRD', 'South America', 'South America'),
('Svalbard and Jan Mayen', 'SJ', 'SJM', 'NOK', 'Europe', 'Northern Europe'),
('Sweden', 'SE', 'SWE', 'SEK', 'Europe', 'Northern Europe'),
('Switzerland', 'CH', 'CHE', 'CHF', 'Europe', 'Western Europe'),
('Syrian Arab Republic', 'SY', 'SYR', 'SYP', 'Asia', 'Western Asia'),
('Taiwan', 'TW', 'TWN', 'TWD', 'Asia', 'Eastern Asia'),
('Tajikistan', 'TJ', 'TJK', 'TJS', 'Asia', 'Central Asia'),
('Tanzania, United Republic of', 'TZ', 'TZA', 'TZS', 'Africa', 'Eastern Africa'),
('Thailand', 'TH', 'THA', 'THB', 'Asia', 'South-eastern Asia'),
('Timor-Leste', 'TL', 'TLS', 'USD', 'Asia', 'South-eastern Asia'), -- Uses USD
('Togo', 'TG', 'TGO', 'XOF', 'Africa', 'Western Africa'),
('Tokelau', 'TK', 'TKL', 'NZD', 'Oceania', 'Polynesia'), -- Uses NZD
('Tonga', 'TO', 'TON', 'TOP', 'Oceania', 'Polynesia'),
('Trinidad and Tobago', 'TT', 'TTO', 'TTD', 'North America', 'Caribbean'),
('Tunisia', 'TN', 'TUN', 'TND', 'Africa', 'Northern Africa'),
('Turkey', 'TR', 'TUR', 'TRY', 'Asia', 'Western Asia'), -- Transcontinental (partly in Europe)
('Turkmenistan', 'TM', 'TKM', 'TMT', 'Asia', 'Central Asia'),
('Turks and Caicos Islands', 'TC', 'TCA', 'USD', 'North America', 'Caribbean'), -- Uses USD
('Tuvalu', 'TV', 'TUV', 'AUD', 'Oceania', 'Polynesia'), -- Uses AUD (also Tuvaluan dollar TVD pegged 1:1)
('Uganda', 'UG', 'UGA', 'UGX', 'Africa', 'Eastern Africa'),
('Ukraine', 'UA', 'UKR', 'UAH', 'Europe', 'Eastern Europe'),
('United Arab Emirates', 'AE', 'ARE', 'AED', 'Asia', 'Western Asia'),
('United Kingdom', 'GB', 'GBR', 'GBP', 'Europe', 'Northern Europe'),
('United States Minor Outlying Islands', 'UM', 'UMI', 'USD', 'Oceania', 'Micronesia/Polynesia'), -- Geographically diverse, uses USD
('United States of America', 'US', 'USA', 'USD', 'North America', 'Northern America'),
('Uruguay', 'UY', 'URY', 'UYU', 'South America', 'South America'),
('Uzbekistan', 'UZ', 'UZB', 'UZS', 'Asia', 'Central Asia'),
('Vanuatu', 'VU', 'VUT', 'VUV', 'Oceania', 'Melanesia'),
('Venezuela', 'VE', 'VEN', 'VES', 'South America', 'South America'),
('Viet Nam', 'VN', 'VNM', 'VND', 'Asia', 'South-eastern Asia'),
('Virgin Islands (British)', 'VG', 'VGB', 'USD', 'North America', 'Caribbean'), -- Uses USD
('Virgin Islands (U.S.)', 'VI', 'VIR', 'USD', 'North America', 'Caribbean'), -- Uses USD
('Wallis and Futuna', 'WF', 'WLF', 'XPF', 'Oceania', 'Polynesia'),
('Western Sahara', 'EH', 'ESH', 'MAD', 'Africa', 'Northern Africa'), -- Disputed, MAD most common currency
('Yemen', 'YE', 'YEM', 'YER', 'Asia', 'Western Asia'),
('Zambia', 'ZM', 'ZMB', 'ZMW', 'Africa', 'Eastern Africa'),
('Zimbabwe', 'ZW', 'ZWE', 'ZWL', 'Africa', 'Eastern Africa') -- ZWL, also uses USD and others
ON CONFLICT (alpha2_code) DO NOTHING;

-- Seed Equipment Types
INSERT INTO public.equipment_types (name, category, description) VALUES
('Tautliner', 'trailer', 'Curtainside trailer with flexible, tarpaulin sides'),
('Coil-Line Trailer', 'trailer', 'Specialized trailer designed for transporting steel coils'),
('Regular Flat-bed Trailer', 'trailer', 'Standard flatbed trailer for general cargo'),
('Oversize Flat-bed Trailer', 'trailer', 'Flatbed trailer designed for oversize cargo transport'),
('40'' Container', 'container', 'Standard 40-foot shipping container'),
('20'' Container', 'container', 'Standard 20-foot shipping container'),
('Rail', 'rail', 'Railway')
ON CONFLICT (name) DO NOTHING;

-- Seed Cargo Types
INSERT INTO public.cargo_types (name, description)
VALUES
  ('Plates', 'Flat, rectangular steel products with uniform thickness.'),
  ('Slabs', 'Thick, flat semi-finished steel with large width and length.'),
  ('Offcuts', 'Irregular leftover pieces from steel cutting processes.'),
  ('Heavy Plates', 'Thicker version of plates, generally over 10 mm thick.'),
  ('Unassorted Sheets', 'Mixed sheet items with varied thickness and size.'),
  ('Slit Coils', 'Coils cut longitudinally into narrower widths.'),
  ('Coils', 'Continuous rolled steel in wound form.'),
  ('Assorted Sheets', 'Grouped sheets of various dimensions and gauges.'),
  ('Strips', 'Narrow, flat steel pieces with small thickness and width.'),
  ('Sheets', 'Thin, flat steel with standardized width and length.'),
  ('Semi-assorted Sheets', 'Partially categorized sheets by thickness or length.'),
  ('Bulb Flats', 'Flat steel with a rounded bulb edge on one side.'),
  ('Blanks', 'Pre-sized flat shapes typically cut from coils or sheets.'),
  ('Perforated Sheets', 'Flat sheets featuring evenly spaced holes or patterns.'),
  ('Sheet Piles', 'Interlocking flat sections with extended vertical edges.'),
  ('Equal Angles', 'L-shaped profiles with equal leg lengths.'),
  ('Unequal Angles', 'L-shaped profiles with different leg lengths.'),
  ('I-Sections', 'Profiles with an I-shaped cross-section and parallel flanges.'),
  ('Ingots', 'Large steel blocks with trapezoidal cross-sections.'),
  ('Hexagonal Bars', 'Solid steel bars with six-sided cross-sections.'),
  ('Profiles', 'Steel items with standardized cross-sectional shapes.'),
  ('Flat Bars', 'Long, flat steel sections with rectangular cross-sections.'),
  ('Round Bars', 'Solid cylindrical steel products with circular cross-section.'),
  ('Round Billets', 'Cylindrical semi-finished steel with uniform diameter.'),
  ('Blooms', 'Large semi-finished steel with rectangular or square section.'),
  ('L-Sections', 'Steel profiles shaped like the letter L.'),
  ('T-Sections', 'Steel profiles shaped like the letter T.'),
  ('U-Sections', 'Channel-shaped profiles with a U cross-section.'),
  ('Square Billets', 'Square semi-finished steel for further processing.'),
  ('Square Bars', 'Solid steel with square cross-sections.'),
  ('Z-Sections', 'Steel profiles with a Z-shaped cross-section.'),
  ('H-Sections', 'Wide flange profiles with an H-shaped cross-section.'),
  ('Rebars', 'Steel bars with surface ribs for mechanical anchoring.'),
  ('Wire Rods', 'Hot-rolled round steel in coil form with small diameter.'),
  ('Octagonal Bars', 'Steel bars with eight flat sides and straight edges.'),
  ('Scrap', 'Irregular steel remnants of various dimensions and grades.'),
  ('Welded Round Tubes', 'Hollow circular steel sections with visible weld seam.'),
  ('Seamless Round Tubes', 'Round hollow sections without weld lines.'),
  ('Square Tubes', 'Hollow steel sections with square cross-section.'),
  ('Oval Tubes', 'Steel tubes with elliptical cross-sections.'),
  ('Rectangular Tubes', 'Hollow steel sections with rectangular profiles.'),
  ('Rectangular Hollow Section', 'Standardized hollow sections with rectangular geometry.'),
  ('Square Hollow Section', 'Standardized hollow sections with square geometry.'),
  ('Baby Coils', 'Small-sized narrow coils for easier handling or testing.'),
  ('Round Tubes', 'Hollow cylindrical sections with circular cross-section.')
;


-- Seed Roles
INSERT INTO public.roles (name) VALUES
('admin'),
('user')
ON CONFLICT (name) DO NOTHING;